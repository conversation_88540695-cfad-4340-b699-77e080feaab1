<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wzc</groupId>
        <artifactId>wzc-common-parent-base3</artifactId>
        <version>1.2.0-SNAPSHOT</version>
    </parent>

    <groupId>com.wzc.be</groupId>
    <artifactId>wzc-es-base3.0</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>wzc-es-api</module>
        <module>wzc-es-service</module>
        <module>wzc-es-sync-service</module>
    </modules>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <profiles>
        <profile>
            <id>pro-cloud</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <es-api-version>1.0.0-SNAPSHOT</es-api-version>
                <data-api-version>1.0.0-SNAPSHOT</data-api-version>
                <orm-version>1.0.0-SNAPSHOT</orm-version>
                <auth-data-starter-version>1.0.1-SNAPSHOT</auth-data-starter-version>
                <oms-base-common-version>1.0.5-SNAPSHOT</oms-base-common-version>
                <jsqlparser-version>4.3</jsqlparser-version>
                <canal-client-version>1.1.5</canal-client-version>
                <canal-protocol-version>1.1.5</canal-protocol-version>
                <hibernate-jpa-api-version>1.0.2.Final</hibernate-jpa-api-version>
                <transmittable-thread-local-version>2.14.2</transmittable-thread-local-version>
            </properties>
        </profile>
        <profile>
            <id>test-cloud</id>
            <properties>
                <es-api-version>1.0.0-SNAPSHOT</es-api-version>
                <data-api-version>1.0.0-SNAPSHOT</data-api-version>
                <orm-version>1.0.0-SNAPSHOT</orm-version>
                <auth-data-starter-version>1.0.1-SNAPSHOT</auth-data-starter-version>
                <oms-base-common-version>1.0.5-SNAPSHOT</oms-base-common-version>
                <jsqlparser-version>4.3</jsqlparser-version>
                <canal-client-version>1.1.5</canal-client-version>
                <canal-protocol-version>1.1.5</canal-protocol-version>
                <hibernate-jpa-api-version>1.0.2.Final</hibernate-jpa-api-version>
                <transmittable-thread-local-version>2.14.2</transmittable-thread-local-version>
            </properties>
        </profile>
    </profiles>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.wzc.be</groupId>
                <artifactId>wzc-es-api</artifactId>
                <version>${es-api-version}</version>
            </dependency>
            <dependency>
                <groupId>com.wzc.be</groupId>
                <artifactId>wzc-data-api</artifactId>
                <version>${data-api-version}</version>
            </dependency>
            <dependency>
                <groupId>com.baidu.mapcloud.cloudnative</groupId>
                <artifactId>orm</artifactId>
                <version>${orm-version}</version>
            </dependency>
            <dependency>
                <groupId>com.baidu.mapcloud-standard</groupId>
                <artifactId>auth-data-starter</artifactId>
                <version>${auth-data-starter-version}</version>
            </dependency>
            <dependency>
                <groupId>com.baidu.mapcloud-standard</groupId>
                <artifactId>oms-base-common</artifactId>
                <version>${oms-base-common-version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.jsqlparser</groupId>
                <artifactId>jsqlparser</artifactId>
                <version>${jsqlparser-version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.otter</groupId>
                <artifactId>canal.client</artifactId>
                <version>${canal-client-version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.otter</groupId>
                <artifactId>canal.protocol</artifactId>
                <version>${canal-protocol-version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local-version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.javax.persistence</groupId>
                <artifactId>hibernate-jpa-2.1-api</artifactId>
                <version>${hibernate-jpa-api-version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
