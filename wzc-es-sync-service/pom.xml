<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wzc.be</groupId>
        <artifactId>wzc-es-base3.0</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>wzc-es-sync-service</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.wzc</groupId>
            <artifactId>wzc-common-web-base3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wzc</groupId>
            <artifactId>wzc-common-kafka-base3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wzc</groupId>
            <artifactId>wzc-common-cache-base3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wzc</groupId>
            <artifactId>wzc-common-api-base3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.otter</groupId>
            <artifactId>canal.client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.otter</groupId>
            <artifactId>canal.protocol</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.javax.persistence</groupId>
            <artifactId>hibernate-jpa-2.1-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.mapcloud.cloudnative</groupId>
            <artifactId>orm-sharding</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.mapcloud.cloudnative</groupId>
            <artifactId>job</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.mapcloud.cloudnative</groupId>
            <artifactId>orm-es</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.mapcloud.cloudnative</groupId>
            <artifactId>orm</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>transmittable-thread-local</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.mapcloud-standard</groupId>
            <artifactId>oms-base-common</artifactId>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <!-- **表示多层目录，*表示一级目录 -->
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>