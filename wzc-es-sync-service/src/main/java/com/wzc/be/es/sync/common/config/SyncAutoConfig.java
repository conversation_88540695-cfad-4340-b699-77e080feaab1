/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.sync.common.config;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.google.common.collect.Maps;
import com.wzc.be.es.sync.core.converter.TableNameConverter;
import com.wzc.be.es.sync.core.factory.EntryColumnModelFactory;
import com.wzc.be.es.sync.core.handler.DriverTableHandler;
import com.wzc.be.es.sync.core.handler.OtherTableHandler;
import com.wzc.be.es.sync.core.handler.SyncMessageHandlerImpl;
import com.wzc.be.es.sync.core.handler.impl.CommonEntryHandler;
import com.wzc.be.es.sync.core.handler.impl.RowDataHandlerImpl;
import com.wzc.be.es.sync.core.handler.inter.EntryHandler;
import com.wzc.be.es.sync.core.handler.inter.MessageHandler;
import com.wzc.be.es.sync.core.handler.inter.RowDataHandler;
import com.wzc.be.es.sync.core.model.sync.EsExtendField;
import com.wzc.be.es.sync.core.model.sync.FieldMapping;
import com.wzc.be.es.sync.core.model.sync.SyncGroup;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * canal同步相关配置
 * <AUTHOR>
 * @date 2022年09月30日 5:15 PM
 */
@Configuration
@ConfigurationProperties(prefix = "sync.canal")
public class SyncAutoConfig {

    public static final String MAIN = "main";

    @Getter
    @Setter
    private List<SyncGroup> groupList;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private DriverTableHandler driverTableHandler;

    @Autowired
    private OtherTableHandler otherTableHandler;

    @Autowired
    private TableNameConverter tableNameConverter;

    /**
     * 初始化EntryHandler
     */
    @PostConstruct
    public void init() {
        dynamicInitEntryHandlers();
    }

    /**
     * 初始化同步分组map
     */
    @Bean(name = "syncGroupMap")
    public Map<String, SyncGroup> syncGroupMap(List<EsExtendField> extendFieldList) {
        Map<String, SyncGroup> syncGroupMap = groupList.stream()
                .collect(Collectors.toMap(SyncGroup::getGroupId, Function.identity()));
        Map<String, List<EsExtendField>> extendFieldMap = extendFieldList.stream()
                .collect(Collectors.groupingBy(EsExtendField::getGroupName));
        for (Map.Entry<String, SyncGroup> syncGroupEntry : syncGroupMap.entrySet()) {
            SyncGroup subSyncGroup = syncGroupEntry.getValue();
            if (!extendFieldMap.containsKey(subSyncGroup.getGroupId())) {
                continue;
            }
            List<EsExtendField> groupExtendFieldList = extendFieldMap.get(subSyncGroup.getGroupId());
            subSyncGroup.setExtendFieldList(groupExtendFieldList);
        }
        return syncGroupMap;
    }

    @Bean
    public RowDataHandler<CanalEntry.RowData> rowDataHandler() {
        return new RowDataHandlerImpl(new EntryColumnModelFactory());
    }

    /**
     * 初始化messageHandler
     */
    @Bean(name = "syncMessageHandler")
    public MessageHandler syncFlatMessageHandler(RowDataHandler<CanalEntry.RowData> rowDataHandler,
                                                 List<EntryHandler> entryHandlers) {
        return new SyncMessageHandlerImpl(entryHandlers, rowDataHandler, tableNameConverter);
    }


    private void dynamicInitEntryHandlers() {
        populateConfig();
        ConfigurableApplicationContext context = (ConfigurableApplicationContext)applicationContext;
        DefaultListableBeanFactory beanFactory = (DefaultListableBeanFactory)context.getBeanFactory();
        groupList.forEach(syncGroup -> {
            syncGroup.getTables().forEach(tableInfo -> {
                tableInfo.setTableEventHandler(otherTableHandler);
                if (syncGroup.getMainGroup() && tableInfo.isDriverTable()) {
                    tableInfo.setTableEventHandler(driverTableHandler);
                }
                BeanDefinitionBuilder beanDefinition = BeanDefinitionBuilder.rootBeanDefinition(CommonEntryHandler.class);
                beanDefinition.addPropertyValue("tableInfo", tableInfo);
                beanDefinition.addPropertyValue("syncGroup", syncGroup);
                beanFactory.registerBeanDefinition(syncGroup.getGroupId() + "_" + tableInfo.getTableName(), beanDefinition.getBeanDefinition());
                tableNameConverter.add(syncGroup.getDatabase(), syncGroup.getTables());
            });
        });
    }


    private void populateConfig(){
        groupList.forEach(syncGroup -> {
            Map<String, String> fieldMapping = syncGroup.getFieldMapping();
            String main = fieldMapping.get(MAIN);
            if (StringUtils.isNotBlank(main)) {
                List<FieldMapping> mappings = JSONArray.parseArray(main, FieldMapping.class);
                Map<String, String> mainMap = mappings.stream()
                        .collect(Collectors.toMap(FieldMapping::getOriginName, FieldMapping::getTargetName));
                syncGroup.setMainMappingMap(mainMap);
            }else {
                syncGroup.setMainMappingMap(Maps.newHashMap());
            }

            syncGroup.getTables().forEach(tableInfo -> {
                Map<String, String> columnToEsFieldMap = Maps.newHashMap();
                tableInfo.getColumnInEs().forEach(column -> {
                    String[] split = StringUtils.split(column, ":");
                    if (split.length == 1) {
                        columnToEsFieldMap.put(split[0], split[0]);
                        return;
                    }
                    columnToEsFieldMap.put(split[0], split[1]);
                });
                tableInfo.setColumnToEsFieldMap(columnToEsFieldMap);
            });
        });
    }
}
