/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.sync.core.model.sync;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022年09月30日 5:12 PM
 */
@Data
public class SyncGroup implements Serializable {

    /**
     * es主键字段名(es字段)
     */
     private String docIdColumnName;

    /**
     * 当前group基础的join语句，不包含查询条件
     */
    private String joinSql;

    /**
     * 数据库名
     */
    private String database;

    /**
     * 当前组join涉及的所有表信息
     */
    private List<TableInfo> tables;

    /**
     * join结果column名(别名)与es中field名的映射关系
     */
    private Map<String, String> fieldMapping;

    /**
     *  主数据索引名
     */
    private String esMainIndex;

    /**
     * join结果column名(别名)与es中field名的映射关系
     * main key对应内容
     */
    private Map<String, String> mainMappingMap;

    /**
     * 当前组的组名 做为数据初始化的pathVariable
     */
    private String groupId;


    private boolean syncInsertOpen;

    private boolean syncUpdateOpen;

    /**
     * 是否开启同步任务
     */
    private boolean autoSyncOpen;

    /**
     * es扩展字段实现类
     */
    private List<EsExtendField> extendFieldList;


    /**
     * 是否主group 默认true
     */
    private Boolean mainGroup = true;

}
