package com.wzc.be.es.sync.common.config;

import com.wzc.be.common.kafka.config.KafkaClientJaas;
import com.wzc.be.common.kafka.config.KafkaProducerConfig;
import com.wzc.be.common.kafka.config.proper.KafkaProducerProperties;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class KafkaConfig {

    @Bean("KafkaTemplateByte")
    public KafkaTemplate<String,byte[]> kafkaTemplate(KafkaProducerProperties kafkaProducerProperties){
        return new KafkaTemplate<>(this.producerFactory(kafkaProducerProperties));
    }

    public Map<String, Object> producerConfigs(KafkaProducerProperties kafkaProducerProperties) {
        Map<String, Object> props = new HashMap<>(16);
        if (kafkaProducerProperties != null) {
            props.put("bootstrap.servers", kafkaProducerProperties.getServers());
            props.put("retries", kafkaProducerProperties.getRetries());
            props.put("batch.size", kafkaProducerProperties.getBatchSize());
            props.put("linger.ms", kafkaProducerProperties.getLinger());
            props.put("buffer.memory", kafkaProducerProperties.getBufferMemory());
            props.put("key.serializer", StringSerializer.class);
            props.put("value.serializer", ByteArraySerializer.class);
            props.put("acks", kafkaProducerProperties.getAcks());
            KafkaClientJaas.setKafkaClientJaasMap(props, kafkaProducerProperties);
        }
        return props;
    }

    public ProducerFactory<String, byte[]> producerFactory(KafkaProducerProperties kafkaProducerProperties) {
        return new DefaultKafkaProducerFactory<>(this.producerConfigs(kafkaProducerProperties));
    }
}
