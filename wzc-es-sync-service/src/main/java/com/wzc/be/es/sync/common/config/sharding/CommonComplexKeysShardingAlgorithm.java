/*
 * Copyright (C) 2023 Baidu, Inc. All Rights Reserved.
 */
package com.wzc.be.es.sync.common.config.sharding;

import com.baidu.mapcloud.standard.oms.common.config.SingleRedisSequence;
import com.google.common.collect.Range;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Properties;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingAlgorithm;
import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingValue;


@Slf4j
public class CommonComplexKeysShardingAlgorithm implements ComplexKeysShardingAlgorithm<String> {
    public static final String LOGIC_SUB_NO = "oms_no";

    public static Properties properties;
    public static Integer shardingCount;

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames,
                                         ComplexKeysShardingValue<String> shardingValue) {
        // 范围查询的分片键值集合:sub_no/parent_no
        Map<String, Range<String>> shardingRangeMaps = shardingValue.getColumnNameAndRangeValuesMap();

        // 精确查询的分片键值集合:sub_no/parent_no
        Map<String, Collection<String>> shardingMaps = shardingValue.getColumnNameAndShardingValuesMap();

        if (!shardingRangeMaps.isEmpty()){
            throw new UnsupportedOperationException("只支持精确查询，不支持范围查询");
        }

        // 逻辑表的名称，oms-base
        String tableName = shardingValue.getLogicTableName();

        List<String> noList = new ArrayList<>();
        // oms_no：单号分片键值的集合
        noList.addAll(shardingMaps.getOrDefault(LOGIC_SUB_NO, new ArrayList<>()));

        return noList.stream()
                .filter(StringUtils::isNotBlank)
                .map( id->
                        MessageFormat.format(tableName + "_{0}",
                        Integer.valueOf(SingleRedisSequence.getShardingString(id, shardingCount)))
                )
                .collect(Collectors.toSet());
    }

    @Override
    public Properties getProps() {
        return properties;
    }

    @Override
    public void init(Properties properties) {
        CommonComplexKeysShardingAlgorithm.properties = properties;
        Object shardingCount = properties.get("shardingCount");
        if (Objects.nonNull(shardingCount)) {
            CommonComplexKeysShardingAlgorithm.shardingCount = Integer.valueOf((String) shardingCount);
        }
    }

    @Override
    public String getType() {
        return "COMMON";
    }
}


