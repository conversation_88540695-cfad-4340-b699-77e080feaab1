package com.wzc.be.es.sync.core.extend;

import com.wzc.be.es.sync.core.model.sync.EsExtendField;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * <p>
 *     BMS TOC业务支付操作员名称字段
 * </p>
 *
 * <AUTHOR> Assistant
 * @since 2025/02/18
 */
@Component
public class BmsTocBusinessPayOperatorNameField extends EsExtendField<String, Map<String, Object>> {

    private static final String FIELD_NAME = "payOperatorName";
    private static final String GROUP_NAME = "bmsTocBusinessIndex";
    private static final String DATABASE_NAME = "zt_user";

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public String getData(Map<String, Object> esSourceMap) {
        Object payOperatorIdObj = esSourceMap.get("payOperatorId");
        if (isNull(payOperatorIdObj)) {
            return "";
        }
        if (!StringUtils.isNumeric(String.valueOf(payOperatorIdObj))) {
            return "";
        }
        
        // 获取支付操作员ID
        Long payOperatorId = Long.valueOf(String.valueOf(payOperatorIdObj));
        String querySql = "select user_name as payOperatorName from mid_user_base where id = ? limit 1";
        List<Map<String, Object>> resultList = jdbcTemplate.queryForList(querySql, payOperatorId);
        if (CollectionUtils.isEmpty(resultList)) {
            return "";
        }
        Map<String, Object> resultMap = resultList.get(0);
        if (MapUtils.isEmpty(resultMap)) {
            return "";
        }
        Object payOperatorName = resultMap.get(FIELD_NAME);
        if (nonNull(payOperatorName)) {
            return payOperatorName.toString();
        }
        return "";
    }

    @Override
    public String getFieldName() {
        return FIELD_NAME;
    }

    @Override
    public String getGroupName() {
        return GROUP_NAME;
    }

    @Override
    public String getDataBaseName() {
        return DATABASE_NAME;
    }
}
