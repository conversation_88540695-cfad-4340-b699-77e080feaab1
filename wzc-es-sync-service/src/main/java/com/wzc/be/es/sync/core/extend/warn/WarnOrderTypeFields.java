package com.wzc.be.es.sync.core.extend.warn;

import cn.hutool.core.util.StrUtil;
import com.alibaba.google.common.collect.Maps;
import com.wzc.be.es.sync.core.model.sync.EsExtendField;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * <p>
 *     异常报警车牌号
 * </p>
 *
 * <AUTHOR>
 * @since 2023/12/25
 */
 @Component
public class WarnOrderTypeFields extends EsExtendField<Map<String,Object>, Map<String, Object>> {

    /**
     * 车牌号
     */
    private static final String orderTypeField = "orderType";

    private static final String GROUP_NAME = "cscWarnExceptionIndex";

    @Value("${tms_database_name:wzc_tms_base_3}")
    private String DATABASE_NAME;

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public Map<String,Object> getData(Map<String, Object> esSourceMap) {
        // 获取订单号
        String subWaybillNo = nonNull(esSourceMap.get("subWaybillNo")) ? (String) esSourceMap.get("subWaybillNo") : null;
        if (Objects.nonNull(subWaybillNo)) {
            // 查询地址信息
            String querySql = "SELECT order_type as orderType FROM tms_waybill WHERE waybill_no = ? AND deleted = 0";

            List<Map<String, Object>> resultList = jdbcTemplate.queryForList(querySql, subWaybillNo);

            if (CollectionUtils.isEmpty(resultList)) {
                return Maps.newHashMap();
            }

            Map<String, Object> resultMap = resultList.get(0);
            if (MapUtils.isEmpty(resultMap)) {
                return Maps.newHashMap();
            }

            return resultMap;
        }

        return null;

    }

    @Override
    public String getFieldName() {
        return null;
    }

    @Override
    public List<String> getFieldsName() {
        return List.of(orderTypeField);
    }

    @Override
    public String getGroupName() {
        return GROUP_NAME;
    }

    @Override
    public String getDataBaseName() {
        return DATABASE_NAME;
    }
}
