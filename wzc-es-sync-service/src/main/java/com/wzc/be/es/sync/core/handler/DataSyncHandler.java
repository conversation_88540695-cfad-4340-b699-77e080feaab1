package com.wzc.be.es.sync.core.handler;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.wzc.be.es.sync.common.constants.CanalEsConstants;
import com.wzc.be.es.sync.common.config.SyncProperties;
import com.wzc.be.es.sync.core.model.job.EsSyncParam;
import com.wzc.be.es.sync.core.model.job.SyncCursor;
import com.wzc.be.es.sync.core.model.sync.SyncGroup;
import com.wzc.be.es.sync.core.model.sync.TableInfo;
import com.wzc.be.es.sync.core.util.ElasticSearchUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * es数据同步handler
 *
 * <AUTHOR>
 * @date 2023年03月22日
 */
@Slf4j
@Component
public class DataSyncHandler {

    @Autowired
    private Map<String, SyncGroup> syncGroupMap;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private DriverTableHandler driverTableHandler;

    @Autowired
    private OtherTableHandler otherTableHandler;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private SyncProperties syncProperties;

    private static final ExecutorService DB_DATA_SYNC_EXECUTOR = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors() * 2,
            Runtime.getRuntime().availableProcessors() * 2,
            60, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(10000),
            ThreadUtil.createThreadFactory("DB-DATA-SYNC_EXECUTOR"),
            new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 数据同步任务
     */
    public void syncEsData(EsSyncParam param) {
        log.debug("syncData param:{}", param);

        // 指定初始化参数方式运行
        if (nonNull(param) && StringUtils.isNotBlank(param.getGroupName())) {
            TableInfo tableInfo = null;
            SyncGroup syncGroup = syncGroupMap.get(param.getGroupName());
            if (isNull(syncGroup)) {
                log.debug("syncGroup无法匹配 group:{}", param.getGroupName());
                return;
            }
            if (StringUtils.isNotBlank(param.getTableName())) {
                Optional<TableInfo> tableInfoOp = syncGroup.getTables().stream()
                        .filter(t -> t.getTableName().equals(param.getTableName()))
                        .findFirst();
                if (tableInfoOp.isPresent()) {
                    tableInfo = tableInfoOp.get();
                } else {
                    log.info("syncGroupTable无法匹配 tableName:{}", param.getTableName());
                    return;
                }
            }
            boolean update = nonNull(param.getUpdate()) ? param.getUpdate() : false;
            boolean init = nonNull(param.getInit()) ? param.getInit() : false;
            if (nonNull(tableInfo)) {
                syncGroup(syncGroup, Collections.singletonList(tableInfo), param.getWhereCondition() , init, update);
            } else {
                // 初始化未指定表只运行主表
                List<TableInfo> driverTables = syncGroup.getTables().stream()
                                .filter(t -> Boolean.TRUE.equals(t.isDriverTable()))
                                        .collect(Collectors.toList());
                syncGroup(syncGroup, driverTables, param.getWhereCondition(), init, update);
            }
            return;
        }

        // 常规运行逻辑
        List<String> groupNames = Lists.newArrayList(syncGroupMap.keySet());
        if (CollectionUtils.isEmpty(groupNames)) {
            log.debug("未配置group");
            return;
        }
        Collections.sort(groupNames);
        // 常规巡回数据对比
        while (true) {
            if (Boolean.FALSE.equals(syncProperties.getOpenDataSync())) {
                log.debug("syncEsData openDataSync未开启 openDataSync:{}", syncProperties.getOpenDataSync());
                return;
            }
            if (Boolean.TRUE.equals(SyncProperties.getShutdown())) {
                log.debug("syncEsData 停机 shutdown:{}", SyncProperties.getShutdown());
                return;
            }
            String groupCursorStr = stringRedisTemplate.opsForValue().get(CanalEsConstants.CANAL_ES_SYNC_GROUPCURSOR);
            SyncCursor groupSyncCursor;
            if (StringUtils.isBlank(groupCursorStr)) {
                // 初始化group游标
                groupSyncCursor = initCursor(groupNames);
                stringRedisTemplate.opsForValue().set(CanalEsConstants.CANAL_ES_SYNC_GROUPCURSOR, JSONUtil.toJsonStr(groupSyncCursor), 3, TimeUnit.DAYS);
            } else {
                groupSyncCursor = JSONUtil.toBean(groupCursorStr, SyncCursor.class);
            }
            SyncGroup syncGroup = syncGroupMap.get(groupSyncCursor.getCurrent());
            // 调用同步
            syncGroup(syncGroup, null, null, false, true);
            // 判断是否是最后一个group
            if (groupSyncCursor.getCurrent().equals(groupNames.get(groupNames.size() - 1))) {
                // 删除group游标
                stringRedisTemplate.delete(CanalEsConstants.CANAL_ES_SYNC_GROUPCURSOR);
                log.debug("全部同步完成");
                return;
            }
            // 移动group游标
            nextCursor(groupNames, groupSyncCursor);
            log.debug("syncEsData 移动group游标");
            stringRedisTemplate.opsForValue().set(CanalEsConstants.CANAL_ES_SYNC_GROUPCURSOR, JSONUtil.toJsonStr(groupSyncCursor), 3, TimeUnit.DAYS);
        }
    }

    /**
     * 同步group
     *
     * @param init 是否初始化操作调用
     * @param assignTableList 指定的同步表
     */
    private void syncGroup(SyncGroup syncGroup, List<TableInfo> assignTableList, String whereCondition, boolean init, boolean update) {
        log.debug("syncGroup group:{}", syncGroup.getGroupId());
        //是否开启同步任务
        if (Boolean.FALSE.equals(init) && Boolean.FALSE.equals(syncGroup.isAutoSyncOpen())) {
            log.debug("syncGroup group未开启autoSync group:{}", syncGroup.getGroupId());
            return;
        }
        // 是否指定运行表
        if (CollectionUtils.isEmpty(assignTableList)) {
            String groupTableCursorKey = CanalEsConstants.CANAL_ES_SYNC_GROUPTABLECURSOR_PRE + syncGroup.getGroupId();
            List<String> groupTableNames = initGroupTableNames(syncGroup.getTables());
            if (CollectionUtils.isEmpty(groupTableNames)) {
                log.debug("syncGroup group未配置table groupName:{}", syncGroup.getGroupId());
                return;
            }
            while (true) {
                if (Boolean.FALSE.equals(syncProperties.getOpenDataSync())) {
                    log.debug("syncGroup openDataSync未开启 openDataSync:{}", syncProperties.getOpenDataSync());
                    return;
                }
                if (Boolean.TRUE.equals(SyncProperties.getShutdown())) {
                    log.debug("syncEsData 停机 shutdown:{}", SyncProperties.getShutdown());
                    return;
                }
                // 查询表游标
                String groupTableCursorStr = stringRedisTemplate.opsForValue().get(groupTableCursorKey);
                log.debug("syncGroup groupTableCursorKey:{} groupTableCursorStr:{}", groupTableCursorKey, groupTableCursorStr);
                SyncCursor groupTableCursor = null;
                if (StringUtils.isBlank(groupTableCursorStr)) {
                    // 初始化table游标
                    groupTableCursor = initCursor(groupTableNames);
                    stringRedisTemplate.opsForValue().set(groupTableCursorKey, JSONUtil.toJsonStr(groupTableCursor), 3, TimeUnit.DAYS);
                } else {
                    groupTableCursor = JSONUtil.toBean(groupTableCursorStr, SyncCursor.class);
                }
                Map<String, TableInfo> tableInfoMap = syncGroup.getTables().stream()
                        .collect(Collectors.toMap(TableInfo::getTableName, Function.identity()));
                TableInfo currentTableInfo = tableInfoMap.get(groupTableCursor.getCurrent());
                syncTable(syncGroup, currentTableInfo, whereCondition, init, update);
                // 判断是否是最后一个表
                if (groupTableCursor.getCurrent().equals(groupTableNames.get(groupTableNames.size() - 1))) {
                    // 删除分组表游标
                    stringRedisTemplate.delete(groupTableCursorKey);
                    log.debug("group同步完成 groupName:{}", groupTableCursor.getCurrent());
                    return;
                }
                // 移动分组表游标
                nextCursor(groupTableNames, groupTableCursor);
                log.debug("syncGroup 移动分组表游标");
                stringRedisTemplate.opsForValue().set(groupTableCursorKey, JSONUtil.toJsonStr(groupTableCursor), 3, TimeUnit.DAYS);
            }
        } else {
            assignTableList.sort(Comparator.comparing(TableInfo::isDriverTable).reversed());
            assignTableList.parallelStream().forEach(tableInfo -> syncTable(syncGroup, tableInfo, whereCondition, init, update));
        }
    }

    /**
     * 同步table
     */
    private void syncTable(SyncGroup syncGroup, TableInfo tableInfo, String whereCondition, boolean init, boolean update) {
        log.debug("syncTable groupName:{} tableInfo:{} whereCondition:{}", syncGroup.getGroupId(), tableInfo.getTableName(), whereCondition);
        //表是否开启同步任务
        if (Boolean.FALSE.equals(init) && Boolean.FALSE.equals(tableInfo.isAutoSyncOpen())) {
            log.debug("syncGroup 表未开启autoSync table:{}", tableInfo.getTableName());
            return;
        }
        Map<String, String> columnToEsFieldMap = tableInfo.getColumnToEsFieldMap();
        //是否有映射数据库id字段
        boolean hasDbId = columnToEsFieldMap.containsKey("id");
        StringBuilder stringBuilder = new StringBuilder();
        for (Map.Entry<String, String> entry : columnToEsFieldMap.entrySet()) {
            //key为数据库中的字段
            stringBuilder.append(entry.getKey()).append(",");
        }
        //不包含id映射则添加
        if (!hasDbId){
            stringBuilder.append("id").append(",");
        }

        //如果配置有表的删除逻辑字段 则添加进去查询
        if (StringUtils.isNotBlank(tableInfo.getTableDeleteColumn())){
            stringBuilder.append(tableInfo.getTableDeleteColumn()).append(",");
        }

        String tableColumns = stringBuilder.deleteCharAt(stringBuilder.length() - 1).toString();
        StringBuilder sqlStringBulder = new StringBuilder();

        sqlStringBulder.append(" select ").append(tableColumns).append(" from ").append(tableInfo.getTableName())
                .append(" where ").append(" id ").append(" > ? and ")
                .append(" id ")
                .append(" <= ?");
        if (StringUtils.isNotBlank(whereCondition)) {
            sqlStringBulder.append(" ").append(whereCondition);
        }
        // 非初始化时限制数据创建时间
        if (Boolean.FALSE.equals(init)) {
            DateTime createTimeBegin = DateUtil.offsetDay(new Date(), -syncProperties.getDataSyncOffsetDays());
            sqlStringBulder.append(" and create_time >= '").append(createTimeBegin.toString()).append("'");
        }
        sqlStringBulder.append(" order by ").append(" id ").append(" asc limit ?");
        String sql = sqlStringBulder.toString();
        log.debug("syncGroup sql语句:{}", sql);

        long startId = 0L;
        long endId = Long.MAX_VALUE;

        // 查询历史游标
        String cursorKey = CanalEsConstants.CANAL_ES_SYNC_GROUP_TABLE_CURSOR_PRE + syncGroup.getGroupId() + "." + tableInfo.getTableName();
        if (!init) {
            String cursorStr = stringRedisTemplate.opsForValue().get(cursorKey);
            if (StringUtils.isNotBlank(cursorStr)) {
                startId = Long.parseLong(cursorStr);
            }
        }
        Integer batchSize = 500;
        while (true) {
            if (Boolean.FALSE.equals(syncProperties.getOpenDataSync())) {
                log.debug("syncTable openDataSync未开启 openDataSync:{}", syncProperties.getOpenDataSync());
                return;
            }
            if (Boolean.TRUE.equals(SyncProperties.getShutdown())) {
                log.debug("syncEsData 停机 shutdown:{}", SyncProperties.getShutdown());
                return;
            }
            List<Map<String, Object>> allMapList = jdbcTemplate.queryForList(sql, startId, endId, batchSize);
            if (CollectionUtils.isEmpty(allMapList)) {
                log.debug("batch init data finished, last startId is : {}", startId);
                if (!init) {
                    log.debug("删除数据表同步游标 cursorKey:{}", cursorKey);
                    stringRedisTemplate.delete(cursorKey);
                }
                break;
            }
            log.debug("任务syncGroup:{},数据库:{},表:{},查询到{}条数据, 当前位置 startId:{} endId:{} batchSize:{}", syncGroup.getGroupId(), syncGroup.getDatabase(), tableInfo.getTableName(), allMapList.size(), startId, endId, batchSize);
            // 拆分
            List<List<Map<String, Object>>> mapPartList = Lists.partition(allMapList, 100);
            for (List<Map<String, Object>> subMapList : mapPartList) {
                DB_DATA_SYNC_EXECUTOR.execute(() -> {
                    try {
                        for (Map<String, Object> subMap : subMapList) {
                            ElasticSearchUtil.jdbcDataStandardization(subMap);
                            if (Boolean.TRUE.equals(tableInfo.isDriverTable()) && syncGroup.getMainGroup()) {
                                // 驱动表
                                if (Boolean.TRUE.equals(init) && Boolean.FALSE.equals(update)) {
                                    driverTableHandler.insert(subMap, tableInfo, syncGroup, false, true, Integer.MAX_VALUE);
                                } else {
                                    driverTableHandler.update(null, subMap, subMap.keySet(), tableInfo, syncGroup, Integer.MAX_VALUE);
                                }
                            } else {
                                // 非驱动表
                                if (Boolean.TRUE.equals(init) && Boolean.FALSE.equals(update)) {
                                    otherTableHandler.insert(subMap, tableInfo, syncGroup, false, false, Integer.MAX_VALUE);
                                } else {
                                    otherTableHandler.update(null, subMap, subMap.keySet(), tableInfo, syncGroup, Integer.MAX_VALUE);
                                }
                            }
                        }
                    } catch (Exception ex) {
                        log.error("db data sync error", ex);
                    }
                });
            }
            Map<String, Object> last = allMapList.get(allMapList.size() - 1);
            startId = Long.parseLong(String.valueOf(last.get("id")));
            if (!init) {
                log.debug("记录表同步游标 cursorKey:{} startId:{}", cursorKey, startId);
                stringRedisTemplate.opsForValue().set(cursorKey, String.valueOf(startId), 3, TimeUnit.DAYS);
            }
        }
    }

    /**
     * 初始化游标
     */
    private SyncCursor initCursor(List<String> nameList) {
        int index = 0;
        // 初始化group游标
        String name = nameList.get(index);
        SyncCursor cursor = new SyncCursor();
        cursor.setCurrent(name);
        if (index + 1 < nameList.size()) {
            cursor.setNext(nameList.get(index + 1));
        } else {
            cursor.setNext(nameList.get(0));
        }
        return cursor;
    }

    /**
     * 移动同步游标
     */
    private void nextCursor(List<String> nameList,  SyncCursor syncCursor) {
        syncCursor.setLast(syncCursor.getCurrent());
        syncCursor.setCurrent(syncCursor.getNext());
        // 更新next
        int currentIndex = nameList.indexOf(syncCursor.getCurrent());
        if (currentIndex + 1 < nameList.size()) {
            syncCursor.setNext(nameList.get(currentIndex + 1));
        } else {
            syncCursor.setNext(nameList.get(0));
        }
    }

    /**
     * 初始化排序分组表名
     */
    private List<String> initGroupTableNames(List<TableInfo> tableInfoList) {
        List<TableInfo> driverTableList = tableInfoList.stream()
                .filter(tableInfo -> Boolean.TRUE.equals(tableInfo.isDriverTable()))
                .collect(Collectors.toList());
        List<TableInfo> otherTableList = tableInfoList.stream()
                .filter(tableInfo -> Boolean.FALSE.equals(tableInfo.isDriverTable()))
                .sorted(Comparator.comparing(TableInfo::getTableName))
                .collect(Collectors.toList());
        List<String> groupTableNames = new ArrayList<>();
        groupTableNames.addAll(driverTableList.stream().map(TableInfo::getTableName).distinct().collect(Collectors.toList()));
        groupTableNames.addAll(otherTableList.stream().map(TableInfo::getTableName).distinct().collect(Collectors.toList()));
        return groupTableNames;
    }
}
