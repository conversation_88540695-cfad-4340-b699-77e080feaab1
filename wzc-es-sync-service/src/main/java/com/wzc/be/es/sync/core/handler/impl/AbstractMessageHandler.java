package com.wzc.be.es.sync.core.handler.impl;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.alibaba.otter.canal.protocol.Message;
import com.wzc.be.es.sync.core.converter.Converter;
import com.wzc.be.es.sync.core.handler.inter.EntryHandler;
import com.wzc.be.es.sync.core.handler.inter.MessageHandler;
import com.wzc.be.es.sync.core.handler.inter.RowDataHandler;
import com.wzc.be.es.sync.core.context.CanalContext;
import com.wzc.be.es.sync.core.model.CanalModel;
import com.wzc.be.es.sync.core.util.HandlerUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2022/09/2921:38
 */
@Slf4j
public abstract class AbstractMessageHandler implements MessageHandler<Message> {

    private Map<String, List<EntryHandler>> tableHandlerMap;

    private RowDataHandler<CanalEntry.RowData> rowDataHandler;

    private Converter<String, String> tableNameConverter;

    private static final ExecutorService HANDLER_THREADPOOL = new ThreadPoolExecutor(40, 60,
            60, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(10000),
            ThreadUtil.createThreadFactory("Message-Handler"),
            new ThreadPoolExecutor.CallerRunsPolicy());


    public AbstractMessageHandler(List<? extends EntryHandler> entryHandlers,
                                  RowDataHandler<CanalEntry.RowData> rowDataHandler) {
        this.tableHandlerMap = HandlerUtil.getTableHandlerListMap(entryHandlers);
        this.rowDataHandler = rowDataHandler;
    }

    public AbstractMessageHandler(List<? extends EntryHandler> entryHandlers,
                                  RowDataHandler<CanalEntry.RowData> rowDataHandler,
                                  Converter<String, String> tableNameConverter) {
        this.tableHandlerMap = HandlerUtil.getTableHandlerListMap(entryHandlers);
        this.rowDataHandler = rowDataHandler;
        this.tableNameConverter = tableNameConverter;
    }

    @Override
    @SneakyThrows
    public  void handleMessage(Message message) {
        List<CanalEntry.Entry> entries = message.getEntries();
        for (CanalEntry.Entry entry : entries) {
            if (!entry.getEntryType().equals(CanalEntry.EntryType.ROWDATA)) {
                continue;
            }

            String handlerKey = entry.getHeader().getSchemaName() + ":" + entry.getHeader().getTableName();
            handlerKey = tableNameConverter.convert(handlerKey);
            List<EntryHandler> entryHandlers = HandlerUtil.getEntryHandlerList(tableHandlerMap, handlerKey);
            if (CollectionUtils.isEmpty(entryHandlers)) {
                continue;
            }
            try {
                CanalModel model =
                        CanalModel.Builder.builder().id(message.getId()).table(entry.getHeader().getTableName())
                                .executeTime(entry.getHeader().getExecuteTime())
                                .database(entry.getHeader().getSchemaName()).build();
                CanalContext.setModel(model);
                CanalContext.setTtlModel(model);
                CanalEntry.RowChange rowChange = CanalEntry.RowChange.parseFrom(entry.getStoreValue());
                List<CanalEntry.RowData> rowDataList = rowChange.getRowDatasList();
                CanalEntry.EventType eventType = rowChange.getEventType();
                List<Future> futureList = new ArrayList<>();
                for (CanalEntry.RowData rowData : rowDataList) {
                    Future future = HANDLER_THREADPOOL.submit(() -> {
                        entryHandlers.forEach(entryHandler -> {
                            try {
                                rowDataHandler.handlerRowData(rowData, entryHandler, eventType);
                            } catch (Exception e) {
                                log.error("handlerRowData error", e);
                                throw new RuntimeException(e);
                            }
                        });
                    });
                    futureList.add(future);
                }
                for (Future future : futureList) {
                    future.get();
                }
            }finally {
                CanalContext.removeModel();
                CanalContext.removeTtlModel();
            }
        }
    }

}
