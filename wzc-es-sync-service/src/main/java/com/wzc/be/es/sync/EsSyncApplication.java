package com.wzc.be.es.sync;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 */
@Slf4j
@SpringBootApplication
@EnableFeignClients
public class EsSyncApplication {

    public static void main(String[] args) {
        log.debug("开始启动base3.0 Es 数据同步服务");
        SpringApplication.run(EsSyncApplication.class, args);
        log.debug("启动完成base3.0 Es 数据同步服务");
    }
}
