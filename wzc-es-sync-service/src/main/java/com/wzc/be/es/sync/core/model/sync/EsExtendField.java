package com.wzc.be.es.sync.core.model.sync;

import java.util.List;

/**
 * es宽表扩展字段自定义生成
 *
 * <AUTHOR>
 * @date 2023年03月06日
 */
public abstract class EsExtendField<R,M> {

    public abstract R getData(M m);

    public abstract String getFieldName();

    /**
     * 需要新增的字段集合
     * @return
     */
    public List<String> getFieldsName(){
        return null;
    }

    public abstract String getGroupName();

    public abstract String getDataBaseName();

}
