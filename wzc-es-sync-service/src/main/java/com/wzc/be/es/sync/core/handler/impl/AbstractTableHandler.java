/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.sync.core.handler.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baidu.mapcloud.cloudnative.orm.crypto.storage.api.EncryptStorageApi;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wzc.be.es.sync.common.config.SyncProperties;
import com.wzc.be.es.sync.common.constants.CanalEsConstants;
import com.wzc.be.es.sync.core.context.CanalContext;
import com.wzc.be.es.sync.core.handler.inter.TableEventHandler;
import com.wzc.be.es.sync.core.model.CanalModel;
import com.wzc.be.es.sync.core.model.delay.CanalMessageDelay;
import com.wzc.be.es.sync.core.model.sync.*;
import com.wzc.be.es.sync.core.util.ElasticSearchUtil;
import com.wzc.be.es.sync.core.util.SpelUtil;
import com.wzc.common.cache.utils.RedisDelayQueue;
import com.wzc.common.util.JdkSerializeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.action.DocWriteRequest;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN;
import static cn.hutool.core.date.DatePattern.NORM_TIME_PATTERN;
import static com.wzc.be.es.sync.common.constants.CanalEsConstants.CANAL_ACTION_DELETE;
import static com.wzc.be.es.sync.common.constants.CanalEsConstants.SYNC_EXECUTE_TIME;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * <AUTHOR>
 * @date 2022年10月09日 9:00 PM
 */
@Slf4j
public abstract class AbstractTableHandler implements TableEventHandler, Serializable {

    public static final String DOC_ID = "docId";

    @Autowired
    protected ElasticSearchUtil elasticSearchUtil;

    @Autowired
    protected JdbcTemplate jdbcTemplate;

    @Autowired
    protected RestHighLevelClient restHighLevelClient;

    @Autowired
    protected ScheduledThreadPoolExecutor executor;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private SyncProperties syncProperties;

    @Autowired
    private EncryptStorageApi encryptStorageApi;

    @Override
    public void insert(Map<String, Object> map, TableInfo tableInfo, SyncGroup syncGroup, boolean fromUpdate, boolean fromInit, Integer delayCount) {
        log.debug("AbstractTableHandler insert groupName:{} tableName:{}", syncGroup.getGroupId(), tableInfo.getTableName());

        //不符合插入条件 过滤
        if(judgeConditionBySpEls(map,tableInfo) || isDeletedBySpEl(map,tableInfo)){
            return;
        }

        String baseSql = syncGroup.getJoinSql();
        String querySql = tableInfo.getJoinQueryCondition();
        String sql = baseSql + " " + querySql;
        String mainKey = tableInfo.getMainTableKey();
        String val = String.valueOf(map.get(mainKey));
        List<Map<String, Object>> mapList = jdbcTemplate.queryForList(sql, val);
        if (CollectionUtils.isEmpty(mapList)) {
            log.info("数据库无法查询到关联数据  groupName:{} tableName:{} id:{}", syncGroup.getGroupId(), tableInfo.getTableName(), val);
            return;
        }

        // 来自update的不再校验es
        if (!fromUpdate && !fromInit) {
            // 刷新相关索引
            refreshIndex(syncGroup, Collections.singletonList(val));

            String mainKeyField = tableInfo.getColumnToEsFieldMap().get(tableInfo.getMainTableKey());
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.query(
                    QueryBuilders.termQuery(mainKeyField, map.get(tableInfo.getMainTableKey())));
            SearchResponse response = elasticSearchUtil.search(sourceBuilder, syncGroup.getEsMainIndex());
            if (nonNull(response)
                    && nonNull((response.getHits()))
                    && ArrayUtils.isNotEmpty(response.getHits().getHits())) {
                // 索引已存在，不处理
                log.debug("索引已存在，不处理 groupName:{} tableName:{} keyId:{}", syncGroup.getGroupId(), tableInfo.getTableName(), map.get(tableInfo.getMainTableKey()));
                return;
            }
        }

        // 处理字段mapping
        List<Map<String, Object>> finalList = Lists.newArrayList();
        this.executeMainMapping(syncGroup, mapList, finalList);
        this.extendFieldMapping(syncGroup, finalList);
        this.decryptFields(tableInfo,finalList);
        // 批量写主索引
        this.saveEs(syncGroup, finalList, fromInit);
        saveInsertCache(syncGroup, finalList);
    }

    /**
     * 写入es索引新增缓存记录
     */
    protected void saveInsertCache(SyncGroup syncGroup, List<Map<String, Object>> List) {
        log.debug("saveInsertCache group:{} list:{}", syncGroup.getGroupId(), List);
        TableInfo driverTable = syncGroup.getTables().stream()
                .filter(tableInfo -> Boolean.TRUE.equals(tableInfo.isDriverTable()))
                .findFirst().orElse(null);
        if (isNull(driverTable)) {
            log.warn("saveInsertCache driverTable is null group:{}", syncGroup.getGroupId());
            return;
        }
        String esMainKeyField = driverTable.getColumnToEsFieldMap().get(driverTable.getMainTableKey());
        log.debug("saveInsertCache esMainKeyField:{}", esMainKeyField);
        // 增加主索引写入缓存
        for (Map<String, Object> subMap : List) {
            String cacheKey = CanalEsConstants.CANAL_ES_CACHE_INDEXINSERT_PRE + syncGroup.getEsMainIndex() + ":" + subMap.get(esMainKeyField);
            log.debug("saveInsertCache cacheKey:{}", cacheKey);
            stringRedisTemplate.opsForValue().set(cacheKey, String.valueOf(System.currentTimeMillis()), 30, TimeUnit.SECONDS);
        }
    }

    /**
     * 检查group是否需要刷新索引
     */
    protected void refreshIndex(SyncGroup syncGroup, List<Object> mainTableIds) {
        log.debug("refreshIndex group:{} ids:{}", syncGroup.getGroupId(), mainTableIds);
        // 判断相关主键近期是否有新增操作
        boolean indexHasNewData = false;
        for (Object mainTableId : mainTableIds) {
            String cacheKey = CanalEsConstants.CANAL_ES_CACHE_INDEXINSERT_PRE + syncGroup.getEsMainIndex() + ":" + mainTableId;
            Boolean hasKey = stringRedisTemplate.hasKey(cacheKey);
            if (Boolean.TRUE.equals(hasKey)) {
                indexHasNewData = Boolean.TRUE;
                break;
            }
        }
        if (Boolean.TRUE.equals(indexHasNewData)) {
            // 执行索引刷新
            log.debug("refreshIndex 执行索引刷新开始");
            elasticSearchUtil.refresh(syncGroup.getEsMainIndex());
            log.debug("refreshIndex 执行索引刷新结束");
            // 清空相关缓存
            String keyPrefix = CanalEsConstants.CANAL_ES_CACHE_INDEXINSERT_PRE + syncGroup.getEsMainIndex();
            Set<String> cacheKeys = scanKeysPrefix(keyPrefix);
            for (String cacheKey : cacheKeys) {
                if (!cacheKey.contains(keyPrefix)) {
                    log.error("scanKeys获取到错误的key cacheKey:{} prefix:{}", cacheKey, keyPrefix);
                    continue;
                }
                log.debug("refreshIndex delete cacheKey:{}", cacheKey);
                stringRedisTemplate.delete(cacheKey);
            }
        }
    }

    /**
     * 根据前缀获取相关缓存key
     */
    private Set<String> scanKeysPrefix(String keyPrefix) {
        Set<String> keys = stringRedisTemplate.execute((RedisCallback<Set<String>>) connection -> {
            Set<String> keysTmp = new HashSet<>();
            Cursor<byte[]> cursor = connection.scan(ScanOptions.scanOptions().match( keyPrefix + "*").count(1000).build());
            while (cursor.hasNext()) {
                keysTmp.add(new String(cursor.next()));
            }
            return keysTmp;
        });
        return keys;
    }

    /**
     * 新增es索引记录
     */
    protected void saveEs(SyncGroup syncGroup, List<Map<String, Object>> finalList, boolean fromInit) {
        log.debug("saveEs group:{}", syncGroup.getGroupId());
        finalList.forEach(map -> {
            Object docId = map.get(DOC_ID);
            IndexRequest indexRequest = new IndexRequest(syncGroup.getEsMainIndex())
                    .opType(DocWriteRequest.OpType.INDEX )
                    .source(XContentType.JSON, toArgs(map));
            if (nonNull(docId)) {
                indexRequest.id(String.valueOf(docId));
            }
            log.info("插入到es groupName:{}", syncGroup.getGroupId());
            if (Boolean.TRUE.equals(fromInit)) {
                elasticSearchUtil.insert(indexRequest);
            } else {
                elasticSearchUtil.immediateInsert(indexRequest);
            }
        });
    }

    /**
     * 将批量数据转换成es参数数组
     *
     * @param data
     * @return
     */
    public Object[] toArgs(Map<String, Object> data) {
        List<Object> args = Lists.newArrayList();
        data.remove(DOC_ID);
        if (CollectionUtil.isEmpty(data)) {
            throw new RuntimeException("批量操作数据不可为空 !");
        }
        data.forEach((key, value) -> {
            args.add(key);
            args.add(value);
        });
        return args.toArray();
    }

    public void executeMainMapping(SyncGroup syncGroup, List<Map<String, Object>> mapList,
                                   List<Map<String, Object>> finalList) {
        Map<String, String> mainMappingMap = syncGroup.getMainMappingMap();
        Set<String> mainKeySet = mainMappingMap.keySet();
        mapList.forEach(map -> {
            String docId = null;
            if (nonNull(syncGroup.getDocIdColumnName())
                    && map.containsKey(syncGroup.getDocIdColumnName())
                    && nonNull(map.get(syncGroup.getDocIdColumnName()))) {
                docId = map.get(syncGroup.getDocIdColumnName()).toString();
            }
            this.assembleTargetResult(finalList, mainMappingMap, mainKeySet, map, docId);
        });
    }

    /**
     * 处理扩展字段
     */
    public void extendFieldMapping(SyncGroup syncGroup, List<Map<String, Object>> finalList) {
        if (CollectionUtils.isEmpty(syncGroup.getExtendFieldList())) {
            return;
        }
        for (Map<String, Object> finalListMap : finalList) {
            for (EsExtendField esExtendField : syncGroup.getExtendFieldList()) {
                Object value = esExtendField.getData(finalListMap);
                if (Objects.isNull(value)) {
                    continue;
                }
                if (StrUtil.isNotBlank(esExtendField.getFieldName())){
                    finalListMap.put(esExtendField.getFieldName(), value);
                }
                //返回字段集合不为空 则循环赋值
                List<String> fieldsName = esExtendField.getFieldsName();
                if (CollectionUtil.isNotEmpty(fieldsName)){
                    Map<String, Object> map = toMap(value);
                    fieldsName.stream()
                            .filter(map::containsKey)
                            .forEach(f -> finalListMap.putIfAbsent(f, map.get(f)));
                }
            }
        }
    }

    public Map<String, Object> toMap(Object obj){
        ObjectMapper mapper = new ObjectMapper();
        return mapper.convertValue(obj, Map.class);
    }

    /**
     * 处理需要解密的字段
     */
    public void decryptFields(TableInfo tableInfo, List<Map<String, Object>> finalList) {
        String needDecryptField = tableInfo.getNeedDecryptField();
        if (StrUtil.isEmpty(needDecryptField)) {
            return;
        }
        //有配置多个需要解密字段的话，是以逗号隔开
        List<String> needDecryptFieldList = StrUtil.split(needDecryptField, ",");
        needDecryptFieldList.forEach(fieldName -> {
            for (Map<String,Object> fieldMap:finalList){
                Object value = fieldMap.get(fieldName);
                if (ObjectUtil.isNotNull(value)){
                    String decryptStr = encryptStorageApi.decrypt(StrUtil.toString(value));
                    fieldMap.put(fieldName,decryptStr);
                    break;
                }
            }
        });
    }

    private void assembleTargetResult(List<Map<String, Object>> finalList, Map<String, String> mainMappingMap,
                                      Set<String> mainKeySet, Map<String, Object> map, String docId) {
        Map<String, Object> result = Maps.newHashMap();
        mainKeySet.forEach(originName -> {
            Object tempVal = map.get(originName);
            if (nonNull(tempVal)) {
                String targetName = mainMappingMap.get(originName);
                if (StringUtils.isNotBlank(targetName)) {
                    result.put(targetName, tempVal);
                    map.remove(originName);
                }
            }
        });
        result.putAll(map);
        if (nonNull(docId)) {
            result.put(DOC_ID, docId);
        }
        finalList.add(this.executeTimestamp(result));
    }


    private Map<String, Object> executeTimestamp(Map<String, Object> result) {
        Map<String, Object> last = Maps.newHashMap();
        result.forEach((key, val) -> {
            val = convertCanalDataToEsData(val);
            last.put(key, val);
        });
        return last;
    }

    /**
     * 根据spEl表达式，判断是否符合过滤条件，满足的将被过滤（不 新增和更新）。
     * @param after
     * @return
     */
    public boolean judgeConditionBySpEls(Map<String, Object> after,TableInfo tableInfo) {
        if (CollectionUtil.isNotEmpty(tableInfo.getConditionBySpEl())) {
            for (String spEl : tableInfo.getConditionBySpEl()) {
                if (SpelUtil.verifySpEL(after, spEl)) {
                    log.debug("judgeConditionBySpEl spEl={}", spEl);
                    return (boolean) SpelUtil.getValue(after, spEl);
                }
            }
        }
        return false;
    }

    /**
     * 是否满足新增条件
     * @param after
     * @param tableInfo
     * @return
     */
    public boolean judgeInsertCondition(Map<String, Object> after,TableInfo tableInfo) {
        List<EqualsCondition> conditionList = tableInfo.getInsertCondition();
        if (CollectionUtils.isNotEmpty(conditionList)) {
            for (EqualsCondition condition : conditionList) {
                String column = condition.getColumn();
                List<String> values = condition.getValues();
                // 字段不存在不insert
                if (!after.containsKey(column)) {
                    return true;
                }
                // 字段为null不insert
                if (isNull(after.get(column))) {
                    return true;
                }
                String current = String.valueOf(after.get(column));
                if (CollectionUtils.isNotEmpty(values) && !values.contains(current)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判定MySQL是否为逻辑删除，ES则需要物理删除
     * @param after
     * @return
     */
    public boolean isDeletedBySpEl(Map<String, Object> after,TableInfo tableInfo) {
        String spEl = tableInfo.getJudgeDeleteBySpEl();
        if (StringUtils.isNotBlank(spEl) && SpelUtil.verifySpEL(after, spEl)) {
            log.debug("isDeletedBySpEl spEl={}", spEl);
            return (boolean) SpelUtil.getValue(after, spEl);
        }
        return false;
    }


    @Override
    public void update(Map<String, Object> before, Map<String, Object> after, Set<String> updateColumnSet,
                       TableInfo tableInfo, SyncGroup syncGroup, Integer delayCount) {
        log.debug("AbstractTableHandler groupName:{} tableName:{}", syncGroup.getGroupId(), tableInfo.getTableName());
        String mainKey = tableInfo.getMainTableKey();
        Object id = after.get(mainKey);
        MainTableKeyData mainTableKeyData = getMainTableId(tableInfo, syncGroup, id);
        if (isNull(mainTableKeyData)) {
            log.debug("table:[{}], id ：[{}]计算mainTableKeyData为null,不处理", tableInfo.getTableName(), id);
            return;
        }

        //不符合更新条件过滤
        if (judgeConditionBySpEls(after,tableInfo)){
            return;
        }

        //如果符合删除标记 es进行物理删除
        if (isDeletedBySpEl(after,tableInfo)){
            this.delete(after,tableInfo,syncGroup, 0);
            return;
        }

        // 刷新相关索引
        refreshIndex(syncGroup, mainTableKeyData.getIds());

        SearchRequest searchRequest = getSearchRequest(after, tableInfo, syncGroup, mainTableKeyData);
        SearchResponse searchResponse;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.info("restHighLevelClient.search error, after is :" + JSON.toJSONString(after), e);
            // 5s后重试
            executor.schedule(
                    () -> update(before, after, updateColumnSet, tableInfo, syncGroup, delayCount), 5L, TimeUnit.SECONDS);
            return;
        }
        if (Objects.isNull(searchResponse)
                || Objects.isNull((searchResponse.getHits()))
                || ArrayUtils.isEmpty(searchResponse.getHits().getHits())) {
            // 主索引不存在，直接insert, 防止并发重复写入问题 主id 纬度加锁
            log.debug("group:[{}] table:[{}], id ：[{}]在es不存在, 重新初始化", syncGroup.getGroupId(), tableInfo.getTableName(), id);
            this.insert(after, tableInfo, syncGroup, true, false, delayCount);
            return;
        }
        // 需要更新的文档
        SearchHits searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();
        List<SearchHit> hitList = new ArrayList<>(hits.length);
        // 是否一对多从表
        if (Boolean.TRUE.equals(tableInfo.getOneToMany())) {
            // 判断es数据是否匹配
            for (SearchHit hit : hits) {
                Map<String, Object> sourceAsMap = hit.getSourceAsMap();
                String esFromTableIdColumn = tableInfo.getColumnToEsFieldMap().get(mainKey);
                Object esFromTableId = sourceAsMap.get(esFromTableIdColumn);
                if (isNull(esFromTableId) || mysqlEsEquals(id, esFromTableId)) {
                    hitList.add(hit);
                    break;
                }
            }
            // 如果无匹配新增数据
            if (CollectionUtils.isEmpty(hitList)) {
                log.debug("table {} es数据不存在, 重新初始化", tableInfo.getTableName());
                this.insert(after, tableInfo, syncGroup, true, false, delayCount);
                return;
            }
        } else {
            hitList.addAll(Arrays.asList(hits));
        }
        for (SearchHit hit : hitList) {
            updateEsData(syncGroup, tableInfo, before, after, hit, false, false, delayCount, false);
        }
    }

    public SearchRequest getSearchRequest(Map<String, Object> after,
                                          TableInfo tableInfo,
                                          SyncGroup syncGroup,
                                          MainTableKeyData mainTableKeyData){
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        SearchRequest searchRequest = new SearchRequest(syncGroup.getEsMainIndex());
        //配置关联关系不为空
        if (Objects.nonNull(tableInfo.getTableRelationInfo())) {
            //关联关系字段在es中的字段名
            String esField = tableInfo.getTableRelationInfo().getEsField();
            //关联关系字段在数据库中的字段名
            String column = tableInfo.getTableRelationInfo().getColumn();
            Object columnVal = after.get(column); // 相关字段的值
            if (columnVal instanceof String) {
                sourceBuilder.query(QueryBuilders.boolQuery()
                        .should(QueryBuilders.matchPhraseQuery(esField, columnVal))
                        .should(QueryBuilders.termQuery(esField, columnVal)));
            } else {
                sourceBuilder.query(QueryBuilders.boolQuery()
                        .must(QueryBuilders.termQuery(esField, columnVal)));
            }
        } else {
            // 使用主表主键查询
            sourceBuilder.query(QueryBuilders.termsQuery(mainTableKeyData.getEsColumnName(), mainTableKeyData.getIds()));
        }
        searchRequest.source(sourceBuilder);
        log.debug("update防止重复写入check，查询条件：{}", sourceBuilder);
        return searchRequest;
    }

    /**
     * 更新es数据
     */
    protected void updateEsData(SyncGroup syncGroup, TableInfo tableInfo, Map<String, Object> before, Map<String, Object> map, SearchHit hit, boolean fromUpdate, boolean fromInit, Integer delayCount, boolean fromInsert) {
        Map<String, Object> sourceAsMap = hit.getSourceAsMap();
        boolean hasUpdate = false;
        for (String column : tableInfo.getColumnToEsFieldMap().keySet()) {
            // 过滤非es相关表字段
            String targetName = tableInfo.getColumnToEsFieldMap().get(column);
            if (StringUtils.isBlank(targetName)) {
                continue;
            }
            Object newVal = map.get(column);
            newVal = convertCanalDataToEsData(newVal);
            Object oldVal = sourceAsMap.get(targetName);
            boolean valEquals = mysqlEsEquals(newVal, oldVal);
            if (tableInfo.getCheckMysqlEsEquals() && valEquals) {
                log.debug("新旧值相同跳过");
                continue;
            }
            sourceAsMap.put(targetName, newVal);
            log.debug("update check targetName:{} oldVal:{} oldType:{} newVal:{} newType:{}", targetName, oldVal, nonNull(oldVal) ? oldVal.getClass() : null, newVal, nonNull(newVal) ? newVal.getClass() : null );
            hasUpdate = true;
        }
        if (!hasUpdate) {
            // 无更新跳过
            log.debug("no update");
            return;
        }
        this.extendFieldMapping(syncGroup, Collections.singletonList(sourceAsMap));
        this.decryptFields(tableInfo,Collections.singletonList(sourceAsMap));
        Map<String, Object> updateMap = getUpdateMap(syncGroup, tableInfo, sourceAsMap);
        updateMap = fieldFilter(tableInfo, updateMap);
        UpdateRequest updateRequest = new UpdateRequest(syncGroup.getEsMainIndex(), hit.getId());
        // 是否使用painless脚本更新数据
        if (StringUtils.isNotBlank(tableInfo.getScriptUpdate())) {
            updateRequest.script(new Script(ScriptType.INLINE,"painless", tableInfo.getScriptUpdate(), updateMap));
        } else {
            updateRequest.doc(updateMap);
        }
        log.info("es更新数据 groupName:{} updateMap:{} id:{}", syncGroup.getGroupId(), updateMap, hit.getId());
        try {
            elasticSearchUtil.immediateUpdate(updateRequest);
            saveInsertCache(syncGroup, Collections.singletonList(sourceAsMap));
            log.info("es更新成功 groupName:{} updateMap:{} id:{}", syncGroup.getGroupId(), updateMap, hit.getId());
        } catch (ElasticsearchStatusException statusException) {
            log.error("es更新失败 ElasticsearchStatusException status:{},groupName:{},updateMap：{}，id:{}", statusException.status(), syncGroup.getGroupId(), updateMap, hit.getId());
            log.debug(ExceptionUtil.getMessage(statusException));
            String action = CanalEsConstants.CANAL_ACTION_UPDATE;
            if (Boolean.TRUE.equals(fromInsert)) {
                action = CanalEsConstants.CANAL_ACTION_INSERT;
            }
            sendToDelayQueue(action, syncGroup, tableInfo, before, map, fromUpdate, fromInit, delayCount, statusException);
        }
    }

    protected void sendToDelayQueue(String action, SyncGroup  syncGroup, TableInfo tableInfo, Map<String, Object> beforMap, Map<String, Object> map, boolean fromUpdate, boolean fromInit, Integer delayCount, Exception ex) {
        // 在重试次数内发送到延迟队列
        if (nonNull(delayCount) && delayCount < syncProperties.getSyncMaxDelayCount()) {
            CanalMessageDelay messageDelay = messageDelaySet(action, beforMap, map, tableInfo, syncGroup, fromUpdate, fromInit, delayCount);
            String messageDelayString =  JdkSerializeUtils.serializeObjectToString(messageDelay);
            // 当前时间延后3秒
            RedisDelayQueue.delayQueue(CanalEsConstants.CANAL_MESSAGE_HANDLER_DELAY_QUEUE + syncGroup.getGroupId(),
                    messageDelayString, syncProperties.getSyncDelayTime(), TimeUnit.SECONDS);
            log.info("从表insert延时队列写入 groupName:{} tableName:{} delayCount:{}", syncGroup.getGroupId(), tableInfo.getTableName(), delayCount);
        } else {
            if (nonNull(ex)) {
                log.info("sendToDelayQueue达到最大重试次数 groupName:{} tableName:{} delayCount:{} exception:{}", syncGroup.getGroupId(), tableInfo.getTableName(), delayCount, ExceptionUtil.stacktraceToString(ex));
            } else {
                log.info("sendToDelayQueue达到最大重试次数 groupName:{} tableName:{} delayCount:{}", syncGroup.getGroupId(), tableInfo.getTableName(), delayCount);
            }
        }
    }


    private CanalMessageDelay messageDelaySet(String action,Map<String, Object> beforMap,Map<String, Object> map,TableInfo tableInfo,SyncGroup syncGroup,
                                              boolean fromUpdate,boolean fromInit,Integer delayCount){
        CanalMessageDelay messageDelay = new CanalMessageDelay();
        messageDelay.setAction(action);
        messageDelay.setBeforeDataMap(beforMap);
        messageDelay.setTableInfo(tableInfo);
        messageDelay.setGroupId(syncGroup.getGroupId());
        messageDelay.setFromUpdate(fromUpdate);
        messageDelay.setFromInit(fromInit);
        messageDelay.setDelayCount(++delayCount);
        CanalModel canalModel = CanalContext.getTtlModel();
        if (ObjectUtil.isNotNull(canalModel)){
            map.put(SYNC_EXECUTE_TIME,canalModel.getExecuteTime());
        }
        messageDelay.setDataMap(map);
        return messageDelay;
    }

    /**
     * 提取需要更新的字段
     */
    protected Map<String, Object> getUpdateMap(SyncGroup syncGroup, TableInfo tableInfo, Map<String, Object> sourceAsMap) {
        // 复制当前表所有字段复制到updateMa中
        Map<String, Object> updateMap =  new HashMap<>();
        for (String column : tableInfo.getColumnToEsFieldMap().keySet()) {
            // 过滤非es相关表字段
            String targetName = tableInfo.getColumnToEsFieldMap().get(column);
            if (StringUtils.isBlank(targetName)) {
                continue;
            }
            updateMap.put(targetName, sourceAsMap.get(targetName));
        }
        // 如果有扩展字段也复制到updateMap
        if (!CollectionUtils.isEmpty(syncGroup.getExtendFieldList())) {
            for (EsExtendField<?,?> esExtendField : syncGroup.getExtendFieldList()) {
                if (StrUtil.isNotBlank(esExtendField.getFieldName())){
                    updateMap.put(esExtendField.getFieldName(), sourceAsMap.get(esExtendField.getFieldName()));
                }
                List<String> fieldsName = esExtendField.getFieldsName();
                if (CollectionUtil.isNotEmpty(fieldsName)){
                    fieldsName.forEach(f -> updateMap.put(f, sourceAsMap.get(f)));
                }
            }
        }
        return updateMap;
    }

    /**
     * 过滤非es相关字段
     * @return
     */
    protected Map<String, Object> fieldFilter(TableInfo tableInfo, Map<String, Object> sourceAsMap){
        if (CollectionUtils.isNotEmpty(tableInfo.getFieldFilter())) {
            for (String field : tableInfo.getFieldFilter()) {
                if (StringUtils.isNotBlank(field)) {
                    sourceAsMap.remove(field);
                }
            }
        }
        return sourceAsMap;
    }

    /**
     * 获得驱动表主键es字段名和id
     */
    protected MainTableKeyData getMainTableId(TableInfo tableInfo, SyncGroup syncGroup, Object id) {
        MainTableKeyData mainTableKeyData = new MainTableKeyData();
        if (tableInfo.isDriverTable()) {
            log.debug("getMainTableId table [{}] 是驱动表，id :{}", tableInfo.getTableName(), id);
            String esMainKeyName = tableInfo.getColumnToEsFieldMap().get(tableInfo.getMainTableKey());
            mainTableKeyData.setEsColumnName(esMainKeyName);
            mainTableKeyData.setIds(Collections.singletonList(id));
            return mainTableKeyData;
        }
        if (StringUtils.isBlank(tableInfo.getJoinQueryCondition())) {
            log.debug("joinQueryCondition为空 group:{} table:{}", syncGroup.getGroupId(), tableInfo.getTableName());
            String esMainKeyName = tableInfo.getColumnToEsFieldMap().get(tableInfo.getMainTableKey());
            mainTableKeyData.setEsColumnName(esMainKeyName);
            mainTableKeyData.setIds(Collections.singletonList(id));
            return mainTableKeyData;
        }
        String baseSql = syncGroup.getJoinSql();
        String querySql = tableInfo.getJoinQueryCondition();
        String sql = baseSql + " " + querySql;
        log.debug("getMainTableId sql and param is :{} {}", querySql, id);
        List<Map<String, Object>> queryForList = jdbcTemplate.queryForList(sql, id);
        if (CollectionUtils.isEmpty(queryForList)) {
            log.info("getMainTableId表：{} ，update -> insert 操作，id： {}，" +
                    "查询数据不存在，不处理", tableInfo.getTableName(), id);
            // join 结果为空 不处理
            return null;
        }
        TableInfo driverTableInfo = syncGroup.getTables().stream().filter(TableInfo::isDriverTable).findFirst().get();
        String esMainKeyName = driverTableInfo.getColumnToEsFieldMap().get(driverTableInfo.getMainTableKey());
        List<Object> resultList = new ArrayList<>();
        for (Map<String, Object> subMap : queryForList) {
            String mainKey = driverTableInfo.getMainTableKey();
            Object mainKeyVal = subMap.get(driverTableInfo.getColumnToEsFieldMap().get(mainKey));
            resultList.add(mainKeyVal);
        }
        resultList = resultList.stream().distinct().collect(Collectors.toList());
        mainTableKeyData.setEsColumnName(esMainKeyName);
        mainTableKeyData.setIds(resultList);
        mainTableKeyData.setList(queryForList);
        return mainTableKeyData;
    }

    @Override
    public void delete(Map<String, Object> stringObjectMap, TableInfo tableInfo, SyncGroup syncGroup, Integer delayCount) {
        String mainKey = tableInfo.getMainTableKey();
        String id = String.valueOf(stringObjectMap.get(mainKey));
        // 查询主索引，获取需要更新的文档
        String mainKeyField = tableInfo.getColumnToEsFieldMap().get(mainKey);
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery().must(QueryBuilders.termQuery(mainKeyField, id));
        // 配置删除方式，则直接删除
        if (tableInfo.getDeleteByQuery()) {
            DeleteByQueryRequest queryRequest = new DeleteByQueryRequest(syncGroup.getEsMainIndex());
            queryRequest.setQuery(queryBuilder);
            log.debug("deleteByQueryRequest builder : {}", queryBuilder);
            Boolean flag = elasticSearchUtil.deleteByQuery(queryRequest);
            if (!flag) {
                sendToDelayQueue(CANAL_ACTION_DELETE, syncGroup, tableInfo, null, stringObjectMap, false, false, delayCount, null);
            }
            return;
        }
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(queryBuilder);
        SearchRequest searchRequest = new SearchRequest(syncGroup.getEsMainIndex());
        searchRequest.source(sourceBuilder);
        SearchResponse searchResponse;
        try {
            log.debug("source builder : {}", sourceBuilder);
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("restHighLevelClient.search error, map is :" + JSON.toJSONString(stringObjectMap), e);
            // 5s后重试
            executor.schedule(
                    () -> delete(stringObjectMap, tableInfo, syncGroup, delayCount), 5L, TimeUnit.SECONDS);
            return;
        }
        if (Objects.isNull(searchResponse)
                || Objects.isNull((searchResponse.getHits()))
                || ArrayUtils.isEmpty(searchResponse.getHits().getHits())) {
            // 主索引不存在，直接return
            log.debug("索引不存在， exit {}", tableInfo.getTableName());
            return;
        }
        // 需要删除的文档
        SearchHits searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();
        for (SearchHit hit : hits) {
            // 是否使用脚本操作
            if (StringUtils.isNotBlank(tableInfo.getScriptDelete())) {
                UpdateRequest updateRequest = new UpdateRequest(syncGroup.getEsMainIndex(), hit.getId());
                updateRequest.script(new Script(ScriptType.INLINE,"painless",
                        tableInfo.getScriptDelete(), stringObjectMap));
                elasticSearchUtil.immediateUpdate(updateRequest);
            } else {
                DeleteRequest deleteRequestMain = new DeleteRequest(syncGroup.getEsMainIndex()).id(hit.getId());
                elasticSearchUtil.delete(deleteRequestMain);
            }
        }
    }

    /**
     * mysql与es值对比
     *
     * @param mysqlVal newVal
     * @param esVal oldVal
     *
     * @retrun true=相同 false=不相同
     */
    protected boolean mysqlEsEquals(Object mysqlVal, Object esVal) {
        if (mysqlVal == esVal) {
            return true;
        }
        // 其中一个为null
        if (isNull(mysqlVal) || isNull(esVal)) {
            return false;
        }
        if (mysqlVal instanceof BigDecimal) {
            if (esVal instanceof BigDecimal) {
                return ((BigDecimal) mysqlVal).compareTo((BigDecimal) esVal) == 0;
            }
            if (esVal instanceof String || esVal instanceof Integer || esVal instanceof Long || esVal instanceof Double || esVal instanceof Float) {
                return ((BigDecimal) mysqlVal).compareTo(NumberUtil.toBigDecimal(esVal.toString())) == 0;
            }
            log.debug("mysqlEsEquals instanceof BigDecimal 未兼容的esVal类型 type:{}", esVal.getClass());
            throw new RuntimeException("mysqlEsEquals instanceof BigDecimal 未兼容的esVal类型 type:" + esVal.getClass());
        }
        if (mysqlVal instanceof Integer || mysqlVal instanceof Long || mysqlVal instanceof Double || mysqlVal instanceof Float) {
            if (esVal instanceof BigDecimal) {
                return (NumberUtil.toBigDecimal(mysqlVal.toString())).compareTo((BigDecimal) esVal) == 0;
            }
            if (esVal instanceof String || esVal instanceof Integer || esVal instanceof Long || esVal instanceof Double || esVal instanceof Float) {
                return (NumberUtil.toBigDecimal(mysqlVal.toString())).compareTo(NumberUtil.toBigDecimal(esVal.toString())) == 0;
            }
            log.debug("mysqlEsEquals instanceof Number 未兼容的esVal类型 type:{}", esVal.getClass());
            throw new RuntimeException("mysqlEsEquals instanceof Number 未兼容的esVal类型 type:" + esVal.getClass());
        }
        // 字符串用hashCode快速匹配,如果hashCode不同字符串值肯定不同, hashCode值相同字符串值未必相同
        if (mysqlVal instanceof String && esVal instanceof String) {
            if (mysqlVal.hashCode() != esVal.hashCode()) {
                return false;
            }
        }
        return mysqlVal.equals(esVal);
    }

    /**
     * 转换canal源数据类型为es对应类型
     */
    protected Object convertCanalDataToEsData(Object value) {
        if (value instanceof Timestamp) {
            LocalDateTime localDateTime = ((Timestamp) value).toLocalDateTime();
            return LocalDateTimeUtil.formatNormal(localDateTime);
        }
        if (value instanceof Date) {
            return value.toString();
        }
        if (value instanceof LocalDateTime) {
            return ((LocalDateTime) value).format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN));
        }
        if (value instanceof LocalDate) {
            return ((LocalDate)value).format(DateTimeFormatter.ofPattern(NORM_DATE_PATTERN));
        }
        if (value instanceof LocalTime) {
            return ((LocalTime) value).format(DateTimeFormatter.ofPattern(NORM_TIME_PATTERN));
        }
        return value;
    }

}
