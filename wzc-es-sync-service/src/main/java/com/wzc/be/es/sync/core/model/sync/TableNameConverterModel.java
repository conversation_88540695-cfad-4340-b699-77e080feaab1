/*
 * Copyright (C) 2023 Baidu, Inc. All Rights Reserved.
 */
package com.wzc.be.es.sync.core.model.sync;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName TableNameConverter
 **/
@Data
public class TableNameConverterModel implements Serializable {

    /**
     * 判定条件 正则表达式
     */
    private String condition;

    /**
     * 转换后表名，支持spEL表达式
     */
    private String tableName;
}
