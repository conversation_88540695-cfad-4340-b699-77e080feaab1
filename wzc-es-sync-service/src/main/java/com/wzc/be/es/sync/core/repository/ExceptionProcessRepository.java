package com.wzc.be.es.sync.core.repository;

import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import com.wzc.be.es.sync.common.exception.ExceptionEnum;
import com.wzc.be.es.sync.core.model.excep.ExcepProcessMessage;
import com.wzc.be.es.sync.core.model.excep.SubWaybillDocument;
import com.wzc.be.es.sync.core.repository.mapper.SubWaybillMapper;
import com.wzc.common.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @description:
 * @Author: keeper Yu yxbwzx
 * @Date: 2023/9/5 20:46
 * @since 1.0.0
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ExceptionProcessRepository {

    private final SubWaybillMapper waybillMapper;

    public void updateExceptionProcessStatua(ExcepProcessMessage excepMessage) {
        if(StringUtils.isBlank(excepMessage.getSubWaybillNo())){
            throw new ServiceException(ExceptionEnum.ERROR_EXCEPTION_SUBWAYBILLNO_NULL);
        }
        if(excepMessage.getCategory()==null){
            throw new ServiceException(ExceptionEnum.ERROR_EXCEPTION_CATEGORY_NULL);
        }
        if(excepMessage.getProcessStatus()==null){
            throw new ServiceException(ExceptionEnum.ERROR_EXCEPTION_PROCESS_NULL);
        }
        LambdaEsQueryWrapper<SubWaybillDocument> queryWrapper = new LambdaEsQueryWrapper<>();
        Integer category = excepMessage.getCategory();
        queryWrapper.eq(SubWaybillDocument::getSubWaybillNo, excepMessage.getSubWaybillNo());
//        queryWrapper.nestedMatch(Waybill::getSubWaybill, FieldUtils.val(SubWaybillDocument::getSubWaybillNo),excepMessage.getSubWaybillNo());
        List<SubWaybillDocument> subWaybillDocuments = waybillMapper.selectList(queryWrapper);
        subWaybillDocuments.forEach(item->{
                switch (category){
                    case 1:
                        //在途异常
                        item.setTransProcess(excepMessage.getProcessStatus());
                        break;
                    case 2:
                        //签收异常
                        item.setSignProcess(excepMessage.getProcessStatus());
                        break;
                    case 3:
                        //监管上报异常
                        item.setSuperviseProcess(excepMessage.getProcessStatus());
                        break;
                    case 4:
                        //监管上报异常
                        item.setReportProcess(excepMessage.getProcessStatus());
                        break;
                    default:
                        throw new ServiceException(ExceptionEnum.ERROR_EXCEPTION_CATEGORY_NULL);
                }
        });
        log.info("更新异常子运单处理状态 {} 状态值：{}",excepMessage.getSubWaybillNo(),excepMessage.getProcessStatus());
        waybillMapper.updateBatchByIds(subWaybillDocuments);
    }
}
