package com.wzc.be.es.sync.core.context;


import com.alibaba.ttl.TransmittableThreadLocal;
import com.wzc.be.es.sync.core.model.CanalModel;

/**
 * <AUTHOR>
 * @date 2022/09/2711:17
 */
public class CanalContext {



    private static ThreadLocal<CanalModel> threadLocal = new ThreadLocal<>();

    private static TransmittableThreadLocal<CanalModel> ttlCanalModel = new TransmittableThreadLocal<>();




    public static CanalModel getModel(){
       return threadLocal.get();
    }


    public static void setModel(CanalModel canalModel){
        threadLocal.set(canalModel);
    }


    public  static void removeModel(){
        threadLocal.remove();
    }

    public static void setTtlModel(CanalModel canalModel){
        ttlCanalModel.set(canalModel);
    }

    public static CanalModel getTtlModel(){
        return ttlCanalModel.get();
    }

    public  static void removeTtlModel(){
        ttlCanalModel.remove();
    }
}
