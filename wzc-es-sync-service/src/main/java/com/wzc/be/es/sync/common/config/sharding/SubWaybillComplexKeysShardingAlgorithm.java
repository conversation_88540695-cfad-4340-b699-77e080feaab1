/*
 * Copyright (C) 2023 Baidu, Inc. All Rights Reserved.
 */
package com.wzc.be.es.sync.common.config.sharding;

import com.google.common.collect.Range;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingAlgorithm;
import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingValue;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * subWaybill分片算法
 * <AUTHOR>
 */
@Slf4j
public class SubWaybillComplexKeysShardingAlgorithm implements ComplexKeysShardingAlgorithm<String> {
    public static final String LOGIC_SUB_NO = "sub_no";
    public static final String LOGIC_PARENT_NO = "parent_no";

    public static Properties properties;

    /**
     * 分表个数
     */
    public static Long shardingCount;

    /**
     * 分表数量长度
     */
    public static int shardingStringSize;

    public static String shardingStringFormat;

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames,
                                         ComplexKeysShardingValue<String> shardingValue) {
        // 范围查询的分片键值集合:sub_no/parent_no
        Map<String, Range<String>> shardingRangeMaps = shardingValue.getColumnNameAndRangeValuesMap();

        // 精确查询的分片键值集合:sub_no/parent_no
        Map<String, Collection<String>> shardingMaps = shardingValue.getColumnNameAndShardingValuesMap();

        if (!shardingRangeMaps.isEmpty()){
            throw new UnsupportedOperationException("只支持精确查询，不支持范围查询");
        }

        // 逻辑表的名称，oms-base
        String tableName = shardingValue.getLogicTableName();

        List<String> noList = new ArrayList<>();
        // sub_no：订单编号分片键值的集合
        noList.addAll(shardingMaps.getOrDefault(LOGIC_SUB_NO, new ArrayList<>()));
        // parent_no：父订单分片键值的集合
        noList.addAll(shardingMaps.getOrDefault(LOGIC_PARENT_NO, new ArrayList<>()));

        return noList.stream().map(
                id-> MessageFormat.format(tableName + "_{0}",
                        Integer.valueOf(getShardingString(id)))).collect(Collectors.toSet());
    }

    public static String getShardingString(String no){
        if (StringUtils.isNotBlank(no) && no.length() >= shardingStringSize) {
            String index = no.substring(no.length() - shardingStringSize);
            return String.format(shardingStringFormat, Integer.parseInt(index) % shardingCount);
        }
        log.warn("getShardingString is Blank!");
        return StringUtils.EMPTY;
    }

    @Override
    public Properties getProps() {
        return properties;
    }

    @Override
    public void init(Properties properties) {
        SubWaybillComplexKeysShardingAlgorithm.properties = properties;
        Object shardingCount = properties.get("shardingCount");
        if (Objects.nonNull(shardingCount)) {
            SubWaybillComplexKeysShardingAlgorithm.shardingCount = Long.valueOf((String) shardingCount);
            SubWaybillComplexKeysShardingAlgorithm.shardingStringSize = shardingCount.toString().length();
            SubWaybillComplexKeysShardingAlgorithm.shardingStringFormat = "%0" + shardingStringSize + "d";
        }
    }

    @Override
    public String getType() {
        return "SUB_WAYBILL";
    }
}


