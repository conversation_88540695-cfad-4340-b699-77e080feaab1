package com.wzc.be.es.sync.common.constants;

/**
 * Redis锁keyName常量
 *
 * <AUTHOR>
 * @date 2023-03-20 16:27:17
 */
public interface CanalEsConstants {

    /**
     * 同步当前巡回位置
     */
    String CANAL_ES_SYNC_GROUPCURSOR = "CANAL:ES:SYNC:GROUP-CURSOR";

    /**
     * group当前巡回表
     */
    String CANAL_ES_SYNC_GROUPTABLECURSOR_PRE = "CANAL:ES:SYNC:GROUP-TABLE-CURSOR:";

    /**
     * group-table 同步位置游标前缀
     */
    String CANAL_ES_SYNC_GROUP_TABLE_CURSOR_PRE = "CANAL:ES:SYNC:GROUP:TABLE:CURSOR:";

    /**
     * canal-es近期新增数据缓存key
     */
    String CANAL_ES_CACHE_INDEXINSERT_PRE = "CANAL:ES:CACHE:INDEX-INSERT:";

    /**
     * canal数据延迟处理队列key名
     */
    String CANAL_MESSAGE_HANDLER_DELAY_QUEUE = "CANAL:MESSAGE-HANDLER:DELAY-QUEUE:";

    /**
     * canal延迟数据执行时间key
     */
    String CANAL_MESSAGE_HANDLER_EXECUTE_TIME_KEY = "CANAL:MESSAGE-HANDLER:EXECUTE-TIME:KEY:";

    /**
     * canal更新es加锁key
     */
    String CANAL_MESSAGE_HANDLER_UPDATE_LOCK_KEY = "CANAL:MESSAGE-HANDLER:LOCK:KEY:";

    /**
     * canal消息新增行为类型
     */
    String CANAL_ACTION_INSERT = "INSERT";

    /**
     * canal消息更新行为类型
     */
    String CANAL_ACTION_UPDATE = "UPDATE";

    /**
     * canal消息删除行为类型
     */
    String CANAL_ACTION_DELETE = "DELETE";

    /**
     * 数据同步执行时间
     */
    String SYNC_EXECUTE_TIME = "syncExecuteTime";

}
