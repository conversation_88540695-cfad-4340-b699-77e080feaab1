package com.wzc.be.es.sync.adapter.consumer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.alibaba.otter.canal.protocol.Message;
import com.google.protobuf.InvalidProtocolBufferException;
import com.wzc.be.es.sync.common.config.SyncAutoConfig;
import com.wzc.be.es.sync.core.converter.TableNameConverter;
import com.wzc.be.es.sync.core.handler.inter.MessageHandler;
import com.wzc.be.es.sync.core.model.sync.SyncGroup;
import com.wzc.be.es.sync.core.model.sync.TableInfo;
import com.wzc.be.es.sync.core.util.CanalMessageSerializerUtil;
import com.wzc.common.cache.redis.CustomRedis;
import com.wzc.common.exception.ServiceException;
import com.wzc.common.string.StringPool;
import com.wzc.common.util.spring.PropertyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.wzc.be.es.sync.common.constants.KafkaConstants.*;

/**
 * canal es消息处理
 *
 * <AUTHOR>
 * @date 2023年03月09日
 */
@Slf4j
@Component
public class CanalEsMessageListener {

    @Resource(name = "syncMessageHandler")
    private MessageHandler messageHandler;

    private final Set<String> syncGroupTables = new HashSet<>();
    private final String cacheMapKey = "es:sync:";
    @Resource(name = "KafkaTemplateByte")
    public KafkaTemplate<String, byte[]> kafkaTemplate;
    @Resource
    private SyncAutoConfig syncAutoConfig;
    @Resource
    private TableNameConverter tableNameConverter;

    @PostConstruct
    private void init() {
        for (SyncGroup syncGroup : syncAutoConfig.getGroupList()) {
            for (TableInfo tableInfo : syncGroup.getTables()) {
                syncGroupTables.add(syncGroup.getDatabase().toLowerCase() + ":" + tableInfo.getTableName().toLowerCase());
            }
        }
    }

    @KafkaListener(topics = KAFKA_TMS_SPRING, groupId = KAFKA_GROUP_SPRING, properties = KAFKA_PROPERTIES, concurrency = "3")
    public void canalMessageTms(Message message) {
        execMessage(message,"tms队列执行");
    }

    @KafkaListener(topics = KAFKA_BMS_SPRING, groupId = KAFKA_GROUP_SPRING, properties = KAFKA_PROPERTIES, concurrency = "3")
    public void canalMessageBms(Message message) {
        execMessage(message,"bms队列执行");
    }

    @KafkaListener(topics = KAFKA_USER_SPRING, groupId = KAFKA_GROUP_SPRING, properties = KAFKA_PROPERTIES, concurrency = "3")
    public void canalMessageUser(Message message) {
        execMessage(message,"user队列执行");
    }

    @KafkaListener(topics = KAFKA_OMS_SPRING, groupId = KAFKA_GROUP_SPRING, properties = KAFKA_PROPERTIES, concurrency = "3")
    public void canalMessageOms(Message message) {
        execMessage(message,"oms队列执行");
    }

    @KafkaListener(topics = KAFKA_BASE_SPRING, groupId = KAFKA_GROUP_SPRING, properties = KAFKA_PROPERTIES, concurrency = "3")
    public void canalMessageBase(Message message) {
        execMessage(message, "base队列执行");
    }

    @KafkaListener(topics = KAFKA_SCRIPT_SPRING, groupId = KAFKA_GROUP_SPRING, properties = KAFKA_PROPERTIES, concurrency = "3")
    public void canalMessageScript(Message message) {
        execMessage(message, "脚本执行");
    }


    @KafkaListener(topics = KAFKA_DEFAULT_SPRING, groupId = KAFKA_GROUP_SPRING, properties = KAFKA_PROPERTIES, concurrency = "3")
    public void canalMessage(ConsumerRecord<String, Message> record) {
        transmitMessage(record);
    }

    private void transmitMessage(ConsumerRecord<String, Message> record) {
        Message message = record.value();
        List<String> tableNames = message.getEntries().stream()
                .map(entry -> tableNameConverter.convert(entry.getHeader().getSchemaName() + ":" + entry.getHeader().getTableName()))
                .distinct().collect(Collectors.toList());
        boolean noneMatch = tableNames.stream().noneMatch(syncGroupTables::contains);
        if (noneMatch) {
            return;
        }
        AtomicBoolean flag = new AtomicBoolean(false);
        AtomicReference<String> topKey = new AtomicReference<>(StringPool.EMPTY);
        AtomicReference<String> dataBase = new AtomicReference<>(StringPool.EMPTY);
        message.getEntries().parallelStream().forEach(item -> {
            try {
                CanalEntry.RowChange rowChange = CanalEntry.RowChange.parseFrom(item.getStoreValue());
                log.info("同步--{}--SQL={}", rowChange.getEventType().name(), rowChange.getSql());
                if (StrUtil.isNotBlank(rowChange.getSql()) && rowChange.getSql().contains(StringPool.SLASH + StringPool.ASTERISK + StringPool.DASH)) {
                    log.info("TYPE={}  SQL={} 表名={} 位置：{},长度：{}，时间：{}",
                            rowChange.getEventType().name(), rowChange.getSql(), item.getHeader().getTableName(), item.getHeader().getLogfileOffset(), message.getEntries().size(), item.getHeader().getExecuteTime());
                    flag.set(true);
                    topKey.set(item.getHeader().getTableName());
                }
            } catch (InvalidProtocolBufferException e) {
                log.error("es同步，转换rowChange异常", e);
            }
            if (StrUtil.isNotBlank(item.getHeader().getSchemaName())) {
                dataBase.set(item.getHeader().getSchemaName());
            }
        });
        message.getEntries().parallelStream()
                .forEach(entry -> {
                    String key = cacheMapKey + entry.getHeader().getTableName() + ":" + entry.getHeader().getExecuteTime();
                    if ((flag.get() && entry.getHeader().getTableName().equals(topKey.get())) ||
                            CustomRedis.hasKey(key)) {
                        CustomRedis.set(key, entry.getHeader().getTableName(), TimeUnit.DAYS.toSeconds(3));
                        flag.set(true);
                    }
                });
        byte[] transmitMessage = CanalMessageSerializerUtil.serializer(message, true);
        String topic = PropertyUtil.getProperty(KAFKA_BASE);
        int partition = record.partition();
        if (flag.get()) {
            //手动执行脚本
            topic = PropertyUtil.getProperty(KAFKA_SCRIPT);
        } else if (dataBase.get().contains(WZC_OMS) || dataBase.get().contains(ZT_OMS)) {
            if(partition == 0){
                log.info("分区数={}",Convert.toStr(partition));
                partition =1;
            }
            topic = PropertyUtil.getProperty(KAFKA_OMS);
        } else if (dataBase.get().contains(WZC_BMS)) {
            topic = PropertyUtil.getProperty(KAFKA_BMS);
        } else if (dataBase.get().contains(ZT_TMS) || dataBase.get().contains(WZC_TMS)) {
            topic = PropertyUtil.getProperty(KAFKA_TMS);
        } else if (dataBase.get().contains(ZT_USER)) {
            topic = PropertyUtil.getProperty(KAFKA_USER);
        }
        kafkaTemplate.send(topic, partition, Convert.toStr(partition), transmitMessage);
    }

    public void execMessage(Message message, String logMsg) {
        try {
            List<String> tableNames = message.getEntries().stream()
                    .map(entry -> tableNameConverter.convert(entry.getHeader().getSchemaName() + ":" + entry.getHeader().getTableName())).collect(Collectors.toList());
            message.getEntries().removeIf(item -> StrUtil.isBlank(item.getHeader().getSchemaName()) ||
                    !syncGroupTables.contains(tableNameConverter.convert(item.getHeader().getSchemaName() + ":" + item.getHeader().getTableName())));
            if (CollUtil.isEmpty(message.getEntries())) {
                log.info("退出同步--->{}", tableNames);
                return;
            }
            tableNames = message.getEntries().stream()
                    .map(entry -> tableNameConverter.convert(entry.getHeader().getSchemaName() + ":" + entry.getHeader().getTableName()))
                    .distinct().collect(Collectors.toList());
            log.info(logMsg);
            log.info("同步表名称：{}", tableNames);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            messageHandler.handleMessage(message);
            stopWatch.stop();
            log.info("执行耗时：{} ms", stopWatch.getTotalTimeMillis());
        } catch (Exception e) {
            log.error("消费异常：", e);
            if (e instanceof ServiceException) {
                return;
            }
            throw e;
        }
    }
}
