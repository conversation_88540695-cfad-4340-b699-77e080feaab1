package com.wzc.be.es.sync.core.model.excep;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @description: 异常处理消息
 * @Author: keeper Yu yxbwzx
 * @Date: 2023/9/5 20:12
 * @since 1.0.0
 */
@Data
public class ExcepProcessMessage {
    /**
     * 子运单号
     */
    private String subWaybillNo;

    /**
     * 异常处理状态 0或null 表示有全部处理  1表示有待处理异常
     */
    private Integer processStatus;

    /**
     * 异常分类 1-在途异常 2-签收异常 3-监管上报异常 4-司机上报异常
     */
    private Integer category;
}
