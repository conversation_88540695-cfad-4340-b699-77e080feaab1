package com.wzc.be.es.sync.adapter.job;

import com.alibaba.fastjson.JSONObject;
import com.wzc.be.es.sync.core.handler.DataSyncHandler;
import com.wzc.be.es.sync.core.model.job.EsSyncParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobContext;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 数据库数据同步到es
 * <AUTHOR>
 */
@Slf4j
@Component
public class DbDataToEsJob {

    @Autowired
    private DataSyncHandler dataSyncHandler;

    /**
     * 数据同步到es
     */
    @XxlJob("esDataAssignSync")
    public ReturnT<String> esDataAssignSync(String param) {
        param = XxlJobContext.getXxlJobContext().getJobParam();
        log.debug("esDataAssignSync 开始执行,参数为:{}",param);
        if (StringUtils.isBlank(param)) {
            log.debug("param为空");
            return new ReturnT<>("param为空");
        }
        JSONObject paramJson = JSONObject.parseObject(param);
        EsSyncParam syncParam = new EsSyncParam();
        syncParam.setGroupName(paramJson.getString("groupId"));
        if (paramJson.containsKey("tableName")) {
            syncParam.setTableName(paramJson.getString("tableName"));
        }
        if (paramJson.containsKey("whereCondition")) {
            syncParam.setWhereCondition(paramJson.getString("whereCondition"));
        }
        if (paramJson.containsKey("update")) {
            syncParam.setUpdate(paramJson.getBoolean("update"));
        }
        if (paramJson.containsKey("init")) {
            syncParam.setInit(paramJson.getBoolean("init"));
        }
        dataSyncHandler.syncEsData(syncParam);
        log.debug("esDataAssignSync 结束执行");
        return ReturnT.SUCCESS;
    }


    /**
     * es数据自动同步任务
     */
    @XxlJob("esDataAutoSync")
     public ReturnT<String> esDataAutoSync(String param) {
        param = XxlJobHelper.getJobParam();
        log.debug("esDataAutoSync 开始执行参数为:{}", param);
        EsSyncParam syncParam = new EsSyncParam();
        dataSyncHandler.syncEsData(syncParam);
        log.debug("esDataAutoSync 结束执行");
        return ReturnT.SUCCESS;
     }
}
