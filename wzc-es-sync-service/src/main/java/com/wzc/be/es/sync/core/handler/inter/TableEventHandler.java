/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.sync.core.handler.inter;


import com.wzc.be.es.sync.core.model.sync.SyncGroup;
import com.wzc.be.es.sync.core.model.sync.TableInfo;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022年10月08日 5:05 PM
 */
public interface TableEventHandler {

    void insert(Map<String, Object> stringObjectMap, TableInfo tableInfo, SyncGroup syncGroup, boolean fromUpdate, boolean fromInit, Integer delayCount);


    void update(Map<String, Object> before, Map<String, Object> after,
                       Set<String> updateColumnSet, TableInfo tableInfo, SyncGroup syncGroup, Integer delayCount);


    void delete(Map<String, Object> stringObjectMap, TableInfo tableInfo, SyncGroup syncGroup, Integer delayCount);
}
