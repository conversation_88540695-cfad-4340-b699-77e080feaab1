package com.wzc.be.es.sync.core.extend;

import com.baidu.mapcloud.cloudnative.orm.crypto.storage.api.EncryptStorageApi;
import com.wzc.be.es.sync.core.model.sync.EsExtendField;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * <p>
 *     子运单司机手机号字段
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/25
 */
@Component
public class SubWaybillDriverPhoneField extends EsExtendField<String, Map<String, Object>> {

    private static final String FIELD_NAME = "driverPhone";
    private static final String GROUP_NAME = "subWaybillIndexWaybillData";
    private static final String DATABASE_NAME = "zt_user";
    private static final String USER_ID_FIELD = "userId";

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private EncryptStorageApi encryptStorageApi;


    @Override
    public String getData(Map<String, Object> esSourceMap) {
        Object driverIdObj = esSourceMap.get("driverId");
        if (isNull(driverIdObj)) {
            return StringUtils.EMPTY;
        }
        if (!StringUtils.isNumeric(String.valueOf(driverIdObj))) {
            return StringUtils.EMPTY;
        }
        // 获取司机ID
        Long driverId = Long.valueOf(String.valueOf(driverIdObj));
        Long userId = getUserId(driverId);
        if (Objects.isNull(userId)) {
            return StringUtils.EMPTY;
        }
        String userPhone = getUserPhone(userId);
        return encryptStorageApi.decrypt(userPhone);
    }

    private String getUserPhone(Long userId) {
        // todo: 加缓存
        String querySql = "select mobile as driverPhone FROM mid_user_base where id = ? limit 1";
        List<Map<String, Object>> resultList = jdbcTemplate.queryForList(querySql, userId);
        if (CollectionUtils.isEmpty(resultList)) {
            return StringUtils.EMPTY;
        }
        Map<String, Object> resultMap = resultList.get(0);
        if (MapUtils.isEmpty(resultMap)) {
            return StringUtils.EMPTY;
        }
        Object userPhone = resultMap.get(FIELD_NAME);
        if (nonNull(userPhone)) {
            return String.valueOf(userPhone);
        }
        return StringUtils.EMPTY;
    }

    private Long getUserId(Long driverId) {
        // todo: 加缓存
        String querySql = "select user_id as userId from mid_driver where id = ? limit 1";
        List<Map<String, Object>> resultList = jdbcTemplate.queryForList(querySql, driverId);
        if (CollectionUtils.isEmpty(resultList)) {
            return null;
        }
        Map<String, Object> resultMap = resultList.get(0);
        if (MapUtils.isEmpty(resultMap)) {
            return null;
        }
        Object userId = resultMap.get(USER_ID_FIELD);
        if (nonNull(userId)) {
            return Long.valueOf(String.valueOf(userId));
        }
        return null;
    }

    @Override
    public String getFieldName() {
        return FIELD_NAME;
    }

    @Override
    public String getGroupName() {
        return GROUP_NAME;
    }

    @Override
    public String getDataBaseName() {
        return DATABASE_NAME;
    }
}
