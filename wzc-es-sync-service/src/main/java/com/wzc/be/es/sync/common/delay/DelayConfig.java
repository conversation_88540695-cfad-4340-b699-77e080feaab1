package com.wzc.be.es.sync.common.delay;


import com.wzc.be.es.sync.common.constants.CanalEsConstants;
import com.wzc.be.es.sync.core.handler.DriverTableHandler;
import com.wzc.be.es.sync.core.handler.OtherTableHandler;
import com.wzc.be.es.sync.core.model.sync.SyncGroup;
import com.wzc.common.cache.utils.RedisDelayQueue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.context.WebServerInitializedEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class DelayConfig {

    @Resource(name = "syncGroupMap")
    private Map<String, SyncGroup> syncGroupMap;

    @Autowired
    private OtherTableHandler otherTableHandler;

    @Autowired
    private DriverTableHandler driverTableHandler;


    @EventListener(WebServerInitializedEvent.class)
    public void init() {
        log.debug("初始化延迟队列----> {}", syncGroupMap.keySet());
        syncGroupMap.keySet().forEach(key -> {
            CanalMessageConsumer consumer = new CanalMessageConsumer(syncGroupMap, otherTableHandler, driverTableHandler);
            RedisDelayQueue.run(CanalEsConstants.CANAL_MESSAGE_HANDLER_DELAY_QUEUE + key, consumer::canalTableDelayMessage);
        });
    }

}
