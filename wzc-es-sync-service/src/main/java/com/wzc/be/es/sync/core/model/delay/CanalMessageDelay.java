package com.wzc.be.es.sync.core.model.delay;

import com.wzc.be.es.sync.core.model.sync.TableInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;

/**
 * canal消息延迟队列数据包装类
 *
 * <AUTHOR>
 * @date 2023年06月13日
 */
@Data
public class CanalMessageDelay implements Serializable {

    /**
     * 操作行为
     */
    private String action;

    /**
     * 修改数据map
     */
    private Map<String, Object> beforeDataMap;

    /**
     * 数据map
     */
    private Map<String, Object> dataMap;

    /**
     * 表信息
     */
    private TableInfo tableInfo;

    /**
     * 同步组
     */
    private String groupId;

    /**
     * 更新字段集合(仅更新操作时候存在)
     */
    private Set<String> updateColumnSet;

    /**
     * 更新来源
     */
    private Boolean fromUpdate;

    /**
     * 初始化来源
     */
    private Boolean fromInit;

    /**
     * 重试次数
     */
    private Integer delayCount = 1;

}
