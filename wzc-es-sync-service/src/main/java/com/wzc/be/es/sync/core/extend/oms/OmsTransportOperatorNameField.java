package com.wzc.be.es.sync.core.extend.oms;

import com.wzc.be.es.sync.core.model.sync.EsExtendField;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * <p>
 *     OMS运输操作员名称字段 - 通用字段，可用于多个OMS相关索引
 * </p>
 *
 * <AUTHOR> Assistant
 * @since 2025/02/18
 */
@Component
public class OmsTransportOperatorNameField extends EsExtendField<String, Map<String, Object>> {

    private static final String FIELD_NAME = "operatorName";
    private static final String GROUP_NAME = "omsBaseGroupTransport"; // 可以根据需要调整
    private static final String DATABASE_NAME = "zt_user";

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public String getData(Map<String, Object> esSourceMap) {
        // 尝试多个可能的操作员ID字段
        Object operatorIdObj = getOperatorId(esSourceMap);
        if (isNull(operatorIdObj)) {
            return "";
        }
        if (!StringUtils.isNumeric(String.valueOf(operatorIdObj))) {
            return "";
        }
        
        // 获取操作员ID
        Long operatorId = Long.valueOf(String.valueOf(operatorIdObj));
        String querySql = "select user_name as operatorName from mid_user_base where id = ? limit 1";
        List<Map<String, Object>> resultList = jdbcTemplate.queryForList(querySql, operatorId);
        if (CollectionUtils.isEmpty(resultList)) {
            return "";
        }
        Map<String, Object> resultMap = resultList.get(0);
        if (MapUtils.isEmpty(resultMap)) {
            return "";
        }
        Object operatorName = resultMap.get(FIELD_NAME);
        if (nonNull(operatorName)) {
            return operatorName.toString();
        }
        return "";
    }

    /**
     * 获取操作员ID，支持多种字段名
     */
    private Object getOperatorId(Map<String, Object> esSourceMap) {
        // 按优先级尝试不同的操作员ID字段
        String[] operatorIdFields = {
            "operatorId", 
            "createId", 
            "updateId", 
            "auditId",
            "acceptOperatorId",
            "confirmOperatorId"
        };
        
        for (String field : operatorIdFields) {
            Object value = esSourceMap.get(field);
            if (nonNull(value)) {
                return value;
            }
        }
        return null;
    }

    @Override
    public String getFieldName() {
        return FIELD_NAME;
    }

    @Override
    public String getGroupName() {
        return GROUP_NAME;
    }

    @Override
    public String getDataBaseName() {
        return DATABASE_NAME;
    }
}
