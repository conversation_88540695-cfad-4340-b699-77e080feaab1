package com.wzc.be.es.sync.core.model.job;

import lombok.Data;

import java.io.Serializable;

/**
 * es数据同步任务参数
 */
@Data
public class EsSyncParam implements Serializable {

    /**
     * 宽表分组名
     */
    private String groupName;

    /**
     * 数据库表名
     */
    private String tableName;

    /**
     * 过滤条件
     */
    private String whereCondition;

    /**
     * 是否初始化操作
     */
    private Boolean init;

    /**
     * 决定tableHandler调用insert还是update
     */
    private Boolean update;

}
