/*
 * Copyright (C) 2023 Baidu, Inc. All Rights Reserved.
 */
package com.wzc.be.es.sync.core.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.expression.MapAccessor;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public class SpelUtil {
    /**
     * spel解析器
     */
    private static final SpelExpressionParser PARSER = new SpelExpressionParser();
    /**
     * spel缓存
     */
    private static final ConcurrentHashMap<String, Expression> EXPRESSION_MAP =
            new ConcurrentHashMap<>(256);

    /**
     * 获取表达式值
     *
     * @param param
     * @param elExpression #root['city']== '福州'  || city=='福州'
     * @return
     */
    public static Object getValue(Map<String, Object> param, String elExpression) {
        Expression expression = getExpression(elExpression);
        StandardEvaluationContext context = new StandardEvaluationContext(param);
        context.addPropertyAccessor(new MapAccessor());
        Object value = expression.getValue(context);
        return value;
    }

    /**
     * 验证SpEL[不符合]
     *
     * @param param        参数
     * @param elExpression 表达式
     * @return
     */
    public static boolean verifyNotSpEL(Map<String, Object> param, String elExpression) {
        return !verifySpEL(param, elExpression);
    }

    /**
     * 验证SpEL[符合]
     *
     * @param param        参数
     * @param elExpression 表达式
     * @return
     */
    public static boolean verifySpEL(Map<String, Object> param, String elExpression) {
        try {
            Object value = SpelUtil.getValue(param, elExpression);
            if (!(value instanceof Boolean)) {
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return false;
    }

    /**
     * 从缓存中获取spel编译表达式
     *
     * @return SpelExpression
     */
    private static Expression getExpression(String el) {
        Expression expression = EXPRESSION_MAP.get(el);
        if (expression != null) {
            return expression;
        }
        return EXPRESSION_MAP.computeIfAbsent(el, k -> PARSER.parseRaw(el));
    }
}
