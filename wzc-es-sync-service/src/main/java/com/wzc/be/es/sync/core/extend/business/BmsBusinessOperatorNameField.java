package com.wzc.be.es.sync.core.extend.business;

import com.alibaba.google.common.collect.Maps;
import com.wzc.be.es.sync.core.model.sync.EsExtendField;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * <p>
 *     BMS业务操作员名称字段
 * </p>
 *
 * <AUTHOR> Assistant
 * @since 2025/02/18
 */
@Component
public class BmsBusinessOperatorNameField extends EsExtendField<Map<String, Object>, Map<String, Object>> {

    /**
     * 应收审核操作员名称
     */
    private static final String arAuditOperatorNameField = "arAuditOperatorName";
    
    /**
     * 应付审核操作员名称
     */
    private static final String apAuditOperatorNameField = "apAuditOperatorName";
    
    /**
     * 货主审核操作员名称
     */
    private static final String shipperAuditOperatorNameField = "shipperAuditOperatorName";

    private static final String GROUP_NAME = "bmsBusinessIndex";
    private static final String DATABASE_NAME = "zt_user";

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public Map<String, Object> getData(Map<String, Object> esSourceMap) {
        Map<String, Object> resultMap = Maps.newHashMap();
        
        // 获取应收审核操作员名称
        Object arAuditOperatorIdObj = esSourceMap.get("arAuditOperatorId");
        if (nonNull(arAuditOperatorIdObj) && StringUtils.isNumeric(String.valueOf(arAuditOperatorIdObj))) {
            String operatorName = getOperatorName(Long.valueOf(String.valueOf(arAuditOperatorIdObj)));
            if (StringUtils.isNotBlank(operatorName)) {
                resultMap.put(arAuditOperatorNameField, operatorName);
            }
        }
        
        // 获取应付审核操作员名称
        Object apAuditOperatorIdObj = esSourceMap.get("apAuditOperatorId");
        if (nonNull(apAuditOperatorIdObj) && StringUtils.isNumeric(String.valueOf(apAuditOperatorIdObj))) {
            String operatorName = getOperatorName(Long.valueOf(String.valueOf(apAuditOperatorIdObj)));
            if (StringUtils.isNotBlank(operatorName)) {
                resultMap.put(apAuditOperatorNameField, operatorName);
            }
        }
        
        // 获取货主审核操作员名称
        Object shipperAuditOperatorIdObj = esSourceMap.get("shipperAuditOperatorId");
        if (nonNull(shipperAuditOperatorIdObj) && StringUtils.isNumeric(String.valueOf(shipperAuditOperatorIdObj))) {
            String operatorName = getOperatorName(Long.valueOf(String.valueOf(shipperAuditOperatorIdObj)));
            if (StringUtils.isNotBlank(operatorName)) {
                resultMap.put(shipperAuditOperatorNameField, operatorName);
            }
        }
        
        return resultMap;
    }

    /**
     * 根据操作员ID获取操作员名称
     */
    private String getOperatorName(Long operatorId) {
        if (isNull(operatorId)) {
            return "";
        }
        
        String querySql = "select user_name as operatorName from mid_user_base where id = ? limit 1";
        List<Map<String, Object>> resultList = jdbcTemplate.queryForList(querySql, operatorId);
        if (CollectionUtils.isEmpty(resultList)) {
            return "";
        }
        
        Map<String, Object> result = resultList.get(0);
        if (MapUtils.isEmpty(result)) {
            return "";
        }
        
        Object operatorName = result.get("operatorName");
        return nonNull(operatorName) ? operatorName.toString() : "";
    }

    @Override
    public String getFieldName() {
        return null;
    }

    @Override
    public List<String> getFieldsName() {
        return List.of(arAuditOperatorNameField, apAuditOperatorNameField, shipperAuditOperatorNameField);
    }

    @Override
    public String getGroupName() {
        return GROUP_NAME;
    }

    @Override
    public String getDataBaseName() {
        return DATABASE_NAME;
    }
}
