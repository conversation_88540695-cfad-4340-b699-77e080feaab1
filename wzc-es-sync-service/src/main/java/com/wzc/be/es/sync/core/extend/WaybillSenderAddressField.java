/*
 * Copyright (C) 2023 Baidu, Inc. All Rights Reserved.
 */
package com.wzc.be.es.sync.core.extend;

import com.wzc.be.es.sync.core.model.sync.EsExtendField;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static java.util.Objects.nonNull;

/**
 * <AUTHOR>
 * @ClassName OrderItemsField
 **/
@Component
public class WaybillSenderAddressField extends EsExtendField<List<Map<String, Object>>, Map<String, Object>> {

    private static final String FIELD_NAME = "sender";
    private static final String GROUP_NAME = "omsBaseWaybill";
    private static final String DATABASE_NAME = "oms-base";

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public List<Map<String, Object>> getData(Map<String, Object> esSourceMap) {
        // 获取订单号
        String omsNo = nonNull(esSourceMap.get("baseNo")) ? (String) esSourceMap.get("baseNo") : null;
        if (Objects.nonNull(omsNo)) {
            // 查询货物信息
            String querySql = "SELECT address,id FROM oms_address WHERE base_no = ? AND type = 1 AND is_deleted = 0";

            List<Map<String, Object>> resultList = jdbcTemplate.queryForList(querySql, omsNo);

            return resultList;
        }
        return null;
    }

    @Override
    public String getFieldName() {
        return FIELD_NAME;
    }

    @Override
    public String getGroupName() {
        return GROUP_NAME;
    }

    @Override
    public String getDataBaseName() {
        return DATABASE_NAME;
    }
}
