package com.wzc.be.es.sync.core.util;

import com.google.common.collect.Lists;
import com.wzc.be.es.sync.core.annotation.CanalTable;
import com.wzc.be.es.sync.core.enums.TableNameEnum;
import com.wzc.be.es.sync.core.handler.inter.EntryHandler;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2022/09/2713:33
 */
public class HandlerUtil {

    public static EntryHandler getEntryHandler(List<? extends EntryHandler> entryHandlers, String tableName) {
        EntryHandler globalHandler = null;
        for (EntryHandler handler : entryHandlers) {
            String canalTableName = getCanalTableName(handler);
            if (TableNameEnum.ALL.name().toLowerCase().equals(canalTableName)) {
                globalHandler = handler;
                continue;
            }
            if (tableName.equals(canalTableName)) {
                return handler;
            }
            String name = GenericUtil.getTableGenericProperties(handler);
            if (name != null) {
                if (name.equals(tableName)) {
                    return handler;
                }
            }
        }
        return globalHandler;
    }

    public static EntryHandler getEntryHandler(Map<String, EntryHandler> map, String tableName) {
        EntryHandler entryHandler = map.get(tableName);
        if (entryHandler == null) {
            return map.get(TableNameEnum.ALL.name().toLowerCase());
        }
        return entryHandler;
    }

    public static List<EntryHandler> getEntryHandlerList(Map<String, List<EntryHandler>> map, String tableName) {
        List<EntryHandler> list = map.get(tableName);
        if (CollectionUtils.isEmpty(list)) {
            return map.get(TableNameEnum.ALL.name().toLowerCase());
        }
        return list;
    }

    public static Map<String, List<EntryHandler>> getTableHandlerMap(List<? extends EntryHandler> entryHandlers) {
        Map<String, List<EntryHandler>> map = new ConcurrentHashMap<>();
        if (entryHandlers != null && entryHandlers.size() > 0) {
            for (EntryHandler handler : entryHandlers) {
                if (StringUtils.isBlank(handler.getTableName())) {
                    throw new RuntimeException("canalDatabaseName为空");
                }
                if (StringUtils.isBlank(handler.getDatabaseName())) {
                    throw new RuntimeException("canalTableName为空");
                }
                if (StringUtils.isBlank(handler.getGroupName())) {
                    throw new RuntimeException("canalGroupName为空");
                }
                String handlerKey = new String(handler.getDatabaseName() + ":" + handler.getTableName()).toLowerCase();
                if (!map.containsKey(handlerKey)) {
                    map.put(handlerKey, new ArrayList<>());
                }
                boolean notExist = map.get(handlerKey).stream()
                                .noneMatch(subHandler -> StringUtils.equalsIgnoreCase(subHandler.getGroupName(), handler.getGroupName()));
                if (notExist) {
                    map.get(handlerKey).add(handler);
                }
            }
        }
        return map;
    }


    public static Map<String, List<EntryHandler>> getTableHandlerListMap(List<? extends EntryHandler> entryHandlers) {
        Map<String, List<EntryHandler>> map = new ConcurrentHashMap<>();
        if (entryHandlers != null && entryHandlers.size() > 0) {
            for (EntryHandler handler : entryHandlers) {
                if (StringUtils.isBlank(handler.getTableName())) {
                    throw new RuntimeException("tableName为空");
                }
                if (StringUtils.isBlank(handler.getDatabaseName())) {
                    throw new RuntimeException("databaseName为空");
                }
                if (StringUtils.isBlank(handler.getGroupName())) {
                    throw new RuntimeException("groupName为空");
                }
                String canalDatabaseName = handler.getDatabaseName();
                String canalTableName = handler.getTableName();
                String groupName = handler.getGroupName();
                String handlerKey = new String(canalDatabaseName + ":" + canalTableName).toLowerCase();
                List<EntryHandler> handlers = map.get(handlerKey);
                if (CollectionUtils.isEmpty(handlers)) {
                    handlers = Lists.newArrayList(handler);
                }else {
                    boolean notExist = handlers.stream()
                            .noneMatch(subHandler -> StringUtils.equalsIgnoreCase(subHandler.getGroupName(), groupName));
                    if (notExist) {
                        handlers.add(handler);
                    }
                }
                map.putIfAbsent(handlerKey, handlers);
            }
        }
        return map;
    }


    public static String getCanalTableName(EntryHandler entryHandler) {
        CanalTable canalTable = entryHandler.getClass().getAnnotation(CanalTable.class);
        if (canalTable != null) {
            return canalTable.value();
        }
        return entryHandler.getTableName();
    }

}
