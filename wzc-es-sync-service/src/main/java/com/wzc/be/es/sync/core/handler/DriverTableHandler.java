/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.sync.core.handler;

import com.wzc.be.es.sync.core.handler.impl.AbstractTableHandler;
import com.wzc.be.es.sync.core.model.sync.SyncGroup;
import com.wzc.be.es.sync.core.model.sync.TableInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022年10月08日 5:05 PM
 */
@Slf4j
@Component
public class DriverTableHandler extends AbstractTableHandler {

    @Override
    public void insert(Map<String, Object> stringObjectMap,
                       TableInfo tableInfo, SyncGroup syncGroup, boolean fromUpdate, boolean fromInit, Integer delayCount) {
        super.insert(stringObjectMap, tableInfo, syncGroup, fromUpdate, fromInit, delayCount);
    }


    @Override
    public void update(Map<String, Object> before, Map<String, Object> after,
                       Set<String> updateColumnSet, TableInfo tableInfo, SyncGroup syncGroup, Integer delayCount) {
        super.update(before, after, updateColumnSet, tableInfo, syncGroup, delayCount);

    }


    @Override
    public void delete(Map<String, Object> stringObjectMap, TableInfo tableInfo, SyncGroup syncGroup, Integer delayCount) {
        super.delete(stringObjectMap, tableInfo, syncGroup, delayCount);
    }
}
