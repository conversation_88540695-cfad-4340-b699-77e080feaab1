package com.wzc.be.es.sync.core.util;


import com.wzc.be.es.sync.core.handler.inter.EntryHandler;

import javax.persistence.Table;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2022/09/2910:45
 */
public class GenericUtil {


    private static Map<Class<? extends EntryHandler>, Class> cache = new ConcurrentHashMap<>();


    static String getTableGenericProperties(EntryHandler entryHandler) {
        Class<?> tableClass = getTableClass(entryHandler);
        if (tableClass != null) {
            Table annotation = tableClass.getAnnotation(Table.class);
            if (annotation != null) {
                return annotation.name();
            }
        }
        return null;
    }


    @SuppressWarnings("unchecked")
    public static <T> Class<T> getTableClass(EntryHandler object) {
        Class<? extends EntryHandler> handlerClass = object.getClass();
        Class tableClass = cache.get(handlerClass);
        if (tableClass == null) {
            Type[] interfacesTypes = handlerClass.getGenericInterfaces();
            for (Type t : interfacesTypes) {
                Class c = (Class) ((ParameterizedType) t).getRawType();
                if (c.equals(EntryHandler.class)) {
                    Object obj = ((ParameterizedType) t).getActualTypeArguments()[0];
                    if (obj instanceof ParameterizedType) {
                        tableClass = (Class) ((ParameterizedType) obj).getRawType();
                    }else {
                        tableClass = (Class) obj;
                    }
                    cache.putIfAbsent(handlerClass, tableClass);
                    return tableClass;
                }
            }
        }
        return tableClass;
    }


}
