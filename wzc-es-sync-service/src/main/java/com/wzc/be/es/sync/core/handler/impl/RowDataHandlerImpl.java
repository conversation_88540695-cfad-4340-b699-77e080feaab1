package com.wzc.be.es.sync.core.handler.impl;

import com.alibaba.otter.canal.protocol.CanalEntry;
import com.wzc.be.es.sync.core.handler.inter.EntryHandler;
import com.wzc.be.es.sync.core.handler.inter.RowDataHandler;
import com.wzc.be.es.sync.core.factory.IModelFactory;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/09/2917:21
 */
@Slf4j
public class RowDataHandlerImpl implements RowDataHandler<CanalEntry.RowData> {



    private IModelFactory<List<CanalEntry.Column>> modelFactory;




    public RowDataHandlerImpl(IModelFactory modelFactory) {
        this.modelFactory = modelFactory;
    }

    @Override
    public <R> void handlerRowData(CanalEntry.RowData rowData, EntryHandler<R> entryHandler,
                                   CanalEntry.EventType eventType) throws Exception {
        if (entryHandler != null) {
            switch (eventType) {
                case INSERT:
                    R object = modelFactory.newInstance(entryHandler, rowData.getAfterColumnsList());
                    log.debug("表：{}，INSERT 数据", entryHandler.getTableName());
                    entryHandler.insert(object);
                    break;
                case UPDATE:
                    Set<String> updateColumnSet =
                            rowData.getAfterColumnsList().stream().filter(CanalEntry.Column::getUpdated)
                            .map(CanalEntry.Column::getName).collect(Collectors.toSet());
                    R before = modelFactory.newInstance(entryHandler, rowData.getBeforeColumnsList());
                    R after = modelFactory.newInstance(entryHandler, rowData.getAfterColumnsList());
                    log.debug("表：{}，UPDATE 数据 updateColumnSet：{}",
                            entryHandler.getTableName(), updateColumnSet);
                    entryHandler.update(before, after, updateColumnSet);
                    break;
                case DELETE:
                    R o = modelFactory.newInstance(entryHandler, rowData.getBeforeColumnsList());
                    log.debug("表：{}，DELETE 数据", entryHandler.getTableName());
                    entryHandler.delete(o);
                    break;
                default:
                    break;
            }
        }
    }
}
