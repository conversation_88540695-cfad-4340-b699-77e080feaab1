package com.wzc.be.es.sync.common.config.sharding;

import com.google.common.collect.Lists;
import com.google.common.collect.Range;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingAlgorithm;
import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingValue;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2023/3/28
 */
@Slf4j
public class DispatchCarBaseComplexShardingAlgorithm implements ComplexKeysShardingAlgorithm<String> {

    private static final String DISPATCH_CAR_NO = "dispatch_car_no";
    private static final String WAYBILL_NO = "waybill_no";

    public static Properties properties;

    /**
     * 分表个数
     */
    public static Long shardingCount;

    /**
     * 分表数量长度
     */
    public static int shardingStringSize;

    public static String shardingStringFormat;

    @Override
    public Collection<String> doSharding(Collection<String> collection,
                                         ComplexKeysShardingValue<String> complexKeysShardingValue) {
        // 实现  >，>=, <=，<  和 BETWEEN AND 等操作
        Map<String, Range<String>> shardingRangeMaps = complexKeysShardingValue.getColumnNameAndRangeValuesMap();
        if (!shardingRangeMaps.isEmpty()){
            throw new UnsupportedOperationException("只支持精确查询，不支持范围查询");
        }
        // 逻辑表的名称
        String tableName = complexKeysShardingValue.getLogicTableName();
        // 等于号   in 的值
        Map<String, Collection<String>> shardingMaps = complexKeysShardingValue.getColumnNameAndShardingValuesMap();
        List<String> noList = Lists.newArrayList();
        // dispatch_car_no：派车单号分片键值的集合
        noList.addAll(shardingMaps.getOrDefault(DISPATCH_CAR_NO, new ArrayList<>()));
        // waybill_no：订单号分片键值的集合
        noList.addAll(shardingMaps.getOrDefault(WAYBILL_NO, new ArrayList<>()));
        return noList.stream().map(id-> MessageFormat.format(tableName + "_{0}",
                        Integer.valueOf(getShardingString(id))))
                .collect(Collectors.toSet());
    }

    public static String getShardingString(String no){
        if (StringUtils.isNotBlank(no) && no.length() >= shardingStringSize) {
            String index = no.substring(no.length() - shardingStringSize);
            return String.format(shardingStringFormat, Integer.parseInt(index) % shardingCount);
        }
        log.warn("getShardingString is Blank!");
        return StringUtils.EMPTY;
    }

    @Override
    public Properties getProps() {
        return properties;
    }

    @Override
    public void init(Properties properties) {
        DispatchCarBaseComplexShardingAlgorithm.properties = properties;
        Object shardingCount = properties.get("shardingCount");
        if (Objects.nonNull(shardingCount)) {
            DispatchCarBaseComplexShardingAlgorithm.shardingCount = Long.valueOf((String) shardingCount);
            DispatchCarBaseComplexShardingAlgorithm.shardingStringSize = shardingCount.toString().length();
            DispatchCarBaseComplexShardingAlgorithm.shardingStringFormat = "%0" + shardingStringSize + "d";
        }
    }

    @Override
    public String getType() {
        return "WAYBILL";
    }
}
