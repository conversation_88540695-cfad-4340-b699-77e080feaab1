/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.sync.core.util;

import cn.hutool.core.date.DatePattern;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.elasticsearch.action.ActionListener;
import org.elasticsearch.action.admin.indices.refresh.RefreshRequest;
import org.elasticsearch.action.admin.indices.refresh.RefreshResponse;
import org.elasticsearch.action.bulk.BackoffPolicy;
import org.elasticsearch.action.bulk.BulkProcessor;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.unit.ByteSizeUnit;
import org.elasticsearch.common.unit.ByteSizeValue;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;

import static java.util.Objects.isNull;

/**
 * <AUTHOR>
 * @date 2022年10月09日 5:27 PM
 */
@Slf4j
@Component
public class ElasticSearchUtil implements Serializable {
    private static final Logger LOGGER = LoggerFactory.getLogger(ElasticSearchUtil.class);

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    private BulkProcessor bulkProcessor;

    @PostConstruct
    public void init() {
        BulkProcessor.Listener listener = new BulkProcessor.Listener() {
            @Override
            public void beforeBulk(long executionId, BulkRequest request) {
                //重写beforeBulk,在每次bulk request发出前执行,在这个方法里面可以知道在本次批量操作中有多少操作数
                log.debug("beforeBulk executionId:{} num:{} policy:{}", executionId, request.numberOfActions(), request.getRefreshPolicy());
            }


            @Override
            public void afterBulk(long executionId, BulkRequest request, BulkResponse response) {
                //重写afterBulk方法，每次批量请求结束后执行，可以在这里知道是否有错误发生。
                if (response.hasFailures()) {
                    LOGGER.error("Bulk [{}] executed with failures,response = {}",
                            executionId, response.buildFailureMessage());
                } else {
                    LOGGER.info("Bulk [{} , {} requests] completed in {} milliseconds",
                            executionId, request.numberOfActions(), response.getTook().getMillis());
                }
            }

            @Override
            public void afterBulk(long executionId, BulkRequest request, Throwable failure) {
                //重写方法，如果发生错误就会调用。
                LOGGER.error("Failed to execute bulk", failure);
            }
        };

        //在这里调用build()方法构造bulkProcessor,在底层实际上是用了bulk的异步操作
        BiConsumer<BulkRequest, ActionListener<BulkResponse>> bulkConsumer =
                (request, bulkListener) -> restHighLevelClient.bulkAsync(request, RequestOptions.DEFAULT, bulkListener);
        BulkProcessor bulkProcessor = BulkProcessor.builder(bulkConsumer, listener)
                // 1000条数据请求执行一次bulk
                .setBulkActions(2000)
                // 5mb的数据刷新一次bulk
                .setBulkSize(new ByteSizeValue(10L, ByteSizeUnit.MB))
                // 并发请求数量, 0不并发, 1并发允许执行
                .setConcurrentRequests(0)
                // 固定1s必须刷新一次
                .setFlushInterval(TimeValue.timeValueSeconds(1L))
                // 重试5次，间隔2s
                .setBackoffPolicy(BackoffPolicy.constantBackoff(TimeValue.timeValueSeconds(5L), 5))
                .build();
        this.bulkProcessor = bulkProcessor;
    }

    @PreDestroy
    public void destroy() {
        try {
            bulkProcessor.awaitClose(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            LOGGER.error("Failed to close bulkProcessor", e);
        }
        LOGGER.info("bulkProcessor closed!");
    }

    /**
     * 修改
     *
     * @param request
     */
    public void update(UpdateRequest request) {
        this.bulkProcessor.add(request);
    }

    /**
     * 新增
     *
     * @param request
     */
    public void insert(IndexRequest request) {
        this.bulkProcessor.add(request);
    }


    /**
     * 单条数据同步写入
     * @param request
     */
    public void immediateInsert(IndexRequest request) {
        try {
            IndexResponse response = this.restHighLevelClient.index(request, RequestOptions.DEFAULT);
            LOGGER.debug("immediateInsert completed id:{} status:{}", response.getId(), response.status());
        } catch (IOException e) {
            LOGGER.error("immediateInsert error", e);
        }
    }

    /**
     * 单条数据同步更新
     * @param request
     */
    public void immediateUpdate(UpdateRequest request) {
        try {
            UpdateResponse response = this.restHighLevelClient.update(request, RequestOptions.DEFAULT);
            LOGGER.debug("immediateUpdate completed id:{} status:{}", response.getId(), response.status());
        } catch (IOException e) {
            LOGGER.error("immediateUpdate error", e);
        }
    }


    /**
     * 删除
     * @param deleteRequest
     */
    public void delete(DeleteRequest deleteRequest) {
        this.bulkProcessor.add(deleteRequest);
    }

    /**
     * 根据查询条件删除
     * @param searchRequest 查询条件
     */
    public Boolean deleteByQuery(DeleteByQueryRequest searchRequest) {
        try {
            BulkByScrollResponse response = this.restHighLevelClient
                    .deleteByQuery(searchRequest, RequestOptions.DEFAULT);
            LOGGER.info("deleteByQuery deleted:{} status:{}", response.getDeleted(), response.getStatus());
            return response.getDeleted() > 0;
        } catch (IOException e) {
            LOGGER.error("deleteByQuery request: {}, error:", searchRequest, e);
        }
        return false;
    }


    public SearchResponse search(SearchSourceBuilder sourceBuilder, String index) {
//        String mainKey = tableInfo.getMainTableKey();
//        Object id = after.get(mainKey);
//        // 查询主索引，获取需要更新的文档
//        String mainKeyField = tableInfo.getColumnToEsFieldMap().get(mainKey);
//        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
//        sourceBuilder.query(QueryBuilders.termQuery(mainKeyField, id));
        SearchRequest searchRequest = new SearchRequest(index);
        searchRequest.source(sourceBuilder);
        SearchResponse searchResponse = null;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            LOGGER.error("restHighLevelClient.search error, sourceBuilder is :" + sourceBuilder , e);
        }
        return searchResponse;
    }

    /**
     * 主动刷新索引
     */
    public void refresh(String indexName) {
        RefreshRequest request = new RefreshRequest(indexName);
        try {
            RefreshResponse refreshResponse = restHighLevelClient.indices().refresh(request, RequestOptions.DEFAULT);
            LOGGER.debug("refresh completed ind、ex:{} status:{}", indexName, refreshResponse.getStatus());
        } catch (Exception ex) {
            log.error("es refresh index error", ex);
        }
    }

    /**
     * jdbc数据标准化
     */
    public static Map<String, Object> jdbcDataStandardization(Map<String, Object> map) {
        if (MapUtils.isEmpty(map)) {
            return map;
        }
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            Object newValue = jdbcDateTimeFormat(entry.getValue());
            if (newValue != entry.getValue()) {
                entry.setValue(newValue);
            }
        }
        return map;
    }

    /**
     * jdbc时间数据转换为es标准格式
     */
    private static Object jdbcDateTimeFormat(Object time) {
        if (isNull(time)) {
            return time;
        }
        if (time instanceof LocalDateTime) {
            return ((LocalDateTime) time).format(DateTimeFormatter.ofPattern(DatePattern.NORM_DATETIME_PATTERN));
        }
        return time;
    }
}
