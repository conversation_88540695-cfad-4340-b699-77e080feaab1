/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.sync.core.handler;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wzc.be.es.sync.common.constants.CanalEsConstants;
import com.wzc.be.es.sync.core.handler.impl.AbstractTableHandler;
import com.wzc.be.es.sync.core.model.sync.MainTableKeyData;
import com.wzc.be.es.sync.core.model.sync.SyncGroup;
import com.wzc.be.es.sync.core.model.sync.TableInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * <AUTHOR>
 * @date 2022年10月08日 5:05 PM
 */
@Slf4j
@Component
public class OtherTableHandler extends AbstractTableHandler {

    @Override
    public void insert(Map<String, Object> map, TableInfo tableInfo, SyncGroup syncGroup, boolean fromUpdate, boolean fromInit, Integer delayCount) {
        if (tableInfo.isInnerJoin()) {
            this.doInnerJoinInsertEvent(map, tableInfo, syncGroup, fromUpdate, fromInit, delayCount);
            return;
        }

        //不符合插入条件 过滤
        if (judgeConditionBySpEls(map,tableInfo) || judgeInsertCondition(map,tableInfo)){
            return;
        }

        String mainKey = tableInfo.getMainTableKey();
        Object id = map.get(mainKey);
        MainTableKeyData mainTableKeyData = getMainTableId(tableInfo, syncGroup, id);
        if (isNull(mainTableKeyData)) {
            log.info("table:[{}], id ：[{}]计算mainTableKeyData为null,不处理", tableInfo.getTableName(), id);
            return;
        }

        // 刷新相关索引
        refreshIndex(syncGroup, mainTableKeyData.getIds());

        SearchResponse searchResponse;
        SearchRequest searchRequest = getSearchRequest(map, tableInfo, syncGroup, mainTableKeyData);
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("查询是否已初始化失败", e);
            return;
        }
        if (isNull(searchResponse) || searchResponse.getHits().getTotalHits().value < 1L) {
            log.info("es记录不存在, 从表insert存入延时队列");
            // 从表insert存入延时队列
            if (Boolean.TRUE.equals(fromInit)) {
                return;
            }
            sendToDelayQueue(CanalEsConstants.CANAL_ACTION_INSERT, syncGroup, tableInfo, null, map, fromUpdate, fromInit, delayCount, null);
            return;
        }
        // 从表第一条记录写入，直接update进去
        // 需要更新的文档
        SearchHits searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();

        List<SearchHit> hitList = new ArrayList<>(hits.length);
        // 是否一对多从表
        if (Boolean.TRUE.equals(tableInfo.getOneToMany())) {
            // 判断es数据是否匹配
            for (SearchHit hit : hits) {
                Map<String, Object> sourceAsMap = hit.getSourceAsMap();
                String esFromTableIdColumn = tableInfo.getColumnToEsFieldMap().get(mainKey);
                Object esFromTableId = sourceAsMap.get(esFromTableIdColumn);
                if (isNull(esFromTableId) || mysqlEsEquals(id, esFromTableId)) {
                    hitList.add(hit);
                    break;
                }
            }
            // 如果无匹配新增数据
            if (CollectionUtils.isEmpty(hitList)) {
                log.info("一对多从表无法匹配数据, 新增es记录");
                // 处理字段mapping
                List<Map<String, Object>> finalList = Lists.newArrayList();
                this.executeMainMapping(syncGroup, mainTableKeyData.getList(), finalList);
                this.extendFieldMapping(syncGroup, finalList);
                // 批量写主索引
                this.saveEs(syncGroup, finalList, fromInit);
                saveInsertCache(syncGroup, finalList);
                return;
            }
        } else {
            hitList.addAll(Arrays.asList(hits));
        }
        for (SearchHit hit : hitList) {
            updateEsData(syncGroup, tableInfo, null, map, hit, fromUpdate, fromInit, delayCount, true);
        }
    }


    private void doInnerJoinInsertEvent(Map<String, Object> map, TableInfo tableInfo,
                                        SyncGroup syncGroup, boolean fromUpdate, boolean fromInit, Integer delayCount) {
        // 先找到驱动表对应id
        String baseSql = syncGroup.getJoinSql();
        String querySql = tableInfo.getJoinQueryCondition();
        String sql = baseSql + " " + querySql + " limit 1";
        String mainKey = tableInfo.getMainTableKey();
        String currentTableIdVal = String.valueOf(map.get(mainKey));
        List<Map<String, Object>> queryForList = jdbcTemplate.queryForList(sql, currentTableIdVal);
        if (CollectionUtils.isEmpty(queryForList)) {
            log.info("表：{} ， insert 操作，id： {}， 查询数据不存在，不处理", tableInfo.getTableName(), currentTableIdVal);
            // join 结果为空 不处理
            return;
        }
        Map<String, Object> mainMap = queryForList.get(0);
        // 驱动表信息
        TableInfo driverTableInfo = syncGroup.getTables()
                .stream().filter(TableInfo::isDriverTable).findFirst().get();
        if (ObjectUtils.isEmpty(driverTableInfo)) {
            log.warn("sync group [{}] need assign a driver table");
            return;
        }
        String driverTableIdField = driverTableInfo.getColumnToEsFieldMap().get(driverTableInfo.getMainTableKey());
        String currentTableIdField = tableInfo.getColumnToEsFieldMap().get(tableInfo.getMainTableKey());
        Object driverTableIdVal = mainMap.get(driverTableInfo.getMainTableKey());
        if (ObjectUtils.isEmpty(driverTableIdVal)) {
            log.warn("the driver table mainkey val is null, exit");
            return;
        }

        // 查询确认记录是否已存在
        SearchRequest searchRequest = new SearchRequest(syncGroup.getEsMainIndex());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.query(QueryBuilders.termQuery(driverTableIdField, driverTableIdVal));
        sourceBuilder.query(QueryBuilders.termQuery(currentTableIdField, currentTableIdVal));
        searchRequest.source(sourceBuilder);
        SearchResponse searchResponse = null;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("查询是否已初始化失败", e);
        }

        if (nonNull(searchResponse) && searchResponse.getHits().getTotalHits().value >= 1L) {
            // 已经初始化
            log.debug("inner join records has in es : {}", tableInfo.getTableName());
            return;
        }

        Map<String,Object> param = Maps.newHashMap();
        param.put(driverTableInfo.getMainTableKey(), driverTableIdVal);
        super.insert(param, driverTableInfo, syncGroup, fromUpdate, fromInit, delayCount);
    }


    @Override
    public void update(Map<String, Object> before, Map<String, Object> after,
                       Set<String> updateColumnSet, TableInfo tableInfo, SyncGroup syncGroup, Integer delayCount) {
        super.update(before, after, updateColumnSet, tableInfo, syncGroup, delayCount);
    }


    @Override
    public void delete(Map<String, Object> map, TableInfo tableInfo, SyncGroup syncGroup, Integer delayCount) {
        if (tableInfo.isInnerJoin()) {
            // 内连接直接删除
            log.info("内连接直接删除es记录：{} ", tableInfo.getTableName());
            super.delete(map, tableInfo, syncGroup, delayCount);
            return;
        }
        // 查询索引，获取需要更新的文档
        SearchResponse searchResponse;
        String mainKey = tableInfo.getMainTableKey();
        String id = String.valueOf(map.get(mainKey));
        MainTableKeyData mainTableKeyData = getMainTableId(tableInfo, syncGroup, id);
        SearchRequest searchRequest = getSearchRequest(map, tableInfo, syncGroup, mainTableKeyData);
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("restHighLevelClient.search error, map is :" + JSON.toJSONString(map), e);
            // 5s后重试
            executor.schedule(
                    () -> delete(map, tableInfo, syncGroup, delayCount), 5L, TimeUnit.SECONDS);
            return;
        }
        if (Objects.isNull(searchResponse)
                || Objects.isNull((searchResponse.getHits()))
                || ArrayUtils.isEmpty(searchResponse.getHits().getHits())) {
            // 索引不存在，直接return
            log.debug("非内连接，es记录不存在，exit ，{}", tableInfo.getTableName());
            return;
        }
        SearchHits searchHits = searchResponse.getHits();
        SearchHit[] hits = searchHits.getHits();
        for (SearchHit hit : hits) {
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            tableInfo.getColumnToEsFieldMap().keySet().forEach(column -> {
                String targetName = tableInfo.getColumnToEsFieldMap().get(column);
                if (StringUtils.isBlank(targetName)) {
                    return;
                }
                sourceAsMap.put(targetName, null);
            });
            this.extendFieldMapping(syncGroup, Collections.singletonList(sourceAsMap));
            Map<String, Object> updateMap = getUpdateMap(syncGroup, tableInfo, sourceAsMap);
            updateMap = fieldFilter(tableInfo, updateMap);
            UpdateRequest updateRequest = new UpdateRequest(syncGroup.getEsMainIndex(), hit.getId());

            // 是否使用脚本操作
            if (StringUtils.isNotBlank(tableInfo.getScriptDelete())) {
                updateRequest.script(new Script(ScriptType.INLINE,"painless",
                        tableInfo.getScriptDelete(), map));
            } else {
                updateRequest.doc(updateMap);
            }
            log.info("es删除数据 groupName:{} updateMap:{} id:{}", syncGroup.getGroupId(), updateMap, hit.getId());
            elasticSearchUtil.immediateUpdate(updateRequest);
            saveInsertCache(syncGroup, Collections.singletonList(sourceAsMap));
        }
    }

}
