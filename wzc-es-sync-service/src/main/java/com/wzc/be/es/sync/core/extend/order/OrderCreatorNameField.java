package com.wzc.be.es.sync.core.extend.order;

import com.wzc.be.es.sync.core.model.sync.EsExtendField;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * <p>
 *     订单创建人名称字段
 * </p>
 *
 * <AUTHOR> Assistant
 * @since 2025/02/18
 */
@Component
public class OrderCreatorNameField extends EsExtendField<String, Map<String, Object>> {

    private static final String FIELD_NAME = "creatorName";
    private static final String GROUP_NAME = "orderIndex";
    private static final String DATABASE_NAME = "zt_user";

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public String getData(Map<String, Object> esSourceMap) {
        Object createIdObj = esSourceMap.get("createId");
        if (isNull(createIdObj)) {
            return StringUtils.EMPTY;
        }
        if (!StringUtils.isNumeric(String.valueOf(createIdObj))) {
            return StringUtils.EMPTY;
        }
        
        // 获取创建人ID
        Long createId = Long.valueOf(String.valueOf(createIdObj));
        String querySql = "select user_name as creatorName from mid_user_base where id = ? limit 1";
        List<Map<String, Object>> resultList = jdbcTemplate.queryForList(querySql, createId);
        if (CollectionUtils.isEmpty(resultList)) {
            return StringUtils.EMPTY;
        }
        Map<String, Object> resultMap = resultList.get(0);
        if (MapUtils.isEmpty(resultMap)) {
            return StringUtils.EMPTY;
        }
        Object creatorName = resultMap.get(FIELD_NAME);
        if (nonNull(creatorName)) {
            return String.valueOf(creatorName);
        }
        return StringUtils.EMPTY;
    }

    @Override
    public String getFieldName() {
        return FIELD_NAME;
    }

    @Override
    public String getGroupName() {
        return GROUP_NAME;
    }

    @Override
    public String getDataBaseName() {
        return DATABASE_NAME;
    }
}
