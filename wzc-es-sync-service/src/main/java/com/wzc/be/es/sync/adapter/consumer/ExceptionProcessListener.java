package com.wzc.be.es.sync.adapter.consumer;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSONObject;
import com.wzc.be.es.sync.core.model.excep.ExcepProcessMessage;
import com.wzc.be.es.sync.core.repository.ExceptionProcessRepository;
import com.wzc.common.cache.redis.CustomRedis;
import com.wzc.common.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @Author: keeper Yu yxbwzx
 * @Date: 2023/9/5 20:04
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ExceptionProcessListener {


    private final ExceptionProcessRepository exceptionProcessRepository;

    @KafkaListener(topics = "${kafka.topic.alarm-process}", groupId = "${kafka.group.alarm-process}")
    public void syncExceptionProcessToES(String message){
        log.info("接收异常处理状态：{}",message);
        ExcepProcessMessage excepMessage = JSONObject.parseObject(message, ExcepProcessMessage.class);
        try{
            if (!CustomRedis.setNx(excepMessage.getSubWaybillNo(), excepMessage.getSubWaybillNo(), 1)) {
                throw new RuntimeException("syncExceptionProcessToES==添加同步延迟==》" + excepMessage.getSubWaybillNo());
            }
            //更新es中异常处理状态
            exceptionProcessRepository.updateExceptionProcessStatua(excepMessage);
        }catch (Exception e){
            log.warn("异常更新异常-->{}", ExceptionUtil.stacktraceToString(e));
            if(e instanceof ServiceException){
                return;
            }
            throw e;
        }
    }
}
