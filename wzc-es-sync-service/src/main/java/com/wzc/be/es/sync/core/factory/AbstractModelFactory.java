package com.wzc.be.es.sync.core.factory;


import com.wzc.be.es.sync.core.handler.inter.EntryHandler;
import com.wzc.be.es.sync.core.enums.TableNameEnum;
import com.wzc.be.es.sync.core.util.GenericUtil;
import com.wzc.be.es.sync.core.util.HandlerUtil;

public abstract class AbstractModelFactory<T> implements IModelFactory<T> {


    @Override
    public <R> R newInstance(EntryHandler entryHandler, T t) throws Exception {
        String canalTableName = HandlerUtil.getCanalTableName(entryHandler);
        if (TableNameEnum.ALL.name().toLowerCase().equals(canalTableName)) {
            return (R) t;
        }
        Class<R> tableClass = GenericUtil.getTableClass(entryHandler);
        if (tableClass != null) {
            return newInstance(tableClass, t);
        }
        return null;
    }


    abstract <R> R newInstance(Class<R> c, T t) throws Exception;
}
