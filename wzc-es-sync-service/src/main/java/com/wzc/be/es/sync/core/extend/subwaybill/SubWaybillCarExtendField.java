package com.wzc.be.es.sync.core.extend.subwaybill;

import com.alibaba.google.common.collect.Maps;
import com.wzc.be.es.sync.core.model.sync.EsExtendField;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static java.util.Objects.isNull;

/**
 * <p>
 *     子运单车辆扩展信息字段
 * </p>
 *
 * <AUTHOR> Assistant
 * @since 2025/02/18
 */
@Component
public class SubWaybillCarExtendField extends EsExtendField<Map<String, Object>, Map<String, Object>> {

    /**
     * 车辆报备状态
     */
    private static final String CAR_REPORT_STATUS = "carReportStatus";
    
    /**
     * 车辆类型
     */
    private static final String CAR_TYPE = "carType";
    
    /**
     * 车辆品牌
     */
    private static final String CAR_BRAND = "carBrand";

    private static final String GROUP_NAME = "subWaybillIndexCarExtend";
    private static final String DATABASE_NAME = "zt_metadata";

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public Map<String, Object> getData(Map<String, Object> esSourceMap) {
        Object carIdObj = esSourceMap.get("carId");
        if (isNull(carIdObj)) {
            return Maps.newHashMap();
        }
        if (!StringUtils.isNumeric(String.valueOf(carIdObj))) {
            return Maps.newHashMap();
        }
        
        // 获取车辆ID
        Long carId = Long.valueOf(String.valueOf(carIdObj));
        String querySql = "select report_status as carReportStatus, car_type as carType, car_brand as carBrand from md_car_extend where car_id = ? limit 1";
        List<Map<String, Object>> resultList = jdbcTemplate.queryForList(querySql, carId);
        if (CollectionUtils.isEmpty(resultList)) {
            return Maps.newHashMap();
        }
        
        Map<String, Object> resultMap = resultList.get(0);
        if (MapUtils.isEmpty(resultMap)) {
            return Maps.newHashMap();
        }
        
        Map<String, Object> extendInfo = Maps.newHashMap();
        
        // 车辆报备状态
        Object carReportStatus = resultMap.get("carReportStatus");
        if (carReportStatus != null) {
            extendInfo.put(CAR_REPORT_STATUS, carReportStatus);
        }
        
        // 车辆类型
        Object carType = resultMap.get("carType");
        if (carType != null) {
            extendInfo.put(CAR_TYPE, carType);
        }
        
        // 车辆品牌
        Object carBrand = resultMap.get("carBrand");
        if (carBrand != null) {
            extendInfo.put(CAR_BRAND, carBrand);
        }
        
        return extendInfo;
    }

    @Override
    public String getFieldName() {
        return null;
    }

    @Override
    public List<String> getFieldsName() {
        return List.of(CAR_REPORT_STATUS, CAR_TYPE, CAR_BRAND);
    }

    @Override
    public String getGroupName() {
        return GROUP_NAME;
    }

    @Override
    public String getDataBaseName() {
        return DATABASE_NAME;
    }
}
