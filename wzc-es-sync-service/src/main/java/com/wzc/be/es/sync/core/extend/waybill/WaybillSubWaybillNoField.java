package com.wzc.be.es.sync.core.extend.waybill;

import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

import com.wzc.be.es.sync.core.model.sync.EsExtendField;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import static java.util.Objects.isNull;

/**
 * <p>
 *     子运单列表查询
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/25
 */
@Component
public class WaybillSubWaybillNoField extends EsExtendField<List<String>, Map<String, Object>> {

    private static final String FIELD_NAME = "subWaybillNoList";
    private static final String GROUP_NAME = "tmsWaybillIndex";
    private static final String DATABASE_NAME = "zt_tms";

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public List<String> getData(Map<String, Object> esSourceMap) {
        Object waybillNoObj = esSourceMap.get("waybillNo");
        if (isNull(waybillNoObj)) {
            return null;
        }
        String waybillNo = String.valueOf(waybillNoObj);
        String querySql = "select sub_waybill_no from tms_sub_waybill where dispatch_car_no = ? and deleted = 0";
        List<String> resultList = jdbcTemplate.queryForList(querySql, String.class, waybillNo);
        if (CollectionUtils.isEmpty(resultList)) {
            return null;
        }
        return resultList;
    }

    @Override
    public String getFieldName() {
        return FIELD_NAME;
    }

    @Override
    public String getGroupName() {
        return GROUP_NAME;
    }

    @Override
    public String getDataBaseName() {
        return DATABASE_NAME;
    }
}
