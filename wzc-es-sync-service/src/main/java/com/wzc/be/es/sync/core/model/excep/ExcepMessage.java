package com.wzc.be.es.sync.core.model.excep;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @description:
 * @Author: keeper Yu yxbwzx
 * @Date: 2023/9/5 20:12
 * @since 1.0.0
 */
@Data
public class ExcepMessage {
    /**
     * 子运单号
     */
    private String subWaybillNo;

    /**
     * 异常发生时间
     */
    private LocalDateTime exceptionTime;

    /**
     * 异常分类
     */
    private Integer category;
}
