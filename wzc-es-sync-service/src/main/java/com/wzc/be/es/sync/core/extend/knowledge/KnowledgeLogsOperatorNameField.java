package com.wzc.be.es.sync.core.extend.knowledge;

import com.alibaba.google.common.collect.Maps;
import com.wzc.be.es.sync.core.model.sync.EsExtendField;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * <p>
 *     知识库业务日志操作员名称字段
 * </p>
 *
 * <AUTHOR> Assistant
 * @since 2025/02/18
 */
@Component
public class KnowledgeLogsOperatorNameField extends EsExtendField<Map<String, Object>, Map<String, Object>> {

    /**
     * 创建人名称
     */
    private static final String CREATOR_NAME = "creatorName";
    
    /**
     * 操作员名称
     */
    private static final String OPERATOR_NAME = "operatorName";

    private static final String GROUP_NAME = "knowledgeLogsSysBusiness";
    private static final String DATABASE_NAME = "zt_user";

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public Map<String, Object> getData(Map<String, Object> esSourceMap) {
        Map<String, Object> resultMap = Maps.newHashMap();
        
        // 获取创建人名称
        Object creatorIdObj = esSourceMap.get("creatorId");
        if (nonNull(creatorIdObj) && StringUtils.isNumeric(String.valueOf(creatorIdObj))) {
            String creatorName = getUserName(Long.valueOf(String.valueOf(creatorIdObj)));
            if (StringUtils.isNotBlank(creatorName)) {
                resultMap.put(CREATOR_NAME, creatorName);
            }
        }
        
        // 获取操作员名称
        Object operatorIdObj = esSourceMap.get("operatorId");
        if (nonNull(operatorIdObj) && StringUtils.isNumeric(String.valueOf(operatorIdObj))) {
            String operatorName = getUserName(Long.valueOf(String.valueOf(operatorIdObj)));
            if (StringUtils.isNotBlank(operatorName)) {
                resultMap.put(OPERATOR_NAME, operatorName);
            }
        }
        
        return resultMap;
    }

    /**
     * 根据用户ID获取用户名称
     */
    private String getUserName(Long userId) {
        if (isNull(userId)) {
            return StringUtils.EMPTY;
        }
        
        String querySql = "select user_name as userName from mid_user_base where id = ? limit 1";
        List<Map<String, Object>> resultList = jdbcTemplate.queryForList(querySql, userId);
        if (CollectionUtils.isEmpty(resultList)) {
            return StringUtils.EMPTY;
        }
        
        Map<String, Object> result = resultList.get(0);
        if (MapUtils.isEmpty(result)) {
            return StringUtils.EMPTY;
        }
        
        Object userName = result.get("userName");
        return nonNull(userName) ? String.valueOf(userName) : StringUtils.EMPTY;
    }

    @Override
    public String getFieldName() {
        return null;
    }

    @Override
    public List<String> getFieldsName() {
        return List.of(CREATOR_NAME, OPERATOR_NAME);
    }

    @Override
    public String getGroupName() {
        return GROUP_NAME;
    }

    @Override
    public String getDataBaseName() {
        return DATABASE_NAME;
    }
}
