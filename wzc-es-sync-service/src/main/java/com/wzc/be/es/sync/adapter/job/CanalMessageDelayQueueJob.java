package com.wzc.be.es.sync.adapter.job;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.wzc.be.es.sync.common.constants.CanalEsConstants;
import com.wzc.be.es.sync.core.handler.DriverTableHandler;
import com.wzc.be.es.sync.core.handler.OtherTableHandler;
import com.wzc.be.es.sync.core.model.delay.CanalMessageDelay;
import com.wzc.be.es.sync.core.model.sync.SyncGroup;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobContext;
import com.xxl.job.core.handler.annotation.XxlJob;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Set;

/**
 * canal消息延时队列定时任务
 *
 * <AUTHOR>
 * @date 2023年06月13日
 */
@Slf4j
@Component
public class CanalMessageDelayQueueJob {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private OtherTableHandler otherTableHandler;

    @Autowired
    private DriverTableHandler driverTableHandler;

    @Resource(name = "syncGroupMap")
    private Map<String, SyncGroup> syncGroupMap;

    @Autowired
    @Qualifier("delayTaskExecutor")
    private ThreadPoolTaskExecutor executor;

    /**
     * canal表记录处理异常延时处理队列
     */
    @XxlJob("canalTableDelayMessage")
    public ReturnT<String> canalTableDelayMessage(String param) {
        long nowTimestamp = System.currentTimeMillis();
        Set<String> delayMessages = stringRedisTemplate.opsForZSet().rangeByScore("CANAL:MESSAGE-HANDLER:DELAY-QUEUE", 0, nowTimestamp,0, 100);
        if (CollectionUtils.isEmpty(delayMessages)) {
            return ReturnT.SUCCESS;
        }
        log.debug("canalTableDelayMessage 延时队列开始处理");
        // 延迟队列数据处理
        for (String delayMessage : delayMessages) {
            try {
                CanalMessageDelay messageDelay = JSONUtil.toBean(delayMessage, CanalMessageDelay.class);
                // 获取group
                if (!syncGroupMap.containsKey(messageDelay.getGroupId())) {
                    log.warn("canalTableDelayMessage 无法匹配group:{}", messageDelay.getGroupId());
                    continue;
                }
                SyncGroup syncGroup = syncGroupMap.get(messageDelay.getGroupId());
                switch (messageDelay.getAction()) {
                    case CanalEsConstants.CANAL_ACTION_INSERT:
                        otherTableHandler.insert(messageDelay.getDataMap(), messageDelay.getTableInfo(), syncGroup,
                                messageDelay.getFromUpdate(), messageDelay.getFromInit(), messageDelay.getDelayCount());
                        break;
                    case CanalEsConstants.CANAL_ACTION_UPDATE:
                        otherTableHandler.update(messageDelay.getBeforeDataMap(), messageDelay.getDataMap(), messageDelay.getUpdateColumnSet(),
                                messageDelay.getTableInfo(), syncGroup, messageDelay.getDelayCount());
                        break;
                    case CanalEsConstants.CANAL_ACTION_DELETE:
                        driverTableHandler.delete(messageDelay.getDataMap(), messageDelay.getTableInfo(), syncGroup, messageDelay.getDelayCount());
                        break;
                    default:
                        log.debug("canalTableDelayMessage 无法匹配的action:{}", messageDelay.getAction());
                        return ReturnT.SUCCESS;
                }
            } catch (Exception ex) {
                log.error("canalTableDelayMessage 处理报错:" + delayMessage, ex);
            }
        }
        Long removeCount = stringRedisTemplate.opsForZSet().remove(CanalEsConstants.CANAL_MESSAGE_HANDLER_DELAY_QUEUE, delayMessages.toArray());
        log.debug("canalTableDelayMessage 清理延迟队列记录{}条", removeCount);
        return ReturnT.SUCCESS;
    }


    /**
     * canal表记录处理异常延时处理队列
     */
    @XxlJob("canalTableDelayMessageSync")
    public ReturnT<String> canalTableDelayMessageSync(String param) {
        param = XxlJobContext.getXxlJobContext().getJobParam();
        Integer delayCount = 100;
        if (StringUtils.isNotBlank(param)) {
            log.debug("canalTableDelayMessageSync, 参数为:{}", param);
            try {
               JSONObject paramJson = JSONObject.parseObject(param);
               if (Objects.nonNull(paramJson.getInteger("delayCount"))) {
                   delayCount = paramJson.getInteger("delayCount");
               }
           } catch (Exception ex) {
               log.error(ex.getMessage(), ex);
           }
        }
        log.debug("canalTableDelayMessageSync 开始执行，delayCount:{}", delayCount);
        long nowTimestamp = System.currentTimeMillis();
        Set<String> delayMessages = stringRedisTemplate.opsForZSet()
                .rangeByScore("CANAL:MESSAGE-HANDLER:DELAY-QUEUE", 0,  nowTimestamp, 0, delayCount);
        if (CollectionUtils.isEmpty(delayMessages)) {
            log.debug("canalTableDelayMessageSync 执行结束, delayMessages is empty");
            return ReturnT.SUCCESS;
        }
        Long removeCount = stringRedisTemplate.opsForZSet()
                .remove("CANAL:MESSAGE-HANDLER:DELAY-QUEUE", delayMessages.toArray());
        log.debug("canalTableDelayMessageSync 延时队列开始处理 清理延迟队列记录{}条", removeCount);
        List<List<String>> lists = ListUtil.split(new ArrayList<>(delayMessages), 10);
        for (List<String> list : lists) {
            executor.execute(() -> {
                for (String delayMessage : list) {
                    try {
                        CanalMessageDelay messageDelay = JSONUtil.toBean(delayMessage, CanalMessageDelay.class);
                        // 获取group
                        if (!syncGroupMap.containsKey(messageDelay.getGroupId())) {
                            log.warn("canalTableDelayMessage 无法匹配group:{}", messageDelay.getGroupId());
                            continue;
                        }
                        SyncGroup syncGroup = syncGroupMap.get(messageDelay.getGroupId());
                        switch (messageDelay.getAction()) {
                            case CanalEsConstants.CANAL_ACTION_INSERT:
                                otherTableHandler.insert(messageDelay.getDataMap(), messageDelay.getTableInfo(), syncGroup,
                                        messageDelay.getFromUpdate(), messageDelay.getFromInit(), messageDelay.getDelayCount());
                                break;
                            case CanalEsConstants.CANAL_ACTION_UPDATE:
                                otherTableHandler.update(messageDelay.getBeforeDataMap(), messageDelay.getDataMap(), messageDelay.getUpdateColumnSet(),
                                        messageDelay.getTableInfo(), syncGroup, messageDelay.getDelayCount());
                                break;
                            case CanalEsConstants.CANAL_ACTION_DELETE:
                                driverTableHandler.delete(messageDelay.getDataMap(), messageDelay.getTableInfo(), syncGroup, messageDelay.getDelayCount());
                                break;
                            default:
                                log.debug("canalTableDelayMessage 无法匹配的action:{}", messageDelay.getAction());
                        }
                    } catch (Exception ex) {
                        log.error("canalTableDelayMessage 处理报错:" + delayMessage, ex);
                    }
                }
            });
        }
        log.debug("canalTableDelayMessageSync 执行结束, delayMessages size:{}", delayMessages.size());
        return ReturnT.SUCCESS;
    }
}
