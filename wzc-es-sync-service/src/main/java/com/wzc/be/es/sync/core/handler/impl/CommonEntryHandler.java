/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.sync.core.handler.impl;

import com.google.common.collect.Sets;
import com.wzc.be.es.sync.core.handler.inter.EntryHandler;
import com.wzc.be.es.sync.core.handler.inter.TableEventHandler;
import com.wzc.be.es.sync.core.model.sync.EqualsCondition;
import com.wzc.be.es.sync.core.model.sync.SyncGroup;
import com.wzc.be.es.sync.core.model.sync.TableInfo;
import com.wzc.be.es.sync.core.util.ElasticSearchUtil;
import com.wzc.be.es.sync.core.util.SpelUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static java.util.Objects.isNull;

/**
 * <AUTHOR>
 * @date 2022年09月30日 3:49 PM
 */
@Slf4j
@Data
public class CommonEntryHandler implements EntryHandler<Map<String, Object>> {

    private TableInfo tableInfo;

    private SyncGroup syncGroup;


    @Override
    public void insert(Map<String, Object> stringObjectMap) {
        if (!syncGroup.isSyncInsertOpen()) {
            log.debug("insert not isSyncInsertOpen");
            return;
        }
        // 不满足特殊的insert 条件， 直接退出
        if (judgeCondition(stringObjectMap)) {
            log.debug("insert judgeInsertCondition not match");
            return;
        }
        // 数据标准化
        ElasticSearchUtil.jdbcDataStandardization(stringObjectMap);
        log.info("索引:【{}】 表【{}】insert event, id : {}", syncGroup.getGroupId(), tableInfo.getTableName(),
                stringObjectMap.get(tableInfo.getMainTableKey()));
        TableEventHandler eventHandler = tableInfo.getTableEventHandler();
        eventHandler.insert(stringObjectMap, tableInfo, syncGroup, false, false, 0);
    }


    @Override
    public void update(Map<String, Object> before, Map<String, Object> after, Set<String> updateColumnSet) {
        if (!syncGroup.isSyncUpdateOpen()) {
            log.debug("update not isSyncUpdateOpen");
            return;
        }
        if (CollectionUtils.isEmpty(updateColumnSet)) {
            log.warn("updateColumnSet is empty");
            return;
        }
        // 不满足特殊的insert 条件， 直接退出
        if (judgeCondition(after)) {
            log.debug("update judgeInsertCondition not match");
            return;
        }
        TableEventHandler eventHandler = tableInfo.getTableEventHandler();
        if (isDeletedBySpEl(after)) {
            // 删除数据
            eventHandler.delete(after, tableInfo, syncGroup, 0);
        } else {
            // 更新的列是否在索引中
            Set<String> columnInEs = tableInfo.getColumnToEsFieldMap().keySet();
            Set<String> tempUpdate = Sets.newHashSet(updateColumnSet);
            tempUpdate.retainAll(columnInEs);
            if (CollectionUtils.isEmpty(tempUpdate)) {
                log.debug("tempUpdate isEmpty");
                return;
            }
            // 数据标准化
            ElasticSearchUtil.jdbcDataStandardization(before);
            ElasticSearchUtil.jdbcDataStandardization(after);
            log.info("索引:【{}】 表【{}】update event, id : {}", syncGroup.getGroupId(), tableInfo.getTableName(),
                    after.get(tableInfo.getMainTableKey()));
            // 更新数据
            updateColumnSet.add(tableInfo.getMainTableKey());
            eventHandler.update(before, after, updateColumnSet, tableInfo, syncGroup, 0);
        }

    }


    /**
     * 不满足写入es 条件的binlog  即不insert 也不 update
     * @param after
     * @return
     */
    private boolean judgeCondition(Map<String, Object> after) {
        return judgeInsertCondition(after) || judgeConditionBySpEls(after);
    }

    /**
     * 根据spEl表达式，判断是否符合过滤条件，满足的将被过滤（不 新增和更新）。
     * @param after
     * @return
     */
    private boolean judgeConditionBySpEls(Map<String, Object> after) {
        if (CollectionUtils.isNotEmpty(tableInfo.getConditionBySpEl())) {
            for (String spEl : tableInfo.getConditionBySpEl()) {
                if (SpelUtil.verifySpEL(after, spEl)) {
                    log.debug("judgeConditionBySpEl spEl={}", spEl);
                    return (boolean) SpelUtil.getValue(after, spEl);
                }
            }
        }
        return false;
    }

    private boolean judgeInsertCondition(Map<String, Object> after) {
        List<EqualsCondition> conditionList = tableInfo.getInsertCondition();
        if (CollectionUtils.isNotEmpty(conditionList)) {
            for (EqualsCondition condition : conditionList) {
                String column = condition.getColumn();
                List<String> values = condition.getValues();
                // 字段不存在不insert
                if (!after.containsKey(column)) {
                    return true;
                }
                // 字段为null不insert
                if (isNull(after.get(column))) {
                    return true;
                }
                String current = String.valueOf(after.get(column));
                if (CollectionUtils.isNotEmpty(values) && !values.contains(current)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判定MySQL是否为逻辑删除，ES则需要物理删除
     * @param after
     * @return
     */
    public boolean isDeletedBySpEl(Map<String, Object> after) {
        String spEl = tableInfo.getJudgeDeleteBySpEl();
        if (StringUtils.isNotBlank(spEl) && SpelUtil.verifySpEL(after, spEl)) {
            log.debug("isDeletedBySpEl spEl={}", spEl);
            return (boolean) SpelUtil.getValue(after, spEl);
        }
        return false;
    }

    @Override
    public void delete(Map<String, Object> stringObjectMap) {
        if (!syncGroup.isSyncUpdateOpen()) {
            log.debug("delete not isSyncUpdateOpen");
            return;
        }
        TableEventHandler eventHandler = tableInfo.getTableEventHandler();
        eventHandler.delete(stringObjectMap, tableInfo, syncGroup, 0);
    }

    @Override
    public String getTableName() {
        return tableInfo.getTableName();
    }

    @Override
    public String getDatabaseName() {
        return syncGroup.getDatabase();
    }

    @Override
    public String getGroupName() {
        return syncGroup.getGroupId();
    }
}
