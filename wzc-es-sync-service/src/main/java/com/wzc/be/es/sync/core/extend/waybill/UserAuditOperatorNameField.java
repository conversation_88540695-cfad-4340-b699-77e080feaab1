package com.wzc.be.es.sync.core.extend.waybill;

import com.wzc.be.es.sync.core.model.sync.EsExtendField;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * <p>
 *     审核记录操作用户名称字段
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/25
 */
@Component
public class UserAuditOperatorNameField extends EsExtendField<String, Map<String, Object>> {

    private static final String FIELD_NAME = "operatorName";
    private static final String GROUP_NAME = "userAuditIndex";
    private static final String DATABASE_NAME = "zt_user";

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public String getData(Map<String, Object> esSourceMap) {
        Object operatorIdObj = esSourceMap.get("operatorId");
        if (isNull(operatorIdObj)) {
            return StringUtils.EMPTY;
        }
        if (!StringUtils.isNumeric(String.valueOf(operatorIdObj))) {
            return StringUtils.EMPTY;
        }
        // 获取司机ID
        Long operatorId = Long.valueOf(String.valueOf(operatorIdObj));
        String querySql = "select user_name as operatorName from mid_user_base where id = ? limit 1";
        List<Map<String, Object>> resultList = jdbcTemplate.queryForList(querySql, operatorId);
        if (CollectionUtils.isEmpty(resultList)) {
            return StringUtils.EMPTY;
        }
        Map<String, Object> resultMap = resultList.get(0);
        // todo: 加缓存
        if (MapUtils.isEmpty(resultMap)) {
            return StringUtils.EMPTY;
        }
        Object operatorName = resultMap.get(FIELD_NAME);
        if (nonNull(operatorName)) {
            return String.valueOf(operatorName);
        }
        return StringUtils.EMPTY;
    }

    @Override
    public String getFieldName() {
        return FIELD_NAME;
    }

    @Override
    public String getGroupName() {
        return GROUP_NAME;
    }

    @Override
    public String getDataBaseName() {
        return DATABASE_NAME;
    }
}
