package com.wzc.be.es.sync.common.constants;

import com.wzc.common.string.StringPool;

/**
 * <AUTHOR>
 */
public class KafkaConstants {

    public static final String WZC_OMS = "wzc_oms";

    public static final String WZC_BMS = "wzc_bms";

    public static final String ZT_USER = "zt_user";

    public static final String ZT_OMS = "zt_oms";

    public static final String WZC_TMS = "wzc_tms";

    public static final String ZT_TMS = "zt_tms";

    public static final String KAFKA_SCRIPT = "kafka.topic.canal-es-message-script";

    public static final String KAFKA_SCRIPT_SPRING = StringPool.DOLLAR_LEFT_BRACE + KAFKA_SCRIPT + StringPool.RIGHT_BRACE;

    public static final String KAFKA_BASE = "kafka.topic.canal-es-message-base";

    public static final String KAFKA_BASE_SPRING = StringPool.DOLLAR_LEFT_BRACE + KAFKA_BASE + StringPool.RIGHT_BRACE;


    public static final String KAFKA_OMS = "kafka.topic.canal-es-message-oms";

    public static final String KAFKA_OMS_SPRING = StringPool.DOLLAR_LEFT_BRACE + KAFKA_OMS + StringPool.RIGHT_BRACE;

    public static final String KAFKA_BMS = "kafka.topic.canal-es-message-bms";

    public static final String KAFKA_BMS_SPRING = StringPool.DOLLAR_LEFT_BRACE + KAFKA_BMS + StringPool.RIGHT_BRACE;


    public static final String KAFKA_TMS = "kafka.topic.canal-es-message-tms";

    public static final String KAFKA_TMS_SPRING = StringPool.DOLLAR_LEFT_BRACE + KAFKA_TMS + StringPool.RIGHT_BRACE;


    public static final String KAFKA_USER = "kafka.topic.canal-es-message-user";

    public static final String KAFKA_USER_SPRING = StringPool.DOLLAR_LEFT_BRACE + KAFKA_USER + StringPool.RIGHT_BRACE;


    public static final String KAFKA_GROUP_SPRING = StringPool.DOLLAR_LEFT_BRACE + "kafka.group.canal-es-message" + StringPool.RIGHT_BRACE;

    public static final String KAFKA_DEFAULT_SPRING = StringPool.DOLLAR_LEFT_BRACE + "kafka.topic.canal-es-message" + StringPool.RIGHT_BRACE;

    public static final String KAFKA_PROPERTIES = "value.deserializer=com.alibaba.otter.canal.client.kafka.MessageDeserializer";

}
