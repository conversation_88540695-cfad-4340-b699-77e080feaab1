package com.wzc.be.es.sync.core.extend.warn;

import com.google.common.collect.Maps;
import com.wzc.be.es.sync.core.model.sync.EsExtendField;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * <p>
 *     异常报警公司id
 * </p>
 *
 * <AUTHOR>
 * @since 2023/12/25
 */
@Component
public class WarnCompanyIdCarrierNameField extends EsExtendField<Map<String,Object>, Map<String, Object>> {

    /**
     * 公司id
     */
    private static final String companyIdField = "companyId";

    /**
     * 公司名称
     */
    private static final String companyNameField = "companyName";

    /**
     * 承运商
     */
    private static final String carrierNameField = "carrierName";

    /**
     * 派车单号
     */
    private static final String dispatchCarNoField = "dispatchCarNo";


    private static final String GROUP_NAME = "cscWarnExceptionIndex";
    private static final String DATABASE_NAME = "zt_oms_base";

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public Map<String,Object> getData(Map<String, Object> esSourceMap) {
        Object subWaybillNoObj = esSourceMap.get("subWaybillNo");
        if (isNull(subWaybillNoObj)) {
            return Maps.newHashMap();
        }
        // 获取公司id
        String subWaybillNo = String.valueOf(subWaybillNoObj);
        String querySql = "select company_id as companyId,carrier_company_name as carrierName,company_name as companyName,dispatch_car_no as dispatchCarNo from oms_sub_waybill where sub_no = ? limit 1";
        List<Map<String, Object>> resultList = jdbcTemplate.queryForList(querySql, subWaybillNo);
        if (CollectionUtils.isEmpty(resultList)) {
            return Maps.newHashMap();
        }
        Map<String, Object> resultMap = resultList.get(0);
        if (MapUtils.isEmpty(resultMap)) {
            return Maps.newHashMap();
        }
        return resultMap;
    }

    @Override
    public String getFieldName() {
        return null;
    }

    @Override
    public List<String> getFieldsName() {
        return List.of(carrierNameField,companyIdField,companyNameField,dispatchCarNoField);
    }

    @Override
    public String getGroupName() {
        return GROUP_NAME;
    }

    @Override
    public String getDataBaseName() {
        return DATABASE_NAME;
    }
}
