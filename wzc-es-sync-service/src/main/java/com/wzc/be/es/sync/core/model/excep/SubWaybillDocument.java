package com.wzc.be.es.sync.core.model.excep;

import cn.easyes.annotation.IndexField;
import cn.easyes.annotation.IndexId;
import cn.easyes.annotation.IndexName;
import cn.easyes.annotation.rely.FieldStrategy;
import cn.easyes.annotation.rely.FieldType;
import lombok.Data;

/**
 * @description:
 * @Author: keeper Yu yxbwzx
 * @Date: 2023/9/5 21:03
 * @since 1.0.0
 */
@Data
@IndexName(value = "sub_waybill_index01")
//@IndexName(value = "waybill_test_index01")
public class SubWaybillDocument {

    @IndexId
    private String id;

    private String subWaybillNo;

    /**
     * 在途异常发生时间
     */
    @IndexField(strategy = FieldStrategy.NOT_NULL,fieldType = FieldType.DATE,dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String transExcepTime;

    /**
     * 签收异常发生时间
     */
    @IndexField(strategy = FieldStrategy.NOT_NULL,fieldType = FieldType.DATE,dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String signExcepTime;

    /**
     * 监管上报异常发生时间
     */
    @IndexField(strategy = FieldStrategy.NOT_NULL,fieldType = FieldType.DATE,dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String superviseExcepTime;

    /**
     * 司机异常上报发生时间
     */
    @IndexField(strategy = FieldStrategy.NOT_NULL,fieldType = FieldType.DATE,dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String reportExcepTime;

    /**
     * 是否存在在途异常 1存在 0 不存在
     */
    @IndexField(strategy = FieldStrategy.NOT_NULL)
    private Integer excepTrans;

    /**
     * 是否存在签收异常 1存在 0 不存在
     */
    @IndexField(strategy = FieldStrategy.NOT_NULL)
    private Integer excepSign;

    /**
     * 是否存在监管上报异常 1存在 0 不存在
     */
    @IndexField(strategy = FieldStrategy.NOT_NULL)
    private Integer excepSupervise;

    /**
     * 是否存在司机上报异常 1存在 0 不存在
     */
    @IndexField(strategy = FieldStrategy.NOT_NULL)
    private Integer excepReport;

    /**
     * 在途异常处理情况 0或null 表示有全部处理  1表示有待处理异常
     */
    @IndexField(strategy = FieldStrategy.NOT_NULL)
    private Integer transProcess;

    /**
     * 签收异常处理情况 0或null 表示有全部处理  1表示有待处理异常
     */
    @IndexField(strategy = FieldStrategy.NOT_NULL)
    private Integer signProcess;

    /**
     * 监管上报异常处理情况 0或null 表示有全部处理  1表示有待处理异常
     */
    @IndexField(strategy = FieldStrategy.NOT_NULL)
    private Integer superviseProcess;

    /**
     * 司机上报异常处理情况 0或null 表示有全部处理  1表示有待处理异常
     */
    @IndexField(strategy = FieldStrategy.NOT_NULL)
    private Integer reportProcess;



}
