package com.wzc.be.es.sync.core.extend;

import com.wzc.be.es.sync.core.model.sync.EsExtendField;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * <p>
 *     子运单订单类型字段
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/25
 */
@Component
public class SubWaybillOrderTypeField extends EsExtendField<Integer, Map<String, Object>> {

    private static final String FIELD_NAME = "orderType";
    private static final String GROUP_NAME = "subWaybillIndex";

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public Integer getData(Map<String, Object> esSourceMap) {
        Object orderNoObj = esSourceMap.get("orderNo");
        if (isNull(orderNoObj)) {
            return null;
        }
        String orderNo = String.valueOf(orderNoObj);
        String querySql = "SELECT order_type as orderType from oms_order_aggregation where order_no = ? limit 1";
        List<Map<String, Object>> resultList = jdbcTemplate.queryForList(querySql, orderNo);
        if (CollectionUtils.isEmpty(resultList)) {
            return null;
        }
        Map<String, Object> resultMap = resultList.get(0);
        if (MapUtils.isEmpty(resultMap)) {
            return null;
        }
        Object orderType = resultMap.get(FIELD_NAME);
        // todo: 加缓存
        if (nonNull(orderType)) {
            return Integer.valueOf(String.valueOf(orderType));
        }
        return null;
    }

    @Override
    public String getFieldName() {
        return FIELD_NAME;
    }

    @Override
    public String getGroupName() {
        return GROUP_NAME;
    }

    @Override
    public String getDataBaseName() {
        return null;
    }
}
