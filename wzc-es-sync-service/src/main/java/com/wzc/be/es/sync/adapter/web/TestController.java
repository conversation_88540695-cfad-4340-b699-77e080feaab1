package com.wzc.be.es.sync.adapter.web;

import com.wzc.be.es.sync.adapter.job.DbDataToEsJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
public class TestController {

    @Autowired
    private DbDataToEsJob dbDataToEsJob;


    @PostMapping("sync")
    public void sync(@RequestBody String param){
        dbDataToEsJob.esDataAssignSync(param);
    }

}
