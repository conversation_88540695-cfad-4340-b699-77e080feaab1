package com.wzc.be.es.sync.core.extend;

import com.wzc.be.es.sync.core.model.sync.EsExtendField;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * <p>
 *     运输计划线路id
 * </p>
 *
 * <AUTHOR>
 * @since 2023/9/9
 */
 @Component
public class SubWaybillOrderRuteIdField extends EsExtendField<Long, Map<String, Object>> {

    private static final String FIELD_NAME = "trackRouteId";
    private static final String GROUP_NAME = "wzcWaybillIndexWaybillData";
    private static final String DATABASE_NAME = "wzc_oms_base_3.0";

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public Long getData(Map<String, Object> esSourceMap) {
        Object chooseIdObj = esSourceMap.get("orderRuleId");
        if (isNull(chooseIdObj)) {
            return null;
        }
        if (!StringUtils.isNumeric(String.valueOf(chooseIdObj))) {
            return null;
        }
        // 获取规则ID
        Long chooseId = Long.valueOf(String.valueOf(chooseIdObj));
        String querySql = "select track_route_id as trackRouteId from oms_rule_snapshot where id = ? limit 1";
        List<Map<String, Object>> resultList = jdbcTemplate.queryForList(querySql, chooseId);
        if (CollectionUtils.isEmpty(resultList)) {
            return null;
        }
        Map<String, Object> resultMap = resultList.get(0);
        if (MapUtils.isEmpty(resultMap)) {
            return null;
        }
        Object trackRouteId = resultMap.get(FIELD_NAME);
        if (nonNull(trackRouteId)) {
            return Long.valueOf(String.valueOf(trackRouteId));
        }
        return null;
    }

    @Override
    public String getFieldName() {
        return FIELD_NAME;
    }

    @Override
    public String getGroupName() {
        return GROUP_NAME;
    }

    @Override
    public String getDataBaseName() {
        return DATABASE_NAME;
    }
}
