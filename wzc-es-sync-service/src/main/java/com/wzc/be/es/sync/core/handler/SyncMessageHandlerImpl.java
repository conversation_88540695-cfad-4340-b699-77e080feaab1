package com.wzc.be.es.sync.core.handler;


import com.alibaba.otter.canal.protocol.CanalEntry;
import com.alibaba.otter.canal.protocol.Message;
import com.wzc.be.es.sync.core.converter.Converter;
import com.wzc.be.es.sync.core.handler.impl.AbstractMessageHandler;
import com.wzc.be.es.sync.core.handler.inter.EntryHandler;
import com.wzc.be.es.sync.core.handler.inter.RowDataHandler;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/09/2710:52
 */
public class SyncMessageHandlerImpl extends AbstractMessageHandler {


    public SyncMessageHandlerImpl(List<? extends EntryHandler> entryHandlers,
                                  RowDataHandler<CanalEntry.RowData> rowDataHandler,
                                  Converter<String, String> tableNameConverter) {
        super(entryHand<PERSON>, rowDataHandler, tableNameConverter);
    }

    @Override
    public void handleMessage(Message message) {
        super.handleMessage(message);
    }


}
