package com.wzc.be.es.sync.core.factory;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.wzc.be.es.sync.core.handler.inter.EntryHandler;
import com.wzc.be.es.sync.core.enums.TableNameEnum;
import com.wzc.be.es.sync.core.util.EntryUtil;
import com.wzc.be.es.sync.core.util.FieldUtil;
import com.wzc.be.es.sync.core.util.GenericUtil;
import com.wzc.be.es.sync.core.util.HandlerUtil;
import org.apache.commons.lang3.StringUtils;

import java.sql.Types;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/09/2916:16
 */
public class EntryColumnModelFactory extends AbstractModelFactory<List<CanalEntry.Column>> {


    @Override
    public <R> R newInstance(EntryHandler entryHandler, List<CanalEntry.Column> columns) throws Exception {
        String canalTableName = HandlerUtil.getCanalTableName(entryHandler);
        if (TableNameEnum.ALL.name().toLowerCase().equals(canalTableName)) {
            Map<String, Object> map = new HashMap<>();
            for (CanalEntry.Column subColumn : columns) {
                map.put(subColumn.getName(), getColumnValue(subColumn));
            }
            return (R) map;
        }
        Class<R> tableClass = GenericUtil.getTableClass(entryHandler);
        if (Map.class.equals(tableClass)) {
            Map<String, Object> map = new HashMap<>();
            for (CanalEntry.Column subColumn : columns) {
                map.put(subColumn.getName(), getColumnValue(subColumn));
            }
            return (R) map;
        }
        if (tableClass != null) {
            return newInstance(tableClass, columns);
        }
        return null;
    }


    @Override
    <R> R newInstance(Class<R> c, List<CanalEntry.Column> columns) throws Exception {
        R object = c.newInstance();
        Map<String, String> columnNames = EntryUtil.getFieldName(object.getClass());
        for (CanalEntry.Column column : columns) {
            String fieldName = columnNames.get(column.getName());
            if (StringUtils.isNotEmpty(fieldName)) {
                FieldUtil.setFieldValue(object, fieldName, column.getValue());
            }
        }
        return object;
    }

    /**
     * 获取canal column的字段值
     */
    private Object getColumnValue(CanalEntry.Column column) {
        if (column.getIsNull()) {
            return null;
        }
        if (Types.TIMESTAMP == column.getSqlType() ) {
            return DateUtil.parseDateTime(column.getValue()).toLocalDateTime();
        }
        if (Types.DATE == column.getSqlType()) {
            return DateUtil.parseDate(column.getValue()).toLocalDateTime().toLocalDate();
        }
        if (Types.TIME == column.getSqlType()) {
            return DateUtil.parseTime(column.getValue()).toLocalDateTime().toLocalTime();
        }
        if (Types.BIGINT == column.getSqlType()
                || Types.INTEGER == column.getSqlType()
                || Types.SMALLINT == column.getSqlType()
                || Types.TINYINT == column.getSqlType()) {
            return Long.valueOf(column.getValue());
        }
        if (Types.DOUBLE == column.getSqlType()) {
            return Double.valueOf(column.getValue());
        }
        if (Types.FLOAT == column.getSqlType()) {
            return Float.valueOf(column.getValue());
        }
        if (Types.DECIMAL == column.getSqlType()) {
            return NumberUtil.toBigDecimal(column.getValue());
        }
        return column.getValue();
    }


}
