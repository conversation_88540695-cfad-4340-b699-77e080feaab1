package com.wzc.be.es.sync.core.extend;

import com.wzc.be.es.sync.core.model.sync.EsExtendField;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * <p>
 *     运单司机名称字段
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/25
 */
@Component
public class SubWaybillDriverNameField extends EsExtendField<String, Map<String, Object>> {

    private static final String FIELD_NAME = "driverName";
    private static final String GROUP_NAME = "subWaybillIndexWaybillData";
    private static final String DATABASE_NAME = "zt_user";

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public String getData(Map<String, Object> esSourceMap) {
        Object driverIdObj = esSourceMap.get("driverId");
        if (isNull(driverIdObj)) {
            return StringUtils.EMPTY;
        }
        if (!StringUtils.isNumeric(String.valueOf(driverIdObj))) {
            return StringUtils.EMPTY;
        }
        // 获取司机ID
        Long driverId = Long.valueOf(String.valueOf(driverIdObj));
        String querySql = "select driver_name as driverName from mid_driver where id = ? limit 1";
        List<Map<String, Object>> resultList = jdbcTemplate.queryForList(querySql, driverId);
        if (CollectionUtils.isEmpty(resultList)) {
            return StringUtils.EMPTY;
        }
        Map<String, Object> resultMap = resultList.get(0);
        // todo: 加缓存
        if (MapUtils.isEmpty(resultMap)) {
            return StringUtils.EMPTY;
        }
        Object driverName = resultMap.get(FIELD_NAME);
        if (nonNull(driverName)) {
            return String.valueOf(driverName);
        }
        return StringUtils.EMPTY;
    }

    @Override
    public String getFieldName() {
        return FIELD_NAME;
    }

    @Override
    public String getGroupName() {
        return GROUP_NAME;
    }

    @Override
    public String getDataBaseName() {
        return DATABASE_NAME;
    }
}
