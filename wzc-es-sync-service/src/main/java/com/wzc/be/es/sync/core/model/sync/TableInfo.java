/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.sync.core.model.sync;

import cn.hutool.core.annotation.PropIgnore;
import com.wzc.be.es.sync.core.handler.inter.TableEventHandler;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022年10月08日 11:50 AM
 */
@Data
public class TableInfo implements Serializable {

    /**
     * 表名
     */
    private String tableName;

    /**
     * 针对分表场景，增加转换器
     */
    private TableNameConverterModel tableNameConverter;

    /**
     * 是否是join语句的驱动表
     */
    private boolean driverTable;

    /**
     * 当前表的主键列名
     */
    private String mainTableKey;


    /**
     * 当前表的删除标记字段
     */
    private String tableDeleteColumn;

    /**
     * 当前表对应的同步es handler
     */
    @PropIgnore
    private transient TableEventHandler tableEventHandler;

    /**
     * 当前表需要同步到es 的字段列名
     */
    private List<String> columnInEs;

    /**
     * 当前表与joinSql结合的按id的查询条件
     */
    private String joinQueryCondition;

    /**
     * 表里面每一列的列名跟es索引名的映射关系
     */
    private Map<String, String> columnToEsFieldMap;

    /**
     * 当前从表与上级表的关联条件字段
     */
    private TableRelationInfo tableRelationInfo;

    @Deprecated
    private boolean isInnerJoin;

    /**
     * 是否开启同步任务
     */
    private boolean autoSyncOpen;

    /**
     * 当前表字段满足条件才会进行后续的insert操作
     */
    @Deprecated
    private List<EqualsCondition> insertCondition;

    /**
     * 是否一对多表
     */
    private Boolean oneToMany = false;

    /**
     * 更新数据脚本
     */
    private String scriptUpdate = "";

    /**
     * 更新数据脚本
     */
    private String scriptDelete = "";

    /**
     * 根据spel表达式，判断是否为删除SQL（逻辑删除场景）
     */
    private String judgeDeleteBySpEl;

    /**
     * 根据spel表达式，判断需要跳过新增、更新操作的数据项
     */
    private List<String> conditionBySpEl;

    /**
     * 过滤某些es字段。
     */
    private List<String> fieldFilter;

    /**
     * 需要解密的字段
     */
    private String needDecryptField;

    /**
     * 当前表是否需要校验mysql和es数据是否一致
     */
    private Boolean checkMysqlEsEquals = true;

    /**
     * 无需对比，直接根据条件删除数据
     */
    private Boolean deleteByQuery = false;

}
