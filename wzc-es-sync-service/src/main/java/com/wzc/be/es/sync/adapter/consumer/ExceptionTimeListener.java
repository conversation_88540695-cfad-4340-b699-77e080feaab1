package com.wzc.be.es.sync.adapter.consumer;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.alibaba.fastjson.JSONObject;
import com.wzc.be.es.sync.core.model.excep.ExcepMessage;
import com.wzc.be.es.sync.core.repository.ExceptionTimeRepository;
import com.wzc.common.cache.redis.CustomRedis;
import com.wzc.common.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @Author: keeper Yu yxbwzx
 * @Date: 2023/9/5 20:04
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ExceptionTimeListener {


    private final ExceptionTimeRepository exceptionTImeRepository;

    @KafkaListener(topics = "${kafka.topic.alarm-exception-time}", groupId = "${kafka.group.alarm-exception-time}")
    public void syncExceptionTimeToES(String message) {
        log.info("接收异常发生时间：{}", message);
        ExcepMessage excepMessage = JSONObject.parseObject(message, ExcepMessage.class);
        try {
            if (!CustomRedis.setNx(excepMessage.getSubWaybillNo(), excepMessage.getSubWaybillNo(), 1)) {
                throw new RuntimeException("syncExceptionTimeToES==添加同步延迟==》" + excepMessage.getSubWaybillNo());
            }
            exceptionTImeRepository.updateExceptionTime(excepMessage);
        } catch (Exception e) {
            log.warn("异常更新异常-->{}", ExceptionUtil.stacktraceToString(e));
            if (e instanceof ServiceException) {
                return;
            }
            throw e;
        }
    }
}
