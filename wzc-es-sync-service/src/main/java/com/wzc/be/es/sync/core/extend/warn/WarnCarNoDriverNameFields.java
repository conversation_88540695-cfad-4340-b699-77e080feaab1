package com.wzc.be.es.sync.core.extend.warn;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.google.common.collect.Maps;
import com.wzc.be.es.sync.core.model.sync.EsExtendField;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static java.util.Objects.isNull;

/**
 * <p>
 *     异常报警车牌号
 * </p>
 *
 * <AUTHOR>
 * @since 2023/12/25
 */
 @Component
public class WarnCarNoDriverNameFields extends EsExtendField<Map<String,Object>, Map<String, Object>> {

    /**
     * 车牌号
     */
    private static final String carNoField = "carNo";

    /**
     * 司机名称
     */
    private static final String driverNameField = "driverName";

    private static final String GROUP_NAME = "cscWarnExceptionIndex";
    private static final String DATABASE_NAME = "zt_tms";

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public Map<String,Object> getData(Map<String, Object> esSourceMap) {
        Object dispatchCarNo = esSourceMap.get("dispatchCarNo");
        if (isNull(dispatchCarNo)) {
            return Maps.newHashMap();
        }
        // 获取车牌
        String dispatchCarNoStr = String.valueOf(dispatchCarNo);
        //派车单号为0 根据子运单号查询
        if (StrUtil.equals(dispatchCarNoStr,"0")){
            Object subWaybillNoObj = esSourceMap.get("subWaybillNo");
            if (isNull(subWaybillNoObj)){
                return Maps.newHashMap();
            }
            String subWaybillNo = String.valueOf(subWaybillNoObj);
            String dispatchCarNoQuerySql = "select dispatch_car_no as dispatchCarNo from oms_sub_waybill where sub_no = ? limit 1";
            List<Map<String, Object>> dispatchCarNoResultList = jdbcTemplate.queryForList(dispatchCarNoQuerySql, subWaybillNo);
            if (CollectionUtils.isEmpty(dispatchCarNoResultList)) {
                return Maps.newHashMap();
            }
            Map<String, Object> dispatchCarNoResultMap = dispatchCarNoResultList.get(0);
            if (MapUtils.isEmpty(dispatchCarNoResultMap)) {
                return Maps.newHashMap();
            }
            Object dispatchCarNoObj = dispatchCarNoResultMap.get("dispatchCarNo");
            dispatchCarNoStr = String.valueOf(dispatchCarNoObj);
        }

        String querySql = "select car_no as carNo,driver_name as driverName from tms_dispatch_car_base where dispatch_car_no = ? limit 1";
        List<Map<String, Object>> resultList = jdbcTemplate.queryForList(querySql, dispatchCarNoStr);
        if (CollectionUtils.isEmpty(resultList)) {
            return Maps.newHashMap();
        }
        Map<String, Object> resultMap = resultList.get(0);
        if (MapUtils.isEmpty(resultMap)) {
            return Maps.newHashMap();
        }
        return resultMap;

    }

    @Override
    public String getFieldName() {
        return null;
    }

    @Override
    public List<String> getFieldsName() {
        return List.of(carNoField,driverNameField);
    }

    @Override
    public String getGroupName() {
        return GROUP_NAME;
    }

    @Override
    public String getDataBaseName() {
        return DATABASE_NAME;
    }
}
