package com.wzc.be.es.sync.core.extend.subwaybill;

import com.wzc.be.es.sync.core.model.sync.EsExtendField;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * <p>
 *     子运单司机名称本地字段
 * </p>
 *
 * <AUTHOR> Assistant
 * @since 2025/02/18
 */
@Component
public class SubWaybillDriverNameLocalField extends EsExtendField<String, Map<String, Object>> {

    private static final String FIELD_NAME = "driverNameLocal";
    private static final String GROUP_NAME = "subWaybillIndexDriverNameLocal";
    private static final String DATABASE_NAME = "zt_user";

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public String getData(Map<String, Object> esSourceMap) {
        Object driverIdObj = esSourceMap.get("driverId");
        if (isNull(driverIdObj)) {
            return StringUtils.EMPTY;
        }
        if (!StringUtils.isNumeric(String.valueOf(driverIdObj))) {
            return StringUtils.EMPTY;
        }
        
        // 获取司机ID
        Long driverId = Long.valueOf(String.valueOf(driverIdObj));
        String querySql = "select driver_name as driverNameLocal from mid_driver where id = ? limit 1";
        List<Map<String, Object>> resultList = jdbcTemplate.queryForList(querySql, driverId);
        if (CollectionUtils.isEmpty(resultList)) {
            return StringUtils.EMPTY;
        }
        Map<String, Object> resultMap = resultList.get(0);
        if (MapUtils.isEmpty(resultMap)) {
            return StringUtils.EMPTY;
        }
        Object driverNameLocal = resultMap.get(FIELD_NAME);
        if (nonNull(driverNameLocal)) {
            return String.valueOf(driverNameLocal);
        }
        return StringUtils.EMPTY;
    }

    @Override
    public String getFieldName() {
        return FIELD_NAME;
    }

    @Override
    public String getGroupName() {
        return GROUP_NAME;
    }

    @Override
    public String getDataBaseName() {
        return DATABASE_NAME;
    }
}
