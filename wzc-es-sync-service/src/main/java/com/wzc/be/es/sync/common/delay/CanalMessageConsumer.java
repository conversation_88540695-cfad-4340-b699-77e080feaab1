package com.wzc.be.es.sync.common.delay;


import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.wzc.be.es.sync.common.constants.CanalEsConstants;
import com.wzc.be.es.sync.core.handler.DriverTableHandler;
import com.wzc.be.es.sync.core.handler.OtherTableHandler;
import com.wzc.be.es.sync.core.model.delay.CanalMessageDelay;
import com.wzc.be.es.sync.core.model.sync.SyncGroup;
import com.wzc.common.util.JdkSerializeUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
public class CanalMessageConsumer {

    private Map<String, SyncGroup> syncGroupMap;

    private OtherTableHandler otherTableHandler;

    private DriverTableHandler driverTableHandler;

    public void canalTableDelayMessage(String data) {
        try {
            CanalMessageDelay messageDelay;
            if(JSONUtil.isTypeJSON(data)) {
                messageDelay = JSONUtil.toBean(data, CanalMessageDelay.class);
            }else {
                messageDelay = JdkSerializeUtils.deserializeStringToObject(data);
            }
            if(ObjUtil.isNull(messageDelay)){
                return;
            }
            // 获取group
            SyncGroup syncGroup = syncGroupMap.get(messageDelay.getGroupId());
            switch (messageDelay.getAction()) {
                case CanalEsConstants.CANAL_ACTION_INSERT:
                    otherTableHandler.insert(messageDelay.getDataMap(), messageDelay.getTableInfo(), syncGroup,
                            messageDelay.getFromUpdate(), messageDelay.getFromInit(), messageDelay.getDelayCount());
                    break;
                case CanalEsConstants.CANAL_ACTION_UPDATE:
                    otherTableHandler.update(messageDelay.getBeforeDataMap(), messageDelay.getDataMap(), messageDelay.getUpdateColumnSet(),
                            messageDelay.getTableInfo(), syncGroup, messageDelay.getDelayCount());
                    break;
                case CanalEsConstants.CANAL_ACTION_DELETE:
                    driverTableHandler.delete(messageDelay.getDataMap(), messageDelay.getTableInfo(), syncGroup, messageDelay.getDelayCount());
                    break;
                default:
                    log.debug("canalTableDelayMessage 无法匹配的action:{}", messageDelay.getAction());
            }
        } catch (Exception ex) {
            log.error("canalTableDelayMessage 处理报错:" + data, ex);
        }
    }
}
