package com.wzc.be.es.sync.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 数据同步相关配置
 *
 * <AUTHOR>
 * @date 2023年03月31日
 */
@Data
@Component
public class SyncProperties {

    @Value("${sync.data.open:true}")
    private Boolean openDataSync;

    @Value("${data.sync.offset.days:180}")
    private Integer dataSyncOffsetDays;

    /**
     * 延时队列重试次数
     */
    @Value("${sync.delay.max-count:5}")
    private Integer syncMaxDelayCount;

    /**
     * 延时队列执行时间值保存时长
     */
    @Value("${sync.delay.value.executeTime.key.expired:10}")
    private Integer syncDelayValueExecuteTimeKeyExpired;

    /**
     * 是否开启从表并发更新的开关
     */
    @Value("${sync.delay.value.key.is.add:false}")
    private Boolean isAddValueKey;

    /**
     * 同步数据更新时加锁等待时长
     */
    @Value("${sync.lock.wait.time:3}")
    private Long syncLockWaitTime;

    /**
     * 同步数据更新时释放锁时长
     */
    @Value("${sync.lock.release.time:5}")
    private Long syncLockReleaseTime;
    /**
     * 延迟队列延迟时间 单位秒
     */
    @Value("${sync.delay.time:3}")
    public Integer syncDelayTime;

    /**
     * 停机标识
     */
    private volatile static boolean shutdown = false;

    public static Boolean getShutdown() {
        return shutdown;
    }

    public static void setShutdown(Boolean shutdown) {
        SyncProperties.shutdown = shutdown;
    }

}
