/*
 * Copyright (C) 2023 Baidu, Inc. All Rights Reserved.
 */
package com.wzc.be.es.sync.core.converter;

import com.wzc.be.es.sync.core.model.sync.TableInfo;
import com.wzc.be.es.sync.core.model.sync.TableNameConverterModel;
import com.wzc.be.es.sync.core.util.RegexUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @ClassName TableNameConverterUtil
 **/
@Component
public class TableNameConverter implements Converter<String, String> {
    private Map<String, List<TableNameConverterModel>> tableNameConverterMap = new HashMap<>();


    public String convertTableName(String databaseName, String tableName){
        List<TableNameConverterModel> tableNameConverters = tableNameConverterMap.get(databaseName);
        if (CollectionUtils.isNotEmpty(tableNameConverters)) {
            for (TableNameConverterModel tableNameConverter : tableNameConverters) {
                // 如果匹配则转换表名
                if (RegexUtils.isMatch(tableNameConverter.getCondition(), tableName)) {
                    return tableNameConverter.getTableName();
                }
            }
        }
        return tableName;
    }

    public String convertHandlerKey(String handlerKey){
        String[] splits = handlerKey.split(":");
        if (Objects.nonNull(splits) && splits.length > 1){
            return splits[0] + ":" + convertTableName(splits[0], splits[1]);
        }
        return handlerKey;
    }

    public void add(String databaseName, TableNameConverterModel tableNameConverter){
        if (StringUtils.isNotBlank(databaseName) && Objects.nonNull(tableNameConverter)) {
            tableNameConverterMap.computeIfAbsent(databaseName, key -> new ArrayList<>()).add(tableNameConverter);
        }
    }

    public void add(String databaseName, List<TableInfo> tables) {
        if (CollectionUtils.isNotEmpty(tables)) {
            for (TableInfo tableInfo : tables) {
                add(databaseName, tableInfo.getTableNameConverter());
            }
        }
    }

    /**
     * HandlerKey转换
     * @param source
     * @return
     */
    @Override
    public String convert(String source) {
        return convertHandlerKey(source);
    }
}
