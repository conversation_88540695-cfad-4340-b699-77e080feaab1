/*
 * Copyright (C) 2023 Baidu, Inc. All Rights Reserved.
 */
package com.wzc.be.es.sync.core.extend.warn;

import com.wzc.be.es.sync.core.model.sync.EsExtendField;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static java.util.Objects.nonNull;

/**
 * 发货地址字段
 * <AUTHOR>
 * @ClassName OrderItemsField
 **/
@Component
public class WarnSubWaybillSenderAddressField extends EsExtendField<List<Map<String, Object>>, Map<String, Object>> {

    private static final String FIELD_NAME = "sender";
    private static final String GROUP_NAME = "cscWarnExceptionIndex";
    private static final String DATABASE_NAME = "zt_oms_base";

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public List<Map<String, Object>> getData(Map<String, Object> esSourceMap) {

        // 获取订单号
        String subWaybillNo = nonNull(esSourceMap.get("subWaybillNo")) ? (String) esSourceMap.get("subWaybillNo") : null;
        if (Objects.nonNull(subWaybillNo)) {
            // 查询地址信息
            String querySql = "SELECT address,id,address_name as addressName,full_address as fullAddress FROM oms_address WHERE oms_no = ? AND type = 1 AND deleted = 0";

            List<Map<String, Object>> resultList = jdbcTemplate.queryForList(querySql, subWaybillNo);

            return resultList;
        }

        return null;
    }

    @Override
    public String getFieldName() {
        return FIELD_NAME;
    }

    @Override
    public String getGroupName() {
        return GROUP_NAME;
    }

    @Override
    public String getDataBaseName() {
        return DATABASE_NAME;
    }
}
