package com.wzc.be.es.sync.core.extend.subwaybill;

import com.wzc.be.es.sync.core.model.sync.EsExtendField;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

/**
 * <p>
 *     运单司机名称字段
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/25
 */
@Component
public class SubWaybillInspectorField extends EsExtendField<Long, Map<String, Object>> {

    private static final String FIELD_NAME = "inspectorId";
    private static final String ROLE_ID = "order_rule_id";
    private static final String GROUP_NAME = "subWaybillIndexWaybillData";

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public Long getData(Map<String, Object> esSourceMap) {
        Object subWaybillNoObj = esSourceMap.get("subWaybillNo");
        if (isNull(subWaybillNoObj)) {
            return null;
        }
        if (!StringUtils.isNumeric(String.valueOf(subWaybillNoObj))) {
            return null;
        }
        // 获取规则ID
        String subWaybillNo = String.valueOf(subWaybillNoObj);
        Long ruleId = getRuleId(subWaybillNo);
        if (Objects.isNull(ruleId)) {
            return null;
        }
        String querySql = "select check_id as inspectorId from oms_check_relation where choose_id = ? limit 1";
        List<Map<String, Object>> resultList = jdbcTemplate.queryForList(querySql, ruleId);
        if (CollectionUtils.isEmpty(resultList)) {
            return null;
        }
        Map<String, Object> resultMap = resultList.get(0);
        if (MapUtils.isEmpty(resultMap)) {
            return null;
        }
        Object roleId = resultMap.get(FIELD_NAME);
        // todo: 加缓存
        if (nonNull(roleId)) {
            return Long.valueOf(String.valueOf(roleId));
        }
        return null;
    }

    private Long getRuleId(String subWaybillNo) {
        String querySql = "select order_rule_id from tms_waybill where waybill_no = ? limit 1";
        List<Map<String, Object>> resultList = jdbcTemplate.queryForList(querySql, subWaybillNo);
        if (CollectionUtils.isEmpty(resultList)) {
            return null;
        }
        Map<String, Object> resultMap = resultList.get(0);
        if (MapUtils.isEmpty(resultMap)) {
            return null;
        }
        Object roleId = resultMap.get(ROLE_ID);
        if (nonNull(roleId)) {
            return Long.valueOf(String.valueOf(roleId));
        }
        return null;
    }

    @Override
    public String getFieldName() {
        return FIELD_NAME;
    }

    @Override
    public String getGroupName() {
        return GROUP_NAME;
    }

    @Override
    public String getDataBaseName() {
        return null;
    }
}
