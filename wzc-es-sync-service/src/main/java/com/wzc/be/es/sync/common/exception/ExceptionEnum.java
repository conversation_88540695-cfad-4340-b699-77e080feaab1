package com.wzc.be.es.sync.common.exception;

import com.baidu.mapcloud.cloudnative.common.model.BizError;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExceptionEnum implements BizError {

    ERROR_SYNC_ES(47000, "同步数据到es失败"),
    ERROR_EXCEPTION_CATEGORY_NULL(47001, "异常类型不能为空"),
    ERROR_EXCEPTION_SUBWAYBILLNO_NULL(47002, "异常子运单号不能为空"),
    ERROR_EXCEPTION_TIME_NULL(47003, "异常发生时间不能为空"),
    ERROR_EXCEPTION_PROCESS_NULL(47004, "异常处理状态不能为空"),
    ;

    final Integer code;
    final String msg;
}
