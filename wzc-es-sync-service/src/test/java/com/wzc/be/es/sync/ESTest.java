package com.wzc.be.es.sync;

import com.wzc.be.es.sync.core.model.excep.ExcepMessage;
import com.wzc.be.es.sync.core.repository.ExceptionTimeRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;

/**
 * @description:
 * @Author: keeper Yu yxbwzx
 * @Date: 2023/9/6 9:25
 * @since 1.0.0
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ESTest {

    @Autowired
    private ExceptionTimeRepository exceptionTImeRepository;

    @Test
    public void testQuery(){
        String subWaybillNo = "YYD20230905195535000624";
        ExcepMessage excepMessage = new ExcepMessage();
        excepMessage.setExceptionTime(LocalDateTime.now());
        excepMessage.setSubWaybillNo(subWaybillNo);
        excepMessage.setCategory(3);
        exceptionTImeRepository.updateExceptionTime(excepMessage);
    }
}
