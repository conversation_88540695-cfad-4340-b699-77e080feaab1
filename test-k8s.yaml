apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: <APP>
    version: <RELEASE_NAME>
  name: <APP>-<RELEASE_NAME>
  namespace: <ENV>
spec:
  replicas: <REPLICAS>
  selector:
    matchLabels:
      app: <APP>
      version: <RELEASE_NAME>
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: <APP>
        version: <RELEASE_NAME>
    spec:
      containers:
        - env:
            - name: JAVA_OPTIONS
              value: '-javaagent:/agent/opentelemetry-javaagent.jar -Dotel.javaagent.extensions=/agent/otel-custom.jar -XX:InitialRAMPercentage=50 -XX:MaxRAMPercentage=50 -XX:MaxGCPauseMillis=200 -Dspring.profiles.active=cloud'
            - name: K8S_APP_NAME
              value: "<APP>"
            - name: K8S_NAMESPACE
              value: "<ENV>"
            - name: K8<PERSON>_NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          image: 'iregistry.baidu-int.com/<ENV>/<APP>:<BUILD_TAG>'
          imagePullPolicy: IfNotPresent
          volumeMounts:
            - name: otel-agent-volume
              mountPath: /agent
          name: <APP>
          lifecycle:
            preStop:
              exec:
                command: ["curl", "-XPOST", "127.0.0.1:8080/management/shutdown"]
          livenessProbe:
            httpGet:
              path: /management/healthz/liveness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            timeoutSeconds: 10
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /management/healthz/readiness
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 45
            timeoutSeconds: 10
            periodSeconds: 10
            successThreshold: 1
            failureThreshold: 3
          resources:
            limits:
              cpu: '2'
              memory: 2Gi
              nvidia.com/gpu: '0'
            requests:
              cpu: 100m
              memory: 1024Mi
              nvidia.com/gpu: '0'
          securityContext:
            privileged: false
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      nodeSelector:
        select: <ENV>
      dnsPolicy: ClusterFirst
      imagePullSecrets:
        - name: <ENV>-harbor-token
      initContainers:
        - args:
            - cp -f  /otel/* /agent/
          command:
            - /bin/sh
            - '-c'
          image: 'iregistry.baidu-int.com/<ENV>/otel-java-agent:1.20.2-jdk8-latest'
          imagePullPolicy: Always
          name: init-myservice
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /agent
              name: otel-agent-volume
      volumes:
        - name: otel-agent-volume
          emptyDir:
            medium: ''
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
