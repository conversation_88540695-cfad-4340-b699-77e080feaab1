package com.wzc.be.es.data.netcargo.qo;

import com.baidu.mapcloud.cloudnative.common.model.PageQO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "发单管理表QO")
public class NetCargoEsQO extends PageQO {

    @Schema(description = "订单号/运输计划单号")
    private String businessNo;

    @Schema(description = "第三方订单号")
    private String thirdNo;

    @Schema(description = "发布方公司")
    private String companyName;

    @Schema(description = "货物名称")
    private String itemNames;

    @Schema(description = "业务类型 1-货主自营 2-平台总包 -3平台自营 4-网络货运")
    private Integer businessType;

    @Schema(description = "发货单位")
    private String senderCustomer;

    @Schema(description = "发货地址")
    private String senderAddress;

    @Schema(description = "收货单位")
    private String receiverCustomer;

    @Schema(description = "收货地址")
    private String receiverAddress;

    @Schema(description = "订单类型（1销售订单，2采购订单，3调拨订单）")
    private Integer orderTypes;

    @Schema(description = "网货审核状态（0-待审核 1-审核中 2-审核通过 3-审核拒绝）")
    private Integer netCargoAuditStatus;

    @Schema(description = "创建人")
    private String netCargoCreator;

    @Schema(description = "创建开始时间")
    private String netCargoStartTime;

    @Schema(description = "创建结束时间")
    private String netCargoEndTime;

    @Schema(description = "合同编号")
    private String contractNo;

    @Schema(description = "合同平台合同审核状态：（-1=草稿,0=审核中,1=审核通过,2=审核不通过,10=已归档,20=变更中,30-已盖章,31:赛马物联已盖章,32:用户已盖章,101-线下签署中），聚合层维护合同审核状态：（3=待生成）")
    private Integer contractStatus;

    @Schema(description = "tab查询（0-全部 1-待审核）")
    private Integer queryTabType;
}