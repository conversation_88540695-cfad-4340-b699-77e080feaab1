package com.wzc.be.es.data.auth.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * @ClassName AuthRecordEs
 * @Description
 * <AUTHOR>
 * @Date 2023/7/6 13:49
 * @Version 1.0
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AuthRecordVO {

    private Long id;

    //名称
    private String name;



    //类型
    private Integer auditType;


    //管理员id
    private Long managerId;

    //管理员名称
    private String managerName;


    //数据来源id
    private Long dataId;


    //审核状态 1:待审核，2：审核未通过，3：审核已通过，4：认证过期
    private Integer status;


    //提交认证时间
    private String authTagDate;


    //审核人
    private Long operatorId;


    //审核人
    private String operatorName;

    //审核时间
    private String auditDate;

    //审核原因
    private String auditComments;


    private Integer auditFlag;

    //车辆注册人
    private Long registerID;

}
