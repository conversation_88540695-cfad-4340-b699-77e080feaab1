package com.wzc.be.es.data.waybill.enums;

import com.baidu.mapcloud.cloudnative.common.model.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 异常类型
 */
@Getter
@AllArgsConstructor
public enum ExceptionTypeEnum implements IEnum {

    ALL(0,"全部"),
    SIGN_EXCEPTION(2,"签收异常"),
    TRANSPORT_EXCEPTION(1,"在途异常"),
    SUPERVISE_EXCEPTION(3,"监管异常"),
    REPORT_EXCEPTION(4,"司机异常上报"),
    ;

    private final Integer code;
    private final String name;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return this.name;
    }
}
