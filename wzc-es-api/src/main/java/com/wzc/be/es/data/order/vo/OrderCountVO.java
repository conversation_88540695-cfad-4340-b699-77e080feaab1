package com.wzc.be.es.data.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * 订单统计响应
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-20
 */
@Schema(description = "<p> 订单统计响应 </p>")
@Data
public class OrderCountVO {

    /**
     * 全部数据
     */
    @Schema(description = "全部数据")
    private Integer allCount = 0;

    /**
     * 待确认数
     */
    @Schema(description = "待确认数")
    private Integer unConfirmCount = 0;

    /**
     * 待审核数
     */
    @Schema(description = "待审核数")
    private Integer waitCheckCount = 0;

    /**
     * 执行中数
     */
    @Schema(description = "执行中数")
    private Integer executionCount = 0;

    /**
     * 已暂停
     */
    @Schema(description = "已暂停")
    private Integer stopCount = 0;

    /**
     * 已完成
     */
    @Schema(description = "已完成")
    private Integer finishCount = 0;

    /**
     * 已取消
     */
    @Schema(description = "已取消")
    private Integer cancelCount = 0;

    /**
     * 已拒绝
     */
    @Schema(description = "已拒绝")
    private Integer refuseCount = 0;

    @Schema(description = "转包数")
    private Integer zbCount = 0;

}
