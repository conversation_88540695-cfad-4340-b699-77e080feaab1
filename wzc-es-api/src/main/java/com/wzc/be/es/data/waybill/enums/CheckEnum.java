package com.wzc.be.es.data.waybill.enums;

import com.baidu.mapcloud.cloudnative.common.model.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
@AllArgsConstructor
public enum CheckEnum implements IEnum {

    TYPE_1(1, "是否存在未签收完成的运单"),
    TYPE_2(2, "是否存在未结算完成的运单"),
    ;

    private final Integer code;
    private final String name;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return this.name;
    }

    public final static Map<Integer, CheckEnum> ENUM_MAP = new HashMap<>();

    static {
        for (CheckEnum value : CheckEnum.values()) {
            ENUM_MAP.put(value.code, value);
        }
    }

}
