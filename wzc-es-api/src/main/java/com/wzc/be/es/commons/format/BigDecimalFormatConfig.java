package com.wzc.be.es.commons.format;


import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * BigDecimal 保留两位
 */
public class BigDecimalFormatConfig extends JsonSerializer<BigDecimal> {
    @Override
    public void serialize(BigDecimal bigDecimal, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (bigDecimal.compareTo(BigDecimal.ZERO) == 0) {
            jsonGenerator.writeString(bigDecimal.stripTrailingZeros().toPlainString());
        } else {
            jsonGenerator.writeString(new DecimalFormat("0.##").format(bigDecimal));
        }
    }
}
