package com.wzc.be.es.data.waybill.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 承运商列表tab页统计
 *
 * <AUTHOR>
 */
@Data
public class CarrierTabGroupCountVO {

    @Schema(description = "全部")
    private Long statusCount0 = 0L;
    @Schema(description = "待开启")
    private Long statusCount1 = 0L;
    @Schema(description = "运输中")
    private Long statusCount2 = 0L;
    @Schema(description = "待签收")
    private Long statusCount3 = 0L;
    @Schema(description = "已签收")
    private Long statusCount4 = 0L;
    @Schema(description = "已撤单")
    private Long statusCount5 = 0L;
    @Schema(description = "待结算")
    private Long statusCount6 = 0L;
    @Schema(description = "待评价")
    private Long statusCount7 = 0L;
    @Schema(description = "待指派")
    private Long statusCount8 = 0L;

}
