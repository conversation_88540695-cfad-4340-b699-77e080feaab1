package com.wzc.be.es.data.warn.vo;


import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 运单异常信息
 * <AUTHOR>
 */
@Data
public class CscWarnExceptionVO {

    private String id;

    private Long exceptionId;

    /**
     * 子运单号
     */
    private String subWaybillNo;

    /**
     * 派车单号
     */
    private String dispatchCarNo;


    /**
     * 托运公司
     */
    private Long companyId;

    /**
     * 0-司机上报异常\r\n1-停留预警\r\n2-偏离预警\r\n3-离线预警\r\n4-高危围栏预警\r\n5-司机签收量与地磅量差异异常\r\n6-围栏外签收异常\r\n7-收货地两公里外签收异常\r\n8-进出厂磅单不齐全异常\r\n9-轨迹缺失异常\r\n10-轨迹不完整异常\r\n11-超速异常\r\n12-超载异常\r\n13-运输时长异常
     */
    private Integer exceptionType;

    /**
     * 异常发生时间
     */
    private String exceptionTime;


    /**
     * 子运单状态
     */
    private Integer subWaybillStatus;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 车牌号
     */
    private String carNo;


    /**
     * 发货地址
     */
    private List<Address> sender;

    /**
     * 收货地址
     */
    private List<Address> receiver;

    /**
     * 处理人名称
     */
    private String operatorName;

    /**
     * 异常处理时间
     */
    private String operateExceptionTime;

    private String createTime;

    private String updateTime;

    /**
     * 异常处理状态 1-已处理 2-未处理
     */
    private Integer operateException;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 承运商名称
     */
    private String carrierName;


    /**
     * 地址信息
     */
    @Data
    public static class Address implements Serializable {

        /**
         * POI
         */
        private String address;

        /**
         * 完整地址
         */
        private String fullAddress;

        /**
         * id
         */
        private Long id;

        /**
         * 地址名称
         */
        private String addressName;
    }

}
