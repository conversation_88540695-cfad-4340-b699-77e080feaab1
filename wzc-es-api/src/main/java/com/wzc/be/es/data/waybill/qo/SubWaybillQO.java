package com.wzc.be.es.data.waybill.qo;

import com.baidu.mapcloud.cloudnative.common.model.PageQO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class SubWaybillQO extends PageQO {

    /**
     * 开始时间 yyyy-MM-dd HH:mm:ss
     */
    private String startTime;

    /**
     * 结束时间 yyyy-MM-dd HH:mm:ss
     */
    private String endTime;

    /**
     * 公司id
     */
    private Long carrierCompanyId;

    /**
     * 运输计划单号集合
     */
    private List<String> parentNoList;

    /**
     * 子运单号
     */
    private String subWaybillNo;

    /**
     * 派车单号
     */
    private String dispatchCarNo;

    /**
     * 子运单号
     */
    private List<String> subWaybillNoList;


    /**
     * 公司id集合
     */
    @Schema(description = "公司id集合")
    private List<Long> companyIdList;

    /**
     * 派车单集合
     */
    @Schema(description = "派车单集合")
    private List<String> dispatchCarNoList;

    /**
     * 派车单状态
     */
    @Schema(description = "派车单状态")
    private List<Integer> status;

    /**
     * 卸货确认开始时间
     */
    @Schema(description = "卸货确认开始时间 yyyy-MM-dd HH:mm:ss")
    private String unLoadStartTime;
    /**
     * 卸货确认结束时间
     */
    @Schema(description = "卸货确认结束时间 yyyy-MM-dd HH:mm:ss")
    private String unLoadEndTime;

    @Schema(description = "是否同步二期数据 (1是  2否)")
    private Integer isSync;

    @Schema(description = "承运商公司名称")
    private String carrierCompanyName;

    /**
     * 货源编号
     */
    @Schema(description = "货源编号")
    private String goodsNo;


    /**
     * 货源编号列表
     */
    @Schema(description = "货源编号列表")
    private List<String> goodsNos;

    /**
     * 司机姓名
     */
    @Schema(description = "司机姓名")
    private String driverName;

    /**
     * 全部查询关键字
     */
    @Schema(description = "全部查询关键字")
    private String allWord;

    /**
     * 运单状态筛选-APP 1-待开启 2-运输中 3-待签收 4-已签收 5-已撤单 6-待结算
     */
    @Schema(description = "运单状态筛选-APP 1-待开启 2-运输中 3-待签收 4-已签收 5-已撤单 6-待结算")
    private Integer tabType;

    /**
     * 合同状态
     */
    @Schema(description = "合同状态")
    private Integer contractStatus;

    /**
     * 评价 0待评价 1已评价
     */
    private Integer isEvaluate;
}
