package com.wzc.be.es.data.waybill.enums;


import com.baidu.mapcloud.cloudnative.common.model.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CarrierTabTypeEnum implements IEnum {

    ALL(0,"全部"),
    STATUS_1(1,"待开启"),
    STATUS_2(2,"运输中"),
    STATUS_3(3,"待签收"),
    STATUS_4(4,"已签收"),
    STATUS_5(5,"已撤单"),
    STATUS_6(6,"待结算"),
    STATUS_7(7,"待评价"),
    STATUS_8(8,"待指派"),
    ;

    private final Integer code;
    private final String name;

}
