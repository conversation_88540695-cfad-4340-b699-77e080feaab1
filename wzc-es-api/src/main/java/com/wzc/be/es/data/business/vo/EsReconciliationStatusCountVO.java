package com.wzc.be.es.data.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "对账状态统计响应类")
@Data
public class EsReconciliationStatusCountVO {

    @Schema(description = "未对账数")
    private Integer unReconciliationCount = 0;

    @Schema(description = "已对账未结算数")
    private Integer alReconciliationCount = 0;

    @Schema(description = "已结算数")
    private Integer alSettleCount = 0;
}
