package com.wzc.be.es.data.waybill.qo;


import com.baidu.mapcloud.cloudnative.common.model.PageQO;
import com.wzc.be.es.data.waybill.enums.CarrierAppOrderListTabEnum;
import com.wzc.be.es.data.waybill.enums.DispatchQueryEnums;
import lombok.Data;

import java.util.List;

/**
 * 承运商APP运单列表请求参数
 * <AUTHOR>
 */
@Data
public class CarrierAppOrderListQO extends PageQO {

    /**
     * 承运商公司id
     */
    private String carrierCompanyId;

    /**
     * Tab切换参数
     */
    private CarrierAppOrderListTabEnum tabType;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 列表检索关键字
     */
    private String queryValue;

    /**
     * 列表检索类型
     */
    private DispatchQueryEnums queryType;

    /**
     * 运单状态多选
     */
    private List<Integer> subWaybillStatus;

    /**
     * 按来源
     */
    private List<Integer> source;

    /**
     * 承运商签收筛选  0全部 1待货主签收 2待自己签收(发布方)
     */
    private Integer ownerSign;
}
