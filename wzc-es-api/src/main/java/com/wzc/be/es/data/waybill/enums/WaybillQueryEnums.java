package com.wzc.be.es.data.waybill.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author：chenxingguang
 * @Date：2024/3/14 20:52
 * @Version：1.0.0
 */
@Getter
@AllArgsConstructor
public enum WaybillQueryEnums {
    ALL(0,"全部"),
    CARRIER(1,"承运商"),
    THIRDORDERNO(2,"第三方单号"),
    ORDERNO(3,"订单号"),
    DISPATCH_CAR_NO(4,"运单号"),
    TRANPORTNO(5,"运输计划单号"),
    ITEMNAME(6,"货物名称"),
    FORM_ADDRESS(7,"装货地"),
    TO_ADDRESS(8,"卸货地"),
    DRIVER(9,"司机"),
    CAR_NO(10,"车牌号查询");


    private final Integer code;

    private final String name;
}
