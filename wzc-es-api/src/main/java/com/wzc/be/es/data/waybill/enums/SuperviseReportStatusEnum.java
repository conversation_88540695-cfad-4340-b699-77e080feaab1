package com.wzc.be.es.data.waybill.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2023/11/12 19:58
 */
@Getter
@AllArgsConstructor
public enum SuperviseReportStatusEnum {

    report_status_0(0, "未上报"),
    report_status_1(1, "已上报"),
    report_status_2(2, "上报中"),
    report_status_3(3, "上报失败"),

            ;

    private final Integer code;

    private final String name;

    public static SuperviseReportStatusEnum getEnum(Integer code) {
        if(null != code){
            for (SuperviseReportStatusEnum c : SuperviseReportStatusEnum.values()) {
                if (c.getCode().equals(code)) {
                    return c;
                }
            }
        }
        return null;
    }
}
