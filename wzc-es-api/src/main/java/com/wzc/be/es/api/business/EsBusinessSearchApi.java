package com.wzc.be.es.api.business;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.data.business.qo.BusinessEsQueryPageQO;
import com.wzc.be.es.data.business.vo.BmsRcGroupVO;
import com.wzc.be.es.data.business.vo.BusinessEsVO;
import com.wzc.be.es.data.business.vo.EsRcAuditStatusCountVO;
import com.wzc.be.es.data.business.vo.EsReconciliationStatusCountVO;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年10月13日
 */
@Schema(description = "es对账单搜索api")
public interface EsBusinessSearchApi {


    /**
     * 对账单分页查询-运单模式
     * @param requestPageQo requestPageQo
     * @return RestResponse
     */
    @PostMapping("/inner/api/es/v1/reconciliation/page/search")
    RestResponse<EsPageVO<BusinessEsVO>> reconciliationPage(@RequestBody BusinessEsQueryPageQO requestPageQo);

    /**
     * 对账单分页查询-运输模式
     * @param requestPageQo requestPageQo
     * @return RestResponse
     */
    @PostMapping("/inner/api/es/v1/reconciliation/group/page/search")
    RestResponse<EsPageVO<BmsRcGroupVO>> reconciliationGroupPage(@RequestBody BusinessEsQueryPageQO requestPageQo);

    /**
     * 对账单列表查询-运单模式
     * @param requestPageQo requestPageQo
     * @return RestResponse
     */
    @PostMapping("/inner/api/es/v1/reconciliation/list/search")
    RestResponse<List<BusinessEsVO>> reconciliationList(@RequestBody BusinessEsQueryPageQO requestPageQo);

    /**
     * 对账单对账状态统计-运单模式
     * @param businessEsQueryPageQo
     * @return
     */
    @PostMapping("/inner/api/es/v1/business/reconciliationStatusCount")
    RestResponse<EsReconciliationStatusCountVO> reconciliationStatusCount(@RequestBody BusinessEsQueryPageQO businessEsQueryPageQo);

    /**
     * 对账单对账状态统计-运输计划模式
     * @param businessEsQueryPageQo
     * @return
     */
    @PostMapping("/inner/api/es/v1/business/rcStatusTransportCount")
    RestResponse<EsReconciliationStatusCountVO> rcStatusTransportCount(@RequestBody BusinessEsQueryPageQO businessEsQueryPageQo);

    /**
     * 对账单审核状态统计-运单模式
     * @param businessEsQueryPageQo
     * @return
     */
    @PostMapping("/inner/api/es/v1/business/rcAuditStatusCount")
    RestResponse<EsRcAuditStatusCountVO> rcAuditStatusCount(@RequestBody BusinessEsQueryPageQO businessEsQueryPageQo);

    /**
     * 对账单审核状态统计-运输计划模式
     * @param businessEsQueryPageQo
     * @return
     */
    @PostMapping("/inner/api/es/v1/business/rcAuditStatusTransportCount")
    RestResponse<EsRcAuditStatusCountVO> rcAuditStatusTransportCount(@RequestBody BusinessEsQueryPageQO businessEsQueryPageQo);


}
