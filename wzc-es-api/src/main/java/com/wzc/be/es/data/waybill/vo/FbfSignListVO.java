package com.wzc.be.es.data.waybill.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 网货发布方签收列表
 */
@Data
public class FbfSignListVO {

    @Schema(description = "运单号")
    private String waybillNo;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "子运单号")
    private String subWaybillNo;

    @Schema(description = "车辆id")
    private Long carId;

    @Schema(description = "车牌号")
    private String carNo;

    @Schema(description = "司机id")
    private Long driverId;

    @Schema(description = "司机名称")
    private String driverName;

    @Schema(description = "司机运输时间(开启运单时间)")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date transStartTime;

    @Schema(description = "司机卸货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date transEndTime;

    @Schema(description = "司机装货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date loadConfirmTime;

    @Schema(description = "派车时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "收发货商")
    private String customerName;

    @Schema(description = "服务方id")
    private Long carCaptainId;

    @Schema(description = "服务方名称")
    private String carCaptainName;

    @Schema(description = "发布方id")
    private Long fbfId;

    @Schema(description = "发布方名称")
    private String fbfName;

    @Schema(description = "线路轨迹是否合规(1-是 2-否)")
    private Integer isLineLegal = 2;

    @Schema(description = "时间是否合规(1-是 2-否)")
    private Integer isTimeLegal = 2;

    @Schema(description = "榜单是否合规(1-是 2-否)")
    private Integer isPoundLegal = 2;

    @Schema(description = "系统审核状态（1-通过、2-未通过）")
    private Integer systemCheckStatus = 2;

    @Schema(description = "人工审核状态（0-未审核、1-通过、2-未通过）")
    private Integer manualCheckStatus = 2;

    @Schema(description = "人工审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date manualCheckTime;

    @Schema(description = "车主声明 0无需上传 1已上传")
    private Integer czStatement = 2;

    @Schema(description = "车队长收入合规性 1是 2否")
    private Integer cdzIncomeLegal = 2;

    @Schema(description = "货主公司id")
    private Long shipperCompanyId;

    @Schema(description = "货主公司名称")
    private String shipperCompanyName;

    @Schema(description = "承运商公司id")
    private Long carrierCompanyId;

    @Schema(description = "承运商公司名称")
    private String carrierCompanyName;

    /**
     * 发货地址
     */
    private List<Address> sender;

    /**
     * 收货地址
     */
    private List<Address> receiver;


    /**
     * 地址信息
     */
    @Data
    public static class Address implements Serializable {

        /**
         * POI
         */
        private String address;

        /**
         * 省
         */
        private String province;

        /**
         * 市
         */
        private String city;

        /**
         * 手机号
         */
        private String phone;

        /**
         * 区
         */
        private String district;

        /**
         * 完整地址
         */
        private String fullAddress;

        /**
         * id
         */
        private Long id;

        /**
         * 类型
         */
        private Integer type;

        /**
         * 收发货商
         */
        @Schema(description = "收发货商")
        private String customerName;

        /**
         * 收货商
         */
        @Schema(description = "收货商")
        private String toCustomerName;
    }

    @Schema(description = "发布方签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date fbfSignTime;

    @Schema(description = "装货场景照片是否合规(1-是 2-否)")
    private Integer isLoadImgLegal = 2;

    @Schema(description = "卸货场景照片是否合规(1-是 2-否)")
    private Integer isUnLoadImgLegal = 2;

    @Schema(description = "运单合规性状态 1-司机签收 2-司机待上传 3-司机已上传 4-发布方待确认 5-发布方已确认 6-人工审核通过")
    private Integer dispatchComplianceType;

}
