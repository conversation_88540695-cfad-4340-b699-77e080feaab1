package com.wzc.be.es.data.waybill.qo;

import com.wzc.be.es.data.waybill.enums.RoleTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
public class WaybillStatisticsQO {

    @Schema(description = "角色类型")
    @NotNull(message = "角色类型不能为空")
    private RoleTypeEnum roleType;

    @Schema(description = "公司id")
    @NotNull(message = "公司id不能为空")
    private Long companyId;

    @Schema(description = "开始时间")
    @NotEmpty(message = "开始时间不能为空")
    private String startTime;

    @Schema(description = "结束时间")
    @NotEmpty(message = "结束时间不能为空")
    private String endTime;

}
