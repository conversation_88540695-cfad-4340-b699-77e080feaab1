package com.wzc.be.es.data.knowledge.qo;

import com.baidu.mapcloud.cloudnative.common.model.PageQO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/12 17:10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@EqualsAndHashCode
public class KnowledgeSearchQO extends PageQO {

    @Schema(description = "操作对象")
    private Integer operationObject;

    @Schema(description = "操作内容")
    private String operationContent;

    @Schema(description = "操作类型")
    private Integer operationType;

    @Schema(description = "发布人名称")
    private String userName;

    @Schema(description = "开始时间")
    private Date startDate;

    @Schema(description = "结束时间")
    private Date endDate;
}
