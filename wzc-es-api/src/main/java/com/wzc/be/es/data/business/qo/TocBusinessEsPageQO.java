package com.wzc.be.es.data.business.qo;

import com.baidu.mapcloud.cloudnative.common.model.PageQO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "toc对账单分页查询请求实体类")
public class TocBusinessEsPageQO extends PageQO {
    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "运单号")
    private String dispatchNo;

    @Schema(description = "子运单号")
    private String subWaybillNo;

    private String transportNo;

    @Schema(description = "车牌号")
    private String carNo;

    @Schema(description = "货物名称")
    private String itemName;

    @Schema(description = "业务类型")
    private Integer businessType;

    @Schema(description = "业务类型")
    private List<Integer> businessTypeList;

    @Schema(description = "发货单位")
    private String fmCustomerName;

    @Schema(description = "收货单位")
    private String toCustomerName;

    @Schema(description = "发货地址")
    private String fmAddress;

    @Schema(description = "收货地址")
    private String toAddress;

    @Schema(description = "对账状态")
    private Integer rcStatus;

    @Schema(description = "托运公司id")
    private Long shipperCompanyId;

    @Schema(description = "发布方类型")
    private Integer apCompanyType;

    @Schema(description = "发布方id")
    private Long apCompanyId;

    @Schema(description = "派车时间开始")
    private String dispatchTimeStart;

    @Schema(description = "派车时间结束")
    private String dispatchTimeEnd;

    @Schema(description = "司机运输时间开始")
    private String transportTimeStart;

    @Schema(description = "司机运输时间结束")
    private String transportTimeEnd;

    @Schema(description = "司机卸货时间开始")
    private String unloadTimeStart;

    @Schema(description = "司机卸货时间结束")
    private String unloadTimeEnd;

    @Schema(description = "服务方名称")
    private String arCompanyName;

    @Schema(description = "支付方式")
    private Integer paymentMethod;

    @Schema(description = "选中的运单号集合")
    private List<String> exportSubWaybillNoList;

    @Schema(description = "0.未支付,1.支付中,2.已支付,3.支付完成,4.支付异常,5.申诉待审核")
    private Integer payStatus;

    @Schema(description = "托运方审核状态（0待审核 1审核通过 2已退回）")
    private Integer shipperAuditStatus;

    private Object[] lastSortValues;

    @Schema(description = "发起付款开始时间")
    private String payStartTime;

    @Schema(description = "发起付款结束时间")
    private String payEndTime;

    @Schema(description = "服务方到账开始时间")
    private String toAccountStartTime;

    @Schema(description = "服务方到账结束时间")
    private String toAccountEndTime;

    @Schema(description = "发布方签收开始时间")
    private String createStartTime;

    @Schema(description = "发布方签收结束时间")
    private String createEndTime;

    @Schema(description = "发布方企业名称")
    private String apCompanyName;

    @Schema(description = "支付完成开始时间")
    private String payFinishStartTime;

    @Schema(description = "支付完成结束时间")
    private String payFinishEndTime;

    @Schema(description = "申诉完成开始时间")
    private String appealFinishStartTime;

    @Schema(description = "申诉完成结束时间")
    private String appealFinishEndTime;

    @Schema(description = "操作人")
    private Long payOperatorId;

    @Schema(description = "操作人名称")
    private String payOperatorName;

    @Schema(description = "1.未对账 2.已对账 3.申诉待审核 4.支付异常")
    private Integer queryTab;

    @Schema(description = "暂估应付单推送状态（0-全部，1-已推送，2-未推送")
    private Integer pushGsStatusAp;

    @Schema(description = "付款单推送状态（0-全部，1-已推送，2-未推送）")
    private Integer pushGsStatusApPay;

}
