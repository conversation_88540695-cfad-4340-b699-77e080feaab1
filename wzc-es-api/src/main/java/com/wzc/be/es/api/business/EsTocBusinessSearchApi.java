package com.wzc.be.es.api.business;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.data.business.qo.TocBusinessEsPageQO;
import com.wzc.be.es.data.business.vo.TocEsBusinessVO;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023年10月13日
 */
@Schema(description = "es网货对账单搜索api")
public interface EsTocBusinessSearchApi {


    /**
     * 网货对账单分页查询-运单模式
     * @param requestPageQo requestPageQo
     * @return RestResponse
     */
    @PostMapping("/inner/api/es/v1/toc/reconciliation/page/search")
    RestResponse<EsPageVO<TocEsBusinessVO>> tocReconciliationPage(@RequestBody TocBusinessEsPageQO requestPageQo);

}
