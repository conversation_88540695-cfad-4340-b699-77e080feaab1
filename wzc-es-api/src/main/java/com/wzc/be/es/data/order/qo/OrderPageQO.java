package com.wzc.be.es.data.order.qo;

import com.baidu.mapcloud.cloudnative.common.model.PageQO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "订单分页查询请求实体类")
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderPageQO extends PageQO {


    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "第三方订单号")
    private String thirdNo;

    @Schema(description = "货物名称")
    private String itemNames;

    @Schema(description = "业务类型 1.平台总包，2.货主自营，3.平台自营，4.网络货运")
    private Integer businessType;

    @Schema(description = "发货单位")
    private String deliveryCustomer;

    @Schema(description = "发货地址")
    private String deliveryAddress;

    @Schema(description = "收货单位")
    private String receiptCustomer;

    @Schema(description = "收货地址")
    private String receiptAddress;

    @Schema(description = "订单类型（1销售订单，2采购订单，3调拨订单）")
    private Integer waybillType;

    @Schema(description = "订单类型集合")
    private List<String> orderTypeList;

    @Schema(description = "业务类型集合")
    private List<String> businessTypeList;

    @Schema(description = "订单来源类型集合")
    private List<String> sourceTypeList;

    @Schema(description = "部门id集合")
    private List<Long> depIdList;

    @Schema(description = "订单状态")
    private Integer status;

    @Schema(description = "需要过滤的订单来源类型")
    private Integer sourceType;

    @Schema(description = "运输模式")
    private Integer transportMode;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "公司id")
    private Long companyId;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "托运公司id")
    private Long shipperCompanyId;

    @Schema(description = "托运公司名称")
    private String shipperCompanyName;

    @Schema(description = "关联用户id数据集合")
    private List<String> userIdList;

    @Schema(description = "公司id数据权限集合")
    private List<String> companyList;
    @Schema(description = "制单人id")
    private Long creatorId;
    @Schema(description = "创建开始时间")
    private String startTime;
    @Schema(description = "创建结束时间")
    private String endTime;
    @Schema(description = "公司配置物料线路权限")
    private List<EsOtherQueryParam> companyAndOtherList;

    @Schema(description = "制单人名称")
    private String makerName;

    private Object[] lastSortValues;

    @Schema(description = "是否升序")
    private Boolean isAsc = false;

    @Schema(description = "是否需要权限")
    private Boolean needAuth = true;

    @Schema(description = "需要过滤的订单号集合")
    private List<String> noInOrderNoList;

    @Schema(description = "订单号集合")
    private List<String> orderNoList;

    @Schema(description = "转网货标识 1网货订单 2非网货订单")
    private Integer netCargo;

    @Schema(description = "二期运单号")
    private String waybillNoSecond;

    @Schema(description = "是否查询转包待确认数据")
    private Boolean querySubtractConfirmOrders = false;

    @Schema(description = "是否同步二期数据 (1是  2否)")
    private Integer isSync;

    @Schema(description = "是否竞价立项使用 1是 2否")
    private Integer isBidding;

    @Schema(description = "是否开启校验载重设备功能  1是，2否，默认否")
    private Integer loadDeviceCheck;

    @Schema(description = "搜索类型")
    private Integer searchType;

    @Schema(description = "搜索关键字")
    private String keyword;

    @Schema(description = "隐藏状态 0-不隐藏 1-隐藏")
    private Integer hideStatus;

    @Schema(description = "合同签署要求（0-无需签署 1-需要签署 2-强制签署）")
    private Integer contractRequire;

    @Schema(description = "合同签约方式 1-线上签约 2-线下签约")
    private Integer contractSignMethod;

    @Schema(description = "合同编号")
    private String contractNo;

    @Schema(description = "合同平台合同审核状态：（-1=草稿,0=审核中,1=审核通过,2=审核不通过,10=已归档,20=变更中,30-已盖章,31:赛马物联已盖章,32:用户已盖章,101-线下签署中），聚合层维护合同审核状态：（3=待生成）")
    private Integer contractStatus;

    @Schema(description = "是否存在合同")
    private Boolean hasContract;


    @Schema(description = "货主公司id集合")
    private List<Long> ownerIdSList;

    @Data
    public static class EsOtherQueryParam{
        private String companyId;
        private List<String> materialSpecies;
        private List<String> route;
    }
}
