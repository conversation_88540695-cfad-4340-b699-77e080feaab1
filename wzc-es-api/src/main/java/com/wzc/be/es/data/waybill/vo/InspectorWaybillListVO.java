package com.wzc.be.es.data.waybill.vo;


import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Data
public class InspectorWaybillListVO {

    /**
     * 派车单号
     */
    private String dispatchCarNo;

    /**
     * 子运单号
     */
    private String subWaybillNo;

    /**
     * 派车单状态
     */
    private Integer status;

    /**
     * 子运单状态
     */
    private Integer subWaybillStatus;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 车牌号
     */
    private String carNo;

    /**
     * 车辆id
     */
    private String carId;

    /**
     * 司机id
     */
    private String driverId;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 要求到货日期
     */
    private String requireDate;

    /**
     * 发货地址
     */
    private AddressInfo senderInfo;

    /**
     * 收货地址
     */
    private AddressInfo receiverInfo;


    /**
     * 货物信息
     */
    private List<Goods> goodsList;

    @Data
    public static class AddressInfo {

        /**
         * POI
         */
        private String poiName;

        /**
         * 完整地址
         */
        private String fullAddress;

    }

    @Data
    public static class Goods {

        private String materielName;
        private String speciesName;
        private String unit;
        private BigDecimal weight;
        private BigDecimal price;

    }
}
