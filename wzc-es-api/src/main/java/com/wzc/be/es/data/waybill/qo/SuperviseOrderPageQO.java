package com.wzc.be.es.data.waybill.qo;

import com.baidu.mapcloud.cloudnative.common.model.PageQO;
import com.wzc.be.es.data.waybill.enums.SuperviseOrderListTabEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/11/9 20:49
 */
@Schema(
        description = "运单/资金流水列表 - 请求参数"
)
@Data
public class SuperviseOrderPageQO extends PageQO {

    /**
     * 创建时间-开始 yyyy-MM-dd HH:mm:ss
     */
    @Schema(description = "创建时间-开始 yyyy-MM-dd HH:mm:ss")
    private String createStartTime;

    /**
     * 创建时间-结束 yyyy-MM-dd HH:mm:ss
     */
    @Schema(description = "创建时间-结束 yyyy-MM-dd HH:mm:ss")
    private String createEndTime;

    /**
     * 运单号
     */
    @Schema(description = "运单号")
    private String dispatchCarNo;

    /**
     * 车牌号
     */
    @Schema(description = "车牌号")
    private String carNo;

    /**
     * 司机姓名
     */
    @Schema(description = "司机姓名")
    private String driverName;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNo;

    /**
     * tab类型1-合规订单 2-不合规订单 3-已上报订单
     */
    @Schema(description = "tab类型1-合规订单 2-不合规订单 3-已上报订单")
    private SuperviseOrderListTabEnum tabType;

    private Object[] lastSortValues;




}
