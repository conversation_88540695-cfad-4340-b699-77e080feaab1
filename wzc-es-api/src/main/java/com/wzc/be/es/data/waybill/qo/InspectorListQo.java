package com.wzc.be.es.data.waybill.qo;


import com.baidu.mapcloud.cloudnative.common.model.PageQO;
import com.wzc.be.es.data.waybill.enums.DispatchQueryEnums;
import com.wzc.be.es.data.waybill.enums.InspectorListTabEnum;
import lombok.Data;

/**
 * 小程序验货人运单列表请求参数
 *
 * <AUTHOR>
 */
@Data
public class InspectorListQo extends PageQO {

    /**
     * 手机号
     */
    private String phone;

    /**
     * 验货人id
     */
    private String inspectorId;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * tab页切换 0全部 1待签收
     */
    private InspectorListTabEnum tabType;

    /**
     * 列表检索关键字
     */
    private String queryValue;

    /**
     * 列表检索关键字
     */
    private DispatchQueryEnums queryType;

}
