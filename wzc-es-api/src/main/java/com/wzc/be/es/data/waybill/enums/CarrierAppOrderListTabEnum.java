package com.wzc.be.es.data.waybill.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 承运商app运单列表切换tab枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum CarrierAppOrderListTabEnum {

    TAB_1(1, "待发车"),
    TAB_2(2, "运输中"),
    TAB_3(3, "待签收"),
    TAB_4(4, "待付款"),
    TAB_5(5, "全部"),
    TAB_6(6, "待指派"),

    ;

    private final Integer code;

    private final String name;

}
