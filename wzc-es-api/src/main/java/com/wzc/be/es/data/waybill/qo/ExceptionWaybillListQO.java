package com.wzc.be.es.data.waybill.qo;

import com.baidu.mapcloud.cloudnative.common.model.PageQO;
import com.wzc.be.es.data.waybill.enums.ExceptionTypeEnum;
import com.wzc.be.es.data.waybill.enums.ProcessTypeEnum;
import lombok.Data;

import java.util.List;

@Data
public class ExceptionWaybillListQO extends PageQO {


    /**
     * 承运商ID
     */
    private Long carrierCompanyId;

    /**
     * 货主ID
     */
    private Long shipperCompanyId;

    /**
     * 运单号
     */
    private String waybillNo;

    /**
     * 发货地
     */
    private String senderAddress;

    /**
     * 收货地
     */
    private String receiverAddress;

    /**
     * 车牌号
     */
    private String carNo;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 联系电话
     */
    private String driverPhone;

    /**
     * 运输进度
     */
    private List<Integer> waybillStatus;

    /**
     * 异常类型
     */
    private ExceptionTypeEnum exceptionType;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 是否待处理
     */
    private ProcessTypeEnum processType;

    /**
     * 是否同步二期数据 1是 2否
     */
    private Integer isSync;

    /**
     * 公司id集合
     */
    private List<String> companyIdList;

}
