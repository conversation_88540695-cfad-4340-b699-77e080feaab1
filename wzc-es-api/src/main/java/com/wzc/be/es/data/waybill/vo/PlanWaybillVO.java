package com.wzc.be.es.data.waybill.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class PlanWaybillVO {

    @Schema(description = "派车单号")
    private String waybillNo;
    @Schema(description = "子运单号")
    private String subWaybillNo;
    @Schema(description = "车辆id")
    private Long carId;
    @Schema(description = "车牌号")
    private String carNo;
    @Schema(description = "司机id")
    private Long driverId;
    @Schema(description = "司机姓名")
    private String driverName;
    @Schema(description = "船员姓名")
    private String ztDriverName;
    @Schema(description = "状态")
    private Integer status;
    @Schema(description = "货主公司id")
    private Long shipperCompanyId;
    @Schema(description = "货主公司名称")
    private String shipperCompanyName;
    @Schema(description = "承运商公司id")
    private Long carrierCompanyId;
    @Schema(description = "承运商公司名称")
    private String carrierCompanyName;
    @Schema(description = "是否二期同步")
    private Integer isSync;
    @Schema(description = "是否网货 1是 2否")
    private Integer zbStatus;

    @Schema(description = "派车时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "二期派车单号")
    private String oldWaybillNo;

    @Schema(description = "二期子运单号")
    private String oldSubWaybillNo;

    @Schema(description = "评价 0待评价 1已评价")
    private Integer isEvaluate;
}
