package com.wzc.be.es.data.waybill.qo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class RelevanceWaybillQO {

    /**
     * 司机手机号
     */
    @Schema(description = "司机手机号")
    private String driverPhone;


    /**
     * 车牌号
     */
    @Schema(description = "车牌号")
    private String carNo;

    /**
     * 销售单位
     */
    @Schema(description = "销售单位")
    private String fromCustomerName;

    /**
     * 采购单位
     */
    @Schema(description = "采购单位")
    private String toCustomerName;


    /**
     * 发货地址
     */
    @Schema(description = "发货地址")
    private String fromAddress;

    /**
     * 收货地址
     */
    @Schema(description = "收货地址")
    private String toAddress;

    /**
     * 运单类型
     */
    @Schema(description = "运单类型")
    private Integer orderType;

}
