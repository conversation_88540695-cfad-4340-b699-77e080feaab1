package com.wzc.be.es.data.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "对账状态统计响应类")
@Data
public class BmsEsRcStatusCountVO {

    @Schema(description = "未对账")
    private Integer noRcCount = 0;

    @Schema(description = "已对账未结算")
    private Integer rcNoSettleCount = 0;

    @Schema(description = "已结算")
    private Integer settleCount = 0;

    @Schema(description = "已对账制证中")
    private Integer rcNoSettleGsCount = 0;

    @Schema(description = "异常")
    private Integer abnormalCount = 0;
}
