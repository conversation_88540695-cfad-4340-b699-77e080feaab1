package com.wzc.be.es.data.waybill.qo;

import cn.hutool.json.JSONObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "订单分页查询请求实体类")
@Data
public class RequestPageQO {


    @Schema(description = "搜索类型")
    private String searchType;

    @Schema(description = "开始位置")
    private Integer from;

    @Schema(description = "查询条数")
    private Integer size;

    @Schema(description = "请求条件实体类")
    private JSONObject requestJson;

}
