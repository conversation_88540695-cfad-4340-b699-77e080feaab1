package com.wzc.be.es.api.common;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.commons.qo.CommonRequestQO;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年08月11日
 */
@Schema(description = "es通用api")
public interface EsCommonSearchApi {

    /**
     * 索引相关的数据新增 同步操作
     * @param qo qo
     * @return Boolean
     */
    @PostMapping("/inner/api/es/v1/common/insert")
    RestResponse<Boolean> insert(@RequestBody CommonRequestQO qo);

    /**
     * 索引相关的数据删除 同步操作
     * @param qo qo
     * @return Boolean
     */
    @PostMapping("/inner/api/es/v1/common/delete")
    RestResponse<Boolean> delete(@RequestBody CommonRequestQO qo);

    /**
     * 索引相关的数据更新 同步操作
     * @param qo qo
     * @return Boolean
     */
    @PostMapping("/inner/api/es/v1/common/update")
    RestResponse<Boolean> update(@RequestBody CommonRequestQO qo);

}
