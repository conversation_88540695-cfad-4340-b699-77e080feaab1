package com.wzc.be.es.data.waybill.vo;


import com.wzc.common.trans.annotation.CustomApiTrans;
import com.wzc.common.trans.enums.TransType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class SubWaybillVO {

    private String id;

    /**
     * 运输计划单号(W111111111111)
     */
    private String parentNo;

    /**
     * 货主公司
     */
    //@CustomApiTrans(transName = "shipperCompanyName",type = TransType.COMPANY_TYPE)
    private Long shipperCompanyId;

    /**
     * 货主公司
     */
    private String shipperCompanyName;

    /**
     * 承运商公司
     */
    //@CustomApiTrans(transName = "carrierCompanyName",type = TransType.COMPANY_TYPE)
    private Long carrierCompanyId;

    /**
     * 承运商公司
     */
    private String carrierCompanyName;

    /**
     * 托运公司
     */
    private Long companyId;

    /**
     * 订单号O713816047986741256256
     */
    private String orderNo;

    /**
     * 派车时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 子运单号 S721436823872090118256
     */
    private String subWaybillNo;

    /**
     * 子运单状态
     */
    private Integer subWaybillStatus;

    /**
     * 派车单状态
     */
    private Integer status;

    /**
     * 派车单号 PCD16899259578784972800
     */
    private String waybillNo;

    /**
     * 司机id
     */
    //@CustomApiTrans(transName = "driverName",type = TransType.USER_TYPE_DRIVER)
    private Long driverId;

    /**
     * 司机姓名
     */
    private String driverName;

    /**
     * 船员姓名
     */
    private String ztDriverName;

    /**
     * 司机手机号
     */
    //@CustomApiTrans(transName = "driverId",transPhone = "driverPhone",type = TransType.USER_TYPE_DRIVER)
    private String driverPhone;

    /**
     * 车辆id
     */
    private Long carId;

    /**
     * 车牌号
     */
    private String carNo;

    /**
     * 发货地址
     */
    private List<Address> sender;

    /**
     * 收货地址
     */
    private List<Address> receiver;

    /**
     * 货物西悉尼
     */
    private List<Item> items;

    /**
     * 结算状态(对上) 1运输未完成 3待结算 4已结算 5已支付 6未对账 7已对账未结算 8已结算待支付
     */
    @Schema(description = "结算状态(对上) 1运输未完成 3待结算 4已结算 5已支付 6未对账 7已对账未结算 8已结算待支付")
    private Integer completeStatus;

    /**
     * 结算状态(对下) 1运输未完成 3待结算 4已结算 5已支付 6未对账 7已对账未结算 8已结算待支付
     */
    @Schema(description = "结算状态(对下) 1运输未完成 3待结算 4已结算 5已支付 6未对账 7已对账未结算 8已结算待支付")
    private Integer completeDownStatus;

    /**
     * 监管异常 1是 0否
     */
    private Integer excepSupervise;

    /**
     * 签收异常 1是 0否
     */
    private Integer excepSign;

    /**
     * 在途异常 1是 0否
     */
    private Integer excepTrans;

    /**
     * 在途异常发生时间
     */
    private String transExcepTime;


    /**
     * 签收异常发生时间
     */
    private String signExcepTime;

    /**
     * 监管上报异常发生时间
     */
    private String superviseExcepTime;

    private Integer orderType;

    /**
     * 二期派车单号
     */
    private String oldWaybillNo;

    /**
     * 二期子运单号
     */
    private String oldSubWaybillNo;

    private Integer zbStatus;

    /**
     * 地址信息
     */
    @Data
    public static class Address implements Serializable {

        /**
         * POI
         */
        private String address;

        /**
         * 省
         */
        private String province;

        /**
         * 市
         */
        private String city;

        /**
         * 手机号
         */
        private String phone;

        /**
         * 区
         */
        private String district;

        /**
         * 完整地址
         */
        private String fullAddress;

        /**
         * id
         */
        private Long id;

        /**
         * 类型
         */
        private Integer type;
        /**
         * 收发货商
         */
        private String customerName;
        /**
         * 收发货商id
         */
        private String customerId;
    }

    @Data
    public static class Item implements Serializable {
        private String itemId;
        private String itemName;
        private String id;
        @Schema(description = "承运商实际结算量")
        private String cysActualSettleWeight;

        @Schema(description = "承运商实际结算价格")
        private String cysActualSettlePrice;

        @Schema(description = "货主实际结算量")
        private String hzActualSettleWeight;

        @Schema(description = "货主实际结算价格")
        private String hzActualSettlePrice;
    }

    /**
     * 业务类型 1货主自营 2平台总包 3平台自营 4网络货运
     */
    @Schema(description = "业务类型 1货主自营 2平台总包 3平台自营 4网络货运")
    private Integer businessType;

    /**
     * 开启运输时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date transStartTime;

    /**
     * 卸货确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date transEndTime;

    /**
     *装货签到时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date loadSignInTime;

    /**
     * 装货确认时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date loadConfirmTime;

    /**
     * 卸货签到时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date unloadSignInTime;

    /**
     * 货主签收时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date ownerSignTime;

    /**
     * 第三方订单号
     */
    private String thirdOrderNo;

    /**
     * 北斗是否正常
     * 司机装货确认时，获取在24小时内是否有轨迹点，设置到运单上
     */
    @Schema(description = "北斗是否正常 1是 2否")
    private Integer bdNormal;

    /**
     * 自动签收 1是 2否
     */
    @Schema(description = "自动签收 1是 2否")
    private Integer autoSign;

    /**
     * 运输模式 1陆运 2船运
     */
    @Schema(description = "运输模式")
    private Integer transportType;

    /**
     * 货源编号
     */
    @Schema(description = "货源编号")
    private String goodsNo;

    /**
     * 合同状态
     */
    @Schema(description = "合同状态")
    private Integer contractStatus;

    /**
     * 评价 0待评价 1已评价
     */
    private Integer isEvaluate;

    /**
     * 超时未开启
     */
    @Schema(description = "超时未签收 0正常 1异常")
    private Integer overtimeUnStart;

    /**
     * 超时未签收
     */
    @Schema(description = "超时未签收 0正常 1异常")
    private Integer overtimeUnSign;

    @Schema(description = "应付结算单号")
    private String payStatementNo;

    @Schema(description = "应收结算单号")
    private String reStatementNo;

    @Schema(description = "应付单号")
    private String payOrderNo;

    @Schema(description = "应收单号")
    private String reOrderNo;

    @Schema(description = "付款申请单号")
    private String payApplyNo;

    @Schema(description = "收款单号")
    private String receiptNo;
}
