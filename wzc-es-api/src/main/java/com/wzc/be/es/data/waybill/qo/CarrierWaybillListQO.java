package com.wzc.be.es.data.waybill.qo;


import com.baidu.mapcloud.cloudnative.common.model.PageQO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wzc.be.es.data.waybill.enums.CarrierTabTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR> <PERSON>
 */
@Data
public class CarrierWaybillListQO extends PageQO {

    /**
     * 运单号
     */
    @Schema(description = "运单号")
    private String waybillNo;

    /**
     * 子运单号集合
     */
    @Schema(description = "子运单号集合")
    private List<String> waybillNolist;

    /**
     * 车牌号
     */
    @Schema(description = "车牌号")
    private String carNo;

    /**
     * 司机姓名
     */
    @Schema(description = "司机姓名")
    private String driverName;

    /**
     * 状态 0全部 1待开启 2运输中 3待签收 4已签收 5已撤单 6待结算
     */
    @Schema(description = "状态 0全部 1待开启 2运输中 3待签收 4已签收 5已撤单 6待结算")
    private CarrierTabTypeEnum tabType;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private List<Integer> status;

    /**
     * 是否短倒
     */
    @Schema(description = "是否短倒")
    private Integer isShort;


    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNo;

    /**
     * 货主名称
     */
    @Schema(description = "货主名称")
    private String shipperName;

    /**
     * 货物名称
     */
    @Schema(description = "货物名称")
    private String goodsName;

    /**
     * 运输计划单号
     */
    @Schema(description = "运输计划单号")
    private String planOrderNo;

    /**
     * 运输计划单号集合
     */
    @Schema(description = "运输计划单号集合")
    private List<String> planOrderNoList;

    @Schema(description = "承运商公司id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long carrierCompanyId;

    @Schema(description = "开始时间")
    private String startTime;
    @Schema(description = "结束时间")
    private String endTime;

    private Object[] lastSortValues;

    /**
     * 请求来源 1-运输档案
     */
    @Schema(description = "请求来源 1-运输档案")
    private Integer reqSource = 0;

    @Schema(description = "派车单号集合")
    private List<String> subWaybillNoList;

    @Schema(description = "查询时间类型 1创建时间 2开启运输 3装货签到 4装货确认 5卸货签到 6卸货确认 7货主签收 8装货一次过磅时间 9装货二次过磅时间 10卸货一次过磅时间 11卸货二次过磅时间")
    private Integer dateType = 1;

    /**
     * 发货商
     */
    @Schema(description = "发货商")
    private String fromCustomerName;

    /**
     * 收货商
     */
    @Schema(description = "收货商")
    private String toCustomerName;

    /**
     * 发货地址
     */
    @Schema(description = "发货地址")
    private String fromAddress;

    /**
     * 收货地址
     */
    @Schema(description = "收货地址")
    private String toAddress;

    /**
     * 第三方派车单号
     */
    @Schema(description = "第三方派车单号")
    private String thirdOrderNo;

    /**
     * 业务类型 1货主自营 2平台总包 3平台自营 4网络货运
     */
    @Schema(description = "业务类型 1货主自营 2平台总包 3平台自营 4网络货运")
    private Integer businessType;

    /**
     * 运单类型 1销售订单，2采购订单，3调拨订单
     */
    @Schema(description = "运单类型 1销售订单，2采购订单，3调拨订单")
    private Integer waybillType;

    /**
     * 北斗是否正常
     * 司机装货确认时，获取在24小时内是否有轨迹点，设置到运单上
     */
    @Schema(description = "北斗是否正常 1是 2否")
    private Integer bdNormal;

    /**
     * 自动签收 1是 2否
     */
    @Schema(description = "自动签收 1是 2否")
    private Integer autoSign;

    /**
     * 是否同步二期数据 1是 2否
     */
    @Schema(description = "是否同步二期数据 1是 2否")
    private Integer isSync;

    /**
     * 1-汽运 2-船运
     */
    @Schema(description = "1-汽运 2-船运")
    private Integer transportType;

    /**
     * 司机姓名-中台运单表中
     */
    @Schema(description = "司机姓名-中台运单表中")
    private String ztDriverName;

    @Schema(description = "app聚合搜索")
    private String appSearchValue;

    /**
     * 评价 0待评价 1已评价
     */
    private Integer isEvaluate;

    /**
     * 结算状态(对上) 1运输未完成 3待结算 4已结算 5已支付 6未对账 7已对账未结算 8已结算待支付
     */
    @Schema(description = "结算状态(对上) 1运输未完成 3待结算 4已结算 5已支付 6未对账 7已对账未结算 8已结算待支付")
    private Integer completeStatus;

    /**
     * 结算状态(对下) 1运输未完成 3待结算 4已结算 5已支付 6未对账 7已对账未结算 8已结算待支付
     */
    @Schema(description = "结算状态(对下) 1运输未完成 3待结算 4已结算 5已支付 6未对账 7已对账未结算 8已结算待支付")
    private Integer completeDownStatus;

    /**
     * 运单来源 0我找车系统 3竞价 4抢单
     */
    private Integer dispatchSourceNew;


    @Schema(description = "应付结算单号")
    private String payStatementNo;

    @Schema(description = "应收结算单号")
    private String reStatementNo;

    @Schema(description = "应付单号")
    private String payOrderNo;

    @Schema(description = "应收单号")
    private String reOrderNo;

    @Schema(description = "付款申请单号")
    private String payApplyNo;

    @Schema(description = "收款单号")
    private String receiptNo;

    @Schema(description = "承运商实际结算量")
    private String cysActualSettleWeight;

    @Schema(description = "承运商实际结算价格")
    private String cysActualSettlePrice;

    @Schema(description = "货主实际结算量")
    private String hzActualSettleWeight;

    @Schema(description = "货主实际结算价格")
    private String hzActualSettlePrice;

    @Schema(description = "货源编号")
    private String goodsNo;
}
