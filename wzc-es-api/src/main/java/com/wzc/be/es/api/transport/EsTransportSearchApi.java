package com.wzc.be.es.api.transport;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.data.transport.qo.TransportQueryPageQO;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.data.transport.vo.TransportEsVO;
import com.wzc.be.es.data.transport.vo.TransportGroupVO;
import com.wzc.be.es.data.transport.vo.TransportStatusCountVO;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年08月11日
 */
@Schema(description = "es运输计划搜索api")
public interface EsTransportSearchApi {


    /**
     * 运输计划分页查询
     * @param requestPageQo requestPageQo
     * @return RestResponse
     */
    @PostMapping("/inner/api/es/v1/transport/search")
    RestResponse<EsPageVO<TransportEsVO>> transportSearch(@RequestBody TransportQueryPageQO requestPageQo);

    /**
     * 运输计划按分组编号分页查询
     * @param requestPageQo requestPageQo
     * @return RestResponse
     */
    @PostMapping("/inner/api/es/v1/transport/group/search")
    RestResponse<EsPageVO<TransportGroupVO>> transportGroupSearch(@RequestBody TransportQueryPageQO requestPageQo);

    /**
     * 运输计划按分组编号分页查询
     * @param requestPageQo requestPageQo
     * @return RestResponse
     */
    @PostMapping("/inner/api/es/v1/transport/groupNo/search")
    RestResponse<EsPageVO<TransportGroupVO>> transportGroupNoSearch(@RequestBody TransportQueryPageQO requestPageQo);

    /**
     * app运输计划分页查询
     * @param requestPageQo requestPageQo
     * @return RestResponse
     */
    @PostMapping("/inner/api/es/v1/app/transport/search")
    RestResponse<EsPageVO<TransportEsVO>> appTransportSearch(@RequestBody TransportQueryPageQO requestPageQo);

    /**
     * 运输计划统计
     * @param waybillQueryPageQo waybillQueryPageQo
     * @return RestResponse
     */
    @PostMapping("/inner/api/es/v1/transport/count")
    RestResponse<TransportStatusCountVO> transportCount(@RequestBody TransportQueryPageQO waybillQueryPageQo);

    /**
     * 运输计划集合查询
     * @param queryPageQO queryPageQO
     * @return RestResponse<List<TransportEsVO>>
     */
    @PostMapping("/inner/api/es/v1/transport/list")
    RestResponse<List<TransportEsVO>> transportListSearch(@RequestBody TransportQueryPageQO queryPageQO);


    /**
     * 运输计划更新状态信息
     * @param queryPageQO queryPageQO
     * @return RestResponse<List<TransportEsVO>>
     */
    @PostMapping("/inner/api/es/v1/transport/update")
    RestResponse<Boolean> transportUpdate(@RequestBody TransportQueryPageQO queryPageQO);
}
