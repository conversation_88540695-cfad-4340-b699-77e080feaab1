package com.wzc.be.es.api.knowledge;

import com.baidu.mapcloud.cloudnative.common.model.Pagination;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.commons.qo.CommonRequestQO;
import com.wzc.be.es.data.knowledge.qo.KnowledgeSearchQO;
import com.wzc.be.es.data.knowledge.vo.KnowledgeSearchVO;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/12 17:08
 */
@Schema(description = "知识库搜索相关ES API")
public interface EsKnowledgeSearchApi {

    /**
     * 搜索知识库操作日志
     * @param qo qo
     * @return Boolean
     */
    @PostMapping("/inner/api/es/v1/knowledge/logs/search")
    RestResponse<EsPageVO<KnowledgeSearchVO>> knowledgeLogsSearch(@RequestBody KnowledgeSearchQO qo);
}
