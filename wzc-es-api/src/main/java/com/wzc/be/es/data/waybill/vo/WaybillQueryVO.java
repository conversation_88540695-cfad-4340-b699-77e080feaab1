package com.wzc.be.es.data.waybill.vo;

import com.wzc.common.trans.annotation.CustomApiTrans;
import com.wzc.common.trans.enums.TransType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wzc.be.es.data.waybill.enums.AddressTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Schema(
        description = "运单搜索返回内容VO对象"
)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WaybillQueryVO implements Serializable {

    private String id;

    @Schema(description = "派车单号")
    private String waybillNo;

    @Schema(description = "运单状态")
    private Integer waybillStatus;

    @Schema(description = "派车单状态")
    private Integer status;

    @Schema(description = "结算状态")
    private Integer completeStatus;

    @Schema(description = "承运商公司ID")
    private Long carrierCompanyId;

    @Schema(description = "公司ID")
    private Long companyId;

    @CustomApiTrans(transName = "driverName",type = TransType.USER_TYPE_DRIVER)
    @Schema(description = "司机ID")
    private Long driverId;

    @Schema(description = "司机名称")
    private String driverName;

    @Schema(description = "车辆ID")
    private Long carId;

    @Schema(description = "车牌号")
    private String carNo;

    @Schema(description = "派车时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern ="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "子运单列表")
    private List<SubWaybillBean> subWaybill;

    /**
     * 二期派车单号
     */
    private String oldWaybillNo;

    /**
     * 二期子运单号
     */
    private String oldSubWaybillNo;

    /**
     * 货源编号
     */
    @Schema(description = "货源编号")
    private String goodsNo;

    @Schema(description = "轮次")
    private Integer round;

    @Schema(description = "子运单号")
    private String subWaybillNo;

    @Schema(description = "订单号")
    private String orderNo;

    /**
     * 评价 0待评价 1已评价
     */
    private Integer isEvaluate;

    /**
     * 运单合规性状态 1-司机签收 2-司机待上传 3-司机已上传 4-发布方待确认 5-发布方已确认 6-人工审核通过
     * 非网货的此字段为null
     */
    @Schema(description = "运单合规性状态 1-司机签收 2-司机待上传 3-司机已上传 4-发布方待确认 5-发布方已确认 6-人工审核通过")
    private Integer dispatchComplianceType;


    @Schema(
            description = "结算状态"
    )
    private Integer settleStatus;


    /**
     * 业务类型 1货主自营 2平台总包 3平台自营 4网络货运
     */
    @Schema(description = "业务类型 1货主自营 2平台总包 3平台自营 4网络货运")
    private Integer businessType;

    @Data
    public static class SubWaybillBean implements Serializable {

        @Schema(description = "子运单编号")
        private String subWaybillNo;

        @Schema(description = "子运单状态")
        private Integer subWaybillStatus;

        @Schema(description = "结算状态")
        private Integer completeStatus;

        @Schema(description = "订单编号")
        private String orderNo;

        @Schema(description = "承运商名称")
        private String carrierCompanyName;

        @Schema(description = "订单或计划运单编号")
        private String parentNo;

        @CustomApiTrans(transName = "shipperCompanyName",type = TransType.COMPANY_TYPE)
        @Schema(description = "货主公司ID")
        private String shipperCompanyId;

        @Schema(description = "货主公司名称")
        private String shipperCompanyName;

        @Schema(description = "收货人信息")
        private ReceiverOrSenderBean receiver;

        @Schema(description = "发货人信息")
        private ReceiverOrSenderBean sender;

        @Schema(description = "物料信息集合")
        private List<ItemsBean> items;

        @Data
        public static class ReceiverOrSenderBean implements Serializable {
            @Schema(description = "ID")
            private String id;

            @Schema(description = "地址")
            private String address;

            @Schema(description = "完整地址")
            private String fullAddress;

            private String subWaybillNo;

            private Integer type;

            private String customerName;

            @Schema(description = "地址类型 1地址 2围栏")
            private AddressTypeEnum source;
        }


        @Data
        public static class ItemsBean implements Serializable {
            private String id;
            private String itemId;
            private String itemName;
        }
    }
}
