package com.wzc.be.es.data.warn.qo;

import com.baidu.mapcloud.cloudnative.common.model.PageQO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "csc异常运单分页查询请求实体类")
public class CscWarnExceptionEsPageQO extends PageQO {

    /**
     * 公司名称
     */
    @Schema(description = "公司名称")
    private String companyName;

    /**
     * 公司id
     */
    @Schema(description = "公司id")
    private Long companyId;

    /**
     * 运单号
     */
    @Schema(description = "运单号")
    private String dispatchCarNo;

    /**
     * 子运单号
     */
    @Schema(description = "子运单号")
    private String subWaybillNo;

    /**
     * 司机名称
     */
    @Schema(description = "司机名称")
    private String driverName;

    /**
     * 车牌号
     */
    @Schema(description = "车牌号")
    private String carNo;

    /**
     * 发货地址
     */
    @Schema(description = "发货地址")
    private String fmAddress;

    /**
     * 收货地址
     */
    @Schema(description = "收货地址")
    private String toAddress;

    /**
     * 承运商名称
     */
    @Schema(description = "承运商名称")
    private String carrierName;

    /**
     * 异常类型
     */
    @Schema(description = "异常类型")
    private List<Integer> exceptionTypeList;

    /**
     * 处理状态
     */
    @Schema(description = "处理状态")
    private Integer operateException;

    /**
     * 处理人
     */
    @Schema(description = "处理人")
    private String operatorName;

    /**
     * 预警时间开始
     */
    @Schema(description = "预警时间开始")
    private String exceptionStartTime;

    /**
     * 预警时间结束
     */
    @Schema(description = "预警时间结束")
    private String exceptionEndTime;

    /**
     * 处理时间开始
     */
    @Schema(description = "处理时间开始")
    private String operateExceptionStartTime;

    /**
     * 处理时间结束
     */
    @Schema(description = "处理时间结束")
    private String operateExceptionEndTime;

    /**
     * 异常处理结果 1-异常 2-误报
     */
    @Schema(description = "异常处理结果 1-异常 2-误报")
    private Integer operateType;

    /**
     * 订单类型（1.采购订单，2.销售订单，3.调拨订单）
     */
    @Schema(description = "订单类型（1.采购订单，2.销售订单，3.调拨订单）")
    private Integer waybillType;
}
