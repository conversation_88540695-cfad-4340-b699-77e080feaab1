package com.wzc.be.es.data.waybill.enums;

import com.baidu.mapcloud.cloudnative.common.model.IEnum;
import com.wzc.be.es.data.waybill.enums.LoadUploadStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @<PERSON> zhanwang
 * @Date 2023/11/11 14:38
 */
@Getter
@AllArgsConstructor
public enum LoadOrderStatusEnum implements IEnum {

    LOAD_ORDER_NO(0,"否"),
    LOAD_ORDER_YES(1,"是"),
    ;

    private final Integer code;
    private final String name;

    public static LoadUploadStatusEnum getEnum(Integer code) {
        if(null != code) {
            for (LoadUploadStatusEnum c : LoadUploadStatusEnum.values()) {
                if (c.getCode().equals(code)) {
                    return c;
                }
            }
        }
        return null;
    }
}
