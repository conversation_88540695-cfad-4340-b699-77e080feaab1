package com.wzc.be.es.data.transport.qo;

import com.baidu.mapcloud.cloudnative.common.model.PageQO;
import com.wzc.be.es.data.order.qo.OrderPageQO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 订单运输计划--分页查询参数
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "订单运输计划--分页查询参数")
public class TransportQueryPageQO extends PageQO {

    /**
     * 运单号
     */
    @Schema(description = "运单号")
    private String transportNo;

    /**
     * 第三方单号
     */
    @Schema(description = "第三方单号")
    private String thirdNo;

    /**
     * 运单状态：1 待接单,2 已拒绝,3 进行中,4 已完成,5 暂停 6已取消
     */
    @Schema(description = "运单状态：1 待接单,2 已拒绝,3 进行中,4 已完成,5 暂停 6已取消")
    private Integer status;

    /**
     * 创建开始时间
     */
    @Schema(description = "创建开始时间")
    private String startTime;

    /**
     * 创建结束时间
     */
    @Schema(description = "创建结束时间")
    private String endTime;

    @Schema(description = "有效期")
    private String validate;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 公司id集合
     */
    private List<Long> companyIds;

    /**
     * 托运公司id
     */
    @Schema(description = "托运公司id")
    private Long shipperCompanyId;

    /**
     * 承运商id
     */
    @Schema(description = "承运商id")
    private Long carrierId;

    /**
     * 承运商
     */
    @Schema(description = "承运商")
    private String carrierName;

    /**
     * 运单号列表
     */
    private List<String> transportNoList;

    @Schema(description = "订单号集合")
    private List<String> orderNoList;

    /**
     * 发货地址
     */
    @Schema(description = "发货地址")
    private String deliveryAddress;

    /**
     * 收货地址
     */
    @Schema(description = "收货地址")
    private String receiptAddress;

    /**
     * 货物名称
     */
    @Schema(description = "货物名称")
    private String itemNames;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "共享运输计划分组编号")
    private String groupNo;

    /**
     * 来源
     */
    @Schema(description = "来源")
    private Integer sourceType;

    /**
     * 货主名称
     */
    @Schema(description = "货主名称")
    private String ownerName;

    @Schema(description = "搜索类型")
    private Integer searchType;

    @Schema(description = "搜索关键字")
    private String keyword;

    @Schema(description = "是否需要分页 默认需要")
    private Boolean needPage = true;

    @Schema(description = "数据权限公司集合")
    private List<String> companyList;

    @Schema(description = "订单类型集合")
    private List<String> orderTypeList;

    @Schema(description = "运输计划来源类型集合")
    private List<String> sourceTypeList;

    @Schema(description = "关联用户id数据集合")
    private List<String> userIdList;

    @Schema(description = "状态集合")
    private List<Integer> statusList;

    private List<Long> depIdList;

    @Schema(description = "公司配置物料线路权限")
    private List<OrderPageQO.EsOtherQueryParam> companyAndOtherList;

    @Schema(description = "是否升序")
    private Boolean isAsc = false;

    @Schema(description = "是否从定时任务过来")
    private Boolean isFromJob = false;

    @Schema(description = "是否需要权限")
    private Boolean needAuth = true;

    @Schema(description = "转网货标识 1网货订单 2非网货订单")
    private Integer netCargo;

    @Schema(description = "需过滤的运输计划编号")
    private List<String> noInTransportNoList;

    @Schema(description = "是否同步二期数据 (1是  2否)")
    private Integer isSync;

    @Schema(description = "是否开启校验载重设备功能  1是，2否，默认否")
    private Integer loadDeviceCheck;

    @Schema(description = "货源单号")
    private String goodsNo;

    @Schema(description = "运输方式")
    private Integer transportType;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "合同签署要求（0-无需签署 1-需要签署 2-强制签署）")
    private Integer contractRequire;

    @Schema(description = "合同签约方式 1-线上签约 2-线下签约")
    private Integer contractSignMethod;

    @Schema(description = "合同编号")
    private String contractNo;

    @Schema(description = "合同平台合同审核状态：（-1=草稿,0=审核中,1=审核通过,2=审核不通过,10=已归档,20=变更中,30-已盖章,31:赛马物联已盖章,32:用户已盖章,101-线下签署中），聚合层维护合同审核状态：（3=待生成）")
    private Integer contractStatus;

    @Schema(description = "全部查询关键字")
    private String allWord;

    @Schema(description = "是否存在合同")
    private Boolean hasContract;

    @Schema(description = "接单时间")
    private String acceptTime ;

    @Schema(description = "业务类型0-默认 1-总包 2-自营 3-平台")
    private Integer businessType;

    @Schema(description = "订单类型（1销售订单，2采购订单，3调拨订单）")
    private Integer queryOrderType;

    @Schema(description = "货主公司id集合")
    private List<Long> ownerIdSList;
}
