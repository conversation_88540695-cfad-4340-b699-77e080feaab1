package com.wzc.be.es.data.waybill.qo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 打卡补录校验司机车辆是否在时间段内开启了运单
 * <AUTHOR>
 */
@Data
public class CheckDriverAndCarWaybillQO {

    @Schema(description = "派车单")
    private String waybillNo;
    @Schema(description = "司机id")
    private String driverId;
    @Schema(description = "车辆id")
    private String carId;
    @Schema(description = "开始时间")
    private String startTime;
    @Schema(description = "结束时间")
    private String endTime;

}
