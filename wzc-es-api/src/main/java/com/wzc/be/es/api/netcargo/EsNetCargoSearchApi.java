package com.wzc.be.es.api.netcargo;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.data.netcargo.qo.NetCargoEsQO;
import com.wzc.be.es.data.netcargo.vo.NetCargoCountEsVO;
import com.wzc.be.es.data.netcargo.vo.NetCargoEsVO;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Schema(description = "es发单管理搜索api")
public interface EsNetCargoSearchApi {

    /**
     * 发单管理分页查询
     *
     * @param qo
     * @return
     */
    @PostMapping("/inner/api/es/v1/netcargo/page")
    RestResponse<EsPageVO<NetCargoEsVO>> page(@RequestBody NetCargoEsQO qo);

    /**
     * 发单管理状态统计
     *
     * @param qo
     * @return
     */
    @PostMapping("/inner/api/es/v1/netcargo/count")
    RestResponse<NetCargoCountEsVO> count(@RequestBody NetCargoEsQO qo);
}
