package com.wzc.be.es.data.items.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 货物信息
 * <AUTHOR>
 */
@Data
@Schema(description = "货物信息")
public class ItemsVO {

    @Schema(description = "物料基础id")
    private Long itemId;

    @Schema(description = "物类基础id")
    private Long speciesId;

    @Schema(description = "物料名称")
    private String itemName;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "派车量")
    private Double deliveryWeight;

    @Schema(description = "总量")
    private Double itemWeight;

    @Schema(description = "货主签收量")
    private Double signWeight;

    @Schema(description = "装货量")
    private Double loadWeight;

    @Schema(description = "卸货量")
    private Double unloadWeight;

    @Schema(description = "货物单位")
    private Integer itemUnit;
}
