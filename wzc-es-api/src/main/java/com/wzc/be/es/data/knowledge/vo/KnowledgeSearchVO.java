package com.wzc.be.es.data.knowledge.vo;

import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/12 17:19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@EqualsAndHashCode
public class KnowledgeSearchVO {

    /**
     * 业务ID
     */
    private Long businessId;

    /**
     * 操作对象
     */
    private Integer operationObject;

    /**
     * 操作内容
     */
    private String operationContent;

    /**
     * 操作类型
     */
    private Integer operationType;

    /**
     * 操作人名称
     */
    private String userName;

    /**
     * 创建者ID
     */
    private Long creatorId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改者ID
     */
    private Long operatorId;

    /**
     * 修改时间
     */
    private Date updateTime;
}
