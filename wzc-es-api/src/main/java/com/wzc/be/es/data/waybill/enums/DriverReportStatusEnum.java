package com.wzc.be.es.data.waybill.enums;

import com.baidu.mapcloud.cloudnative.common.model.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/11/10 0:49
 */
@Getter
@AllArgsConstructor
public enum DriverReportStatusEnum implements IEnum {

    NOT_REPORT(1, "未上报"),
    REPORTING(2, "上报中"),
    HAS_REPORTED(3, "已上报"),
    REPORT_FAILED(4, "上报失败");

    private Integer code;
    private String name;

    public static DriverReportStatusEnum getEnum(Integer code) {
        if(null != code) {
            for (DriverReportStatusEnum c : DriverReportStatusEnum.values()) {
                if (c.getCode().equals(code)) {
                    return c;
                }
            }
        }
        return null;
    }

}
