package com.wzc.be.es.api.dispatch;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.data.dispatch.vo.DispatchStatusVO;
import com.wzc.be.es.data.waybill.qo.RequestPageQO;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年08月11日
 */
@Schema(description = "es派车搜索api")
public interface EsDispatchSearchApi {

    /**
     * 查询大单下派车单是否已开启运输
     * @param requestPageQo requestPageQo
     * @return RestResponse<List<DispatchStatusVO>>
     */
    @PostMapping("/inner/api/es/v1/dispatch/startDispatch")
    RestResponse<List<DispatchStatusVO>> startDispatch(@RequestBody RequestPageQO requestPageQo);
}
