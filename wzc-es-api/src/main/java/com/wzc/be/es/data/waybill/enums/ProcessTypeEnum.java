package com.wzc.be.es.data.waybill.enums;

import com.baidu.mapcloud.cloudnative.common.model.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 是否待处理
 */
@Getter
@AllArgsConstructor
public enum ProcessTypeEnum implements IEnum {
    PROCESS(1, "是"),
    UN_PROCESS(0, "否"),
    ;

    private final Integer code;
    private final String name;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return this.name;
    }
}
