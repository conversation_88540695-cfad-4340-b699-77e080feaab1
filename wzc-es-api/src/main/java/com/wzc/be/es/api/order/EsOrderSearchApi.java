package com.wzc.be.es.api.order;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.data.order.qo.OrderPageQO;
import com.wzc.be.es.data.order.vo.OrderCountVO;
import com.wzc.be.es.data.order.vo.OrderIndexVO;
import com.wzc.be.es.data.order.vo.PlanOrderCountVO;
import com.wzc.be.es.data.waybill.qo.RequestPageQO;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023年08月11日
 */
@Schema(description = "es订单搜索api")
public interface EsOrderSearchApi {


    /**
     * 订单分页查询
     * @param requestPageQo requestPageQo
     * @return Rest<JSONObject>
     */
    @PostMapping("/inner/api/es/v1/order/search")
    RestResponse<EsPageVO<OrderIndexVO>> orderSearch(@RequestBody OrderPageQO requestPageQo);

    /**
     * 订单统计
     * @param requestPageQo requestPageQo
     * @return RestResponse
     */
    @PostMapping("/inner/api/es/v1/order/count")
    RestResponse<OrderCountVO> orderCount(@RequestBody OrderPageQO requestPageQo);

    /**
     * 计划单统计
     * @param requestPageQo requestPageQo
     * @return RestResponse<PlanOrderCountVO>
     */
    @PostMapping("/inner/api/es/v1/order/planOrderCount")
    RestResponse<PlanOrderCountVO> selectPlanOrderCount(@RequestBody RequestPageQO requestPageQo);

    /**
     * 订单分页查询
     * @param requestPageQo requestPageQo
     * @return Rest<JSONObject>
     */
    @PostMapping("/inner/api/es/v1/orderSzy/search")
    RestResponse<EsPageVO<OrderIndexVO>> orderSzySearch(@RequestBody OrderPageQO requestPageQo);
}
