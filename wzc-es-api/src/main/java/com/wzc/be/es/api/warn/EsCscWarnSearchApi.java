package com.wzc.be.es.api.warn;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.data.warn.qo.CscWarnExceptionEsPageQO;
import com.wzc.be.es.data.warn.vo.CscWarnExceptionVO;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023年12月25日
 */
@Schema(description = "es运单异常搜索api")
public interface EsCscWarnSearchApi {


    /**
     * 运单异常预警分页查询
     * @param requestPageQo requestPageQo
     * @return RestResponse
     */
    @PostMapping("/inner/api/es/v1/csc/warn/page/search")
    RestResponse<EsPageVO<CscWarnExceptionVO>> cscWarnExceptionPage(@RequestBody CscWarnExceptionEsPageQO requestPageQo);

}
