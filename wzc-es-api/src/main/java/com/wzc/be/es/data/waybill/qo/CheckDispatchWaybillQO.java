package com.wzc.be.es.data.waybill.qo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class CheckDispatchWaybillQO {

    /**
     * 司机id
     */
    private Long driverId;

    /**
     * 车辆id
     */
    private Long carId;

    /**
     * 子运单状态
     */
    private List<Integer> subWaybillStatusList;


    /**
     * 结算状态(对上) 1运输未完成 3待结算 4已结算 5已支付 6未对账 7已对账未结算 8已结算待支付
     */
    @Schema(description = "结算状态(对上) 1运输未完成 3待结算 4已结算 5已支付 6未对账 7已对账未结算 8已结算待支付")
    private List<Integer> completeStatusList;

    /**
     * 结算状态(对下) 1运输未完成 3待结算 4已结算 5已支付 6未对账 7已对账未结算 8已结算待支付
     */
    @Schema(description = "结算状态(对下) 1运输未完成 3待结算 4已结算 5已支付 6未对账 7已对账未结算 8已结算待支付")
    private List<Integer> completeDownStatusList;

}
