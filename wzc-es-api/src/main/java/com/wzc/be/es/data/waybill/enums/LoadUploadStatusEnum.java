package com.wzc.be.es.data.waybill.enums;

import com.baidu.mapcloud.cloudnative.common.model.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * @<PERSON> zhan<PERSON>
 * @Date 2023/11/11 14:08
 */
@Getter
@AllArgsConstructor
public enum LoadUploadStatusEnum implements IEnum {

    LOAD_UPLOAD_NO(0,"未上传"),
    LOAD_UPLOAD_YES(1,"已上传"),
    ;

    private final Integer code;
    private final String name;

    public static LoadUploadStatusEnum getEnum(Integer code) {
        if(null != code){
            for (LoadUploadStatusEnum c : LoadUploadStatusEnum.values()) {
                if (c.getCode().equals(code)) {
                    return c;
                }
            }
        }
        return null;
    }
}
