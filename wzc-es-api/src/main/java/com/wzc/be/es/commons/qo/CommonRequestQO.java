package com.wzc.be.es.commons.qo;


import com.wzc.be.es.commons.enums.SearchTypeEnums;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 索引关联数据请求参数
 *
 * <AUTHOR> wei
 */
@Data
public class CommonRequestQO{

    @Schema(description = "搜索类型")
    private SearchTypeEnums searchTypeEnums;

    private List<CommonRequestItemQO> commonRequestItemQoList;

    @Data
    public static class CommonRequestItemQO{
        @Schema(description = "企业id")
        private Long companyId;

        @Schema(description = "唯一标识")
        private String unionNo;

        @Schema(description = "状态")
        private Integer status;

        @Schema(description = "变化后状态")
        private Integer afterStatus;
    }

}
