package com.wzc.be.es.data.waybill.qo;

import com.baidu.mapcloud.cloudnative.common.model.PageQO;
import com.wzc.be.es.data.waybill.enums.CarReportStatusEnum;
import com.wzc.be.es.data.waybill.enums.DriverReportStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class RepostStatusQO extends PageQO {

    @Schema(description = "是否网络货运 1是 2否")
    private Integer zbStatus;


    /**
     * 司机id
     */
    private Long driverId;


    /**
     * 车辆id
     */
    private Long carId;


    /**
     * 司机上报状态 1-未上报 2-上报中 3-已上报 4-上报失败
     */
    @Schema(description = "司机上报状态")
    private DriverReportStatusEnum driverReportStatus;


    /**
     * 车辆上报状态 1-未上报 2-已上报 3-上报失败 4-上报中
     */
    @Schema(description = "车辆上报状态")
    private CarReportStatusEnum carReportStatus;

}
