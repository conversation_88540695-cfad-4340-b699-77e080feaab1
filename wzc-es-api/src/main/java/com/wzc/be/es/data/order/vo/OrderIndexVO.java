package com.wzc.be.es.data.order.vo;

import com.wzc.be.es.data.items.vo.ItemsVO;
import com.wzc.be.es.data.shipping.vo.ShippingVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 订单索引响应类
 * <AUTHOR>
 * @Date: 2023-06-13
 */
@Data
@ToString
public class OrderIndexVO {

    private String id;

    private Long orderId;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "第三方订单号")
    private String thirdNo;

    private String baseNo;

    @Schema(description = "货物名称")
    private String itemNames;

    @Schema(description = "业务类型 1.平台总包，2.货主自营，3.平台自营，4.网络货运")
    private Integer businessType;

    @Schema(description = "发货单位")
    private String deliveryCustomer;

    @Schema(description = "发货地址")
    private String deliveryAddress;

    @Schema(description = "收货单位")
    private String receiptCustomer;

    @Schema(description = "收货地址")
    private String receiptAddress;

    @Schema(description = "订单类型（1.采购订单，2.销售订单，3.调拨订单）")
    private Integer waybillType;

    @Schema(description = "订单状态")
    private Integer status;

    @Schema(description = "运输模式")
    private Integer transportMode;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "公司id")
    private Long companyId;

    @Schema(description = "托运人公司ID")
    private Long shipperCompanyId;

    @Schema(description = "创建时间")
    private String createTime;

    @Schema(description = "二期运单号")
    private String waybillNoSecond;

    @Schema(description = "是否开启校验载重设备功能  1是，2否，默认否")
    private Integer loadDeviceCheck;

    private Long ownerId;

    private List<ItemsVO> items;

    private List<ShippingVO> sender;

    private List<ShippingVO> receiver;

    @Schema(description = "隐藏状态 0-不隐藏 1-隐藏")
    private Integer hideStatus;

}
