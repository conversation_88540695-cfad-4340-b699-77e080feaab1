package com.wzc.be.es.api.business;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.data.business.qo.BmsEsBusinessQueryPageQO;
import com.wzc.be.es.data.business.vo.BmsBusinessEsVO;
import com.wzc.be.es.data.business.vo.BmsEsAuditStatusCountVO;
import com.wzc.be.es.data.business.vo.BmsEsRcStatusCountVO;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Schema(description = "es对账单搜索api")
public interface EsBmsBusinessSearchApi {

    /**
     * 对账单分页查询
     *
     * @param qo
     * @return
     */
    @PostMapping("/inner/api/es/v1/business/page")
    RestResponse<EsPageVO<BmsBusinessEsVO>> page(@RequestBody BmsEsBusinessQueryPageQO qo);

    /**
     * 对账单对账状态统计
     *
     * @param qo
     * @return
     */
    @PostMapping("/inner/api/es/v1/business/rcStatusCount")
    RestResponse<BmsEsRcStatusCountVO> rcStatusCount(@RequestBody BmsEsBusinessQueryPageQO qo);

    /**
     * 对账单审核状态统计
     *
     * @param qo
     * @return
     */
    @PostMapping("/inner/api/es/v1/business/auditStatusCount")
    RestResponse<BmsEsAuditStatusCountVO> auditStatusCount(@RequestBody BmsEsBusinessQueryPageQO qo);
}
