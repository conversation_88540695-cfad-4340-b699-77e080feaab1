package com.wzc.be.es.data.waybill.enums;

import com.baidu.mapcloud.cloudnative.common.model.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 角色枚举
 */
@Getter
@AllArgsConstructor
public enum RoleTypeEnum implements IEnum {
    OWNER(1,"货主"),
    CARRIER(2,"承运商"),
    ;

    private final Integer code;
    private final String name;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return this.name;
    }
}
