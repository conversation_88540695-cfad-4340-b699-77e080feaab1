package com.wzc.be.es.data.waybill.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wzc.be.es.commons.format.BigDecimalWeightConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * PC承运商运单列表
 *
 * <AUTHOR> <PERSON>
 */
@Data
public class CarrierWaybillListVO {

    @Schema(description = "id")
    private String id;

    @Schema(description = "运单号")
    private String waybillNo;

    @Schema(description = "运单标识")
    private List<String> tagList;

    @Schema(description = "车牌号")
    private String carNo;

    @Schema(description = "司机姓名")
    private String driverName;

    @Schema(description = "船员姓名")
    private String ztDriverName;

    @Schema(description = "货物名称")
    private String goodsName;

    @Schema(description = "货主名称")
    private String shipperName;

    @Schema(description = "运输进度(状态)")
    private Integer status;

    @Schema(description = "收货地")
    private String receiverAddress;

    @Schema(description = "发货地")
    private String senderAddress;

    @Schema(description = "结算状态")
    private Integer settleStatus;

    @Schema(description = "结算状态(对上) 1运输未完成 3待结算 4已结算 5已支付 6未对账 7已对账未结算 8已结算待支付")
    private Integer completeStatus;

    @Schema(description = "结算状态(对下) 1运输未完成 3待结算 4已结算 5已支付 6未对账 7已对账未结算 8已结算待支付")
    private Integer completeDownStatus;

    @Schema(description = "派车时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date dispatchDate;

    @Schema(description = "运输计划单号")
    private String planOrderNo;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "子运单单号")
    private String subWaybillNo;

    private Integer merge;

    @Schema(description = "二期派车单号")
    private String oldWaybillNo;

    @Schema(description = "二期子运单号")
    private String oldSubWaybillNo;

    @Schema(description = "货物信息")
    private List<GoodsInfo> goodsInfoList;

    @Schema(description = "订单类型（1.采购订单，2.销售订单，3.调拨订单）")
    private Integer orderType;

    @Schema(description = "业务类型 1货主自营 2平台总包 3平台自营 4网络货运")
    private Integer businessType;

    @Schema(description = "开启运输时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date waybillStartTime;

    @Schema(description = "卸货确认时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date unloadConfirmTime;

    @Schema(description = "装货签到时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date loadSignTime;

    @Schema(description = "装货确认时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date loadConfirmTime;

    @Schema(description = "卸货签到时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date unloadSignTime;

    @Schema(description = "货主签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date ownerSignTime;

    @Schema(description = "第三方订单号")
    private String thirdOrderNo;

    @Schema(description = "北斗是否正常 1是 2否 司机装货确认时，获取在24小时内是否有轨迹点，设置到运单上")
    private Integer bdNormal;

    @Schema(description = "自动签收 1是 2否")
    private Integer autoSign;

    @Schema(description = "运输模式 1陆运 2船运")
    private Integer transportType;

    @Schema(description = "评价 0待评价 1已评价")
    private Integer isEvaluate;

    @Schema(description = "应付结算单号")
    private String payStatementNo;

    @Schema(description = "应收结算单号")
    private String reStatementNo;

    @Schema(description = "应付单号")
    private String payOrderNo;

    @Schema(description = "应收单号")
    private String reOrderNo;

    @Schema(description = "付款申请单号")
    private String payApplyNo;

    @Schema(description = "收款单号")
    private String receiptNo;

    @Schema(description = "货源编号")
    private String goodsNo;


    @Data
    public static class GoodsInfo {

        private String id;

        @Schema(description = "物料id")
        private String itemId;

        @Schema(description = "货物名称")
        private String goodsName;

        @Schema(description = "货物单位")
        private String unit;

        @Schema(description = "运价")
        private BigDecimal price;

        @Schema(description = "预提量")
        @JsonSerialize(using = BigDecimalWeightConfig.class)
        private BigDecimal goodsWeight = BigDecimal.ZERO;

        @Schema(description = "装货量")
        @JsonSerialize(using = BigDecimalWeightConfig.class)
        private BigDecimal loadWeight = BigDecimal.ZERO;

        @Schema(description = "卸货量")
        @JsonSerialize(using = BigDecimalWeightConfig.class)
        private BigDecimal unloadWeight = BigDecimal.ZERO;

        @Schema(description = "司机签收量")
        @JsonSerialize(using = BigDecimalWeightConfig.class)
        private BigDecimal driverSignWeight = BigDecimal.ZERO;

        @Schema(description = "货主签收量")
        @JsonSerialize(using = BigDecimalWeightConfig.class)
        private BigDecimal shipperSignWeight = BigDecimal.ZERO;

        @Schema(description = "装货一次过磅量")
        @JsonSerialize(using = BigDecimalWeightConfig.class)
        private BigDecimal firstLoadWeight = BigDecimal.ZERO;

        @Schema(description = "装货二次过磅量")
        @JsonSerialize(using = BigDecimalWeightConfig.class)
        private BigDecimal secondLoadWeight = BigDecimal.ZERO;

        @Schema(description = "卸货一次过磅量")
        @JsonSerialize(using = BigDecimalWeightConfig.class)
        private BigDecimal firstUnloadWeight = BigDecimal.ZERO;

        @Schema(description = "卸货二次过磅量")
        @JsonSerialize(using = BigDecimalWeightConfig.class)
        private BigDecimal secondUnloadWeight = BigDecimal.ZERO;

        @Schema(description = "磅差")
        private BigDecimal weightDifference;

        @Schema(description = "一次计量时间")
        private Date oneWeightTime;

        @Schema(description = "二次计量时间")
        private Date twoWeightTime;

        @Schema(description = "司机装货确认量")
        private BigDecimal driverLoadConfirmWeight;

        @Schema(description = "确认司机签收量")
        private BigDecimal confirmDriverLoadWeight;

        @Schema(description = "承运商实际结算量")
        private String cysActualSettleWeight;

        @Schema(description = "承运商实际结算价格")
        private String cysActualSettlePrice;

        @Schema(description = "货主实际结算量")
        private String hzActualSettleWeight;

        @Schema(description = "货主实际结算价格")
        private String hzActualSettlePrice;

    }

}
