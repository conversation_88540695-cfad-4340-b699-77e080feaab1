package com.wzc.be.es.data.waybill.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wzc.be.es.data.waybill.enums.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/11/9 23:25
 */
@Data
public class SuperviseOrderPageVO {

    /**
     * 运单号
     */
    @Schema(description = "运单号")
    private String dispatchCarNo;

    /**
     * 子运单号
     */
    @Schema(description = "子运单号")
    private String subWaybillNo;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNo;

    /**
     * SDK上报状态（装） 0-失败 1-成功
     */
    @Schema(description = "SDK上报状态（装）")
    private LoadUploadStatusEnum loadUploadStatus;

    /**
     * SDK上报状态（卸） 0-失败 1-成功
     */
    @Schema(description = "SDK上报状态（卸）")
    private LoadUploadStatusEnum unloadUploadStatus;

    /**
     * 支付状态0-未支付 1-已支付
     */
    @Schema(description = "支付状态0-未支付 1-已支付")
    private PayStatusEnum payStatus;

    /**
     * 司机上报状态 1-未上报 2-上报中 3-已上报 4-上报失败
     */
    @Schema(description = "司机上报状态")
    private DriverReportStatusEnum driverReportStatus;

    /**
     * 车辆上报状态 1-未上报 2-已上报 3-上报失败 4-上报中
     */
    @Schema(description = "车辆上报状态")
    private CarReportStatusEnum carReportStatus;




    /**
     * 司机
     */
    @Schema(description = "司机")
    private String driverName;

    /**
     * 车牌号
     */
    @Schema(description = "车牌号")
    private String carNo;

    /**
     * 运输轨迹
     */
    @Schema(description = "运输轨迹")
    private Integer track;

    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String driverPhone;

    /**
     * 发货地
     */
    @Schema(description = "发货地")
    private String fromAddress;

    /**
     * 收货地
     */
    @Schema(description = "收货地")
    private String toAddress;

    /**
     * 货物
     */
    @Schema(description = "货物")
    private String materialName;

    /**
     * 发货时间
     */
    @Schema(description = "发货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date fromTime;

    /**
     * 装货单据
     */
    @Schema(description = "装货单据")
    private LoadOrderStatusEnum loadOrder;

    /**
     * 到达时间
     */
    @Schema(description = "到达时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date arrivalTime;

    /**
     * 卸货单据
     */
    @Schema(description = "卸货单据")
    private LoadOrderStatusEnum unloadOrder;

    /**
     * 签收时间
     */
    @Schema(description = "签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date signInTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 监管运单上报状态0-未上报 1-已上报 2-上报中
     */
    @Schema(description = "监管运单上报状态0-未上报 1-已上报 2-上报中")
    private SuperviseReportStatusEnum reportWaybillStatus;

    /**
     * 监管流水上报状态0-未上报 1-已上报 2-上报中
     */
    @Schema(description = "监管流水上报状态0-未上报 1-已上报 2-上报中")
    private SuperviseReportStatusEnum reportRecordStatus;

    /**
     * 装货单据
     */
    @Schema(description = "装货单据")
    private List<String> loadOrderList;

    /**
     * 卸货单据
     */
    @Schema(description = "卸货单据")
    private List<String> unloadOrderList;


    /**
     * 结算状态(对上) 1运输未完成 3待结算 4已结算 5已支付 6未对账 7已对账未结算 8已结算待支付
     */
    @Schema(description = "结算状态(对上) 1运输未完成 3待结算 4已结算 5已支付 6未对账 7已对账未结算 8已结算待支付")
    private Integer completeStatus;

    /**
     * 车辆id
     */
    @Schema(description = "车辆id")
    private Long carId;


    /**
     * 司机id
     */
    @Schema(description = "司机id")
    private Long driverId;

    /**
     * 签收人
     */
    @Schema(description = "签收人")
    private String signInUser;

}
