package com.wzc.be.es.commons.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/07/06
 */
@Getter
@AllArgsConstructor
public enum SearchTypeEnums {

    /**
     * 搜索类型handler枚举
     */
    ORDER_PAGE("order", "订单分页搜索"),

    ORDER_AFTER_PAGE("orderSearchAfter", "订单分页深度搜索"),

    ORDER_COUNT("orderCount", "订单状态统计"),

    TRANSPORT_PAGE("transport", "运输计划分页搜索"),

    TRANSPORT_GROUP_PAGE("transportGroup", "运输计划分组分页搜索"),

    TRANSPORT_GROUP_NO_PAGE("transportGroupNo", "货主端，平台端 运输计划分组分页搜索"),

    TRANSPORT_APP_PAGE("transportApp", "运输计划app分页搜索"),

    TRANSPORT_LIST("transportList", "运输计划集合"),

    TRANSPORT_COUNT("transportCount", "运输计划状态统计"),

    TRANSPORT_UPDATE("transportUpdate", "运输计划修改"),

    BUSINESS_RC_YD_PAGE("bmsRcYdModelPage", "对账单运单模式分页搜索"),

    TOC_BUSINESS_RC_YD_PAGE("bmsTocRcYdPage", "网货对账单分页搜索"),

    TOC_BUSINESS_RC_YD_AFTER_PAGE("bmsTocRcSearchAfter", "网货对账单深度分页搜索"),

    CSC_WARN_EXCEPTION_PAGE("cscWarnExceptionPage", "csc异常运单分页查询"),

    BUSINESS_RC_YD_LIST("bmsRcYdModelList", "对账单运单模式列表搜索"),

    BUSINESS_RC_TRANSPORT_PAGE("bmsRcTransportModel", "对账单运输模式分页搜索"),

    RECONCILIATION_STATUS_COUNT("bmsReconciliationRcStatusCount", "对账单对账状态统计"),

    RECONCILIATION_STATUS_TRANSPORT_COUNT("bmsTransportRcStatusCount", "对账单对账状态运输计划模式统计"),

    RC_SHIPPER_AUDIT_STATUS_COUNT("bmsRcShipperAuditStatusCount", "对账单平台审核状态统计"),

    RC_AUDIT_STATUS_TRANSPORT_COUNT("bmsRcAuditStatusGroupCount", "对账单审核状态运输计划模式统计"),

    RC_SHIPPER_AUDIT_STATUS_TRANSPORT_COUNT("bmsRcShipperAuditStatusGroupCount", "对账单平台审核状态运输计划模式统计"),

    RC_AUDIT_STATUS_COUNT("bmsRcShipperAuditStatusCount", "对账单审核状态统计"),

    INSPECTOR_WAYBILL("inspectorWaybill", "验货人运单列表"),

    CARRIER_APP_ORDER_LIST("carrierAppOrderListHandler", "承运商APP运单列表"),

    CARRIER_PC_ORDER_LIST("carrierWaybillQueryListHandler", "承运商PC运单列表"),

    DRIVER_APP_ORDER_LIST("waybillQueryListHandler", "司机APP运单列表"),

    CARRIER_TAB_GROUP("carrierTabGroupHandler", "承运商列表tab页统计"),

    WAYBILL("waybillHandler", "查询运单信息"),

    CHECK_ACTIVATE_WAYBILL("checkActivateWaybillHandler", "司机查询是否有已开启的运单"),

    CHECK_DISPATCH_WAYBILL("checkDispatchWaybillHandler", "司机查询已派车成功的运单"),

    OMS_SEARCH_WAYBILL_LIST("omsSearchWaybillHandler", "OMS查询运单列表"),

    OWNER_WAYBILL_QUERY_LIST("ownerWaybillQueryListHandler", "PC货主查询运单列表"),

    OWNER_WAYBILL_QUERY_LIST_AFTER("ownerWaybillQueryAfterSearchHandler", "PC货主查询运单列表-深度查询"),

    CARRIER_WAYBILL_QUERY_LIST_AFTER("carrierWaybillQueryAfterHandler", "PC承运商查询运单列表-深度查询"),

    OWNER_TAB_GROUP("ownerTabGroupHandler", "货主运单列表tab页统计"),

    SUB_WAYBILL_LIST("subWaybillHandler", "子运单查询"),

    EXCEPTION_WAYBILL_LIST("exceptionWaybillHandler", "异常运单查询"),

    EXCEPTION_TAB_COUNT("exceptionTabCountHandler", "异常运单tab统计"),

    CHECK_DRIVER_WAYBILL("checkDriverWaybillHandler", "异常运单tab统计"),

    PLAN_ORDER_COUNT("orderCountHandler", "订单号统计派车数"),

    TRANSPORT_NO_COUNT("transportNoCount", "运输计划单号统计派车数"),

    WAYBILL_STATISTICS("waybillStatistics", "公司运输统计"),
    FBF_WAYBILL("fbfWaybillHandler", "网货发布方签收列表"),
    FBF_WAYBILL_EXPORT("fbfWaybillAfterHandler", "网货发布方签收列表"),

    OWNER_WAYBILL("ownerWaybillHandler", "根据货主公司id查询是否有运单"),
    WAYBILL_QUERY("waybillQueryHandler", "查询运单"),
    PLAN_WAYBILL("planWaybillHandler", "根据运输计划单号查询派车单列表"),
    RELEVANCE_WAYBILL("relevanceWaybillHandler", "关联运单查询"),
    SUPERVISE_WAYBILL_LIST("superviseWaybillQueryListHandler", "监管订单搜索"),
    SUPERVISE_WAYBILL_LIST_EXPORT("superviseWaybillQueryListExportHandler", "监管订单导出"),
    WAYBILL_QUERY_PAGE("waybillQueryPageHandler", "查询运单分页"),
    CHECK_DRIVER_AND_CAR_WAYBILL_HANDLER("CheckDriverAndCarWaybillHandler", "打卡补录校验司机车辆是否在时间段内开启了运单"),
    ORDER_SZY_PAGE("orderSzy", "数智院订单搜索"),
    DRIVER_GRABBING_WAYBILL_LIST("driverWaybillQueryGoodsAndRoundHandler", "司机抢单运单列表"),
    KNOWLEDGE_SEARCH_LOGS_HANDLER("knowledgeSearchLogsHandler", "知识库日志列表"),
    FBF_COMPLETE_STATUS_WAYBILL("fbfCompleteStatusWaybillHandler", "查询发布方是否有待结算的运单"),

    NET_CARGO_PAGE("netCargoPage", "发单管理分页查询"),
    NET_CARGO_COUNT("netCargoCount", "发单管理状态统计"),
    SUPERVISE_TOB_WAYBILL_LIST("superviseTobWaybillQueryListHandler", "监管订单搜索(tob)"),
    REPOST_STATUS_WAYBILL_LIST("repostStatusListHandler", "根据条件查询未上报的运单"),
    ;


    private final String type;

    private final String name;
}
