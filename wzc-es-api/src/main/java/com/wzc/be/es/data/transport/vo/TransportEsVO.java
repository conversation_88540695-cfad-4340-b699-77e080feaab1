package com.wzc.be.es.data.transport.vo;

import com.wzc.common.trans.annotation.CustomApiTrans;
import com.wzc.common.trans.enums.TransType;
import com.wzc.be.es.data.items.vo.ItemsVO;
import com.wzc.be.es.data.shipping.vo.ShippingVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023-06-13
 */
@Data
@ToString
public class TransportEsVO {

    private String id;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "第三方订单号")
    private String thirdNo;

    @Schema(description = "运输计划编号")
    private String transportNo;

    @Schema(description = "共享运输计划分组编号")
    private String groupNo;

    private String baseNo;

    @Schema(description = "运输计划状态")
    private Integer status;

    @Schema(description = "订单类型")
    private Integer orderType;

    @Schema(description = "停运申请状态")
    private Integer endStatus;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "公司id")
    private Long companyId;

    @Schema(description = "创建人id")
    private Long createId;

    @CustomApiTrans(transName = "shipperCompanyName",type = TransType.COMPANY_TYPE)
    @Schema(description = "托运人公司ID")
    private Long shipperCompanyId;

    @Schema(description = "托运人公司名称")
    private String shipperCompanyName;

    @Schema(description = "承运商id")
    private Long carrierId;

    @Schema(description = "承运商名称")
    private String carrierName;

    @Schema(description = "创建时间")
    private String createTime;

    private String startTime;

    private String endTime;

    @Schema(description = "来源")
    private Integer sourceType;

    @Schema(description = "运输方式")
    private Integer transportType;

    @Schema(description = "公司名称")
    private String companyName;

    private Integer assignType;

    private List<ItemsVO> items;

    private List<ShippingVO> sender;

    private List<ShippingVO> receiver;

    @Schema(description = "合同平台合同审核状态")
    private Integer contractStatus;

    @Schema(description = "接单时间")
    private String acceptTime;
}
