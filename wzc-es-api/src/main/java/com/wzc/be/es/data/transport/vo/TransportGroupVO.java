package com.wzc.be.es.data.transport.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @Date: 2023-06-13
 */
@Data
@ToString
public class TransportGroupVO {

    @Schema(description = "共享运输计划分组编号")
    private String transportNo;

    private String groupNo;

    @Schema(description = "承运商名称")
    private String carrierNames;

    @Schema(description = "创建时间")
    private String createTime;

    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "指派方式 1、共享余量 2、固定量")
    private Integer assignType;

    @Schema(description = "是否开启校验载重设备功能  1是，2否，默认否")
    private Integer loadDeviceCheck;

    @Schema(description = "接单时间")
    private String acceptTime;

}
