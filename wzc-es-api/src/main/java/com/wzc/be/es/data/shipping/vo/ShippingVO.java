package com.wzc.be.es.data.shipping.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Schema(description = "装卸货地址信息")
public class ShippingVO {

    @Schema(description = "装卸货地")
    private String address;

    @Schema(description = "详细地址")
    private String fullAddress;

    @Schema(description = "基本信息编号")
    private String baseNo;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "装卸货单位")
    private String customerName;

    @Schema(description = "联系人")
    private String name;

    @Schema(description = "联系电话")
    private String phone;
}
