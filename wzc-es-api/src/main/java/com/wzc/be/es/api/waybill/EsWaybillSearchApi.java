package com.wzc.be.es.api.waybill;

import com.baidu.mapcloud.cloudnative.common.model.Pagination;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.commons.PageVO;
import com.wzc.be.es.data.waybill.qo.SuperviseOrderPageQO;
import com.wzc.be.es.data.waybill.vo.SuperviseOrderPageVO;
import com.wzc.be.es.data.waybill.qo.*;
import com.wzc.be.es.data.waybill.vo.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年08月11日
 */
@Schema(description = "es运单搜索api")
public interface EsWaybillSearchApi {

    @PostMapping("/inner/api/es/v1/waybill/queryList")
    RestResponse<PageVO<WaybillQueryVO>> waybillQuery(@RequestBody DriverWaybillListQO qo);


    /**
     * 承运商PC端运单列表
     * @param qo requestPageQo
     * @return RestResponse<PageVO<CarrierWaybillListVO>>
     */
    @Operation(description = "承运商PC端运单列表")
    @PostMapping("/inner/api/es/v1/waybill/carrier/list")
    RestResponse<PageVO<CarrierWaybillListVO>> carrierWaybillList(@RequestBody CarrierWaybillListQO qo);

    /**
     * 司机开启运单校验是否有已开启运单查询
     * @param qo requestPageQo
     * @return RestResponse<WaybillQueryVO>
     */
    @PostMapping("/inner/api/es/v1/waybill/activate")
    RestResponse<WaybillQueryVO> activateWaybill(@RequestBody CheckActivateWaybillQO qo);

    /**
     * 司机已派车成功的运单查询
     * @param qo requestPageQo
     * @return RestResponse<WaybillQueryVO>
     */
    @PostMapping("/inner/api/es/v1/waybill/dispatch")
    RestResponse<List<WaybillQueryVO>> dispatchWaybill(@RequestBody CheckDispatchWaybillQO qo);

    /**
     * 小程序验货人运单列表
     * @param qo 查询参数
     * @return RestResponse<PageVO<InspectorWaybillListVO>>
     */
    @Operation(description = "小程序验货人运单列表")
    @PostMapping("/inner/api/es/v1/waybill/inspectorList")
    RestResponse<PageVO<InspectorWaybillListVO>> inspectorWaybillList(@RequestBody InspectorListQo qo);


    /**
     * 查询运单信息
     * @param qo 查询参数
     * @return RestResponse<WaybillQueryVO>
     */
    @Operation(description = "查询运单信息")
    @PostMapping("/inner/api/es/v1/waybill/select")
    RestResponse<WaybillQueryVO> selectWaybillInfo(@RequestBody SelectWaybillInfoQO qo);

    /**
     * 承运商列表tab页统计
     * @param qo 查询参数
     * @return RestResponse<CarrierTabGroupCountVO>
     */
    @Operation(description = "承运商列表tab页统计")
    @PostMapping("/inner/api/es/v1/waybill/carrierTabGroupCount")
    RestResponse<CarrierTabGroupCountVO> carrierTabGroupCount(@RequestBody CarrierTabGroupCountQO qo);

    /**
     * 承运商APP运单列表
     * @param qo requestPageQo
     * @return RestResponse<PageVO<InspectorWaybillListVO>>
     */
    @Operation(description = "承运商APP运单列表")
    @PostMapping("/inner/api/es/v1/waybill/carrierAppOrderList")
    RestResponse<PageVO<WaybillQueryVO>> carrierAppOrderList(@RequestBody CarrierAppOrderListQO qo);


    /**
     * OMS查询运单列表
     */
    @Operation(description = "OMS查询运单列表")
    @PostMapping("/inner/api/es/v1/waybill/omsSearchWaybill")
    RestResponse<List<WaybillQueryVO>> omsSearchWaybill(@RequestBody OmsSearchWaybillQO qo);

    /**
     * 货主PC运单列表
     */
    @Operation(description = "货主PC运单列表")
    @PostMapping("/inner/api/es/v1/waybill/ownerWaybillList")
    RestResponse<Pagination<SubWaybillVO>> ownerWaybillList(@RequestBody OwnerWaybillListQO qo);

    /**
     * 货主PC运单列表-深度查询
     */
    @Operation(description = "货主PC运单列表-深度查询")
    @PostMapping("/inner/api/es/v1/waybill/ownerWaybillListAfter")
    RestResponse<EsPageVO<SubWaybillVO>> ownerWaybillListAfter(@RequestBody OwnerWaybillListQO qo);

    /**
     * 承运商PC端运单列表-深度查询
     * @param qo requestPageQo
     * @return RestResponse<PageVO<CarrierWaybillListVO>>
     */
    @Operation(description = "承运商PC端运单列表-深度查询")
    @PostMapping("/inner/api/es/v1/waybill/carrier/carrierWaybillListAfter")
    RestResponse<EsPageVO<CarrierWaybillListVO>> carrierWaybillListAfter(@RequestBody CarrierWaybillListQO qo);

    /**
     * 货主列表tab页统计
     * @param qo 查询参数
     * @return RestResponse<CarrierTabGroupCountVO>
     */
    @Operation(description = "货主列表tab页统计")
    @PostMapping("/inner/api/es/v1/waybill/ownerTabGroupCount")
    RestResponse<CarrierTabGroupCountVO> ownerTabGroupCount(@RequestBody OwnerWaybillListQO qo);

    /**
     * 子运单查询
     * @param qo 查询参数
     */
    @Operation(description = "子运单查询")
    @PostMapping("/inner/api/es/v1/subWaybill/list")
    RestResponse<List<SubWaybillVO>> subWaybillList(@RequestBody SubWaybillQO qo);



    /**
     * 异常运单查询
     */
    @Operation(description = "异常运单查询")
    @PostMapping("/inner/api/es/v1/waybill/exceptionWaybillList")
    RestResponse<Pagination<SubWaybillVO>> exceptionWaybillList(@RequestBody ExceptionWaybillListQO qo);

    /**
     * 异常运单tab统计
     */
    @Operation(description = "异常运单tab统计")
    @PostMapping("/inner/api/es/v1/waybill/exceptionTabCount")
    RestResponse<ExceptionCountVO> exceptionTabCount(@RequestBody ExceptionWaybillListQO qo);

    /**
     * 校验司机运单状态
     */
    @Operation(description = "校验司机运单状态")
    @PostMapping("/inner/api/es/v1/waybill/checkDriverWaybill")
    RestResponse<Boolean> checkDriverWaybill(@RequestBody CheckDriverWaybillQO qo);


    /**
     * 订单号统计派车数
     */
    @Operation(description = "订单号统计派车数")
    @PostMapping("/inner/api/es/v1/waybill/orderCount")
    RestResponse<List<OrderCountVO>> orderCount(@RequestBody OrderCountQO qo);

    /**
     * 运输计划号统计派车数
     */
    @Operation(description = "运输计划号统计派车数")
    @PostMapping("/inner/api/es/v1/waybill/transportCount")
    RestResponse<List<TransportCountVO>> transportCount(@RequestBody TransportCountQO qo);

    /**
     * 公司运输统计
     */
    @Operation(description = "公司运输统计")
    @PostMapping("/inner/api/es/v1/waybillStatistics")
    RestResponse<WaybillStatisticsVO> waybillStatistics(@RequestBody WaybillStatisticsQO qo);


    /**
     * 网货发布方签收列表
     */
    @Operation(description = "网货发布方签收列表")
    @PostMapping("/inner/api/es/v1/waybill/fbfSignList")
    RestResponse<Pagination<FbfSignListVO>> fbfWaybillList(@RequestBody FbfSignListQO qo);

    /**
     * 网货发布方签收列表
     */
    @Operation(description = "网货发布方签收列表")
    @PostMapping("/inner/api/es/v1/waybill/fbfWaybillListExport")
    RestResponse<EsPageVO<FbfSignListVO>> fbfWaybillListExport(@RequestBody FbfSignListQO qo);

    /**
     * 根据货主公司id查询是否有运单
     *
     */
    @Operation(description = "根据货主公司id查询是否有运单")
    @PostMapping("/inner/api/es/v1/waybill/ownerWaybill")
    RestResponse<Boolean> ownerWaybill(@RequestBody OwnerWaybillQO qo);


    /**
     * 根据承运商公司id，运单号查询
     *
     */
    @Operation(description = "根据承运商公司id，运单号查询")
    @PostMapping("/inner/api/es/v1/waybill/waybillQueryList")
    RestResponse<List<SubWaybillVO>> waybillQueryList(@RequestBody SubWaybillQO qo);


    /**
     * 根据运输计划查询派车单列表
     */
    @Operation(description = "根据运输计划查询派车单列表")
    @PostMapping("/inner/api/es/v1/waybill/selectByPlanNo")
    RestResponse<Pagination<PlanWaybillVO>> selectByPlanNo(@RequestBody PlanWaybillQO qo);

    /**
     * 监管运单列表
     * @param requestPageQo
     * @return
     */
    @PostMapping("/inner/api/es/v1/waybill/superviseList")
    RestResponse<Pagination<SuperviseOrderPageVO>> superviseList(@RequestBody SuperviseOrderPageQO requestPageQo);

    /**
     * 监管运单列表导出
     * @param requestPageQo
     * @return
     */
    @PostMapping("/inner/api/es/v1/waybill/superviseListExport")
    RestResponse<EsPageVO<SuperviseOrderPageVO>> superviseListExport(@RequestBody SuperviseOrderPageQO requestPageQo);

    /**
     * 抢单相关运单查询
     * @param qo
     * @return
     */
    @Operation(description = "抢单相关运单查询")
    @PostMapping("/inner/api/es/v1/waybill/waybillQueryPage")
    RestResponse<EsPageVO<SubWaybillVO>> waybillQueryPage(@RequestBody SubWaybillQO qo);
    /**
     * 关联运单查询
     *
     */
    @Operation(description = "根据货主公司id查询是否有运单")
    @PostMapping("/inner/api/es/v1/waybill/relevanceWaybill")
    RestResponse<List<RelevanceWaybillVO>> relevanceWaybill(@RequestBody RelevanceWaybillQO qo);

    /**
     * 打卡补录校验司机车辆是否在时间段内开启了运单
     *
     */
    @Operation(description = "打卡补录校验司机车辆是否在时间段内开启了运单")
    @PostMapping("/inner/api/es/v1/waybill/checkDriverAndCarWaybill")
    RestResponse<Boolean> checkDriverAndCarWaybill(@RequestBody CheckDriverAndCarWaybillQO qo);

    /**
     * 司机抢单相关信息查询
     *
     */
    @Operation(description = "司机抢单相关信息查询")
    @PostMapping("/inner/api/es/v1/waybill/searchWaybillByGoodsNoAndRound")
    RestResponse<List<WaybillQueryVO>> searchWaybillByGoodsNoAndRound(@RequestBody DriverWaybillListQO qo);

    /**
     * 查询发布方是否有待结算的运单
     *
     */
    @Operation(description = "查询发布方是否有待结算的运单")
    @PostMapping("/inner/api/es/v1/waybill/fbfCompleteStatusWaybill")
    RestResponse<Boolean> fbfCompleteStatusWaybill(@RequestBody FbfCompleteStatusWaybillQO qo);


    /**
     * 监管运单列表
     * @param requestPageQo
     * @return
     */
    @PostMapping("/inner/api/es/v1/waybill/superviseTobList")
    RestResponse<Pagination<SuperviseOrderPageVO>> superviseTobList(@RequestBody SuperviseOrderPageQO requestPageQo);


    /**
     * 监管运单列表
     *
     * @param repostStatusQO
     * @return
     */
    @PostMapping("/inner/api/es/v1/waybill/reportStatusList")
    RestResponse<Pagination<SuperviseOrderPageVO>> reportStatusList(@RequestBody RepostStatusQO repostStatusQO);
}
