package com.wzc.be.es.data.waybill.enums;

import com.baidu.mapcloud.cloudnative.common.model.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @<PERSON> zhanwang
 * @Date 2023/11/10 0:47
 */
@Getter
@AllArgsConstructor
public enum CarReportStatusEnum implements IEnum {

    REPORT_WAIT(1, 1,"未上报"),
    REPORT_PASS(2, 3,"已上报"),
    REPORT_FAIL(3, 4,"上报失败"),
    REPORT_ING(4, 2,"上报中");

    private final Integer code;
    private final Integer driverCode;
    private final String name;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return this.name;
    }


    public static CarReportStatusEnum getEnum(Integer code) {
        if(null != code) {
            for (CarReportStatusEnum c : CarReportStatusEnum.values()) {
                if (c.getCode().equals(code)) {
                    return c;
                }
            }
        }
        return null;
    }
}
