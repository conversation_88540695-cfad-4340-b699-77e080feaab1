package com.wzc.be.es.data.waybill.qo;


import com.baidu.mapcloud.cloudnative.common.model.PageOrderType;
import com.baidu.mapcloud.cloudnative.common.model.PageQO;
import com.wzc.be.es.data.waybill.enums.DispatchQueryEnums;
import com.wzc.be.es.data.waybill.enums.DriverTabTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 司机运单列表搜索参数
 *
 * <AUTHOR> wei
 */
@Data
public class DriverWaybillListQO extends PageQO {

    @Schema(description = "TAB页切换")
    private DriverTabTypeEnum status;

    @Schema(description = "司机ID")
    private String driverId;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "查询方式")
    private DispatchQueryEnums queryType;

    @Schema(description = "查询值")
    private String queryValue;

    @Schema(description = "排序方式")
    private PageOrderType orderType;

    @Schema(description = "运单类型 默认 0全部 1销售 2采购 3调拨")
    private Integer waybillType;

    @Schema(description = "是否需要合同 0否 1是")
    private Integer contractRequire;

    @Schema(description = "签订合同 0未签订，1已签订")
    private Integer signContract;

    @Schema(description = "合同状态 0-待生成 1-待签约 30-已签约 101-线下签约")
    private Integer contractStatus;

    @Schema(description = "货源编号")
    private String goodsNo;

    @Schema(description = "轮次")
    private List<Integer> round;

    @Schema(description = "司机ID集合")
    private List<Long> driverIds;

    @Schema(description = "运单状态")
    private List<Integer> waybillStatus;
    /**
     * 评价 0待评价 1已评价
     */
    private Integer isEvaluate;
}
