package com.wzc.be.es.data.business.qo;

import com.baidu.mapcloud.cloudnative.common.model.PageQO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */

@Data
@Schema(
        description = "对账单分页查询请求实体类"
)
public class BusinessEsQueryPageQO extends PageQO {

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "运单号")
    private String dispatchNo;

    @Schema(description = "司机名称")
    private String driverName;

    @Schema(description = "车牌号")
    private String carNo;

    @Schema(description = "运输计划单号")
    private String transportNo;

    @Schema(description = "业务类型")
    private Integer businessType;

    @Schema(description = "订单类型")
    private Integer queryOrderType;

    @Schema(description = "发货单位")
    private String fmCustomerName;

    @Schema(description = "收货单位")
    private String toCustomerName;

    @Schema(description = "收款方审核状态")
    private Integer arAuditStatus;

    @Schema(description = "付款方审核状态")
    private Integer apAuditStatus;

    @Schema(description = "平台方审核状态")
    private Integer ptAuditStatus;

    @Schema(description = "审核状态")
    private Integer auditStatus;

    @Schema(description = "公司id")
    private Long companyId;

    @Schema(description = "托运公司id")
    private Long shipperCompanyId;

    @Schema(description = "承运商id")
    private Long carrierId;

    @Schema(description = "创建开始时间")
    private String startTime;

    @Schema(description = "创建结束时间")
    private String endTime;

    @Schema(description = "对账状态（0 未对账 1 已对账）")
    private Integer rcStatus;

    @Schema(description = "结算状态（0未结算 1已结算）")
    private Integer settlementStatus;

    @Schema(description = "运单号集合")
    private List<String> dispatchNoList;

    private List<String> transportNoList;

    @Schema(description = "审核日期类型 0.平台 1.货主")
    private Integer auditTimeType;

    @Schema(description = "审核开始时间")
    private String auditStartTime;

    private String shipperAuditStartTime;

    private String shipperAuditEndTime;

    private String apAuditStartTime;

    private String apAuditEndTime;

    @Schema(description = "审核结束时间")
    private String auditEndTime;

    @Schema(description = "审核状态集合")
    private List<Integer> auditStatusList;

    @Schema(description = "应收结算单号")
    private String arSettlementNo;

    @Schema(description = "应付结算单号")
    private String apSettlementNo;

    @Schema(description = "子运单号")
    private String subWaybillNo;

    private String rcStartTime;
    private String rcEndTime;

    private List<String> noInBusinessNoList;

}

