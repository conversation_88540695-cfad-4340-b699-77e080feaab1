package com.wzc.be.es.data.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "对账审核状态统计响应类")
@Data
public class EsRcAuditStatusCountVO {

    @Schema(description = "待审核")
    private Integer waitAudit = 0;

    @Schema(description = "审核通过")
    private Integer auditPass = 0;

    @Schema(description = "审核拒绝")
    private Integer auditReject = 0;

    @Schema(description = "全部")
    private Integer allCount = 0;
}
