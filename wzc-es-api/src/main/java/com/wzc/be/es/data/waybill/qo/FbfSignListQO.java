package com.wzc.be.es.data.waybill.qo;

import com.baidu.mapcloud.cloudnative.common.model.PageQO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 网货发布方签收列表
 */
@Data
public class FbfSignListQO extends PageQO {

    @Schema(description = "请求来源 1货主 2承运商 3平台")
    private Integer requestSource;

    @Schema(description = "运单号")
    private String waybillNo;

    @Schema(description = "运单号集合")
    private List<String> waybillNoList;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "子运单号")
    private String subWaybillNo;

    @Schema(description = "服务方姓名")
    private String fwfName;

    @Schema(description = "发布方")
    private String fbfName;

    @Schema(description = "承运商")
    private String carrierCompanyName;

    @Schema(description = "车牌号")
    private String carNo;

    @Schema(description = "货物名称")
    private String goodsName;

    @Schema(description = "发货商")
    private String fromCustomerName;

    @Schema(description = "发货地址")
    private String fromAddress;

    @Schema(description = "收货商")
    private String toCustomerName;

    @Schema(description = "收货地址")
    private String toAddress;

    @Schema(description = "日期类型 1派车时间 2司机运输时间 3司机卸货时间 4人工审核时间")
    private Integer dateType;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;

    @Schema(description = "发布方id")
    private Long fbfId;

    @Schema(description = "车主声明 0无需上传 1已上传 2未上传")
    private Integer czStatement;

    @Schema(description = "线路轨迹是否合规(1-是 2-否)")
    private Integer isLineLegal;

    @Schema(description = "时间是否合规(1-是 2-否)")
    private Integer isTimeLegal;

    @Schema(description = "榜单是否合规(1-是 2-否)")
    private Integer isPoundLegal;

    @Schema(description = "系统审核状态（1-通过、2-未通过）")
    private Integer systemCheckStatus;

    @Schema(description = "人工审核状态（0-未审核、1-通过、2-未通过）")
    private Integer manualCheckStatus;

    @Schema(description = "车队长收入合规性(1-是 2-否)")
    private Integer cdzIncomeLegal;

    @Schema(description = "装货场景照片是否合规(1-是 2-否)")
    private Integer isLoadImgLegal;

    @Schema(description = "卸货场景照片是否合规(1-是 2-否)")
    private Integer isUnLoadImgLegal;

    private Object[] lastSortValues;

    @Schema(description = "派车开始时间")
    private String dispatchStartTime;

    @Schema(description = "派车结束时间")
    private String dispatchEndTime;

    @Schema(description = "司机运输开始时间")
    private String tranStartTime;

    @Schema(description = "司机运输结束时间")
    private String tranEndTime;

    @Schema(description = "司机卸货开始时间")
    private String unlLoadStartTime;

    @Schema(description = "司机卸货结束时间")
    private String unlLoadEndTime;

    @Schema(description = "司机装货开始时间")
    private String loadStartTime;

    @Schema(description = "司机装货结束时间")
    private String loadEndTime;

    @Schema(description = "人工审核开始时间")
    private String manualCheckStartTime;

    @Schema(description = "人工审核结束时间")
    private String manualCheckEndTime;

    @Schema(description = "发布方签收状态 默认0 1已签收")
    private Integer fbfSignStatus;

    /**
     * 正常未审核：展示数据为全平台网货运单发布方已签收、系统审核状态为通过、人工审核状态为未审核的运单数据；
     * 异常未审核：展示数据为全平台网货运单发布方已签收、系统审核状态为未通过、人工审核状态为未审核的运单数据；
     * 已审核：展示数据为全平台网货运单发布方已签收、人工审核状态为已审核（通过、拒绝）的运单数据；
     */
    @Schema(description = "标签tab页切换查询 0正常未审核 1异常未审核 2已审核")
    private Integer auditTabStatus;

    /**
     * 待签收：司机卸货确认后，系统自动判断合规性后单据列表或发布方签收后平台人工审核异常后回退至此列表；
     * 待司机补录：如发布方发现运单存在上传图片异常，可回退司机进行图片重新上传，待司机重新上传单据列表；
     * 已签收：发布方签收后平台人工审核通过或待人工审核列表；
     */
    @Schema(description = "发布方标签tab页切换查询 0待签收 1待司机补录 2已签收")
    private Integer fbfTabStatus;

    @Schema(description = "是否查询异常运单")
    private boolean isAbnormal;

    @Schema(description = "托运公司")
    private Long companyId;

    @Schema(description = "货主公司")
    private Long shipperCompanyId;

    @Schema(description = "承运商公司")
    private Long carrierCompanyId;


    @Schema(description = "子运单状态")
    private List<Integer> subWaybillStatusList;

    @Schema(description = "冻结类型 1现金 2授信")
    private Integer freezeType;

    @Schema(description = "发布方签收时间开始时间")
    private String fbfSignTimeStartTime;

    @Schema(description = "发布方签收时间结束时间")
    private String fbfSignTimeEndTime;

    @Schema(description = "派车类型 1承运商派车 2货主派车")
    private Integer dispatchCarType;
}
