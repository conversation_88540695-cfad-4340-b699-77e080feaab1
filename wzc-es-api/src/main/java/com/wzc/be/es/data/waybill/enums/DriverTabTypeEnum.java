package com.wzc.be.es.data.waybill.enums;


import com.baidu.mapcloud.cloudnative.common.model.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 司机运单列表tab页切换枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DriverTabTypeEnum implements IEnum {


    STATUS_1(1,"待执行"),
    STATUS_2(2,"待签收"),
    STATUS_3(3,"待收款"),
    STATUS_4(4,"全部"),
    ;

    private final Integer code;
    private final String name;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return this.name;
    }
}
