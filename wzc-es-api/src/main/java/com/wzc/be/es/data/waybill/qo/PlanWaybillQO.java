package com.wzc.be.es.data.waybill.qo;


import com.wzc.common.base.PageInfoQo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PlanWaybillQO extends PageInfoQo {

    @Schema(description = "运输计划")
    private List<String> parentNoList;

    @Schema(description = "运单号")
    private String waybillNo;

    @Schema(description = "车牌号")
    private String carNo;

    @Schema(description = "司机姓名")
    private String driverName;

    @Schema(description = "状态")
    private List<Integer> status;

    @Schema(description = "是否二期数据 1是 2否")
    private Integer isSync;

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "结束时间")
    private String endTime;
}
