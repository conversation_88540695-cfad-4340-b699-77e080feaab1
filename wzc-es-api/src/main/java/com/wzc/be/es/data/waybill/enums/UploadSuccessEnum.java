package com.wzc.be.es.data.waybill.enums;

import com.baidu.mapcloud.cloudnative.common.model.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @<PERSON> <PERSON>
 * @Date 2023/11/10 1:04
 */
@Getter
@AllArgsConstructor
public enum UploadSuccessEnum implements IEnum {

    UPLOAD_FAIL(0, "失败"),
    UPLOAD_SUCCESS(1, "成功");

    private Integer code;
    private String name;

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
