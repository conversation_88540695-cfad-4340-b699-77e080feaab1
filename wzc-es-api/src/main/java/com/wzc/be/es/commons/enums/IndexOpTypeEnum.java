package com.wzc.be.es.commons.enums;

import com.baidu.mapcloud.cloudnative.common.model.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum IndexOpTypeEnum implements IEnum {

    /**
     * 索引对象操作类型
     */
    INSERT(1,"新增"),
    DELETE(2,"删除"),
    UPDATE(3,"修改")
    ;

    private final Integer code;
    private final String name;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return this.name;
    }
}
