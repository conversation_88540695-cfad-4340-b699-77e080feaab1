package com.wzc.be.es.data.waybill.enums;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@Schema(
        description = "派车单查询方法枚举"
)
public enum DispatchQueryEnums {

    ALL(0,"全部类型"),
    CAR_NO(1,"车牌号查询"),
    FORM_ADDRESS(2,"装货地查询"),
    TO_ADDRESS(3,"卸货地查询"),
    CARRIER(4,"承运商查询"),
    MATERIAL_NAME(5,"物料名称"),
    DISPATCH_CAR_NO(6,"运单号"),
    CONTRACT_NO(7,"合同编号"),
    ;

    private final Integer code;

    private final String name;
}
