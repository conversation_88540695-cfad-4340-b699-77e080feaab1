package com.wzc.be.es.data.business.qo;

import com.baidu.mapcloud.cloudnative.common.model.PageQO;
import com.wzc.be.es.data.order.qo.OrderPageQO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Schema(description = "对账单分页查询请求实体类")
public class BmsEsBusinessQueryPageQO extends PageQO {

    @Schema(description = "子运单号")
    private String subWaybillNo;

    @Schema(description = "业务类型")
    private Integer businessType;

    @Schema(description = "订单类型")
    private Integer queryOrderType;

    @Schema(description = "货主对账状态")
    private Integer rcStatus;

    @Schema(description = "货主对账开始时间")
    private String rcStartTime;

    @Schema(description = "货主对账结束时间")
    private String rcEndTime;

    @Schema(description = "审核状态")
    private Integer auditStatus;

    @Schema(description = "货主审核状态")
    private Integer apAuditStatus;

    @Schema(description = "承运商审核状态")
    private Integer arAuditStatus;

    @Schema(description = "平台审核状态")
    private Integer shipperAuditStatus;

    @Schema(description = "审核开始时间")
    private String auditStartTime;

    @Schema(description = "审核结束时间")
    private String auditEndTime;

    @Schema(description = "货主审核开始时间")
    private String apAuditStartTime;

    @Schema(description = "货主审核结束时间")
    private String apAuditEndTime;

    @Schema(description = "平台审核开始时间")
    private String shipperAuditStartTime;

    @Schema(description = "平台审核结束时间")
    private String shipperAuditEndTime;

    @Schema(description = "发货单位")
    private String fmCustomerName;

    @Schema(description = "收货单位")
    private String toCustomerName;

    @Schema(description = "运输计划单号")
    private String transportNo;

    @Schema(description = "司机名称")
    private String driverName;

    @Schema(description = "车牌号")
    private String carNo;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "创建开始时间")
    private String startTime;

    @Schema(description = "创建结束时间")
    private String endTime;

    @Schema(description = "分页查询tab类型")
    private Integer queryTabType;

    @Schema(description = "结算状态")
    private Integer settlementStatus;

    @Schema(description = "货主公司id")
    private Long companyId;

    @Schema(description = "平台公司id")
    private Long shipperCompanyId;

    @Schema(description = "承运商id")
    private Long carrierId;

    @Schema(description = "是否平台对上")
    private Boolean isShipperUp;

    @Schema(description = "应收结算单号")
    private String arSettlementNo;

    @Schema(description = "应付结算单号")
    private String apSettlementNo;

    @Schema(description = "付款方名称")
    private String apCompanyName;

    @Schema(description = "付款方名称")
    private String arCompanyName;

    @Schema(description = "货物名称")
    private String itemName;

    @Schema(description = "时间状态")
    private Integer clockInNode;

    @Schema(description = "运输开始时间")
    private String transportStartTime;

    @Schema(description = "运输结束时间")
    private String transportEndTime;

    @Schema(description = "运输方式")
    private Integer transportType;

    @Schema(description = "是否调量")
    private Integer isAdjustWeight;

    @Schema(description = "推送gs状态(平台自营推送状态，平台总包应付推送状态)")
    private Integer pushGsStatus;

    @Schema(description = "推送gs状态（平台总包应收推送状态）")
    private Integer pushGsStatusUp;

    @Schema(description = "过滤子运单号")
    private List<String> filterDispatchNos;

    @Schema(description = "运单来源")
    private Integer dispatchSource;

    @Schema(description = "发货地址")
    private String fmAddress;

    @Schema(description = "收货地址")
    private String toAddress;

    @Schema(description = "货主单价下限")
    private BigDecimal hzPriceMin;

    @Schema(description = "货主单价上限")
    private BigDecimal hzPriceMax;

    @Schema(description = "承运商单价下限")
    private BigDecimal ptPriceMin;

    @Schema(description = "承运商单价上限")
    private BigDecimal ptPriceMax;

    @Schema(description = "是否异常处理")
    private Integer  isAbnormal;

    /**********数据权限相关字段***************/
    @Schema(description = "订单类型集合")
    private List<String> orderTypeList;

    @Schema(description = "公司id数据权限集合")
    private List<String> companyList;

    @Schema(description = "关联用户id数据集合")
    private List<String> userIdList;

    @Schema(description = "部门id集合")
    private List<Long> depIdList;

    @Schema(description = "公司配置物料线路权限")
    private List<OrderPageQO.EsOtherQueryParam> companyAndOtherList;

    @Schema(description = "订单来源类型集合")
    private List<String> sourceTypeList;
}
