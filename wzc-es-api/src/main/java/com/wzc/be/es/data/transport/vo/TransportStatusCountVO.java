package com.wzc.be.es.data.transport.vo;

import lombok.Data;

/**
 * <p>
 * 运输计划统计响应
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
@Data
public class TransportStatusCountVO {

    /**
     * 全部数据
     */
    private Integer allCount = 0;

    /**
     * 待接单
     */
    private Integer unConfirmCount = 0;


    /**
     * 执行中数
     */
    private Integer executionCount = 0;

    /**
     * 已暂停
     */
    private Integer stopCount = 0;

    /**
     * 已完成
     */
    private Integer finishCount = 0;

    /**
     * 已取消
     */
    private Integer cancelCount = 0;

    /**
     * 已拒绝
     */
    private Integer refuseCount = 0;

    /**
     * 停运申请
     */
    private Integer endCount = 0;

    /**
     * 已删除
     */
    private Integer deleteCount = 0;


}
