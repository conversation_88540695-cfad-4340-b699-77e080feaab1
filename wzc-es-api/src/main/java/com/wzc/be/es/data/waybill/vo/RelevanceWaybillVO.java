package com.wzc.be.es.data.waybill.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class RelevanceWaybillVO {

    /**
     * 派车单号
     */
    @Schema(description = "派车单号")
    private String waybillNo;


    /**
     * 子运单号
     */
    @Schema(description = "子运单号")
    private String subWaybillNo;

    /**
     * 子运单状态
     */
    @Schema(description = "子运单状态")
    private Integer subWaybillStatus;

    /**
     * 司机id
     */
    @Schema(description = "司机id")
    private Long driverId;

    /**
     * 司机姓名
     */
    @Schema(description = "司机姓名")
    private String driverName;

    /**
     * 司机手机号
     */
    @Schema(description = "司机手机号")
    private String driverPhone;

    /**
     * 车辆id
     */
    @Schema(description = "车辆id")
    private Long carId;

    /**
     * 车牌号
     */
    @Schema(description = "车牌号")
    private String carNo;

    /**
     * 货物信息
     */
    @Schema(description = "货物信息")
    private List<Item> items;


    @Data
    public static class Item implements Serializable {
        private String id;
        private String itemId;
        private String itemName;
        private String speciesId;
    }


}
