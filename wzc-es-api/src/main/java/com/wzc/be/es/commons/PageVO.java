package com.wzc.be.es.commons;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(
        description = "分页返回类"
)
@Data
public class PageVO<T> {
    @Schema(
            description = "查询记录集",
            required = true
    )
    private List<T> content;
    @Schema(
            description = "当前页码",
            required = true
    )
    private Integer page;
    @Schema(
            description = "当前页码记录数",
            required = true
    )
    private Integer size;
    @Schema(
            description = "总记录数"
    )
    private Long total;
    @Schema(
            description = "总页码数"
    )
    private Integer totalPage;
    @Schema(
            description = "是否有下一页",
            required = false
    )
    private Boolean hasNext;
}
