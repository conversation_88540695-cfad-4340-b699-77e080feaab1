<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.wzc.be</groupId>
        <artifactId>wzc-es-base3.0</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>wzc-es-service</artifactId>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.wzc</groupId>
            <artifactId>wzc-common-web-base3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.mapcloud.cloudnative</groupId>
            <artifactId>job</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wzc</groupId>
            <artifactId>wzc-common-api-base3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wzc.be</groupId>
            <artifactId>wzc-es-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wzc.be</groupId>
            <artifactId>wzc-data-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.mapcloud.cloudnative</groupId>
            <artifactId>orm-es</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wzc</groupId>
            <artifactId>wzc-common-cache-base3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.mapcloud-standard</groupId>
            <artifactId>auth-data-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.wzc</groupId>
            <artifactId>wzc-common-kafka-base3</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wzc</groupId>
            <artifactId>wzc-common-easy-trans</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <!-- **表示多层目录，*表示一级目录 -->
                    <include>**/*.xml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
