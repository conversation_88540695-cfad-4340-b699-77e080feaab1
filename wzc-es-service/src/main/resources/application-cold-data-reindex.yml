# ES冷数据Reindex迁移配置示例

es:
  cold:
    data:
      offline:
        # 基础配置
        enabled: true                           # 是否启用冷数据下线功能
        reindexMode: true                       # 启用Reindex模式（true=Reindex迁移，false=Kafka发送）
        retentionMonths: 12                     # 数据保留月数（默认12个月）
        
        # 性能配置
        pageSize: 1000                          # 批量处理大小
        deleteBatchSize: 500                    # 批量删除大小
        indexIntervalMs: 5000                   # 索引处理间隔时间（毫秒）
        batchIntervalMs: 200                    # 批次处理间隔时间（毫秒）
        queryTimeoutSeconds: 30                 # ES查询超时时间（秒）
        scrollTimeoutMinutes: 5                 # Scroll查询超时时间（分钟）
        
        # Reindex配置
        reindexBatchSize: 1000                  # Reindex批量大小
        reindexTimeoutMinutes: 30               # Reindex超时时间（分钟）

        # 异步任务配置
        taskPollIntervalSeconds: 10             # 异步任务轮询间隔（秒）
        taskMaxWaitMinutes: 120                 # 异步任务最大等待时间（分钟）
        
        # 冷数据索引配置
        coldIndexShards: 1                      # 冷数据索引分片数
        coldIndexReplicas: 0                    # 冷数据索引副本数
        coldIndexRetentionYears: 5              # 冷数据索引保留年数
        
        # 自动化配置
        autoOptimizeColdIndex: true             # 是否自动优化冷数据索引设置
        autoCreateColdAlias: true               # 是否自动创建冷数据别名
        
        # 业务状态配置
        order:
          completedStatus: 6                    # 订单完成状态值
          settlementCompletedStatus: 5          # 订单结算完成状态值（已支付）
        subWaybill:
          completedStatus: 6                    # 子运单完成状态值
          settlementCompletedStatus: 5          # 子运单结算完成状态值（已支付）

# 索引配置
order:
  search:
    index: order_index01

transport:
  search:
    index: transport_index01
  group:
    no:
      search:
        index: group_transport_index

waybill:
  search:
    index: sub_waybill_index01

csc:
  warn:
    exception:
      search:
        index: csc_warn_exception_index

# XXL-Job配置示例
xxl:
  job:
    admin:
      addresses: http://xxl-job-admin:8080/xxl-job-admin
    executor:
      appname: es-service
      address: 
      ip: 
      port: 9999
      logpath: /data/applogs/xxl-job/jobhandler
      logretentiondays: 30
    accessToken: default_token

# 日志配置
logging:
  level:
    com.wzc.be.es.adapter.job.EsColdDataReindexJob: INFO
    com.wzc.be.es.adapter.service.ColdDataIndexService: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 示例：不同环境的配置差异
---
spring:
  profiles: dev
es:
  cold:
    data:
      offline:
        retentionMonths: 6                      # 开发环境保留6个月
        coldIndexRetentionYears: 2              # 冷数据保留2年
        indexIntervalMs: 1000                   # 更短的间隔时间

---
spring:
  profiles: test
es:
  cold:
    data:
      offline:
        retentionMonths: 3                      # 测试环境保留3个月
        coldIndexRetentionYears: 1              # 冷数据保留1年
        pageSize: 500                           # 更小的批量大小

---
spring:
  profiles: prod
es:
  cold:
    data:
      offline:
        retentionMonths: 12                     # 生产环境保留12个月
        coldIndexRetentionYears: 5              # 冷数据保留5年
        pageSize: 2000                          # 更大的批量大小
        reindexBatchSize: 2000                  # 更大的Reindex批量
        indexIntervalMs: 10000                  # 更长的间隔时间
