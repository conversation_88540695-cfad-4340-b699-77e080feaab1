apollo.bootstrap.namespaces=application,openfeign,web,redis,kafka
apollo.meta=http://**********:30080
apollo.configService=${apollo.meta}
apollo.cluster=zt
logging.config=classpath:logback-local.xml
#
#service.auth.url=http://**********:31886/
#zt.auth.url=http://**********:31886/
#service.user.url=http://**********:31884
#zt.user.url=http://**********:31884
#
#
#easy-es.address = ***********:9200
#easy-es.username = elastic
#easy-es.password = wzcDevEs@2022
#easy-es.banner = false
#
#es.insert.dataTo.redis.expireTime = 3000
#es.delete.dataTo.redis.expireTime = 3000
#es.update.dataTo.redis.expireTime = 3000
#
##easy-es.address = ***********:9200
##easy-es.username = elastic
##easy-es.password = XPH49=N3FqOE!-Kw
#
#
#spring.redis.port = 63791
#spring.redis.database = 1
#spring.redis.timeout = 60000
#cloud.native.redis.enabled = true
#spring.redis.host = ***********
#spring.redis.password = ^5b6M>hw47f
