/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.core.business.handler;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.core.business.index.BusinessIndex;
import com.wzc.be.es.data.business.qo.BmsEsBusinessQueryPageQO;
import com.wzc.be.es.data.business.qo.BusinessEsQueryPageQO;
import com.wzc.be.es.data.business.vo.BusinessEsVO;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023年10月13日
 * bms对账单列表查询
 */
@Slf4j
@Component
public class BmsReconciliationListSearchHandler extends BmsBusinessBaseSearchHandler<BmsEsBusinessQueryPageQO, List<BusinessEsVO>> {

    @Value("${bms.business.search.index:bms_business_index}")
    private String index;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Override
    public RestResponse<List<BusinessEsVO>> search(BmsEsBusinessQueryPageQO queryPageQo) {
        log.info("对账单列表模糊查询 search param is : {}", JSONUtil.toJsonStr(queryPageQo));
        try {
            SearchRequest searchRequest = new SearchRequest(getIndex());
            SearchSourceBuilder sourceBuilder = generateSearchSourceBuilder(queryPageQo);
            searchRequest.source(sourceBuilder);

            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            SearchHit[] hitResult = searchHits.getHits();
            JSONArray array = new JSONArray();
            Stream.of(hitResult).forEach(itemHit -> {
                Map<String, Object> sourceAsMap = itemHit.getSourceAsMap();
                array.add(sourceAsMap);
            });
            List<BusinessEsVO> businessEsVoList = JSONUtil.toList(array, BusinessEsVO.class);
            return RestResponse.success(businessEsVoList);
        } catch (Exception e) {
            log.error("对账单列表查询异常.search error", e);
        }
        return RestResponse.error(ExcepitonEnum.ERROR_UNKNOW);
    }

    /**
     * 查询条件组装
     *
     * @param queryPageQo 请求参数
     * @return LambdaEsQueryWrapper
     */
    private SearchSourceBuilder generateSearchSourceBuilder(BmsEsBusinessQueryPageQO queryPageQo) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder mainBoolQueryBuilder = getMainBoolQueryBuilder(queryPageQo);

        sourceBuilder.fetchSource(FieldUtils.val(BusinessIndex::getBusinessId),null);
        sourceBuilder.query(mainBoolQueryBuilder);
        //默认倒序排序
        SortOrder sortOrder = SortOrder.DESC;
        sourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(BusinessIndex::getUpdateTime)).order(sortOrder));
        sourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
        sourceBuilder.trackTotalHits(true);

        return sourceBuilder;
    }

    @Override
    public String getSearchType() {
        return SearchTypeEnums.BUSINESS_RC_YD_LIST.getType();
    }

    @Override
    public String getIndex() {
        return index;
    }


}
