/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.common.handler;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022年10月13日 3:34 PM
 */
public interface SearchHandler {
    RestResponse search(JSONObject json);

    String getSearchType();

    String getIndex();

    default String wildcardsExp(String value) {
        return "*" + value + "*";
    }

    default String formatDateTime(Date date) {
        String format = DateUtil.format(date, DatePattern.NORM_DATETIME_FORMAT);
        return format;
    }

    default String formatYYYYMMDDDateTime(Date date) {
        String format = DateUtil.format(date, DatePattern.NORM_DATE_FORMAT);
        return format;
    }


    default double formatDoubleScale(Double value) {
        double roundedValue = new BigDecimal(value).setScale(2, RoundingMode.HALF_UP).doubleValue();
        return roundedValue;
    }

}
