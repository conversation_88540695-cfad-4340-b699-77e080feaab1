package com.wzc.be.es.core.business.service;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.common.service.EsBaseSearchService;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.data.business.qo.BusinessEsQueryPageQO;
import com.wzc.be.es.data.business.qo.TocBusinessEsPageQO;
import com.wzc.be.es.data.business.vo.BmsBusinessEsVO;
import com.wzc.be.es.data.business.vo.BmsEsAuditStatusCountVO;
import com.wzc.be.es.data.business.vo.BmsEsRcStatusCountVO;
import com.wzc.be.es.data.business.vo.TocEsBusinessVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class EsTocBusinessSearchService extends EsBaseSearchService {

        /**
     * 网货对账单分页查询
     * @param requestPageQo
     * @return
     */
    public RestResponse<EsPageVO<TocEsBusinessVO>> tocReconciliationPage(TocBusinessEsPageQO requestPageQo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.TOC_BUSINESS_RC_YD_PAGE.getType());
        return searchHandler.search(requestPageQo);
    }
}
