package com.wzc.be.es.adapter.action.business;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.api.business.EsBmsBusinessSearchApi;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.core.business.service.EsBmsBusinessSearchService;
import com.wzc.be.es.data.business.qo.BmsEsBusinessQueryPageQO;
import com.wzc.be.es.data.business.vo.BmsBusinessEsVO;
import com.wzc.be.es.data.business.vo.BmsEsAuditStatusCountVO;
import com.wzc.be.es.data.business.vo.BmsEsRcStatusCountVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class BmsBusinessAction implements EsBmsBusinessSearchApi {

    private final EsBmsBusinessSearchService businessSearchService;

    /**
     * 对账单分页查询
     *
     * @param qo
     * @return
     */
    @Override
    public RestResponse<EsPageVO<BmsBusinessEsVO>> page(BmsEsBusinessQueryPageQO qo) {
        return businessSearchService.page(qo);
    }

    /**
     * 对账单对账状态统计
     *
     * @param requestPageQo
     * @return
     */
    @Override
    public RestResponse<BmsEsRcStatusCountVO> rcStatusCount(BmsEsBusinessQueryPageQO requestPageQo) {
        return businessSearchService.rcStatusCount(requestPageQo);
    }

    /**
     * 对账单审核状态统计
     *
     * @param requestPageQo
     * @return
     */
    @Override
    public RestResponse<BmsEsAuditStatusCountVO> auditStatusCount(BmsEsBusinessQueryPageQO requestPageQo) {
        return businessSearchService.rcAuditStatusCount(requestPageQo);
    }
}
