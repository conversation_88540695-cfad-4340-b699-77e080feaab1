package com.wzc.be.es.common.enums;

import com.baidu.mapcloud.cloudnative.common.model.BizError;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ExcepitonEnum implements BizError {
    /**
     * 错误码，对应错误信息
     */
    ERROR_UNKNOW(44001, "异常"),
    PARAM_IS_INVALID(44002, "参数错误"),
    PARSE_JOSN_ERROR(44003, "json转换错误"),
    AUTH_CHECK_ERROR(44004, "未配置货主企业数据权限");


    private final Integer code;
    private final String msg;
}



