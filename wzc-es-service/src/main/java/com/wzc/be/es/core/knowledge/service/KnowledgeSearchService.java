package com.wzc.be.es.core.knowledge.service;

import com.baidu.mapcloud.cloudnative.common.model.Pagination;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.common.service.EsBaseSearchService;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.data.knowledge.qo.KnowledgeSearchQO;
import com.wzc.be.es.data.knowledge.vo.KnowledgeSearchVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/12 17:18
 */
@Component
@Slf4j
public class KnowledgeSearchService extends EsBaseSearchService {

    public RestResponse<EsPageVO<KnowledgeSearchVO>> knowledgeLogsSearch(KnowledgeSearchQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.KNOWLEDGE_SEARCH_LOGS_HANDLER.getType());
        return searchHandler.search(qo);
    }
}
