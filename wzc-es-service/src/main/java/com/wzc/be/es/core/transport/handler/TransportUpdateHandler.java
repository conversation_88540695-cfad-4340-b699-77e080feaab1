/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.core.transport.handler;

import cn.easyes.core.toolkit.FieldUtils;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.google.common.collect.Maps;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.core.transport.index.TransportIndex;
import com.wzc.be.es.data.transport.qo.TransportQueryPageQO;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

import static com.wzc.be.es.commons.enums.SearchTypeEnums.TRANSPORT_UPDATE;

/**
 * <AUTHOR>
 * @date 2023年07月05日
 * 运输计划修改
 */
@Slf4j
@Component
public class TransportUpdateHandler extends TransportBaseSearchHandler<TransportQueryPageQO, Void> {

    @Resource
    private EsIndexProperties esIndexProperties;

    @Resource
    private RestHighLevelClient restHighLevelClient;

    @Override
    public RestResponse<Void> search(TransportQueryPageQO transportQueryPageQo) {
        try {
            UpdateByQueryRequest updateByQueryRequest = new UpdateByQueryRequest();
            // 更新时版本冲突
            updateByQueryRequest.setConflicts("proceed");
            updateByQueryRequest.indices(esIndexProperties.getTransportIndex());
            updateByQueryRequest.setQuery(new TermQueryBuilder(FieldUtils.val(TransportIndex::getTransportNo), transportQueryPageQo.getTransportNo()));
            Map<String,Object> paramsMap = Maps.newHashMap();
            paramsMap.put("status",transportQueryPageQo.getStatus());
            updateByQueryRequest.setScript(new Script(ScriptType.INLINE, "painless",
                    "ctx._source.status = params.status", paramsMap));
            //数据为存储而不是更新
            restHighLevelClient.updateByQuery(updateByQueryRequest, RequestOptions.DEFAULT);
            return RestResponse.success();
        } catch (Exception e) {
            log.error("运输计划更新异常.search error", e);
            return RestResponse.error(ExcepitonEnum.ERROR_UNKNOW);
        }
    }

    @Override
    public String getSearchType() {
        return TRANSPORT_UPDATE.getType();
    }

    @Override
    public String getIndex() {
        return esIndexProperties.getTransportIndex();
    }


}
