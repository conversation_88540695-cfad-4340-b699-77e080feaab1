package com.wzc.be.es.adapter.data;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.data.data.es.warn.dto.CscWarnExceptionDTO;
import com.wzc.be.data.data.es.warn.dto.CscWarnExceptionEsPageDTO;
import com.wzc.be.es.adapter.data.feign.DwdCscWarnExceptionIndexFeign;
import com.wzc.be.es.core.warn.converter.WarnConverter;
import com.wzc.be.es.data.warn.qo.CscWarnExceptionEsPageQO;
import com.wzc.be.es.data.warn.vo.CscWarnExceptionVO;
import com.wzc.common.core.api.utils.ApiResultUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DwdCscWarnExceptionIndexClient {

    private final DwdCscWarnExceptionIndexFeign dwdCscWarnExceptionIndexFeign;

    public List<CscWarnExceptionVO> search(CscWarnExceptionEsPageQO queryPageQo) {
        CscWarnExceptionEsPageDTO cscWarnExceptionDTO = WarnConverter.INSTANCE.exceptionQOToDTO(queryPageQo);
        RestResponse<List<CscWarnExceptionDTO>> search = dwdCscWarnExceptionIndexFeign.search(cscWarnExceptionDTO);
        List<CscWarnExceptionDTO> cscWarnExceptionDTOList = ApiResultUtils.getDataOrThrow(search);
        return WarnConverter.INSTANCE.exceptionDTOToVO(cscWarnExceptionDTOList);
    }

}
