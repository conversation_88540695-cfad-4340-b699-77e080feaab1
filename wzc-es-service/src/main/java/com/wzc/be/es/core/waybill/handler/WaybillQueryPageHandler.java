package com.wzc.be.es.core.waybill.handler;

import cn.easyes.core.biz.EsPageInfo;
import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import cn.hutool.core.util.ObjectUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.enums.ContractStatusEnums;
import com.wzc.be.es.common.enums.SubWaybillStatusEnum;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.core.waybill.converter.WaybillConverter;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.core.waybill.mapper.SubWaybillListMapper;
import com.wzc.be.es.data.waybill.qo.SubWaybillQO;
import com.wzc.be.es.data.waybill.vo.SubWaybillVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class WaybillQueryPageHandler implements BaseSearchHandler<SubWaybillQO, EsPageVO<SubWaybillVO>> {

    @Value("${waybillQueryHandler.search.type:waybillQueryPageHandler}")
    private String searchType;

    @Resource
    private EsIndexProperties esIndexProperties;

    @Resource
    private SubWaybillListMapper subWaybillListMapper;

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.esIndexProperties.getSubWaybillIndex();
    }


    @Override
    public RestResponse<EsPageVO<SubWaybillVO>> search(SubWaybillQO qo) {
//        AssertsUtils.isBlank(qo.getUnLoadStartTime(), ExcepitonEnum.ERROR_UNKNOW,"卸货确认开始时间不能为空");
//        AssertsUtils.isBlank(qo.getUnLoadEndTime(), ExcepitonEnum.ERROR_UNKNOW,"卸货确认结束时间不能为空");
        LambdaEsQueryWrapper<SubWaybillIndex> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.index(esIndexProperties.getSubWaybillIndex());
        genSearch(wrapper,qo);
        EsPageInfo<SubWaybillIndex> esPageInfo = subWaybillListMapper.pageQuery(wrapper, qo.getPage(), qo.getSize());
        EsPageVO<SubWaybillVO> page = new EsPageVO<>();
        List<SubWaybillVO> list = WaybillConverter.INSTANCE.toSubWaybillVO(esPageInfo.getList());
        page.setList(list);
        page.setTotal(esPageInfo.getTotal());
        return RestResponse.success(page);
    }


    private void genSearch(LambdaEsQueryWrapper<SubWaybillIndex> wrapper,SubWaybillQO qo){
        wrapper.isNotNull(SubWaybillIndex::getSubWaybillNo);
        if(StringUtils.isNotEmpty(qo.getGoodsNo())){
            wrapper.eq("goodsNo.keyword",qo.getGoodsNo());
        }

        if(StringUtils.isNotEmpty(qo.getDriverName())){
            wrapper.like(SubWaybillIndex::getDriverName,qo.getDriverName());
        }

        if(CollectionUtils.isNotEmpty(qo.getCompanyIdList())){
            wrapper.in(SubWaybillIndex::getCarrierCompanyId,qo.getCompanyIdList());
        }

        if(CollectionUtils.isNotEmpty(qo.getDispatchCarNoList())){
            wrapper.in(SubWaybillIndex::getWaybillNo,qo.getDispatchCarNoList());
        }

        if(CollectionUtils.isNotEmpty(qo.getStatus())){
            wrapper.in(SubWaybillIndex::getSubWaybillStatus,qo.getStatus());
        }
        if(ObjectUtil.isNotNull(qo.getIsSync())){
            wrapper.eq(SubWaybillIndex::getIsSync,qo.getIsSync());
        }
        if(StringUtils.isNotBlank(qo.getUnLoadStartTime()) && StringUtils.isNotBlank(qo.getUnLoadEndTime())){
            wrapper.ge(SubWaybillIndex::getTransEndTime, qo.getUnLoadStartTime());
            wrapper.le(SubWaybillIndex::getTransEndTime, qo.getUnLoadEndTime());
        }

        if(StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())){
            wrapper.ge(SubWaybillIndex::getTransStartTime, qo.getStartTime());
            wrapper.le(SubWaybillIndex::getTransStartTime, qo.getEndTime());
        }

        if(StringUtils.isNotBlank(qo.getCarrierCompanyName())){
            wrapper.like(SubWaybillIndex::getCarrierCompanyName,qo.getCarrierCompanyName());
        }

        if(StringUtils.isNotBlank(qo.getDispatchCarNo())){
            wrapper.like(SubWaybillIndex::getWaybillNo,qo.getDispatchCarNo());
        }

        if (StringUtils.isNotBlank(qo.getAllWord())) {
            wrapper.ge(SubWaybillIndex::getTransStartTime, qo.getAllWord())
                    .or().le(SubWaybillIndex::getTransStartTime, qo.getAllWord())
                    .or().eq(SubWaybillIndex::getWaybillNo, qo.getAllWord())
                    .or().eq(SubWaybillIndex::getDriverName, qo.getAllWord())
                    .or().eq(SubWaybillIndex::getStatus, qo.getAllWord())
                    .or().eq(SubWaybillIndex::getContractStatus, qo.getAllWord());
        }

        if (ObjectUtil.isNotNull(qo.getContractStatus())) {
            if ( ContractStatusEnums.YES.getCode().equals(qo.getContractStatus())) {
                wrapper.eq(SubWaybillIndex::getContractStatus, ContractStatusEnums.YES.getCode());
            }else {
                wrapper.ne(SubWaybillIndex::getContractStatus, ContractStatusEnums.YES.getCode());
            }
        }

        if (ObjectUtil.isNotNull(qo.getTabType())) {
            switch (qo.getTabType()) {
                case 1 :
                    wrapper.eq(SubWaybillIndex::getStatus, SubWaybillStatusEnum.WAIT_START.getCode());
                    break;
                case 2 :
                    wrapper.in(SubWaybillIndex::getStatus, List.of(SubWaybillStatusEnum.WAIT_APPOINTMENT_LOAD.getCode(), SubWaybillStatusEnum.WAIT_LOAD_SIGN.getCode(),
                            SubWaybillStatusEnum.WAIT_LOAD.getCode(), SubWaybillStatusEnum.WAIT_APPOINTMENT_UNLOAD.getCode(),
                            SubWaybillStatusEnum.WAIT_UNLOAD_SIGN.getCode(), SubWaybillStatusEnum.WAIT_UNLOAD.getCode()));
                    break;
                case 3 :
                    wrapper.eq(SubWaybillIndex::getStatus, SubWaybillStatusEnum.WAIT_SIGN.getCode());
                    break;
                case 4 :
                    wrapper.eq(SubWaybillIndex::getStatus, SubWaybillStatusEnum.FINISH.getCode());
                    break;
                case 5 :
                    wrapper.eq(SubWaybillIndex::getStatus, SubWaybillStatusEnum.CANCELLED.getCode());
                    break;
                case 6 :
                    wrapper.eq(SubWaybillIndex::getCompleteStatus,3);
                    wrapper.eq("fbfSignStatus",1);
                    break;
            }
        }
    }

}
