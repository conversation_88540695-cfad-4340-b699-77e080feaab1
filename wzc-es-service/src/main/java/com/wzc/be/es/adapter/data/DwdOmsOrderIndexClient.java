package com.wzc.be.es.adapter.data;

import cn.hutool.core.collection.CollectionUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.data.data.es.order.dto.OrderIndexDTO;
import com.wzc.be.data.data.es.order.dto.OrderPageDTO;
import com.wzc.be.data.data.es.transport.dto.CommonRedisResultDTO;
import com.wzc.be.es.adapter.data.feign.DwdOmsOrderIndexFeign;
import com.wzc.be.es.common.utils.DataUtils;
import com.wzc.be.es.core.common.CommonRedisResultVO;
import com.wzc.be.es.core.order.converter.OrderConverter;
import com.wzc.be.es.core.order.index.OrderIndex;
import com.wzc.be.es.data.order.qo.OrderPageQO;
import com.wzc.be.es.data.order.vo.OrderIndexVO;
import com.wzc.common.core.api.utils.ApiResultUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DwdOmsOrderIndexClient {

    private final DwdOmsOrderIndexFeign dwdOmsOrderIndexFeign;

    public List<OrderIndex> search(OrderPageQO orderPageQo,List<CommonRedisResultVO> commonRedisResultVOList) {
        OrderPageDTO orderPageDTO = OrderConverter.INSTANCE.qoToDTO(orderPageQo);
        if(commonRedisResultVOList != null){
            orderPageDTO.setCommonRedisResultVOList(DataUtils.esRedisResultVOToDataDTO(commonRedisResultVOList));
        }
        RestResponse<List<OrderIndexDTO>> search = dwdOmsOrderIndexFeign.search(orderPageDTO);
        List<OrderIndexDTO> orderIndexDTOList = ApiResultUtils.getDataOrThrow(search);
        return OrderConverter.INSTANCE.dtoListToVOList(orderIndexDTOList);
    }

}
