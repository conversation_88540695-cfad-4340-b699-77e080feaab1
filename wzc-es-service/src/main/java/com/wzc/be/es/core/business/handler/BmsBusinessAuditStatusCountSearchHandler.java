package com.wzc.be.es.core.business.handler;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.enums.AuditStatusEnums;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.commons.enums.BusinessQueryTabTypeEnums;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.core.common.CommonRedisResultVO;
import com.wzc.be.es.data.business.qo.BmsEsBusinessQueryPageQO;
import com.wzc.be.es.data.business.vo.BmsEsAuditStatusCountVO;
import com.wzc.common.user.utils.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.wzc.be.es.common.constant.Constants.UPDATE_INDEX_REDIS_KEY;

@Slf4j
@Component
public class BmsBusinessAuditStatusCountSearchHandler extends BmsBusinessBaseSearchHandler<BmsEsBusinessQueryPageQO, BmsEsAuditStatusCountVO> {

    @Value("${bms.business.search.index:bms_business_index}")
    private String index;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    public RestResponse<BmsEsAuditStatusCountVO> search(BmsEsBusinessQueryPageQO qo) {
        log.info("对账单审核状态统计参数：{}", JSONUtil.toJsonStr(qo));
        SearchRequest searchRequest = new SearchRequest(index);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(0);
        searchSourceBuilder.timeout(new TimeValue(60, TimeUnit.SECONDS));
        searchSourceBuilder.trackTotalHits(true);
        BmsEsAuditStatusCountVO esAuditStatusCountVO = new BmsEsAuditStatusCountVO();
        try {
            Long companyId = null;
            if (UserUtils.isShipperPc()) {
                companyId = qo.getCompanyId();
            }
            if (UserUtils.isOperatePc()) {
                companyId = qo.getShipperCompanyId();
            }
            if (UserUtils.isIdentityCarrier()) {
                companyId = qo.getCarrierId();
            }
            RMapCache<String, String> mapCache = redissonClient.getMapCache(UPDATE_INDEX_REDIS_KEY + index + ":" + companyId);
            List<CommonRedisResultVO> commonRedisResultVOList = mapCache.readAllEntrySet().stream().map(e -> JSONUtil.toBean(e.getValue(), CommonRedisResultVO.class)).collect(Collectors.toList());
            // 全部
            qo.setQueryTabType(BusinessQueryTabTypeEnums.ALL.getCode());
            qo.setFilterDispatchNos(null);
            BoolQueryBuilder allBoolQueryBuilder = super.getMainBoolQueryBuilder(qo);
            searchSourceBuilder.query(allBoolQueryBuilder);
            searchRequest.source(searchSourceBuilder);
            SearchResponse allSearchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            TotalHits allTotalHits = allSearchResponse.getHits().getTotalHits();
            esAuditStatusCountVO.setAllCount(Convert.toInt(allTotalHits.value));
            // 待审核
            qo.setQueryTabType(BusinessQueryTabTypeEnums.UN_AUDIT.getCode());
            List<String> filterDispatchNos = commonRedisResultVOList.stream().filter(c -> ObjectUtil.equals(c.getStatus(), AuditStatusEnums.TO_BE_REVIEWED.getCode())).map(CommonRedisResultVO::getUnionNo).collect(Collectors.toList());
            qo.setFilterDispatchNos(filterDispatchNos);
            BoolQueryBuilder toBeRevieweBoolQueryBuilder = super.getMainBoolQueryBuilder(qo);
            searchSourceBuilder.query(toBeRevieweBoolQueryBuilder);
            searchRequest.source(searchSourceBuilder);
            SearchResponse toBeReviewedSearchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            TotalHits toBeReviewedTotal = toBeReviewedSearchResponse.getHits().getTotalHits();
            esAuditStatusCountVO.setToBeReviewedCount(Convert.toInt(toBeReviewedTotal.value));
            // 已审核
            qo.setQueryTabType(BusinessQueryTabTypeEnums.AUDIT.getCode());
            qo.setFilterDispatchNos(null);
            BoolQueryBuilder passTheExaminationBoolQueryBuilder = super.getMainBoolQueryBuilder(qo);
            searchSourceBuilder.query(passTheExaminationBoolQueryBuilder);
            searchRequest.source(searchSourceBuilder);
            SearchResponse passTheExaminationSearchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            TotalHits passTheExaminationTotal = passTheExaminationSearchResponse.getHits().getTotalHits();
            esAuditStatusCountVO.setPassTheExaminationCount(Convert.toInt(passTheExaminationTotal.value));
            return RestResponse.success(esAuditStatusCountVO);
        } catch (Exception e) {
            log.error("对账单审核状态统计异常: ", e);
            return RestResponse.error(ExcepitonEnum.ERROR_UNKNOW);
        }
    }

    @Override
    public String getSearchType() {
        return SearchTypeEnums.RC_AUDIT_STATUS_COUNT.getType();
    }

    @Override
    public String getIndex() {
        return index;
    }
}
