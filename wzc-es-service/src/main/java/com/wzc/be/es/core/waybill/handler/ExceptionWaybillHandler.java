package com.wzc.be.es.core.waybill.handler;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baidu.mapcloud.cloudnative.common.model.PageOrderType;
import com.baidu.mapcloud.cloudnative.common.model.Pagination;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.data.data.waybill.model.dto.WaybillQueryEntityDTO;
import com.wzc.be.es.adapter.client.waybill.WaybillDorisFeignClient;
import com.wzc.be.es.common.constant.Constants;
import com.wzc.be.es.core.waybill.converter.DorisQueryWaybillConverter;
import com.wzc.be.es.core.waybill.converter.WaybillConverter;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.data.waybill.qo.ExceptionWaybillListQO;
import com.wzc.be.es.data.waybill.vo.SubWaybillVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.pipeline.BucketSortPipelineAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

/**
 * 异常运单相关
 *
 * <AUTHOR> Wei
 */
@Slf4j
@Component
public class ExceptionWaybillHandler extends ExceptionBaseHandler<ExceptionWaybillListQO, Pagination<SubWaybillVO>> {

    @Value("${exceptionWaybill.search.type:exceptionWaybillHandler}")
    private String searchType;

    @Value("${exceptionWaybill.search.index:sub_waybill_index01}")
    private String index;

    @Resource
    private RestHighLevelClient restHighLevelClient;

    @Resource
    private WaybillDorisFeignClient waybillDorisFeignClient;

    @Override
    public RestResponse<Pagination<SubWaybillVO>> search(ExceptionWaybillListQO qo) {
        // 构建搜索请求
        SearchRequest searchRequest = new SearchRequest(index);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
        searchSourceBuilder.trackTotalHits(true);
        try {
            int size = qo.getSize();
            int from = (qo.getPage() - 1) * size;
            searchSourceBuilder.from(from);
            searchSourceBuilder.size(size);
            // 承运商
            if (qo.getCarrierCompanyId() != null) {
                TermsAggregationBuilder aggregation = AggregationBuilders
                        .terms("group_by_waybillNo")
                        .field(FieldUtils.val(SubWaybillIndex::getWaybillNo)).size(Integer.MAX_VALUE);
                // 排序
                PageOrderType orderType = qo.getOrderType();
                if (orderType != null) {
                    switch (orderType) {
                        case ASC:
                            aggregation.subAggregation(new BucketSortPipelineAggregationBuilder("bucket_sort", Collections.singletonList(new FieldSortBuilder("max_created_at").order(SortOrder.ASC))).from(size).size(size));
                            break;
                        case DESC:
                            aggregation.subAggregation(new BucketSortPipelineAggregationBuilder("bucket_sort", Collections.singletonList(new FieldSortBuilder("max_created_at").order(SortOrder.DESC))).from(from).size(size));
                            break;
                    }
                }
                searchSourceBuilder.aggregation(aggregation);
            }

            if (qo.getShipperCompanyId() != null) {
                // 排序
                PageOrderType orderType = qo.getOrderType();
                if (orderType != null) {
                    switch (orderType) {
                        case ASC:
                            searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(SubWaybillIndex::getCreateTime)).order(SortOrder.ASC));
                            break;
                        case DESC:
                            searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(SubWaybillIndex::getCreateTime)).order(SortOrder.DESC));
                            break;
                    }
                }
            }

            BoolQueryBuilder mainQueryBuilder = getMainBoolQueryBuilder(qo);
            searchSourceBuilder.query(mainQueryBuilder);
            searchRequest.source(searchSourceBuilder);
            log.info("查询es条件：{}",searchSourceBuilder.toString());
            List<SubWaybillVO> data = new ArrayList<>();
            Pagination<SubWaybillVO> pagination = new Pagination<>();
            try {
                SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
                SearchHits searchHits = searchResponse.getHits();
                SearchHit[] hitResult = searchHits.getHits();

                JSONArray array = new JSONArray();
                Stream.of(hitResult).forEach(itemHit -> {
                    Map<String, Object> sourceAsMap = itemHit.getSourceAsMap();
                    array.add(sourceAsMap);
                });
                TotalHits totalHits = searchHits.getTotalHits();
                List<SubWaybillIndex> list = JSONUtil.toList(array.toJSONString(0), SubWaybillIndex.class);
                log.info("查询es结果：{}", JSON.toJSONString(list));
                data = WaybillConverter.INSTANCE.toSubWaybillVOList(list);
                pagination.setTotal(totalHits.value);
            }catch (Exception e){
                //查询es 异常  查询doris
                log.info("异常运单相关查询es异常:{},开始查询doris",e);
                //如果不是 es查询异常 便往外抛
                Throwable root = ExceptionUtil.getRootCause(e);
                if(!(root instanceof ElasticsearchException)
                        || !((ElasticsearchException) root).getDetailedMessage().contains(Constants.ES_RESULT_WINDOW_IS_TOO_LARGE)){
                    throw new Exception(e);
                }
                //查询es异常获取不到总数，重新查询
                CountRequest countRequest = new CountRequest(getIndex());
                countRequest.source(searchSourceBuilder);
                CountResponse countResponse = restHighLevelClient.count(countRequest, RequestOptions.DEFAULT);
                pagination.setTotal(countResponse.getCount());
                List<WaybillQueryEntityDTO> dtoList = new ArrayList<>();
                try {
                    dtoList = waybillDorisFeignClient.exceptionWaybillHandlerSearch(DorisQueryWaybillConverter.INSTANCE.esExceptionQOToDorisExceptionQO(qo));
                }catch (Exception exception){
                    log.error("异常运单相关查询doris异常：{}",e);
                    return RestResponse.success(pagination);
                }

                if(CollectionUtil.isNotEmpty(dtoList)){
                    for(WaybillQueryEntityDTO dto:dtoList){
                        SubWaybillVO subWaybillVO = DorisQueryWaybillConverter.INSTANCE.dorisEntityToSubWaybillVO(dto);
                        //发货地信息
                        if(StringUtils.isNotBlank(dto.getSenderInfo())){
                            List<SubWaybillVO.Address> senderAddressList = JSONUtil.toList(dto.getSenderInfo(),SubWaybillVO.Address.class);
                            subWaybillVO.setSender(senderAddressList);
                        }
                        //收货地信息
                        if(StringUtils.isNotBlank(dto.getReceiverInfo())){
                            List<SubWaybillVO.Address> senderAddressList = JSONUtil.toList(dto.getReceiverInfo(),SubWaybillVO.Address.class);
                            subWaybillVO.setReceiver(senderAddressList);
                        }
                        //货物信息
                        if(StringUtils.isNotBlank(dto.getItemsInfo())){
                            List<SubWaybillVO.Item> itemList = JSONUtil.toList(dto.getItemsInfo(),SubWaybillVO.Item.class);
                            subWaybillVO.setItems(itemList);
                        }

                        data.add(subWaybillVO);
                    }
                }
            }

            pagination.setPage(qo.getPage());
            pagination.setSize(qo.getSize());
            pagination.setContent(data);
            return RestResponse.success(pagination);
        } catch (Exception e) {
            log.error("异常运单相关ES查询异常", e);
        }
        Pagination<SubWaybillVO> pagination = new Pagination<>();
        pagination.setPage(qo.getPage());
        pagination.setSize(qo.getSize());
        pagination.setTotal(0L);
        return RestResponse.success(pagination);
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.index;
    }
}
