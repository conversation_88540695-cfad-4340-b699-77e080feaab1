/*
 * Copyright (C) 2023 Baidu, Inc. All Rights Reserved.
 */
package com.wzc.be.es.core.waybill.handler;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.baidu.mapcloud.cloudnative.authentication.data.enums.AuthOptionsEnum;
import com.baidu.mapcloud.cloudnative.authentication.datapermission.core.entity.OtherQueryParam;
import com.baidu.mapcloud.cloudnative.authentication.datapermission.core.rule.UserPermissionRule;
import com.baidu.mapcloud.cloudnative.authentication.datapermission.enums.DataRuleValueEnum;
import com.baidu.mapcloud.cloudnative.common.model.UserContext;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.common.enums.SubWaybillStatusEnum;
import com.wzc.be.es.common.enums.WaybillCompleteStatusEnum;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.common.properties.CommonProperties;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.data.waybill.enums.CarrierTabTypeEnum;
import com.wzc.be.es.data.waybill.enums.WaybillQueryEnums;
import com.wzc.be.es.data.waybill.qo.CarrierTabGroupCountQO;
import com.wzc.be.es.data.waybill.qo.OwnerWaybillListQO;
import com.wzc.common.exception.ServiceException;
import com.wzc.common.user.utils.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.ExistsQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName OwnerBaseHandler
 **/
@Slf4j
public class OwnerBaseHandler <Q,V> implements BaseSearchHandler<Q, V> {

    @Resource
    private CommonProperties commonProperties;

    /**
     * 条件封装返回
     */
    public BoolQueryBuilder getMainBoolQueryBuilder(OwnerWaybillListQO qo){
        qo.setSuperAdminCompanyId(commonProperties.getSuperAdminCompanyId());
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        // 子运单号字段必须存在
        ExistsQueryBuilder fieldName = QueryBuilders.existsQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillNo));
        mainQueryBuilder.must(fieldName);
        CarrierTabTypeEnum tabType = qo.getTabType();
        if (null != tabType) {
            switch (tabType) {
                case STATUS_1:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_START.getCode()));
                    break;
                case STATUS_2:
                    BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                    boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_LOAD.getCode()));
                    boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_UNLOAD.getCode()));
                    boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_LOAD_SIGN.getCode()));
                    boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_UNLOAD_SIGN.getCode()));
                    mainQueryBuilder.must(boolQuery);
                    break;
                case STATUS_3:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_SIGN.getCode()));
                    break;
                case STATUS_4:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.FINISH.getCode()));
                    break;
                case STATUS_5:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.CANCELLED.getCode()));
                    break;
                case STATUS_6:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCompleteStatus), WaybillCompleteStatusEnum.UNRECONCILED.getCode()));
                    break;
                case STATUS_7:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), 8));
                    mainQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getIsEvaluate), 1));
                    break;
                case STATUS_8:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_ASSIGN.getCode()));
                    break;
                default:
                    break;

            }
        }

        WaybillQueryEnums queryType = qo.getQueryType();
        if (queryType != null) {
            String queryValue = qo.getQueryValue();
            switch (queryType) {
                case ALL:
                    BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//                    boolQuery.should(QueryBuilders.wildcardQuery("carNo", wildcardsExp(queryValue)));
//                    boolQuery.should(QueryBuilders.wildcardQuery("oldWaybillNo", wildcardsExp(queryValue)));
//                    boolQuery.should(QueryBuilders.wildcardQuery("oldSubWaybillNo", wildcardsExp(queryValue)));
//                    boolQuery.should(QueryBuilders.wildcardQuery("orderNo", wildcardsExp(queryValue)));
//                    boolQuery.should(QueryBuilders.wildcardQuery("thirdOrderNo", wildcardsExp(queryValue)));
//                    boolQuery.should(QueryBuilders.wildcardQuery("parentNo", wildcardsExp(queryValue)));
//                    boolQuery.should(QueryBuilders.wildcardQuery("sender.fullAddress", wildcardsExp(queryValue)));
//                    boolQuery.should(QueryBuilders.wildcardQuery("receiver.fullAddress", wildcardsExp(queryValue)));
//                    boolQuery.should(QueryBuilders.wildcardQuery("items.itemName", wildcardsExp(queryValue)));
//                    boolQuery.should(QueryBuilders.wildcardQuery("carrierCompanyName", wildcardsExp(queryValue)));
//                    boolQuery.should(QueryBuilders.wildcardQuery("waybillNo", wildcardsExp(queryValue)));
//                    boolQuery.should(QueryBuilders.wildcardQuery("subWaybillNo", wildcardsExp(queryValue)));
//                    boolQuery.should(QueryBuilders.wildcardQuery("driverName", wildcardsExp(queryValue)));
//                    boolQuery.should(QueryBuilders.wildcardQuery("ztDriverName", wildcardsExp(queryValue)));
//                    boolQuery.should(QueryBuilders.wildcardQuery("driverName.keyword", wildcardsExp(queryValue)));
//                    boolQuery.should(QueryBuilders.wildcardQuery("ztDriverName.keyword", wildcardsExp(queryValue)));
//

                    List<String> fields = new ArrayList<>();
                    fields.add("carNo");
                    fields.add("oldWaybillNo");
                    fields.add("oldSubWaybillNo");
                    fields.add("orderNo");
                    fields.add("thirdOrderNo");
                    fields.add("parentNo");
                    fields.add("sender.fullAddress");
                    fields.add("receiver.fullAddress");
                    fields.add("items.itemName");
                    fields.add("carrierCompanyName");
                    fields.add("waybillNo");
                    fields.add("subWaybillNo");
                    fields.add("driverName");
                    fields.add("ztDriverName");
                    fields.add("driverName.keyword");
                    fields.add("ztDriverName.keyword");
                    matchPhraseOrWildcardMustQuery(boolQuery,queryValue,true,fields);
                    mainQueryBuilder.must(boolQuery);
                    break;
                case CAR_NO:
//                    mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getCarNo), wildcardsExp(queryValue)));
                    matchPhraseOrWildcardMustQuery(mainQueryBuilder,queryValue,true,CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getCarNo)));
                    break;
                case FORM_ADDRESS:
//                    mainQueryBuilder.must(QueryBuilders.wildcardQuery("sender.fullAddress", wildcardsExp(queryValue)));
                    matchPhraseOrWildcardMustQuery(mainQueryBuilder,queryValue,true,CollectionUtil.newArrayList("sender.fullAddress"));
                    break;
                case TO_ADDRESS:
//                    mainQueryBuilder.must(QueryBuilders.wildcardQuery("receiver.fullAddress", wildcardsExp(queryValue)));
                    matchPhraseOrWildcardMustQuery(mainQueryBuilder,queryValue,true,CollectionUtil.newArrayList("receiver.fullAddress"));
                    break;
                case CARRIER:
//                    mainQueryBuilder.must(QueryBuilders.wildcardQuery("carrierCompanyName", wildcardsExp(queryValue)));
                    matchPhraseOrWildcardMustQuery(mainQueryBuilder,queryValue,true,CollectionUtil.newArrayList("carrierCompanyName"));
                    break;
                case ITEMNAME:
//                    mainQueryBuilder.must(QueryBuilders.wildcardQuery("items.itemName", wildcardsExp(queryValue)));
                    matchPhraseOrWildcardMustQuery(mainQueryBuilder,queryValue,true,CollectionUtil.newArrayList("items.itemName"));
                    break;
                case DISPATCH_CAR_NO:
                    BoolQueryBuilder boolQuery1 = QueryBuilders.boolQuery();
//                    boolQuery1.should(QueryBuilders.wildcardQuery("waybillNo", wildcardsExp(queryValue)));
//                    boolQuery1.should(QueryBuilders.wildcardQuery("subWaybillNo", wildcardsExp(queryValue)));
//                    boolQuery1.should(QueryBuilders.wildcardQuery("oldWaybillNo", wildcardsExp(queryValue)));
//                    boolQuery1.should(QueryBuilders.wildcardQuery("oldSubWaybillNo", wildcardsExp(queryValue)));
                    matchPhraseOrWildcardMustQuery(boolQuery1,queryValue,true,CollectionUtil.newArrayList("waybillNo","subWaybillNo","oldWaybillNo","oldSubWaybillNo"));
                    mainQueryBuilder.must(boolQuery1);
                    break;
                case THIRDORDERNO:
//                    mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getThirdOrderNo), wildcardsExp(queryValue)));
                    matchPhraseOrWildcardMustQuery(mainQueryBuilder,queryValue,true,CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getThirdOrderNo)));
                    break;
                case ORDERNO:
//                    mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getOrderNo), wildcardsExp(queryValue)));
                    matchPhraseOrWildcardMustQuery(mainQueryBuilder,queryValue,true,CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getOrderNo)));
                    break;
                case TRANPORTNO:
//                    mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getParentNo), wildcardsExp(queryValue)));
                    matchPhraseOrWildcardMustQuery(mainQueryBuilder,queryValue,true,CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getParentNo)));
                    break;
                case DRIVER:
                    BoolQueryBuilder boolQuery2 = QueryBuilders.boolQuery();
//                    boolQuery2.should(QueryBuilders.wildcardQuery("ztDriverName", wildcardsExp(queryValue)));
//                    boolQuery2.should(QueryBuilders.wildcardQuery("driverName", wildcardsExp(queryValue)));
                    matchPhraseOrWildcardMustQuery(boolQuery2,queryValue,true,CollectionUtil.newArrayList("ztDriverName","driverName"));

                    mainQueryBuilder.must(boolQuery2);
                    break;
                default:
                    break;
            }
        }

        // 网货标识
        if(qo.getZbStatus() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getZbStatus), qo.getZbStatus()));
        }

        // 是否同步
        if(qo.getIsSync() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getIsSync), qo.getIsSync()));
        }

        // 运输计划单号集合
        if(CollectionUtils.isNotEmpty(qo.getPlanOrderNoList())){
            mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getParentNo),qo.getPlanOrderNoList()));
        }

        if(CollectionUtils.isNotEmpty(qo.getWaybillNolist())){
            if(qo.getReqSource()==1){
                mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getWaybillNo),qo.getWaybillNolist()));
            }else{
                mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillNo),qo.getWaybillNolist()));
            }
        }
        // 状态
        if(CollectionUtils.isNotEmpty(qo.getStatus())){
            mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus),qo.getStatus()));
        }
        // 货主APP关键字查询（运单号/承运商名称/车牌号）
        if (StringUtils.isNotBlank(qo.getKeyword())) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//            boolQuery.should(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getWaybillNo), wildcardsExp(qo.getKeyword())));
//            boolQuery.should(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getDriverName), wildcardsExp(qo.getKeyword())));
//            boolQuery.should(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getZtDriverName), wildcardsExp(qo.getKeyword())));
//            boolQuery.should(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getCarNo), wildcardsExp(qo.getKeyword())));
            matchPhraseOrWildcardMustQuery(boolQuery,qo.getKeyword(),true,CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getWaybillNo),FieldUtils.val(SubWaybillIndex::getDriverName),FieldUtils.val(SubWaybillIndex::getZtDriverName),FieldUtils.val(SubWaybillIndex::getCarNo)));
            mainQueryBuilder.must(boolQuery);
        }
        // 派车单号
        if (StringUtils.isNotBlank(qo.getWaybillNo())) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//            boolQuery.should(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getWaybillNo),wildcardsExp(qo.getWaybillNo())));
//            boolQuery.should(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillNo),wildcardsExp(qo.getWaybillNo())));
//            boolQuery.should(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getOldWaybillNo),wildcardsExp(qo.getWaybillNo())));
//            boolQuery.should(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getOldSubWaybillNo),wildcardsExp(qo.getWaybillNo())));
            matchPhraseOrWildcardMustQuery(boolQuery,qo.getWaybillNo(),true,CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getWaybillNo),FieldUtils.val(SubWaybillIndex::getSubWaybillNo),FieldUtils.val(SubWaybillIndex::getOldWaybillNo),FieldUtils.val(SubWaybillIndex::getOldSubWaybillNo)));
            mainQueryBuilder.must(boolQuery);
        }

        // 派车单号集合
        if(CollectionUtils.isNotEmpty(qo.getSubWaybillNoList())){
            mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getWaybillNo),qo.getSubWaybillNoList()));
        }

        // 车牌号
        if (StringUtils.isNotBlank(qo.getCarNo())) {
//          mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getCarNo),wildcardsExp(qo.getCarNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getCarNo(),true,CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getCarNo)));
        }
        // 司机姓名
        if (StringUtils.isNotBlank(qo.getDriverName())) {
            //mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getDriverName), wildcardsExp(qo.getDriverName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getDriverName(),true,CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getDriverName)));
        }
        // 船员姓名
        if (StringUtils.isNotBlank(qo.getZtDriverName())){
            mainQueryBuilder.must(getMatchPhraseQuery("ztDriverName",qo.getZtDriverName()));
        }

        // 派车类型
        if (qo.getDispatchCarType() != null) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchCarType), qo.getDispatchCarType()));
        }
        // 船员或者司机
        if (StringUtils.isNotBlank(qo.getDriverOrShipperName())) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//            boolQuery.should(QueryBuilders.wildcardQuery("ztDriverName", wildcardsExp(qo.getDriverOrShipperName())));
//            boolQuery.should(QueryBuilders.wildcardQuery("driverName", wildcardsExp(qo.getDriverOrShipperName())));
            matchPhraseOrWildcardMustQuery(boolQuery,qo.getDriverOrShipperName(),true,CollectionUtil.newArrayList("ztDriverName","driverName"));

            mainQueryBuilder.must(boolQuery);
        }
        // 订单号
        if (StringUtils.isNotBlank(qo.getOrderNo())) {
            if (qo.getReqSource() == 1) {
                mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getOrderNo),qo.getOrderNo()));
            }else{
                mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getOrderNo),wildcardsExp(qo.getOrderNo())));
            }
        }
        // 货物名称
        if (StringUtils.isNotBlank(qo.getGoodsName())) {
            //mainQueryBuilder.must(QueryBuilders.wildcardQuery("items.itemName",wildcardsExp(qo.getGoodsName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getGoodsName(),true,CollectionUtil.newArrayList("items.itemName"));
        }

        // 查询时间类型 1创建时间 2开启运输 3装货签到 4装货确认 5卸货签到 6卸货确认 7货主签收
        if(qo.getDateType() != null){
            switch (qo.getDateType()){
                case 1:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getCreateTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getCreateTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 2:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getTransStartTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getTransStartTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 3:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadSignInTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadSignInTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 4:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadConfirmTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadConfirmTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 5:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getUnloadSignInTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getUnloadSignInTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 6:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getTransEndTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getTransEndTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 7:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getOwnerSignTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getOwnerSignTime)).lte(qo.getEndTime()));
                    }
                    break;

                case 8:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getOrderType),1));
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadFirstTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadFirstTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 9:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getOrderType),1));
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadSecondTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadSecondTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 10:
                    mainQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getOrderType),1));
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getUnloadFirstTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getUnloadFirstTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 11:
                    mainQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getOrderType),1));
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getUnloadSecondTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getUnloadSecondTime)).lte(qo.getEndTime()));
                    }
                    break;
            }

        }

        // 运单来源
        if(Objects.nonNull(qo.getDispatchSourceNew())){
            switch (qo.getDispatchSourceNew()){
                case 0://0我找车系统指派
                    BoolQueryBuilder boolQuery0 = QueryBuilders.boolQuery();
                    boolQuery0.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),0));
                    boolQuery0.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),1));
                    boolQuery0.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),2));
                    boolQuery0.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),7));
                    mainQueryBuilder.must(boolQuery0);
                    break;
                case 3://3竞价
                    BoolQueryBuilder boolQuery3 = QueryBuilders.boolQuery();
                    boolQuery3.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),3));
                    boolQuery3.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),5));
                    boolQuery3.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),8));
                    mainQueryBuilder.must(boolQuery3);
                    break;
                case 4://4抢单
                    BoolQueryBuilder boolQuery4 = QueryBuilders.boolQuery();
                    boolQuery4.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),4));
                    boolQuery4.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),6));
                    boolQuery4.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),9));
                    mainQueryBuilder.must(boolQuery4);
                    break;
            }
        }

        // 发货商
        if(StringUtils.isNotBlank(qo.getFromCustomerName())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery("sender.customerName",wildcardsExp(qo.getFromCustomerName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getFromCustomerName(),true,CollectionUtil.newArrayList("sender.customerName"));
        }

        // 收货商
        if(StringUtils.isNotBlank(qo.getToCustomerName())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery("receiver.customerName",wildcardsExp(qo.getToCustomerName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getToCustomerName(),true,CollectionUtil.newArrayList("receiver.customerName"));
        }

        // 发货地址
        if(StringUtils.isNotBlank(qo.getFromAddress())){
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//            boolQuery.should(QueryBuilders.wildcardQuery("sender.address",wildcardsExp(qo.getFromAddress())));
//            boolQuery.should(QueryBuilders.wildcardQuery("sender.fullAddress",wildcardsExp(qo.getFromAddress())));
            matchPhraseOrWildcardMustQuery(boolQuery,qo.getFromAddress(),true,CollectionUtil.newArrayList("sender.address","sender.fullAddress"));
            mainQueryBuilder.must(boolQuery);
        }

        // 收货地址
        if(StringUtils.isNotBlank(qo.getToAddress())){
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//            boolQuery.should(QueryBuilders.wildcardQuery("receiver.address",wildcardsExp(qo.getToAddress())));
//            boolQuery.should(QueryBuilders.wildcardQuery("receiver.fullAddress",wildcardsExp(qo.getToAddress())));
            matchPhraseOrWildcardMustQuery(boolQuery,qo.getToAddress(),true,CollectionUtil.newArrayList("receiver.address","receiver.fullAddress"));
            mainQueryBuilder.must(boolQuery);
        }

        // 第三方单号
        if(StringUtils.isNotBlank(qo.getThirdOrderNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getThirdOrderNo),wildcardsExp(qo.getThirdOrderNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getThirdOrderNo(),true,CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getThirdOrderNo)));
        }

        // 业务类型
        if(qo.getBusinessType() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getBusinessType),qo.getBusinessType()));
        }

        // 订单类型
        if(qo.getWaybillType() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getOrderType),qo.getWaybillType()));
        }

        // 北斗是否正常
        if(qo.getBdNormal() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getBdNormal),qo.getBdNormal()));
        }

        // 自动签收
        if(qo.getAutoSign() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getAutoSign),qo.getAutoSign()));
        }

        // 承运商公司
        if (StringUtils.isNotBlank(qo.getCarrierCompanyName())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getCarrierCompanyName),wildcardsExp(qo.getCarrierCompanyName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getCarrierCompanyName(),true,CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getCarrierCompanyName)));
        }

        // 货主公司
        if (StringUtils.isNotBlank(qo.getShipperCompanyName())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getShipperCompanyName),wildcardsExp(qo.getShipperCompanyName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getShipperCompanyName(),true,CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getShipperCompanyName)));
        }

        // 运输方式
        if(qo.getTransportType() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getTransportType),qo.getTransportType()));
        }

        // 运输计划单号
        if (StringUtils.isNotBlank(qo.getPlanOrderNo())) {
            if(qo.getReqSource()==1){
                mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getParentNo),qo.getPlanOrderNo()));
            }

        }

        // 评价
        if(Objects.nonNull(qo.getIsEvaluate())){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getIsEvaluate),qo.getIsEvaluate()));
        }

        // 结算状态(对上)
        if(Objects.nonNull(qo.getCompleteStatus())){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCompleteStatus),qo.getCompleteStatus()));
        }
        // 结算状态(对下)
        if(Objects.nonNull(qo.getCompleteDownStatus())){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCompleteDownStatus),qo.getCompleteDownStatus()));
        }

        // 超时未开启
        if(Objects.nonNull(qo.getOvertimeUnStart())){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getOvertimeUnStart),qo.getOvertimeUnStart()));
        }

        // 超时未签收
        if(Objects.nonNull(qo.getOvertimeUnSign())){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getOvertimeUnSign),qo.getOvertimeUnSign()));
        }

        // 运单货主审核状态
        if(Objects.nonNull(qo.getDispatchCheckStatus())){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchCheckStatus),qo.getDispatchCheckStatus()));
        }

        // 应付结算单号
        if(StringUtils.isNotBlank(qo.getPayStatementNo())){
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//            boolQuery.should(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getPayStatementNo),wildcardsExp(qo.getPayStatementNo())));
//            boolQuery.should(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getReStatementNo),wildcardsExp(qo.getPayStatementNo())));
            matchPhraseOrWildcardMustQuery(boolQuery,qo.getPayStatementNo(),true,CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getPayStatementNo),FieldUtils.val(SubWaybillIndex::getReStatementNo)));
            mainQueryBuilder.must(boolQuery);
        }

        // 应收结算单号
        if(StringUtils.isNotBlank(qo.getReStatementNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getReStatementNo),wildcardsExp(qo.getReStatementNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getReStatementNo(),true,CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getReStatementNo)));
        }

        // 应付单号
        if(StringUtils.isNotBlank(qo.getPayOrderNo())){
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//            boolQuery.should(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getPayOrderNo),wildcardsExp(qo.getPayOrderNo())));
//            boolQuery.should(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getReOrderNo),wildcardsExp(qo.getPayOrderNo())));
            matchPhraseOrWildcardMustQuery(boolQuery,qo.getPayOrderNo(),true,CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getPayOrderNo),FieldUtils.val(SubWaybillIndex::getReOrderNo)));
            mainQueryBuilder.must(boolQuery);
        }

        // 应收单号
        if(StringUtils.isNotBlank(qo.getReOrderNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getReOrderNo),wildcardsExp(qo.getReOrderNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getReOrderNo(),true,CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getReOrderNo)));
        }

        // 付款申请单号
        if(StringUtils.isNotBlank(qo.getPayApplyNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getPayApplyNo),wildcardsExp(qo.getPayApplyNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getPayApplyNo(),true,CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getPayApplyNo)));
        }

        // 收款单号
        if(StringUtils.isNotBlank(qo.getReceiptNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getReceiptNo),wildcardsExp(qo.getReceiptNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getReceiptNo(),true,CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getReceiptNo)));
        }

        // 货源编号
        if(StringUtils.isNotBlank(qo.getGoodsNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery("goodsNo.keyword",wildcardsExp(qo.getGoodsNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getGoodsNo(),true,CollectionUtil.newArrayList("goodsNo"));
        }


        // 数据权
        Long companyId = UserUtils.getCompanyId();
        // 运营端分 平台 托运公司
        // 平台 固定公司 超级管理员
        // 托运公司 正常登陆公司
        if(qo.getSuperAdminCompanyId().equals(companyId)){
            if(UserContext.isPlatformAdmin()){
                log.info("运单列表查询-当前登陆角色为平台超级管理员无需数据权限");
            }else{
                // 货主公司集合
                List<String> companyIdList = qo.getCompanyList();
                if(CollectionUtils.isEmpty(companyIdList) || (companyIdList.size() == 1 && companyIdList.contains(companyId))){
                    throw new ServiceException(ExcepitonEnum.AUTH_CHECK_ERROR);
                }
                boolean flag;
                flag = CollectionUtils.isEmpty(companyIdList) || !companyIdList.contains("-1");
                log.info("运单列表查询-当前登陆角色为平台管理员,flag:{}",flag);
                // 1、订单类型权限
                if (CollectionUtil.isNotEmpty(qo.getOrderTypeAuthList())) {
                    BoolQueryBuilder orderTypeQueryBuilder = QueryBuilders.boolQuery()
                            .should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery(FieldUtils.val(SubWaybillIndex::getOrderType))))
                            .should(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getOrderType), qo.getOrderTypeAuthList()));
                    mainQueryBuilder.must(orderTypeQueryBuilder);
                }

                // 订单来源
                if (CollectionUtil.isNotEmpty(qo.getSourceTypeAuthList())) {
                    mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getSourceType), qo.getSourceTypeAuthList()));
                }

                // 2、是否有配置公司数据权限  （配置当前公司限制）
                BoolQueryBuilder authBoolQuery = QueryBuilders.boolQuery();
                String userCompanyId = String.valueOf(ObjectUtil.isNull(qo.getShipperCompanyId()) ? UserContext.companyId().get() : qo.getShipperCompanyId());
                if (CollectionUtil.isNotEmpty(qo.getCompanyList())
                        && UserContext.companyId().isPresent()
                        && qo.getCompanyList().contains(userCompanyId)) {
                    AtomicBoolean isBlank = new AtomicBoolean(true);
                    BoolQueryBuilder innerQuery = QueryBuilders.boolQuery();
                    // 货主查询转包运单
//                    if(Objects.nonNull(qo.getShipperCompanyId())){
//                        innerQuery.should(QueryBuilders.termQuery("waybillCompany.companyId", Convert.toStr(qo.getShipperCompanyId())));
//                    }
                    // 3、指定货主用户
                    if (CollectionUtil.isNotEmpty(qo.getUserIdList())) {
                        isBlank.set(false);
                        innerQuery.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getShipperUserId), qo.getUserIdList()));
                    }
                    // 4、数据权限部门id过滤
                    if (CollectionUtil.isNotEmpty(qo.getDepIdList())) {
                        isBlank.set(false);
                        innerQuery.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getShipperUserDepartment), qo.getDepIdList()));
                    }

                    // 5. 当前公司物料信息
                    List<CarrierTabGroupCountQO.OtherQueryParam> companyAndOtherList = qo.getCompanyAndOtherList();
                    if (CollectionUtil.isNotEmpty(companyAndOtherList)) {
                        companyAndOtherList.stream().filter(
                                param -> Objects.equals(userCompanyId, param.getCompanyId())
                        ).forEach(queryParam -> {
                            // 物料
                            if (CollectionUtil.isNotEmpty(queryParam.getMaterialSpecies())) {
                                isBlank.set(false);
                                List<String> materialSpecies = queryParam.getMaterialSpecies().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                                innerQuery.must(QueryBuilders.termsQuery("items.speciesId", materialSpecies));
                            }
                            // 路线
                            if (CollectionUtil.isNotEmpty(queryParam.getRoute())) {
                                isBlank.set(false);
                                List<String> routes = queryParam.getRoute().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                                innerQuery.must(QueryBuilders.termsQuery("trackRouteId", routes));
                            }
                        });
                    }
                    if(!isBlank.get()){
                        authBoolQuery.should(innerQuery);
                    }
                }

                List<CarrierTabGroupCountQO.OtherQueryParam> companyAndOtherList = qo.getCompanyAndOtherList();
                if (CollectionUtil.isNotEmpty(companyAndOtherList)) {
                    companyAndOtherList.stream().filter(
                            param -> !Objects.equals(userCompanyId, param.getCompanyId())
                    ).forEach(queryParam -> {

                        if(flag){
                            BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                            BoolQueryBuilder innerShouldQuery = QueryBuilders.boolQuery();
                            // 货主公司
                            innerShouldQuery.should(QueryBuilders.termQuery("shipperCompanyId", queryParam.getCompanyId()));
                            // 货主查询转包运单
                            innerShouldQuery.must(QueryBuilders.termQuery("waybillCompany.companyId", Convert.toStr(queryParam.getCompanyId())));
                            shouldQuery.must(innerShouldQuery);
                            // 物料
                            if (CollectionUtil.isNotEmpty(queryParam.getMaterialSpecies())) {
                                List<String> materialSpecies = queryParam.getMaterialSpecies().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(materialSpecies)) {
                                    shouldQuery.must(QueryBuilders.termsQuery("items.speciesId", materialSpecies));
                                }
                            }
                            // 路线
                            if (CollectionUtil.isNotEmpty(queryParam.getRoute())) {
                                List<String> routes = queryParam.getRoute().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                                if (CollectionUtils.isNotEmpty(routes)) {
                                    shouldQuery.must(QueryBuilders.termsQuery("trackRouteId", routes));
                                }
                            }
                            authBoolQuery.should(shouldQuery);
                        }

                    });
                }
                mainQueryBuilder.must(authBoolQuery);

            }
            return mainQueryBuilder;

        }else if(UserUtils.isIdentityShipper()){ // 货主pc
            log.info("运单列表查询-当前登陆角色为货主PC");
            // 1、订单类型权限
            if (CollectionUtil.isNotEmpty(qo.getOrderTypeAuthList())) {
                BoolQueryBuilder orderTypeQueryBuilder = QueryBuilders.boolQuery()
                        .should(QueryBuilders.boolQuery()
                                .mustNot(QueryBuilders.existsQuery(FieldUtils.val(SubWaybillIndex::getOrderType))));
                orderTypeQueryBuilder.should(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getOrderType), qo.getOrderTypeAuthList()));
                mainQueryBuilder.must(orderTypeQueryBuilder);
            }

            // 订单来源
            if (CollectionUtil.isNotEmpty(qo.getSourceTypeAuthList())) {
                mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getSourceType), qo.getSourceTypeAuthList()));
            }

            // 2、是否有配置公司数据权限  （配置当前公司限制）
            BoolQueryBuilder authBoolQuery = QueryBuilders.boolQuery();
            String userCompanyId = String.valueOf(ObjectUtil.isNull(qo.getShipperCompanyId()) ? UserContext.companyId().get() : qo.getShipperCompanyId());
            if (CollectionUtil.isNotEmpty(qo.getCompanyList()) && UserContext.companyId().isPresent() &&
                    qo.getCompanyList().contains(userCompanyId)) {
                BoolQueryBuilder innerQuery = QueryBuilders.boolQuery();
                innerQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getShipperCompanyId), userCompanyId));
                // 货主查询转包运单
                if(Objects.nonNull(qo.getShipperCompanyId())){
                    innerQuery.should(QueryBuilders.termQuery("waybillCompany.companyId", Convert.toStr(qo.getShipperCompanyId())));
                }
                // 3、指定货主用户
                if (CollectionUtil.isNotEmpty(qo.getUserIdList())) {
                    innerQuery.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getShipperUserId), qo.getUserIdList()));
                }
                // 4、数据权限部门id过滤
                if (CollectionUtil.isNotEmpty(qo.getDepIdList())) {
                    innerQuery.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getShipperUserDepartment), qo.getDepIdList()));
                }

                // 5. 当前公司物料信息
                List<CarrierTabGroupCountQO.OtherQueryParam> companyAndOtherList = qo.getCompanyAndOtherList();
                if (CollectionUtil.isNotEmpty(companyAndOtherList)) {
                    companyAndOtherList.stream().filter(
                            param -> Objects.equals(userCompanyId, param.getCompanyId())
                    ).forEach(queryParam -> {
                        // 物料
                        if (CollectionUtil.isNotEmpty(queryParam.getMaterialSpecies())) {
                            List<String> materialSpecies = queryParam.getMaterialSpecies().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                            innerQuery.must(QueryBuilders.termsQuery("items.speciesId", materialSpecies));
                        }
                        // 路线
                        if (CollectionUtil.isNotEmpty(queryParam.getRoute())) {
                            List<String> routes = queryParam.getRoute().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                            innerQuery.must(QueryBuilders.termsQuery("trackRouteId", routes));
                        }
                    });
                }
                authBoolQuery.should(innerQuery);
            }

            // 6、公司物料线路权限配置 （配置其他公司限制）
            List<CarrierTabGroupCountQO.OtherQueryParam> companyAndOtherList = qo.getCompanyAndOtherList();
            if (CollectionUtil.isNotEmpty(companyAndOtherList)) {
                companyAndOtherList.stream().filter(
                        param -> !Objects.equals(userCompanyId, param.getCompanyId())
                ).forEach(queryParam -> {
                    BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                    // 货主公司
                    shouldQuery.must(QueryBuilders.termQuery("shipperCompanyId", queryParam.getCompanyId()));
                    // 物料
                    if (CollectionUtil.isNotEmpty(queryParam.getMaterialSpecies())) {
                        List<String> materialSpecies = queryParam.getMaterialSpecies().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(materialSpecies)) {
                            shouldQuery.must(QueryBuilders.termsQuery("items.speciesId", materialSpecies));
                        }
                    }
                    // 路线
                    if (CollectionUtil.isNotEmpty(queryParam.getRoute())) {
                        List<String> routes = queryParam.getRoute().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(routes)) {
                            shouldQuery.must(QueryBuilders.termsQuery("trackRouteId", routes));
                        }
                    }
                    authBoolQuery.should(shouldQuery);
                });
            }
            mainQueryBuilder.must(authBoolQuery);
            return mainQueryBuilder;
        }else {
            if(UserContext.isPlatformAdmin()){
                BoolQueryBuilder authBoolQuery = QueryBuilders.boolQuery();
                String userCompanyId = String.valueOf(ObjectUtil.isNull(qo.getShipperCompanyId()) ? UserContext.companyId().get() : qo.getShipperCompanyId());
                if (CollectionUtil.isNotEmpty(qo.getCompanyList()) && UserContext.companyId().isPresent() &&
                        qo.getCompanyList().contains(userCompanyId)) {
                    BoolQueryBuilder innerQuery = QueryBuilders.boolQuery();
                    innerQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getShipperCompanyId), userCompanyId));
                    innerQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCompanyId), userCompanyId));
                    // 查询转包运单
                    innerQuery.should(QueryBuilders.termQuery("waybillCompany.companyId", userCompanyId));
                    authBoolQuery.should(innerQuery);
                }
                mainQueryBuilder.must(authBoolQuery);
                return mainQueryBuilder;
            }
            List<String> companyIdList = qo.getCompanyList();
            boolean flag;
            flag = !(CollectionUtils.isNotEmpty(companyIdList) && companyIdList.contains("-1"));
            // 运营端PC
            log.info("运单列表查询-当前登陆角色为运营端PC-托运方");
            // 1、订单类型权限
            if (CollectionUtil.isNotEmpty(qo.getOrderTypeAuthList())) {
                BoolQueryBuilder orderTypeQueryBuilder = QueryBuilders.boolQuery()
                        .should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery(FieldUtils.val(SubWaybillIndex::getOrderType))))
                        .should(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getOrderType), qo.getOrderTypeAuthList()));
                mainQueryBuilder.must(orderTypeQueryBuilder);
            }

            // 订单来源
            if (CollectionUtil.isNotEmpty(qo.getSourceTypeAuthList())) {
                mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getSourceType), qo.getSourceTypeAuthList()));
            }

            // 2、是否有配置公司数据权限  （配置当前公司限制）
            BoolQueryBuilder authBoolQuery = QueryBuilders.boolQuery();
            String userCompanyId = String.valueOf(ObjectUtil.isNull(qo.getShipperCompanyId()) ? UserContext.companyId().get() : qo.getShipperCompanyId());
            BoolQueryBuilder companyQuery = QueryBuilders.boolQuery();
            companyQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getShipperCompanyId), userCompanyId));
            companyQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCompanyId), userCompanyId));
            // 查询转包运单
            companyQuery.should(QueryBuilders.termQuery("waybillCompany.companyId", userCompanyId));
            mainQueryBuilder.must(companyQuery);
            if (CollectionUtil.isNotEmpty(qo.getCompanyList()) && UserContext.companyId().isPresent() &&
                    qo.getCompanyList().contains(userCompanyId) && flag) {
                BoolQueryBuilder innerQuery = QueryBuilders.boolQuery();
                innerQuery.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getShipperCompanyId), userCompanyId));
                // 3、指定用户
                if (CollectionUtil.isNotEmpty(qo.getUserIdList())) {
                    innerQuery.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getShipperUserId), qo.getUserIdList()));
                }
                // 4、数据权限部门id过滤
                if (CollectionUtil.isNotEmpty(qo.getDepIdList())) {
                    innerQuery.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getShipperUserDepartment), qo.getDepIdList()));
                }

                // 5. 当前公司物料信息
                List<CarrierTabGroupCountQO.OtherQueryParam> companyAndOtherList = qo.getCompanyAndOtherList();
                if (CollectionUtil.isNotEmpty(companyAndOtherList)) {
                    companyAndOtherList.stream().filter(
                            param -> Objects.equals(userCompanyId, param.getCompanyId())
                    ).forEach(queryParam -> {
                        // 物料
                        if (CollectionUtil.isNotEmpty(queryParam.getMaterialSpecies())) {
                            List<String> materialSpecies = queryParam.getMaterialSpecies().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                            innerQuery.must(QueryBuilders.termsQuery("items.speciesId", materialSpecies));
                        }
                        // 路线
                        if (CollectionUtil.isNotEmpty(queryParam.getRoute())) {
                            List<String> routes = queryParam.getRoute().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                            innerQuery.must(QueryBuilders.termsQuery("trackRouteId", routes));
                        }
                    });
                }
                authBoolQuery.should(innerQuery);
            }

            // 6、公司物料线路权限配置 （配置其他公司限制）
            List<CarrierTabGroupCountQO.OtherQueryParam> companyAndOtherList = qo.getCompanyAndOtherList();
            if (CollectionUtil.isNotEmpty(companyAndOtherList)) {
                companyAndOtherList.stream().filter(
                        param -> !Objects.equals(userCompanyId, param.getCompanyId())
                ).forEach(queryParam -> {

                    if(flag){
                        BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                        // 货主公司
                        shouldQuery.must(QueryBuilders.termQuery("shipperCompanyId", queryParam.getCompanyId()));
                        // 物料
                        if (CollectionUtil.isNotEmpty(queryParam.getMaterialSpecies())) {
                            List<String> materialSpecies = queryParam.getMaterialSpecies().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(materialSpecies)) {
                                shouldQuery.must(QueryBuilders.termsQuery("items.speciesId", materialSpecies));
                            }
                        }
                        // 路线
                        if (CollectionUtil.isNotEmpty(queryParam.getRoute())) {
                            List<String> routes = queryParam.getRoute().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(routes)) {
                                shouldQuery.must(QueryBuilders.termsQuery("trackRouteId", routes));
                            }
                        }
                        authBoolQuery.should(shouldQuery);
                    }

                });
            }
            mainQueryBuilder.must(authBoolQuery);
        }
        return mainQueryBuilder;
    }

    public JSONObject authReq(UserPermissionRule userPermissionRule,JSONObject jsonObject){
        Boolean needOrderTypeQueryAuth =
                userPermissionRule.needAuth(DataRuleValueEnum.ORDER_TYPE, AuthOptionsEnum.SELECT);
        // 1、数据权限 订单类型集合
        if (needOrderTypeQueryAuth){
            List<String> orderTypeAuthList =
                    userPermissionRule.getValueList(DataRuleValueEnum.ORDER_TYPE, AuthOptionsEnum.SELECT);
            jsonObject.set("orderTypeAuthList", orderTypeAuthList);
        }
        Boolean needSourceTypeQueryAuth =
                userPermissionRule.needAuth(DataRuleValueEnum.SOURCE_TYPE, AuthOptionsEnum.SELECT);
        if (needSourceTypeQueryAuth){
            List<String> sourceTypeAuthList =
                    userPermissionRule.getValueList(DataRuleValueEnum.SOURCE_TYPE, AuthOptionsEnum.SELECT);
            jsonObject.set("sourceTypeAuthList", sourceTypeAuthList);
        }
        // 2、是否有配置公司数据权限
        Boolean needCompanyQueryAuth =
                userPermissionRule.needAuth(DataRuleValueEnum.COMPANY_LIST, AuthOptionsEnum.SELECT);
        if (needCompanyQueryAuth) {
            List<String> companyList =
                    userPermissionRule.getValueList(DataRuleValueEnum.COMPANY_LIST, AuthOptionsEnum.SELECT);
            jsonObject.set("companyList", companyList);
        }
        // 3、指定货主用户
        Boolean needUserQueryAuth =
                userPermissionRule.needAuth(DataRuleValueEnum.USER_LIST, AuthOptionsEnum.SELECT);
        if (needUserQueryAuth){
            List<String> userIdList =
                    userPermissionRule.getValueList(DataRuleValueEnum.USER_LIST, AuthOptionsEnum.SELECT);
            jsonObject.set("userIdList",userIdList);
        }
        // 4、数据权限部门id过滤
        Boolean needDepIdQueryAuth =
                userPermissionRule.needAuth(DataRuleValueEnum.DEPARTMENT_LIST, AuthOptionsEnum.SELECT);
        if (needDepIdQueryAuth){
            List<String> depIdList =
                    userPermissionRule.getValueList(DataRuleValueEnum.DEPARTMENT_LIST, AuthOptionsEnum.SELECT);
            jsonObject.set("depIdList",depIdList);
        }
        // 6、公司物料线路权限配置
        Boolean otherAuth = userPermissionRule.needAddCompanyAndOther();
        if (otherAuth) {
            // 公司物料线路权限配置
            List<OtherQueryParam> companyAndOtherList = userPermissionRule.getCompanyAndOther();
            jsonObject.set("companyAndOtherList",companyAndOtherList);
        }
        return jsonObject;
    }
}
