package com.wzc.be.es.core.business.index;

import cn.easyes.annotation.IndexField;
import cn.easyes.annotation.rely.FieldType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <p>
 * Toc网货对账单表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-11
 */
@Data
public class TocBusinessIndex{

    private String id;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 运输计划号
     */
    private String transportNo;

    /**
     * 运单号
     */
    private String dispatchNo;

    /**
     * 子运单号
     */
    private String subWaybillNo;


    /**
     * 车牌号
     */
    private String carNo;


    /**
     * 对账状态（0未对账 1已对账）
     */
    private Integer rcStatus;

    /**
     * 支付方式（1现金 2垫付）
     */
    private Integer paymentMethod;


    /**
     * 发货单位名称
     */
    private String fmCustomerName;

    /**
     * 发货地址简称
     */
    private String fmAddressName;

    /**
     * 发货地址
     */
    private String fmAddress;

    /**
     * 发货详细地址
     */
    private String fmFullAddress;


    /**
     * 收货单位名称
     */
    private String toCustomerName;

    /**
     * 收货地址简称
     */
    private String toAddressName;

    /**
     * 收货地址
     */
    private String toAddress;

    /**
     * 收货详细地址
     */
    private String toFullAddress;

    /**
     * 派车时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String dispatchTime;

    /**
     * 司机运输时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String transportTime;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String createTime;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String payFinishTime;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String appealFinishTime;

    /**
     * 司机卸货时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String unloadTime;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String updateTime;

    /**
     * 发布方id
     */
    private Long apCompanyId;

    /**
     * 发布方名称
     */
    private String apCompanyName;

    /**
     * 发布方类型（0货主 1平台 2承运商）
     */
    private Integer apCompanyType;

    /**
     * 托运公司id
     */
    private Long shipperCompanyId;

    /**
     * 托运公司名称
     */
    private String shipperCompanyName;

    /**
     * 服务方id
     */
    private Long arCompanyId;

    /**
     * 服务方名称
     */
    private String arCompanyName;

    /**
     * 服务方类型（3司机,4车队长）
     */
    private Integer arCompanyType;

    /**
     * 托运方审核时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String shipperAuditTime;

    /**
     * 托运方审核状态（0待审核 1审核通过 2已退回）
     */
    private Integer shipperAuditStatus;

    /**
     * 业务类型（1货主自营 2平台总包 3平台自营）
     */
    private Integer businessType;

    /**
     * 订单类型（1销售订单 2采购订单 3调拨订单）
     */
    private Integer orderType;

    /**
     * 承运商名称
     */
    private String carrierName;

    /**
     * 支付状态
     */
    private Integer payStatus;


    /**
     * 发起支付时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String payTime;


    /**
     * 服务方到账时间
     */
    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String toAccountTime;

    /**
     * 操作人
     */
    private Long payOperatorId;

    /**
     * 操作人
     */
    private String payOperatorName;


    /**
     * 暂估应付单推送状态（0-全部，1-已推送，2-未推送")
     */
    private Integer pushGsStatusAp;

    /**
     * 付款单推送状态（0-全部，1-已推送，2-未推送）
     */
    private Integer pushGsStatusApPay;

}
