package com.wzc.be.es.adapter.action.knowledge;

import com.baidu.mapcloud.cloudnative.common.model.Pagination;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.api.knowledge.EsKnowledgeSearchApi;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.core.knowledge.service.KnowledgeSearchService;
import com.wzc.be.es.data.knowledge.qo.KnowledgeSearchQO;
import com.wzc.be.es.data.knowledge.vo.KnowledgeSearchVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/12 17:14
 */
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class KnowledgeSearchAction implements EsKnowledgeSearchApi {

    private final KnowledgeSearchService knowledgeSearchService;


    @Override
    public RestResponse<EsPageVO<KnowledgeSearchVO>> knowledgeLogsSearch(KnowledgeSearchQO qo) {
        return knowledgeSearchService.knowledgeLogsSearch(qo);
    }
}
