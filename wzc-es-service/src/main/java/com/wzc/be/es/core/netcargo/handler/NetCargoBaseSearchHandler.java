package com.wzc.be.es.core.netcargo.handler;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.core.order.index.OrderIndex;
import com.wzc.be.es.core.transport.index.TransportIndex;
import com.wzc.be.es.data.netcargo.qo.NetCargoEsQO;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;

import java.util.List;

public class NetCargoBaseSearchHandler<Q, V> implements BaseSearchHandler<Q, V> {

    /**
     * 条件封装返回
     *
     * @param qo
     * @return
     */
    public BoolQueryBuilder getMainBoolQueryBuilder(NetCargoEsQO qo) {
        BoolQueryBuilder mainQueryBuilder = new BoolQueryBuilder();
        // 网货单据
        mainQueryBuilder.must(QueryBuilders.termQuery("netCargo", 1));
        // 状态
        mainQueryBuilder.must(QueryBuilders.termsQuery("status", CollectionUtil.newArrayList(3, 4, 5)));
        // 业务单号
        String businessNo = qo.getBusinessNo();
        if (StrUtil.isNotEmpty(businessNo)) {
            List<String> columns = CollectionUtil.newArrayList();
            columns.add("orderNo.text");
            columns.add("transportNo.text");
            matchPhraseOrWildcardMustQuery(mainQueryBuilder, businessNo, true, columns);
        }
        // 第三方订单号
        String thirdNo = qo.getThirdNo();
        if (StrUtil.isNotEmpty(thirdNo)) {
            matchPhraseOrWildcardMustQuery(mainQueryBuilder, thirdNo, true, StrUtil.split("thirdNo", ","));
        }
        // 发布方公司
        String companyName = qo.getCompanyName();
        if (StrUtil.isNotEmpty(companyName)) {
            List<String> columns = CollectionUtil.newArrayList();
            columns.add("shipperName");
            columns.add("carrierName");
            matchPhraseOrWildcardMustQuery(mainQueryBuilder, companyName, true, columns);
        }
        // 货物名称
        String itemNames = qo.getItemNames();
        if (StrUtil.isNotEmpty(itemNames)) {
            matchPhraseOrWildcardMustQuery(mainQueryBuilder, itemNames, true, StrUtil.split("items.itemName", ","));
        }
        // 业务类型
        Integer businessType = qo.getBusinessType();
        if (ObjectUtil.isNotNull(businessType)) {
            mainQueryBuilder.must(QueryBuilders.termQuery("businessType", businessType));
        }
        // 发货单位
        String senderCustomer = qo.getSenderCustomer();
        if (StrUtil.isNotEmpty(senderCustomer)) {
            matchPhraseOrWildcardMustQuery(mainQueryBuilder, senderCustomer, true, StrUtil.split("sender.customerName", ","));
        }
        // 发货地址
        String senderAddress = qo.getSenderAddress();
        if (StrUtil.isNotEmpty(senderAddress)) {
            List<String> columns = CollectionUtil.newArrayList();
            columns.add("sender.address");
            columns.add("sender.addressName");
            matchPhraseOrWildcardMustQuery(mainQueryBuilder, senderAddress, true, columns);
        }
        // 收货单位
        String receiverCustomer = qo.getReceiverCustomer();
        if (StrUtil.isNotEmpty(receiverCustomer)) {
            matchPhraseOrWildcardMustQuery(mainQueryBuilder, receiverCustomer, true, StrUtil.split("receiver.customerName", ","));
        }
        // 收货地址
        String receiverAddress = qo.getReceiverAddress();
        if (StrUtil.isNotEmpty(receiverAddress)) {
            List<String> columns = CollectionUtil.newArrayList();
            columns.add("receiver.address");
            columns.add("receiver.addressName");
            matchPhraseOrWildcardMustQuery(mainQueryBuilder, receiverAddress, true, columns);
        }
        // 订单类型
        Integer orderTypes = qo.getOrderTypes();
        if (ObjectUtil.isNotNull(orderTypes)) {
            BoolQueryBuilder orderTypeBuilder = new BoolQueryBuilder();
            orderTypeBuilder.should(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getWaybillType), qo.getOrderTypes()));
            orderTypeBuilder.should(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getOrderType), qo.getOrderTypes()));
            mainQueryBuilder.must(orderTypeBuilder);
        }
        // 网货审核状态
        Integer netCargoAuditStatus = qo.getNetCargoAuditStatus();
        if (ObjectUtil.isNotNull(netCargoAuditStatus)) {
            mainQueryBuilder.must(QueryBuilders.termQuery("netCargoAuditStatus", netCargoAuditStatus));
        }
        // 创建人
        String netCargoCreator = qo.getNetCargoCreator();
        if (StrUtil.isNotEmpty(netCargoCreator)) {
            matchPhraseOrWildcardMustQuery(mainQueryBuilder, netCargoCreator, true, StrUtil.split("netCargoCreator", ","));
        }
        // 创建开始时间
        String netCargoStartTime = qo.getNetCargoStartTime();
        if (StrUtil.isNotEmpty(netCargoStartTime)) {
            mainQueryBuilder.must(QueryBuilders.rangeQuery("netCargoCreateTime").gte(netCargoStartTime));
        }
        // 创建结束时间
        String netCargoEndTime = qo.getNetCargoEndTime();
        if (StrUtil.isNotEmpty(netCargoEndTime)) {
            mainQueryBuilder.must(QueryBuilders.rangeQuery("netCargoCreateTime").lte(netCargoEndTime));
        }
        // 合同编号
        String contractNo = qo.getContractNo();
        if (StrUtil.isNotEmpty(contractNo)) {
            matchPhraseOrWildcardMustQuery(mainQueryBuilder, contractNo, true, StrUtil.split("contractRelation.contractNo", ","));
        }
        // 合同状态
        Integer contractStatus = qo.getContractStatus();
        if (ObjectUtil.isNotNull(contractStatus)) {
            mainQueryBuilder.must(QueryBuilders.termQuery("contractStatus", contractStatus));
        }
        // tab查询
        Integer queryTabType = qo.getQueryTabType();
        if (ObjectUtil.equals(1, queryTabType)) {
            mainQueryBuilder.must(QueryBuilders.termQuery("netCargoAuditStatus", 0));
        }
        return mainQueryBuilder;
    }
}
