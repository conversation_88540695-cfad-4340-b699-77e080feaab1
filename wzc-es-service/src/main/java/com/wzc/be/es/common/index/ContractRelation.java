package com.wzc.be.es.common.index;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "合同信息")
public class ContractRelation {

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "合同编号")
    private String contractNo;

    @Schema(description = "合同签约方式（1-线上签约 2-线下签约）")
    private Integer contractSignMethod;

    @Schema(description = "合同类别（1-网货合同 2-物流合同）")
    private Integer contractCategory;
}
