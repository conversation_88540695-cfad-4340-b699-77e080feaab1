package com.wzc.be.es.core.knowledge.handler;

import cn.easyes.core.biz.EsPageInfo;
import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baidu.mapcloud.cloudnative.common.model.Pagination;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.constant.Constants;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.core.knowledge.converter.KnowledgeConverter;
import com.wzc.be.es.core.knowledge.index.KnowledgeSearchIndex;
import com.wzc.be.es.core.knowledge.mapper.KnowledgeSearchMapper;
import com.wzc.be.es.core.order.index.OrderIndex;
import com.wzc.be.es.core.waybill.converter.WaybillConverter;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.data.knowledge.qo.KnowledgeSearchQO;
import com.wzc.be.es.data.knowledge.vo.KnowledgeSearchVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/12 17:16
 */
@Slf4j
@Component

public class KnowledgeSearchLogsHandler implements BaseSearchHandler<KnowledgeSearchQO,EsPageVO<KnowledgeSearchVO>> {

    @Resource
    private KnowledgeSearchMapper knowledgeSearchMapper;

    private String searchType = SearchTypeEnums.KNOWLEDGE_SEARCH_LOGS_HANDLER.getType();

    @Value("${knowledge.search.logs.index:sys_knowledge_logs_index}")
    private String index;

    @Resource
    private RestHighLevelClient restHighLevelClient;

    @Override
    public RestResponse<EsPageVO<KnowledgeSearchVO>> search(KnowledgeSearchQO qo) {
        //构建查询参数
        SearchRequest searchRequest = new SearchRequest(index);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();

        if (StrUtil.isNotEmpty(qo.getOperationContent())){
            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(KnowledgeSearchVO::getOperationContent), wildcardsExp(qo.getOperationContent())));
        }
        if (ObjUtil.isNotNull(qo.getOperationType())){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(KnowledgeSearchVO::getOperationType), qo.getOperationType()));
        }
        if (ObjUtil.isNotNull(qo.getOperationObject())){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(KnowledgeSearchVO::getOperationObject), qo.getOperationObject()));
        }
        if (StrUtil.isNotEmpty(qo.getUserName())){
            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(KnowledgeSearchVO::getUserName), wildcardsExp(qo.getUserName())));
        }
        if (ObjUtil.isNotNull(qo.getStartDate()) && ObjUtil.isNotNull(qo.getEndDate())){
            String startTime = DateUtil.format(qo.getStartDate(),Constants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
            String endTime = DateUtil.format(qo.getEndDate(), Constants.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS);
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(KnowledgeSearchVO::getUpdateTime))
                    .gte(startTime).lte(endTime));
        }
        int from = (qo.getPage() == 0 ? qo.getPage() : qo.getPage() - 1) * qo.getSize();
        searchSourceBuilder.from(from);
        searchSourceBuilder.size(qo.getSize());
        searchSourceBuilder.query(mainQueryBuilder);
        searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(KnowledgeSearchVO::getCreateTime)).order(SortOrder.DESC));
        searchRequest.source(searchSourceBuilder);

        try {
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            SearchHit[] hitResult = searchHits.getHits();

            JSONArray array = new JSONArray();
            Stream.of(hitResult).forEach(itemHit -> {
                Map<String, Object> sourceAsMap = itemHit.getSourceAsMap();
                array.add(sourceAsMap);
            });
            List<KnowledgeSearchIndex> listIndex = JSONUtil.toList(array.toJSONString(0), KnowledgeSearchIndex.class);
            EsPageVO<KnowledgeSearchVO> page = new EsPageVO<>();
            page.setList(KnowledgeConverter.INSTANCE.pageInfoToPageVo(listIndex));
            TotalHits totalHits = searchHits.getTotalHits();
            page.setTotal(totalHits.value);

            return RestResponse.success(page);
        }catch (Exception e){
            log.info("知识库查询异常,开始查询doris",e);
            //如果不是 es查询异常 便往外抛
            Throwable root = ExceptionUtil.getRootCause(e);
            if(!(root instanceof ElasticsearchException) || !((ElasticsearchException) root).getDetailedMessage().contains(Constants.ES_RESULT_WINDOW_IS_TOO_LARGE)){
                throw new RuntimeException(e);
            }
            //查询es异常获取不到总数，重新查询
            return RestResponse.success();
        }
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.index;
    }
}
