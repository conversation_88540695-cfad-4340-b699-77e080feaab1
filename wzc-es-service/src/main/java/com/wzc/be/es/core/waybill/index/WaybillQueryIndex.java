package com.wzc.be.es.core.waybill.index;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Schema(
        description = "运单搜索返回内容VO对象"
)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WaybillQueryIndex implements Serializable {

    private String id;

    @Schema(description = "派车单号")
    private String waybillNo;

    @Schema(description = "运单状态")
    private Integer waybillStatus;

    @Schema(description = "派车单状态")
    private Integer status;

    @Schema(description = "结算状态")
    private Integer completeStatus;

    @Schema(description = "承运商公司ID")
    private Long carrierCompanyId;

    @Schema(description = "司机ID")
    private Long driverId;

    @Schema(description = "公司id")
    private Long companyId;

    @Schema(description = "司机名称")
    private String driverName;

    @Schema(description = "车辆ID")
    private Long carId;

    @Schema(description = "车牌号")
    private String carNo;

    @Schema(description = "派车时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "子运单列表")
    private List<SubWaybillBean> subWaybill;

    @Data
    public static class SubWaybillBean implements Serializable {

        @Schema(description = "子运单编号")
        private String subWaybillNo;

        @Schema(description = "子运单状态")
        private Integer subWaybillStatus;

        @Schema(description = "结算状态")
        private Integer completeStatus;

        @Schema(description = "订单编号")
        private String orderNo;

        @Schema(description = "订单或计划运单编号")
        private String parentNo;

        @Schema(description = "货主公司ID")
        private String shipperCompanyId;

        @Schema(description = "货主公司名称")
        private String shipperCompanyName;

        @Schema(description = "承运商名称")
        private String carrierCompanyName;

        @Schema(description = "收货人信息")
        private ReceiverOrSenderBean receiver;

        @Schema(description = "发货人信息")
        private ReceiverOrSenderBean sender;

        @Schema(description = "物料信息集合")
        private List<ItemsBean> items;

        @Data
        public static class ReceiverOrSenderBean implements Serializable {
            @Schema(description = "ID")
            private String id;

            @Schema(description = "地址")
            private String address;

            @Schema(description = "完整地址")
            private String fullAddress;
            private String subWaybillNo;
            private Integer type;
            private String customerName;

            @Schema(description = "地址类型 1地址 2围栏")
            private Integer source;
        }


        @Data
        public static class ItemsBean implements Serializable {
            private String id;
            private String itemId;
            private String itemName;
            private Long speciesId;
            private String subWaybillNo;
        }
    }
}
