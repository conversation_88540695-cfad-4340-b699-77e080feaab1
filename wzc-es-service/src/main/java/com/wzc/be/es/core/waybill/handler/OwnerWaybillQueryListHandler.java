package com.wzc.be.es.core.waybill.handler;


import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baidu.mapcloud.cloudnative.authentication.datapermission.core.rule.UserPermissionRule;
import com.baidu.mapcloud.cloudnative.common.model.PageOrderType;
import com.baidu.mapcloud.cloudnative.common.model.Pagination;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.data.data.waybill.model.dto.WaybillQueryEntityDTO;
import com.wzc.be.es.common.constant.Constants;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.core.waybill.converter.DorisQueryWaybillConverter;
import com.wzc.be.es.core.waybill.converter.WaybillConverter;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.data.waybill.qo.OwnerWaybillListQO;
import com.wzc.be.es.data.waybill.vo.SubWaybillVO;
import com.wzc.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 货主PC运单列表
 *
 * <AUTHOR> Wei
 */
@Slf4j
@Component
public class OwnerWaybillQueryListHandler extends OwnerBaseHandler<OwnerWaybillListQO,Pagination<SubWaybillVO>>{

    @Value("${ownerWaybillQueryList.search.type:ownerWaybillQueryListHandler}")
    private String searchType;

    @Value("${ownerWaybillQueryList.search.index:sub_waybill_index01}")
    private String index;

    @Autowired
    protected UserPermissionRule userPermissionRule;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Override
    public RestResponse<Pagination<SubWaybillVO>> search(OwnerWaybillListQO qo) {
        if (Strings.isNotBlank(qo.getCarNo())) {
            // 传入的车牌号转为大写
            qo.setCarNo(qo.getCarNo().toUpperCase());
        }
        JSONObject jsonObject = authReq(userPermissionRule, JSONUtil.parseObj(qo));
        qo = JSONUtil.toBean(jsonObject.toJSONString(0),OwnerWaybillListQO.class);
        try {
//            if(0==qo.getReqSource()){
////                //非运输档案请求，不判断此处逻辑
////                if(qo.getRequestType() == 1){
////
////                }
//            }
            // 构建搜索请求
            SearchRequest searchRequest = new SearchRequest(index);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            // 排序
            PageOrderType orderType = qo.getOrderType();
            if(orderType != null){
                switch (orderType){
                    case ASC:
                        searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(SubWaybillIndex::getCreateTime)).order(SortOrder.ASC));
                        break;
                    case DESC:
                        searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(SubWaybillIndex::getCreateTime)).order(SortOrder.DESC));
                        break;
                }
            }
            log.info("货主运单请求参数:{}", JSON.toJSONString(qo));
            BoolQueryBuilder mainQueryBuilder = getMainBoolQueryBuilder(qo);

            int size = qo.getSize();
            int from = (qo.getPage() - 1) * size;

            searchSourceBuilder.from(from);
            searchSourceBuilder.size(size);
            searchSourceBuilder.query(mainQueryBuilder);
            searchSourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
            searchSourceBuilder.trackTotalHits(true);
            searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(SubWaybillIndex::getCreateTime)).order(SortOrder.DESC));
            searchRequest.source(searchSourceBuilder);
            log.info("货主运单请求参数DSL:{}", searchSourceBuilder.toString());
            Pagination<SubWaybillVO> page = new Pagination<>();
            page.setPage(from);
            page.setSize(size);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            SearchHit[] hitResult = searchHits.getHits();
            JSONArray array = new JSONArray();
            Stream.of(hitResult).forEach(itemHit -> {
                Map<String, Object> sourceAsMap = itemHit.getSourceAsMap();
                array.add(sourceAsMap);
            });
            TotalHits totalHits = searchHits.getTotalHits();
            List<SubWaybillIndex> list = JSONUtil.toList(array.toJSONString(0),SubWaybillIndex.class);
            page.setTotal(totalHits.value);

            if (CollectionUtils.isEmpty(list)) {
                return RestResponse.success(page);
            }

            for (SubWaybillIndex subWaybillIndex : list) {
                if(CollectionUtils.isNotEmpty(subWaybillIndex.getItems())){
                    Map<String,SubWaybillIndex.BmsItem> bmsItemMap = new HashMap<>();
                    if(CollectionUtils.isNotEmpty(subWaybillIndex.getBmsItems())){
                        bmsItemMap = subWaybillIndex.getBmsItems().stream().collect(Collectors.toMap(SubWaybillIndex.BmsItem::getItemId, Function.identity(),(o1, o2) -> o1));
                    }
                    for (SubWaybillIndex.Item item : subWaybillIndex.getItems()) {
                        SubWaybillIndex.BmsItem bmsItem = bmsItemMap.get(item.getItemId());
                        if(Objects.nonNull(bmsItem)){
                            item.setHzActualSettleWeight(bmsItem.getHzActualSettleWeight());
                            item.setHzActualSettlePrice(bmsItem.getHzActualSettlePrice());
                            item.setCysActualSettlePrice(bmsItem.getCysActualSettlePrice());
                            item.setCysActualSettleWeight(bmsItem.getCysActualSettleWeight());
                        }else {
                            item.setHzActualSettleWeight("0");
                            item.setHzActualSettlePrice("0");
                            item.setCysActualSettlePrice("0");
                            item.setCysActualSettleWeight("0");
                        }
                    }
                }
                if(Objects.nonNull(subWaybillIndex.getBusinessType()) && subWaybillIndex.getBusinessType() == 1){
                    String reStatementNo = subWaybillIndex.getReStatementNo();
                    String reOrderNo = subWaybillIndex.getReOrderNo();
                    subWaybillIndex.setPayStatementNo(reStatementNo);
                    subWaybillIndex.setPayOrderNo(reOrderNo);
                }
            }
            page.setContent(WaybillConverter.INSTANCE.toSubWaybillVOList(list));
            return RestResponse.success(page);
        }catch (Exception e){
            return RestResponse.success();
        }

    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return index;
    }

}
