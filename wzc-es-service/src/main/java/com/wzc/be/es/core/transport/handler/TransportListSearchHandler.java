/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.core.transport.handler;

import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.common.enums.TransportStatusEnums;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.core.transport.converter.TransportConverter;
import com.wzc.be.es.core.transport.index.TransportIndex;
import com.wzc.be.es.core.transport.mapper.TransportIndexMapper;
import com.wzc.be.es.data.transport.qo.TransportQueryPageQO;
import com.wzc.be.es.data.transport.vo.TransportEsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

import static com.wzc.be.es.common.enums.TransportStatusEnums.APPLY_END;
import static java.util.Objects.nonNull;

/**
 * <AUTHOR>
 * @date 2023年07月05日
 * 运输计划集合查询
 */
@Slf4j
@Component
public class TransportListSearchHandler implements BaseSearchHandler<TransportQueryPageQO, List<TransportEsVO>> {

    @Value("${transport.list.search.type:transportList}")
    private String searchType;

    @Value("${transport.search.index:transport_index}")
    private String index;

    @Resource
    private TransportIndexMapper transportResMapper;

    @Override
    public RestResponse<List<TransportEsVO>> search(TransportQueryPageQO waybillQueryPageQo) {
        log.info("运输计划集合模糊查询 search param is : {}", JSONUtil.toJsonStr(waybillQueryPageQo));
        LambdaEsQueryWrapper<TransportIndex> wrapper = generateSearchSourceBuilder(waybillQueryPageQo);
        wrapper.index(index);
        try {
            List<TransportIndex> transportIndexList = transportResMapper.selectList(wrapper);
            return RestResponse.success(TransportConverter.INSTANCE.indexListToVoList(transportIndexList));
        } catch (Exception e) {
            log.error("运输计划集合查询异常.search error", e);
        }
        return RestResponse.error(ExcepitonEnum.ERROR_UNKNOW);
    }

    /**
     * 查询条件组装
     *
     * @param transportQueryPageQO waybillQueryPageQo
     * @return LambdaEsQueryWrapper
     */
    private LambdaEsQueryWrapper<TransportIndex> generateSearchSourceBuilder(TransportQueryPageQO transportQueryPageQO) {
        LambdaEsQueryWrapper<TransportIndex> wrapper = new LambdaEsQueryWrapper<>();

        wrapper.isNotNull(TransportIndex::getTransportNo);
        //承运商id
        Long carrierId = transportQueryPageQO.getCarrierId();
        if (nonNull(carrierId)){
            wrapper.eq(TransportIndex::getCarrierId,carrierId);
        }
        //运输计划分组编号
        String groupNo = transportQueryPageQO.getGroupNo();
        if (StrUtil.isNotEmpty(groupNo)){
            wrapper.eq(TransportIndex::getGroupNo,groupNo);
        }
        //运输计划状态：1 待接单,2 已拒绝,3 进行中,4 已完成,5 暂停 6已取消
        Integer status = transportQueryPageQO.getStatus();
        if (nonNull(status)) {
            //状态为7查询的为停运状态为已申请的运输计划
            if (Objects.equals(status, APPLY_END.getCode())){
                wrapper.eq(TransportIndex::getEndStatus, TransportStatusEnums.RUNNING.getCode());
            }else {
                wrapper.eq(TransportIndex::getStatus, status);
            }
        }
        List<Integer> statusList = transportQueryPageQO.getStatusList();
        if (CollectionUtil.isNotEmpty(statusList)){
            wrapper.in(TransportIndex::getStatus, statusList);
        }
        String companyName = transportQueryPageQO.getCompanyName();
        if(StringUtils.isNotBlank(companyName)){
            wrapper.like(TransportIndex::getCompanyName, companyName);
        }
        wrapper.orderByDesc(TransportIndex::getCreateTime);

        return wrapper;
    }


    @Override
    public String getSearchType() {
        return searchType;
    }

    @Override
    public String getIndex() {
        return index;
    }


}
