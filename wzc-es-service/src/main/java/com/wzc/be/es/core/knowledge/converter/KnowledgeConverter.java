package com.wzc.be.es.core.knowledge.converter;

import com.wzc.be.es.core.knowledge.index.KnowledgeSearchIndex;
import com.wzc.be.es.data.knowledge.vo.KnowledgeSearchVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年07月13日 15:05
 */
@Mapper(componentModel = "spring")
public interface KnowledgeConverter {

    KnowledgeConverter INSTANCE = Mappers.getMapper(KnowledgeConverter.class);

    List<KnowledgeSearchVO> pageInfoToPageVo(List<KnowledgeSearchIndex> list);
}
