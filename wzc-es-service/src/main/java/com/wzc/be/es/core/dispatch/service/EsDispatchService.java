/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.core.dispatch.service;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.handler.SearchHandler;
import com.wzc.be.es.common.service.EsBaseSearchService;
import com.wzc.be.es.data.dispatch.vo.DispatchStatusVO;
import com.wzc.be.es.data.waybill.qo.RequestPageQO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年10月13日 3:17 PM
 */
@Component
@Slf4j
public class EsDispatchService extends EsBaseSearchService {

    /**
     * 查询大单下派车单是否已开启运输
     */
    public RestResponse<List<DispatchStatusVO>> startDispatch(RequestPageQO requestPageQo) {
        String searchType = requestPageQo.getSearchType();
        SearchHandler searchHandler = getSearchHandler(searchType);
        return searchHandler.search( requestPageQo.getRequestJson());
    }

}
