package com.wzc.be.es.common.enums;

import com.baidu.mapcloud.cloudnative.common.model.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PushGsStatusEnums implements IEnum {

    /**
     * 推送gs状态（0-待推送，1-推送中，2-推送成功，3-推送失败，4-初审退回，5-稽核退回（单据删除））
     */
    TO_BE_PUSHED(0, "待推送"),
    PUSHING(1, "推送中"),
    PUSH_SUCCESS(2, "推送成功"),
    PUSH_ERROR(3,"推送失败"),
    RETURN_OF_FIRST_INSTANCE(4, "初审退回"),
    AUDIT_ROLLBACK(5, "稽核退回")
    ;

    private final Integer code;
    private final String name;
}
