package com.wzc.be.es.core.common;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.models.security.SecurityScheme;
import lombok.Data;

/**
 * <p>
 * redis搜索返回类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-20
 */
@Schema(description = "<p> redis搜索返回类 </p>")
@Data
public class CommonRedisResultVO {


    @Schema(description = "唯一标识")
    private String unionNo;

    @Schema(description = "创建时间")
    private String createTime;

    @Schema(description = "删除标记 0.正常 1.删除")
    private Integer delFlag;

    @Schema(description = "变化前状态")
    private Integer status;

    @Schema(description = "变化后状态")
    private Integer afterStatus;
}
