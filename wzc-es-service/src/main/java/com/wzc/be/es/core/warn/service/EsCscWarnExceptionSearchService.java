/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.core.warn.service;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.common.service.EsBaseSearchService;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.data.warn.qo.CscWarnExceptionEsPageQO;
import com.wzc.be.es.data.warn.vo.CscWarnExceptionVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023年12月25日
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class EsCscWarnExceptionSearchService extends EsBaseSearchService {

    /**
     * 运单异常分页查询
     *
     * @param qo 查询参数
     */
    public RestResponse<EsPageVO<CscWarnExceptionVO>> cscWarnExceptionPage(CscWarnExceptionEsPageQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.CSC_WARN_EXCEPTION_PAGE.getType());
        return searchHandler.search(qo);
    }


}
