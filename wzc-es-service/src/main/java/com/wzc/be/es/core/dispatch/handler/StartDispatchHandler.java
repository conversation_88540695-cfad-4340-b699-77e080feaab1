package com.wzc.be.es.core.dispatch.handler;

import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import cn.hutool.json.JSONObject;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.handler.SearchHandler;
import com.wzc.be.es.core.dispatch.converter.DispatchConverter;
import com.wzc.be.es.core.dispatch.mapper.DispatchStatusMapper;
import com.wzc.be.es.core.dispatch.index.DispatchStatusIndex;
import com.wzc.be.es.data.dispatch.vo.DispatchStatusVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class StartDispatchHandler implements SearchHandler {

    @Value("${startDispatch.search.type:startDispatchHandler}")
    private String searchType;

    @Value("${dispatch.search.index:waybill_index00}")
    private String index;

    @Resource
    private DispatchStatusMapper dispatchStatusMapper;

    @Override
    public RestResponse<List<DispatchStatusVO>> search(JSONObject json) {
        List<String> waybillNo = json.getBeanList("waybillNo", String.class);
        Integer waybillStatus = json.getInt("waybillStatus");
        //增加运单状态查询条件
        LambdaEsQueryWrapper<DispatchStatusIndex> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.in("waybillNo",waybillNo)
                .eq("waybillStatus",waybillStatus)
                .index(index);
        List<DispatchStatusIndex> dispatchStatusIndexList = dispatchStatusMapper.selectList(wrapper);
        return RestResponse.success(DispatchConverter.INSTANCE.indexListToVoList(dispatchStatusIndexList));
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.index;
    }
}
