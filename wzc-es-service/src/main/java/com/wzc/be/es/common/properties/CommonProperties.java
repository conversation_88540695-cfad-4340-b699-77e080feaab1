package com.wzc.be.es.common.properties;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Getter
@Component
@RefreshScope
public class CommonProperties {

    @Value("${superAdminCompanyId:8075684883164340225}")
    private Long superAdminCompanyId;
}
