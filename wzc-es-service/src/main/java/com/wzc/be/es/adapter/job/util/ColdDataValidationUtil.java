package com.wzc.be.es.adapter.job.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 冷数据验证工具类
 * 用于验证发送到Kafka的数据完整性
 *
 * <AUTHOR> Assistant
 * @since 2025/02/18
 */
@Slf4j
public class ColdDataValidationUtil {

    /**
     * 必须包含的元数据字段
     */
    private static final Set<String> REQUIRED_META_FIELDS = Set.of(
        "_id", "_index", "_type", "_score",
        "indexName", "indexDescription", "operation", 
        "offlineTime", "offlineTimestamp", "retentionThreshold"
    );

    /**
     * 订单索引必须包含的业务字段
     */
    private static final Set<String> ORDER_REQUIRED_FIELDS = Set.of(
        "orderId", "orderNo", "status", "createTime"
    );

    /**
     * 子运单索引必须包含的业务字段
     */
    private static final Set<String> SUB_WAYBILL_REQUIRED_FIELDS = Set.of(
        "orderId", "orderNo", "subWaybillNo", "subWaybillStatus", "createTime"
    );

    /**
     * 验证冷数据的完整性
     * @param dataList 要验证的数据列表
     * @param indexName 索引名称
     * @return 验证结果
     */
    public static ValidationResult validateColdData(List<Map<String, Object>> dataList, String indexName) {
        if (dataList == null || dataList.isEmpty()) {
            return ValidationResult.success("数据列表为空，无需验证");
        }

        ValidationResult result = new ValidationResult();
        result.setTotalCount(dataList.size());

        for (int i = 0; i < dataList.size(); i++) {
            Map<String, Object> data = dataList.get(i);
            ValidationError error = validateSingleRecord(data, indexName, i);
            
            if (error != null) {
                result.addError(error);
            } else {
                result.incrementValidCount();
            }
        }

        // 记录验证结果
        if (result.hasErrors()) {
            log.warn("冷数据验证发现问题 - 索引: {}, 总数: {}, 有效: {}, 错误: {}", 
                indexName, result.getTotalCount(), result.getValidCount(), result.getErrorCount());
            
            // 记录前5个错误
            result.getErrors().stream().limit(5).forEach(error -> 
                log.warn("验证错误: {}", error.getMessage()));
        } else {
            log.info("冷数据验证通过 - 索引: {}, 总数: {}", indexName, result.getTotalCount());
        }

        return result;
    }

    /**
     * 验证单条记录
     */
    private static ValidationError validateSingleRecord(Map<String, Object> data, String indexName, int index) {
        if (MapUtils.isEmpty(data)) {
            return new ValidationError(index, "数据记录为空");
        }

        // 验证元数据字段
        for (String field : REQUIRED_META_FIELDS) {
            if (!data.containsKey(field) || data.get(field) == null) {
                return new ValidationError(index, "缺少必需的元数据字段: " + field);
            }
        }

        // 验证operation字段值
        if (!"OFFLINE".equals(data.get("operation"))) {
            return new ValidationError(index, "operation字段值不正确: " + data.get("operation"));
        }

        // 验证索引名称一致性
        if (!indexName.equals(data.get("indexName"))) {
            return new ValidationError(index, "索引名称不一致: 期望=" + indexName + ", 实际=" + data.get("indexName"));
        }

        // 根据索引类型验证业务字段
        ValidationError businessFieldError = validateBusinessFields(data, indexName, index);
        if (businessFieldError != null) {
            return businessFieldError;
        }

        return null;
    }

    /**
     * 验证业务字段
     */
    private static ValidationError validateBusinessFields(Map<String, Object> data, String indexName, int index) {
        Set<String> requiredFields = null;

        if (indexName.contains("order_index")) {
            requiredFields = ORDER_REQUIRED_FIELDS;
        } else if (indexName.contains("sub_waybill_index")) {
            requiredFields = SUB_WAYBILL_REQUIRED_FIELDS;
        }

        if (requiredFields != null) {
            for (String field : requiredFields) {
                if (!data.containsKey(field) || data.get(field) == null) {
                    return new ValidationError(index, "缺少必需的业务字段: " + field);
                }
            }
        }

        return null;
    }

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private int totalCount;
        private int validCount;
        private List<ValidationError> errors = new java.util.ArrayList<>();

        public static ValidationResult success(String message) {
            ValidationResult result = new ValidationResult();
            log.info(message);
            return result;
        }

        public boolean hasErrors() {
            return !errors.isEmpty();
        }

        public void addError(ValidationError error) {
            errors.add(error);
        }

        public void incrementValidCount() {
            validCount++;
        }

        public int getErrorCount() {
            return errors.size();
        }

        // Getters and Setters
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        public int getValidCount() { return validCount; }
        public List<ValidationError> getErrors() { return errors; }
    }

    /**
     * 验证错误类
     */
    public static class ValidationError {
        private final int recordIndex;
        private final String message;

        public ValidationError(int recordIndex, String message) {
            this.recordIndex = recordIndex;
            this.message = message;
        }

        public String getMessage() {
            return String.format("记录[%d]: %s", recordIndex, message);
        }

        public int getRecordIndex() { return recordIndex; }
    }
}
