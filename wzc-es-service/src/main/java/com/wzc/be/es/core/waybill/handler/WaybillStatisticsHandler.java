package com.wzc.be.es.core.waybill.handler;

import cn.easyes.core.biz.EsPageInfo;
import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import com.alibaba.fastjson.JSON;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.core.waybill.mapper.SubWaybillListMapper;
import com.wzc.be.es.data.waybill.qo.WaybillStatisticsQO;
import com.wzc.be.es.data.waybill.vo.WaybillStatisticsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 公司运输统计
 */
@Slf4j
@Component
public class WaybillStatisticsHandler implements BaseSearchHandler<WaybillStatisticsQO, WaybillStatisticsVO> {

    @Value("${waybill.statistics.type:waybillStatistics}")
    private String searchType;

    @Value("${waybill.statistics.index:sub_waybill_index01}")
    private String index;

    @Resource
    private SubWaybillListMapper subWaybillListMapper;


    @Override
    public RestResponse<WaybillStatisticsVO> search(WaybillStatisticsQO qo) {
        log.info("公司运输统计请求参数：{}", JSON.toJSONString(qo));
        WaybillStatisticsVO waybillStatisticsVO = new WaybillStatisticsVO();
        LambdaEsQueryWrapper<SubWaybillIndex> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.isNotNull(SubWaybillIndex::getSubWaybillNo);
        wrapper.index(index);
        List<Integer> statusList = new ArrayList<>();
        statusList.add(7);
        statusList.add(8);
        wrapper.in(SubWaybillIndex::getSubWaybillStatus,statusList);

        switch (qo.getRoleType()){
            case OWNER: wrapper.eq(SubWaybillIndex::getShipperCompanyId,qo.getCompanyId()); break;
            case CARRIER: wrapper.eq(SubWaybillIndex::getCarrierCompanyId, qo.getCompanyId()); break;
        }

        if(StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())){
            wrapper.ge(SubWaybillIndex::getCreateTime, qo.getStartTime());
            wrapper.le(SubWaybillIndex::getCreateTime, qo.getEndTime());
        }

        EsPageInfo<SubWaybillIndex> page1 = subWaybillListMapper.pageQuery(wrapper, 1, 1);
        long waybillTotal = page1.getTotal(); // 运单总数


        wrapper.eq(SubWaybillIndex::getZbStatus,1);
        EsPageInfo<SubWaybillIndex> page2 = subWaybillListMapper.pageQuery(wrapper, 1, 1);
        long zbTotal = page2.getTotal(); // 网货运单总数

        waybillStatisticsVO.setWaybillTotal(waybillTotal);
        waybillStatisticsVO.setZbTotal(zbTotal);
        return RestResponse.success(waybillStatisticsVO);
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.index;
    }
}
