/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.core.business.handler;

import cn.easyes.common.utils.CollectionUtils;
import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.google.common.collect.Lists;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.enums.ComplianceEnums;
import com.wzc.be.data.data.es.toc.model.TocRdBusinessPageQO;
import com.wzc.be.data.data.es.toc.model.TocRdBusinessVO;
import com.wzc.be.es.adapter.data.TocRdBusinessIndexFeignClient;
import com.wzc.be.es.common.constant.Constants;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.common.enums.SubWaybillStatusEnum;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.core.business.converter.TocRdConverter;
import com.wzc.be.es.core.business.index.TocBusinessIndex;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.data.business.qo.TocBusinessEsPageQO;
import com.wzc.be.es.data.business.vo.TocEsBusinessVO;
import com.wzc.be.es.data.waybill.qo.FbfSignListQO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchType;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023年10月13日
 * bms toc对账单分页查询
 */
@Slf4j
@Component
public class BmsTocRdPageSearchHandler extends TocBusinessBaseSearchHandler<TocBusinessEsPageQO, EsPageVO<TocEsBusinessVO>> {

    @Resource
    private EsIndexProperties esIndexProperties;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private TocRdBusinessIndexFeignClient tocRdBusinessIndexFeignClient;

    @Override
    public RestResponse<EsPageVO<TocEsBusinessVO>> search(TocBusinessEsPageQO queryPageQo) {
        SearchSourceBuilder sourceBuilder = generateSearchSourceBuilder(queryPageQo);
        EsPageVO<TocEsBusinessVO> esPageRes = new EsPageVO<>();

        try {
            // 构建搜索请求
            SearchRequest searchRequest = new SearchRequest(esIndexProperties.getBmsTocBusinessIndex());
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            Integer size = queryPageQo.getSize();
            Integer from = (queryPageQo.getPage() - 1) * size;
            searchSourceBuilder.from(from);
            searchSourceBuilder.size(size);
            searchSourceBuilder.trackTotalHits(true);
            BoolQueryBuilder mainQueryBuilder = genSearchSource(queryPageQo);

            searchRequest.source(sourceBuilder);
            searchSourceBuilder.query(mainQueryBuilder);
            if (ObjectUtil.isNotNull(queryPageQo.getLastSortValues()) && queryPageQo.getLastSortValues().length > 0){
                searchSourceBuilder.searchAfter(Convert.toLongArray(queryPageQo.getLastSortValues()));
            }
            searchRequest.source(searchSourceBuilder);
            searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(SubWaybillIndex::getCreateTime)).order(SortOrder.DESC));
            log.info("toc对账单模糊查询 search param is : {}", searchSourceBuilder.toString());
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            TotalHits totalHits = searchHits.getTotalHits();
            SearchHit[] hitResult = searchHits.getHits();
            JSONArray array = new JSONArray();
            Stream.of(hitResult).forEach(itemHit -> {
                Map<String, Object> sourceAsMap = itemHit.getSourceAsMap();
                array.add(sourceAsMap);
            });
            List<TocEsBusinessVO> list = JSONUtil.toList(array, TocEsBusinessVO.class);

            esPageRes.setTotal(totalHits.value);
            esPageRes.setList(list);
            if (hitResult.length > 0) {
                Object[] lastSortValues = hitResult[hitResult.length - 1].getSortValues();
                esPageRes.setLastSortValues(lastSortValues);
            }
            return RestResponse.success(esPageRes);
        } catch (Exception e) {
            log.error("toc对账单查询异常.search error:", e);

            try {
                //如果不是 es查询异常 便往外抛
                if (!(e instanceof ElasticsearchStatusException)
                        || !((ElasticsearchStatusException) e).getDetailedMessage().contains(Constants.ES_SEARCH_PHASE_EXECUTION_EXCEPTION)) {
                    throw e;
                }
                //查询es异常获取不到总数，重新查询
                CountRequest countRequest = new CountRequest(getIndex());
                countRequest.source(sourceBuilder);
                CountResponse countResponse = null;

                countResponse = restHighLevelClient.count(countRequest, RequestOptions.DEFAULT);
                esPageRes.setTotal(countResponse.getCount());
            } catch (Exception exception) {
                exception.printStackTrace();
                return RestResponse.error(ExcepitonEnum.ERROR_UNKNOW);
            }
            TocRdBusinessPageQO tocRdBusinessPageQO = TocRdConverter.INSTANCE.esToDorisQueryDto(queryPageQo);
            List<TocRdBusinessVO> tocEsBusinessVOList = tocRdBusinessIndexFeignClient.search(tocRdBusinessPageQO);
            List<TocEsBusinessVO> list = TocRdConverter.INSTANCE.esToDorisQueryPageDto(tocEsBusinessVOList);
            esPageRes.setList(list);
            return RestResponse.success(esPageRes);
        }

    }

    /**
     * 查询条件组装
     *
     * @param queryPageQo 请求参数
     * @return LambdaEsQueryWrapper
     */
    private SearchSourceBuilder generateSearchSourceBuilder(TocBusinessEsPageQO queryPageQo) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder mainQueryBuilder = getMainBoolQueryBuilder(queryPageQo,null);
        Integer from = queryPageQo.getPage();
        Integer size = queryPageQo.getSize();

        int fromElasticsearch = (from == 0 ? from : from - 1) * size;
        sourceBuilder.size(size);
        sourceBuilder.from(fromElasticsearch);
        String[] fetchSourceFields = {FieldUtils.val(TocBusinessIndex::getSubWaybillNo)};
        sourceBuilder.fetchSource(fetchSourceFields,null);
        sourceBuilder.query(mainQueryBuilder);
        return sourceBuilder;
    }

    @Override
    public String getSearchType() {
        return SearchTypeEnums.TOC_BUSINESS_RC_YD_PAGE.getType();
    }

    @Override
    public String getIndex() {
        return esIndexProperties.getBmsTocBusinessIndex();
    }

    public BoolQueryBuilder genSearchSource(TocBusinessEsPageQO queryPageQo) {
        BoolQueryBuilder mainBoolQueryBuilder = QueryBuilders.boolQuery();
        //业务类型
        Integer businessType = queryPageQo.getBusinessType();
        if(ObjectUtil.isNotNull(businessType)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getBusinessType), businessType));
        }
        List<Integer> businessTypeList = queryPageQo.getBusinessTypeList();
        if(CollectionUtils.isNotEmpty(businessTypeList)){
            mainBoolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(TocBusinessIndex::getBusinessType), businessTypeList));
        }
        //订单号
        String orderNo = queryPageQo.getOrderNo();
        if (StrUtil.isNotEmpty(orderNo)) {
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,orderNo,true,List.of(FieldUtils.val(TocBusinessIndex::getOrderNo)));
        }
        //运输计划编号
        String transportNo = queryPageQo.getTransportNo();
        if (StrUtil.isNotEmpty(transportNo)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,transportNo,true,List.of(FieldUtils.val(TocBusinessIndex::getTransportNo)+".text"));
        }
        //运单号
        String dispatchNo = queryPageQo.getDispatchNo();
        if (StrUtil.isNotEmpty(dispatchNo)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,dispatchNo,true,List.of(FieldUtils.val(TocBusinessIndex::getDispatchNo)));
        }
        //子运单号
        String subWaybillNo = queryPageQo.getSubWaybillNo();
        if (StrUtil.isNotEmpty(subWaybillNo)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,subWaybillNo,true,List.of(FieldUtils.val(TocBusinessIndex::getSubWaybillNo)));
        }
        //发布方id
        Long apCompanyId = queryPageQo.getApCompanyId();
        if (ObjectUtil.isNotNull(apCompanyId)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getApCompanyId), apCompanyId));
        }
        //发布方类型
        Integer apCompanyType = queryPageQo.getApCompanyType();
        if (ObjectUtil.isNotNull(apCompanyType)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getApCompanyType),apCompanyType));
        }
        //对账状态
        Integer rcStatus = queryPageQo.getRcStatus();
        if (ObjectUtil.isNotNull(rcStatus)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getRcStatus),rcStatus));
        }
        //对账状态
        Integer pushGsStatusAp = queryPageQo.getPushGsStatusAp();
        if (ObjectUtil.isNotNull(pushGsStatusAp)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getPushGsStatusAp),pushGsStatusAp));
        }
        //对账状态
        Integer pushGsStatusApPay = queryPageQo.getPushGsStatusApPay();
        if (ObjectUtil.isNotNull(pushGsStatusApPay)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getPushGsStatusApPay),pushGsStatusApPay));
        }
        //平台登录
        Long shipperCompanyId = queryPageQo.getShipperCompanyId();
        if (ObjectUtil.isNotNull(shipperCompanyId)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getShipperCompanyId),shipperCompanyId));
        }
        //平台审核状态
        Integer shipperAuditStatus = queryPageQo.getShipperAuditStatus();
        if (ObjectUtil.isNotNull(shipperAuditStatus)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getShipperAuditStatus),shipperAuditStatus));
        }
        //派车时间比较
        String dispatchTimeStart = queryPageQo.getDispatchTimeStart();
        String dispatchTimeEnd = queryPageQo.getDispatchTimeEnd();
        if (StrUtil.isNotEmpty(dispatchTimeStart)) {
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getDispatchTime))
                    .gte(dispatchTimeStart));
        }
        if (StrUtil.isNotEmpty(dispatchTimeEnd)) {
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getDispatchTime))
                    .lte(dispatchTimeEnd));
        }
        //司机运输时间
        String transportTimeStart = queryPageQo.getTransportTimeStart();
        String transportTimeEnd = queryPageQo.getTransportTimeEnd();
        if (StrUtil.isNotBlank(transportTimeStart)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getTransportTime))
                    .gte(transportTimeStart));
        }
        if (StrUtil.isNotBlank(transportTimeEnd)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getTransportTime))
                    .lte(transportTimeEnd));
        }
        //司机卸货时间
        String unloadTimeStart = queryPageQo.getUnloadTimeStart();
        String unloadTimeEnd = queryPageQo.getUnloadTimeEnd();
        if (StrUtil.isNotBlank(unloadTimeStart)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getUnloadTime))
                    .gte(unloadTimeStart));
        }
        if (StrUtil.isNotBlank(unloadTimeEnd)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getUnloadTime))
                    .lte(unloadTimeEnd));
        }
        //发起支付时间
        String payStartTime = queryPageQo.getPayStartTime();
        String payEndTime = queryPageQo.getPayEndTime();
        if (StrUtil.isNotBlank(payStartTime)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getPayTime))
                    .gte(payStartTime));
        }
        if (StrUtil.isNotBlank(payEndTime)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getPayTime))
                    .lte(payEndTime));
        }

        //发起支付时间
        String toAccountStartTime = queryPageQo.getToAccountStartTime();
        String toAccountEndTime = queryPageQo.getToAccountEndTime();
        if (StrUtil.isNotBlank(toAccountStartTime)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getToAccountTime))
                    .gte(toAccountStartTime));
        }
        if (StrUtil.isNotBlank(toAccountEndTime)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getToAccountTime))
                    .lte(toAccountEndTime));
        }
        //签收时间
        String createStartTime = queryPageQo.getCreateStartTime();
        String createEndTime = queryPageQo.getCreateEndTime();
        if (StrUtil.isNotBlank(createStartTime)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getCreateTime))
                    .gte(createStartTime));
        }
        if (StrUtil.isNotBlank(createEndTime)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getCreateTime))
                    .lte(createEndTime));
        }

        //支付完成时间
        String payFinishStartTime = queryPageQo.getPayFinishStartTime();
        String payFinishEndTime = queryPageQo.getPayFinishEndTime();
        if (StrUtil.isNotBlank(payFinishStartTime)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getPayFinishTime))
                    .gte(payFinishStartTime));
        }
        if (StrUtil.isNotBlank(payFinishEndTime)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getPayFinishTime))
                    .lte(payFinishEndTime));
        }

        //申诉完成时间
        String appealFinishStartTime = queryPageQo.getAppealFinishStartTime();
        String appealFinishEndTime = queryPageQo.getAppealFinishEndTime();
        if (StrUtil.isNotBlank(appealFinishStartTime)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getAppealFinishTime))
                    .gte(appealFinishStartTime));
        }
        if (StrUtil.isNotBlank(appealFinishEndTime)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getAppealFinishTime))
                    .lte(appealFinishEndTime));
        }

        //车牌号
        String carNo = queryPageQo.getCarNo();
        if (StrUtil.isNotEmpty(carNo)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,carNo,true,List.of(FieldUtils.val(TocBusinessIndex::getCarNo)));
        }
        //发货单位
        String fmCustomerName = queryPageQo.getFmCustomerName();
        if (StrUtil.isNotEmpty(fmCustomerName)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,fmCustomerName,true,List.of(FieldUtils.val(TocBusinessIndex::getFmCustomerName)));
        }
        //收货单位
        String toCustomerName = queryPageQo.getToCustomerName();
        if (StrUtil.isNotEmpty(toCustomerName)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,toCustomerName,true,List.of(FieldUtils.val(TocBusinessIndex::getToCustomerName)));
        }
        //服务方姓名
        String arCompanyName = queryPageQo.getArCompanyName();
        if (StrUtil.isNotBlank(arCompanyName)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,arCompanyName,true,List.of(FieldUtils.val(TocBusinessIndex::getArCompanyName)));
        }

        //服务方姓名
        String apCompanyName = queryPageQo.getApCompanyName();
        if (StrUtil.isNotBlank(apCompanyName)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,apCompanyName,true,List.of(FieldUtils.val(TocBusinessIndex::getApCompanyName)));
        }
        //货物名称
        String itemName = queryPageQo.getItemName();
        if (StrUtil.isNotBlank(itemName)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,itemName,true,StrUtil.split("items.itemName",","));
        }
        //支付方式
        Integer paymentMethod = queryPageQo.getPaymentMethod();
        if (ObjectUtil.isNotNull(paymentMethod)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getPaymentMethod),paymentMethod));
        }
        //发货地址
        String fmAddress = queryPageQo.getFmAddress();
        if (StrUtil.isNotBlank(fmAddress)){
            List<String> columns = Lists.newArrayList();
            columns.add(FieldUtils.val(TocBusinessIndex::getFmAddress));
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,fmAddress,true,columns);
        }
        //收货地址
        String toAddress = queryPageQo.getToAddress();
        if (StrUtil.isNotBlank(toAddress)){
            List<String> columns = Lists.newArrayList();
            columns.add(FieldUtils.val(TocBusinessIndex::getToAddress));
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,toAddress,true,columns);
        }
        //导出的时候是否有选中单号
        List<String> exportSubWaybillNoList = queryPageQo.getExportSubWaybillNoList();
        if (CollectionUtil.isNotEmpty(exportSubWaybillNoList)){
            mainBoolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(TocBusinessIndex::getSubWaybillNo),exportSubWaybillNoList));
        }
        //支付状态
        Integer payStatus = queryPageQo.getPayStatus();
        if (ObjectUtil.isNotNull(payStatus)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getPayStatus),payStatus));
        }
        //操作人
        String payOperatorName = queryPageQo.getPayOperatorName();
        if (StrUtil.isNotBlank(payOperatorName)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getPayOperatorName)+ ".keyword",payOperatorName));
        }
        return mainBoolQueryBuilder;
    }
}
