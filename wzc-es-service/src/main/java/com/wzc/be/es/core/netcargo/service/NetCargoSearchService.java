package com.wzc.be.es.core.netcargo.service;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.common.service.EsBaseSearchService;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.data.netcargo.qo.NetCargoEsQO;
import com.wzc.be.es.data.netcargo.vo.NetCargoCountEsVO;
import com.wzc.be.es.data.netcargo.vo.NetCargoEsVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class NetCargoSearchService extends EsBaseSearchService {

    public RestResponse<EsPageVO<NetCargoEsVO>> page(NetCargoEsQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.NET_CARGO_PAGE.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<NetCargoCountEsVO> count(NetCargoEsQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.NET_CARGO_COUNT.getType());
        return searchHandler.search(qo);
    }
}
