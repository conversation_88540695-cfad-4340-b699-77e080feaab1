/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.core.waybill.service;

import com.baidu.mapcloud.cloudnative.common.model.Pagination;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.common.service.EsBaseSearchService;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.commons.PageVO;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.data.waybill.qo.*;
import com.wzc.be.es.data.waybill.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年10月13日 3:17 PM
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class EsWaybillSearchService extends EsBaseSearchService {

    /**
     * 运单分页查询
     *
     * @param qo 查询参数
     * @return RestResponse<PageVO < WaybillQueryVO>>
     */
    public RestResponse<PageVO<WaybillQueryVO>> waybillQuery(DriverWaybillListQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.DRIVER_APP_ORDER_LIST.getType());
        return searchHandler.search(qo);
    }

    /**
     * 承运商PC端运单列表
     */
    public RestResponse<PageVO<CarrierWaybillListVO>> carrierWaybillList(CarrierWaybillListQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.CARRIER_PC_ORDER_LIST.getType());
        return searchHandler.search(qo);
    }

    /**
     * 司机开启运单校验是否有已开启运单查询
     */
    public RestResponse<WaybillQueryVO> activateWaybill(CheckActivateWaybillQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.CHECK_ACTIVATE_WAYBILL.getType());
        return searchHandler.search(qo);
    }

    /**
     * 司机已派车成功的运单查询
     */
    public RestResponse<List<WaybillQueryVO>> dispatchWaybill(CheckDispatchWaybillQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.CHECK_DISPATCH_WAYBILL.getType());
        return searchHandler.search(qo);
    }

    /**
     * 小程序验货人运单列表
     */
    public RestResponse<PageVO<InspectorWaybillListVO>> inspectorWaybillList(InspectorListQo qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.INSPECTOR_WAYBILL.getType());
        return searchHandler.search(qo);
    }

    /**
     * 查询运单信息
     */
    public RestResponse<WaybillQueryVO> selectWaybillInfo(SelectWaybillInfoQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.WAYBILL.getType());
        return searchHandler.search(qo);
    }

    /**
     * 承运商列表tab页统计
     */
    public RestResponse<CarrierTabGroupCountVO> carrierTabGroupCount(CarrierTabGroupCountQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.CARRIER_TAB_GROUP.getType());
        return searchHandler.search(qo);
    }

    /**
     * 承运商APP运单列表
     */
    public RestResponse<PageVO<WaybillQueryVO>> carrierAppOrderList(CarrierAppOrderListQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.CARRIER_APP_ORDER_LIST.getType());
        return searchHandler.search(qo);
    }


    /**
     * OMS查询运单列表
     */
    public RestResponse<List<WaybillQueryVO>> omsSearchWaybill(OmsSearchWaybillQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.OMS_SEARCH_WAYBILL_LIST.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<Pagination<SubWaybillVO>> ownerWaybillList(OwnerWaybillListQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.OWNER_WAYBILL_QUERY_LIST.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<EsPageVO<SubWaybillVO>> ownerWaybillListAfter(OwnerWaybillListQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.OWNER_WAYBILL_QUERY_LIST_AFTER.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<EsPageVO<CarrierWaybillListVO>> carrierWaybillListAfter(CarrierWaybillListQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.CARRIER_WAYBILL_QUERY_LIST_AFTER.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<CarrierTabGroupCountVO> ownerTabGroupCount(OwnerWaybillListQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.OWNER_TAB_GROUP.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<List<SubWaybillVO>> subWaybillList(SubWaybillQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.SUB_WAYBILL_LIST.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<Pagination<SubWaybillVO>> exceptionWaybillList(ExceptionWaybillListQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.EXCEPTION_WAYBILL_LIST.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<ExceptionCountVO> exceptionTabCount(ExceptionWaybillListQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.EXCEPTION_TAB_COUNT.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<Boolean> checkDriverWaybill(CheckDriverWaybillQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.CHECK_DRIVER_WAYBILL.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<List<OrderCountVO>> orderCount(OrderCountQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.PLAN_ORDER_COUNT.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<List<TransportCountVO>> transportCount(TransportCountQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.TRANSPORT_NO_COUNT.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<Pagination<FbfSignListVO>> fbfWaybillList(FbfSignListQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.FBF_WAYBILL.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<Boolean> ownerWaybill(OwnerWaybillQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.OWNER_WAYBILL.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<WaybillStatisticsVO> waybillStatistics(WaybillStatisticsQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.WAYBILL_STATISTICS.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<EsPageVO<FbfSignListVO>> fbfWaybillListExport(FbfSignListQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.FBF_WAYBILL_EXPORT.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<List<SubWaybillVO>> selectByCompanyAndWaybillNo(SubWaybillQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.WAYBILL_QUERY.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<Pagination<SuperviseOrderPageVO>> superviseList(SuperviseOrderPageQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.SUPERVISE_WAYBILL_LIST.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<EsPageVO<SuperviseOrderPageVO>> superviseListExport(SuperviseOrderPageQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.SUPERVISE_WAYBILL_LIST_EXPORT.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<Pagination<PlanWaybillVO>> selectByPlanNo(PlanWaybillQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.PLAN_WAYBILL.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<List<RelevanceWaybillVO>> relevanceWaybill(RelevanceWaybillQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.RELEVANCE_WAYBILL.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<EsPageVO<SubWaybillVO>> selectWaybillPage(SubWaybillQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.WAYBILL_QUERY_PAGE.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<Boolean> checkDriverAndCarWaybill(CheckDriverAndCarWaybillQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.CHECK_DRIVER_AND_CAR_WAYBILL_HANDLER.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<List<WaybillQueryVO>> searchWaybillByGoodsNoAndRound(DriverWaybillListQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.DRIVER_GRABBING_WAYBILL_LIST.getType());
        return searchHandler.search(qo);
    }

    public RestResponse<Boolean> fbfCompleteStatusWaybill(FbfCompleteStatusWaybillQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.FBF_COMPLETE_STATUS_WAYBILL.getType());
        return searchHandler.search(qo);
    }


    public RestResponse<Pagination<SuperviseOrderPageVO>> superviseTobList(SuperviseOrderPageQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.SUPERVISE_TOB_WAYBILL_LIST.getType());
        return searchHandler.search(qo);
    }


    public RestResponse<Pagination<SuperviseOrderPageVO>> reportStatusList(RepostStatusQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.REPOST_STATUS_WAYBILL_LIST.getType());
        return searchHandler.search(qo);
    }
}
