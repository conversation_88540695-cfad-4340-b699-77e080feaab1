/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.core.common;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.enums.DeletedEnums;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.common.service.EsBaseSearchService;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.commons.qo.CommonRequestQO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.wzc.be.es.common.constant.Constants.INSERT_DELETE_INDEX_REDIS_KEY;
import static com.wzc.be.es.common.constant.Constants.UPDATE_INDEX_REDIS_KEY;

/**
 * <AUTHOR>
 * @date 2022年10月13日 3:17 PM
 */
@Service
@Slf4j
public class EsCommonService extends EsBaseSearchService{

    @Autowired
    private RedissonClient redissonClient;

    /**
     * index相关数据新增 缓存redis过期时间 单位 毫秒
     */
    @Value("${es.insert.dataTo.redis.expireTime:100}")
    private Integer insertExpireSeconds;

    /**
     * index相关数据删除 缓存redis过期时间 单位 毫秒
     */
    @Value("${es.delete.dataTo.redis.expireTime:100}")
    private Integer delExpireSeconds;

    /**
     * index相关数据更新 缓存redis过期时间 单位 毫秒
     */
    @Value("${es.update.dataTo.redis.expireTime:100}")
    private Integer updExpireSeconds;

    /**
     * index相关数据缓存最大数
     */
    @Value("${es.insert.dataTo.redis.maxSize:100}")
    private Integer indexMaxSize;

    /**
     * 索引关联数据新增 存redis
     * @param commonRequestQo commonRequestQO
     * @return RestResponse
     */
    public RestResponse<Boolean> insert(CommonRequestQO commonRequestQo) {
        SearchTypeEnums searchTypeEnums = commonRequestQo.getSearchTypeEnums();
        BaseSearchHandler baseSearchHandler = getBaseSearchHandler(searchTypeEnums.getType());
        //按companyId分组
        Map<Long, List<CommonRequestQO.CommonRequestItemQO>> requestItemQoMap = requestItemQoMap(commonRequestQo.getCommonRequestItemQoList());
        requestItemQoMap.forEach((key,value) ->{
            RMapCache<String, String> mapCache = redissonClient.getMapCache(INSERT_DELETE_INDEX_REDIS_KEY + baseSearchHandler.getIndex() + ":" + key);
            mapCache.setMaxSize(indexMaxSize);
            for (CommonRequestQO.CommonRequestItemQO item : value) {
                CommonRedisResultVO commonRedisResultVO = dealRedisResultVO(item.getUnionNo(),DeletedEnums.YES,null,null);
                mapCache.fastPut(item.getUnionNo(), JSONUtil.toJsonStr(commonRedisResultVO),insertExpireSeconds, TimeUnit.MILLISECONDS);
            }
        });
        return RestResponse.success(true);
    }

    /**
     * 索引关联数据删除 存redis
     * @param commonRequestQo commonRequestQO
     * @return RestResponse
     */
    public RestResponse<Boolean> delete(CommonRequestQO commonRequestQo) {
        SearchTypeEnums searchTypeEnums = commonRequestQo.getSearchTypeEnums();
        BaseSearchHandler baseSearchHandler = getBaseSearchHandler(searchTypeEnums.getType());
        //按companyId分组
        Map<Long, List<CommonRequestQO.CommonRequestItemQO>> requestItemQoMap = requestItemQoMap(commonRequestQo.getCommonRequestItemQoList());
        requestItemQoMap.forEach((key,value) -> {
            RMapCache<String, String> mapCache = redissonClient.getMapCache(INSERT_DELETE_INDEX_REDIS_KEY + baseSearchHandler.getIndex() + ":" + key);
            mapCache.setMaxSize(indexMaxSize);
            for (CommonRequestQO.CommonRequestItemQO item : value) {
                CommonRedisResultVO commonRedisResultVO = dealRedisResultVO(item.getUnionNo(),DeletedEnums.NO,null,null) ;
                mapCache.fastPut(item.getUnionNo(), JSONUtil.toJsonStr(commonRedisResultVO),delExpireSeconds, TimeUnit.MILLISECONDS);
            }
        });
        return RestResponse.success(true);
    }

    /**
     * 索引关联数据更新 存redis
     * @param qo qo
     * @return Boolean
     */
    public RestResponse<Boolean> update(CommonRequestQO qo) {
        SearchTypeEnums searchTypeEnums = qo.getSearchTypeEnums();
        BaseSearchHandler baseSearchHandler = getBaseSearchHandler(searchTypeEnums.getType());
        //按companyId分组
        Map<Long, List<CommonRequestQO.CommonRequestItemQO>> requestItemQoMap = requestItemQoMap(qo.getCommonRequestItemQoList());
        requestItemQoMap.forEach((key,value) -> {
            RMapCache<String, String> mapCache = redissonClient.getMapCache(UPDATE_INDEX_REDIS_KEY + baseSearchHandler.getIndex() + ":" + key);
            mapCache.setMaxSize(indexMaxSize);
            for (CommonRequestQO.CommonRequestItemQO item : value) {
                CommonRedisResultVO commonRedisResultVO = dealRedisResultVO(item.getUnionNo(),null,item.getStatus(),item.getAfterStatus());
                mapCache.fastPut(item.getUnionNo(), JSONUtil.toJsonStr(commonRedisResultVO),updExpireSeconds, TimeUnit.MILLISECONDS);
            }
        });
        return RestResponse.success(true);
    }

    /**
     * 按companyId进行分组
     * @param requestItemQoList 请求集合参数
     * @return Map
     */
    private Map<Long, List<CommonRequestQO.CommonRequestItemQO>> requestItemQoMap(List<CommonRequestQO.CommonRequestItemQO> requestItemQoList){
        //按companyId分组
        return requestItemQoList.stream().collect(Collectors.groupingBy(CommonRequestQO.CommonRequestItemQO::getCompanyId));
    }

    /**
     * 处理存入redis数据
     * @param unionNo 唯一编号
     * @return CommonRedisResultVO
     */
    private CommonRedisResultVO dealRedisResultVO(String unionNo,DeletedEnums deletedEnums,Integer beforeStatus,Integer afterStatus){
        CommonRedisResultVO commonRedisResultVo = new CommonRedisResultVO();
        commonRedisResultVo.setUnionNo(unionNo);
        if (ObjectUtil.isNotNull(deletedEnums)){
            commonRedisResultVo.setDelFlag(deletedEnums.getCode());
            if (ObjectUtil.equals(DeletedEnums.YES,deletedEnums)){
                commonRedisResultVo.setCreateTime(DateUtil.formatDateTime(DateUtil.date()));
            }
        }
        if (ObjectUtil.isNotNull(beforeStatus)){
            commonRedisResultVo.setStatus(beforeStatus);
        }
        if (ObjectUtil.isNotNull(afterStatus)){
            commonRedisResultVo.setAfterStatus(afterStatus);
        }
        return commonRedisResultVo;
    }

}
