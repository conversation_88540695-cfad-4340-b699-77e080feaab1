package com.wzc.be.es.common.enums;

import com.baidu.mapcloud.cloudnative.common.model.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum QueryTypeEnums implements IEnum {

    /**
     * 查询类型 1.订单查询 2.运输计划查询
     */
    ORDER(1,"订单查询"),
    TRANSPORT(2,"运输计划查询");

    private Integer code;

    private String name;

    public static String getName(Integer code) {
        for (QueryTypeEnums c : QueryTypeEnums.values()) {
            if (c.getCode().equals(code)) {
                return c.getName();
            }
        }
        return null;
    }
}
