package com.wzc.be.es.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OrderSearchTypeEnums {

    /**
     * 订单app搜索类型枚举
     */
    ALL(1, "全部"),
    ORDER_NO(2, "订单号搜索"),
    THIRD_ORDER_NO(3, "第三方订单号搜索"),
    SEND_ADDRESS(4, "发货地址搜索"),
    RECEIVE_ADDRESS(5, "收货地址搜索"),
    GOODS_NAME(6, "货物名称搜索"),
    CONTRACT_NO(7, "合同编号集合"),
    ;


    private final Integer code;

    private final String name;

}
