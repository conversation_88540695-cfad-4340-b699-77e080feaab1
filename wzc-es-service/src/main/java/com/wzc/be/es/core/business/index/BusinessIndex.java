package com.wzc.be.es.core.business.index;

import cn.easyes.annotation.IndexField;
import cn.easyes.annotation.rely.FieldType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class BusinessIndex {

    @Schema(description = "esID")
    private String id;

    @Schema(description = "业务ID")
    private Long businessId;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "运输计划号")
    private String transportNo;

    @Schema(description = "子运单号")
    private String dispatchNo;

    @Schema(description = "子运单号")
    private String subWaybillNo;

    @Schema(description = "应收结算单号")
    private String arSettlementNo;

    @Schema(description = "应付结算单号")
    private String apSettlementNo;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd")
    @Schema(description = "承运商发起对账日期")
    private String reconciliationDate;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd")
    @Schema(description = "平台发起对账日期")
    private String shipperReconciliationDate;

    @Schema(description = "货主审核状态")
    private Integer arAuditStatus;

    @Schema(description = "平台对上审核状态")
    private Integer shipperAuditStatusUp;

    @Schema(description = "平台对下审核状态")
    private Integer shipperAuditStatus;

    @Schema(description = "承运商审核状态")
    private Integer apAuditStatus;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "货主审核时间")
    private String apAuditTime;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "平台对上审核时间")
    private String shipperAuditTimeUp;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "平台对上审核时间")
    private String shipperAuditTime;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "承运商审核时间")
    private String arAuditTime;

    @Schema(description = "货主对账状态")
    private Integer rcStatus;

    @Schema(description = "平台对账状态")
    private Integer shipperRcStatus;

    @Schema(description = "车牌号")
    private String carNo;

    @Schema(description = "司机姓名")
    private String driverName;

    @Schema(description = "业务类型")
    private Integer businessType;

    @Schema(description = "订单类型")
    private Integer orderType;

    @Schema(description = "结算状态")
    private Integer settlementStatus;

    @Schema(description = "结算状态对上-平台自营")
    private Integer settlementStatusUp;

    @Schema(description = "托运公司id")
    private Long shipperCompanyId;

    @Schema(description = "货主公司id")
    private Long companyId;

    @Schema(description = "发货单位名称")
    private String fmCustomerName;

    @Schema(description = "收货单位名称")
    private String toCustomerName;

    @Schema(description = "承运商公司id")
    private Long carrierId;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private String createTime;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private String updateTime;

    @Schema(description = "付款方名称")
    private String apCompanyName;

    @Schema(description = "收款方名称")
    private String arCompanyName;

    @Schema(description = "货物信息")
    private List<Items> items;

    @Schema(description = "转包关系")
    private List<Relation> relation;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "派车时间")
    private String sentCarTime;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "开启任务时间")
    private String waybillStartTime;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "装货签到时间")
    private String loadSignInTime;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "装货确认时间")
    private String loadConfirmTime;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "卸货签到时间")
    private String unloadSignInTime;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "卸货确认时间")
    private String unloadConfirmTime;

    @Schema(description = "运输方式")
    private Integer transportType;

    @Schema(description = "承运商是否调量")
    private Integer isAdjustWeight;

    @Schema(description = "平台是否调量")
    private Integer shipperIsAdjustWeight;

    @Schema(description = "对下推送gs状态")
    private Integer pushGsStatus;

    @Schema(description = "对上推送gs状态")
    private Integer pushGsStatusUp;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "装货一次过磅时间")
    private Date loadFirstTime;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "装货二次过磅时间")
    private Date loadSecondTime;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "卸货一次过磅时间")
    private Date unloadFirstTime;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "卸货二次过磅时间")
    private Date unloadSecondTime;

    @Schema(description = "运单来源")
    private Integer dispatchSource;

    @Schema(description = "发货地址")
    private String fmAddress;

    @Schema(description = "收货地址")
    private String toAddress;

    @Schema(description = "货主单价")
    private Integer hzPrice;

    @Schema(description = "承运商单价")
    private Integer ptPrice;

    @Schema(description = "(运营)是否异常处理")
    private Integer  isAbnormal;

    @Schema(description = "(承运商)是否异常处理")
    private Integer  isAbnormalCarrier;

}
