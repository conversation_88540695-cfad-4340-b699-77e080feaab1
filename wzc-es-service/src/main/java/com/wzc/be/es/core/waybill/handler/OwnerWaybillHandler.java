package com.wzc.be.es.core.waybill.handler;

import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.core.waybill.mapper.SubWaybillListMapper;
import com.wzc.be.es.data.waybill.qo.OwnerWaybillQO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class OwnerWaybillHandler implements BaseSearchHandler<OwnerWaybillQO, Boolean> {


    @Value("${OwnerWaybill.search.type:ownerWaybillHandler}")
    private String searchType;

    @Value("${OwnerWaybill.search.index:sub_waybill_index01}")
    private String index;

    @Resource
    private SubWaybillListMapper subWaybillListMapper;


    @Override
    public RestResponse<Boolean> search(OwnerWaybillQO req) {
        LambdaEsQueryWrapper<SubWaybillIndex> lambdaQuery = new LambdaEsQueryWrapper<>();
        lambdaQuery.index(index);
        lambdaQuery.limit(1);
        lambdaQuery.eq(SubWaybillIndex::getShipperCompanyName, req.getShipperCompanyName());
        SubWaybillIndex subWaybillIndex = subWaybillListMapper.selectOne(lambdaQuery);
        if(subWaybillIndex != null){
            return RestResponse.success(true);
        }
        return RestResponse.success(false);
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.index;
    }
}
