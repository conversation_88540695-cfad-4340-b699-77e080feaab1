package com.wzc.be.es.core.auth.handler;

import cn.easyes.core.biz.EsPageInfo;
import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONObject;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.data.data.es.auth.model.AuthRecordESVO;
import com.wzc.be.data.data.es.auth.model.AuthRecordQueryPageDTO;
import com.wzc.be.es.adapter.data.AuthRecordSearchIndexFeignClient;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.constant.Constants;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.common.handler.SearchHandler;
import com.wzc.be.es.commons.PageVO;
import com.wzc.be.es.core.auth.converter.AuthConverter;
import com.wzc.be.es.core.auth.index.AuthRecordIndex;
import com.wzc.be.es.core.auth.mapper.AuthRecordEsMapper;
import com.wzc.be.es.data.auth.vo.AuthRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @ClassName AuthRecordSearchHandler
 * @Description
 * <AUTHOR>
 * @Date 2023/7/6 13:40
 * @Version 1.0
 */
@Slf4j
@Component
public class AuthRecordSearchHandler implements SearchHandler {

    @Value("${authRecord.search.type:authRecordEs}")
    private String searchType;

    @Resource
    private EsIndexProperties esIndexProperties;

    @Resource
    private AuthRecordEsMapper authRecordEsMapper;

    @Autowired
    private AuthRecordSearchIndexFeignClient authRecordSearchIndexFeignClient;

    @Resource
    private RestHighLevelClient restHighLevelClient;


    @Override
    public RestResponse<PageVO<AuthRecordVO>> search(JSONObject json) {
        if (ObjUtil.isNull(json)) {
            return RestResponse.error(ExcepitonEnum.PARAM_IS_INVALID);
        }
        String name = json.getStr("name");
        Integer auditType = json.getInt("auditType");
        Long managerId = json.getLong("managerId");
        Integer status = json.getInt("status");
        String operatorName = json.getStr("operatorName");
        String managerName = json.getStr("managerName");
        Date authTagDateStart= json.getDate("authTagDateStart");
        Date authTagDateEnd= json.getDate("authTagDateEnd");
        Date auditDateStart= json.getDate("auditDateStart");
        Date auditDateEnd= json.getDate("auditDateEnd");
        Long registerId = json.getLong("registerId");
        List<Integer>  statusList=json.getBeanList("statusList",Integer.class);
        LambdaEsQueryWrapper<AuthRecordIndex>  lambdaEsQueryWrapper=new LambdaEsQueryWrapper<>();
        lambdaEsQueryWrapper.index(esIndexProperties.getMdAuditIndex());
        if(null !=auditType){
            lambdaEsQueryWrapper.eq(AuthRecordIndex :: getAuditType,auditType);
        }
        if(null !=managerId){
            lambdaEsQueryWrapper.eq(AuthRecordIndex :: getManagerId,managerId);
        }
        if (null != registerId) {
            lambdaEsQueryWrapper.eq(AuthRecordIndex::getRegisterID, registerId);
        }

        if(null !=status){
            lambdaEsQueryWrapper.eq(AuthRecordIndex :: getStatus,status);
        }else{
            lambdaEsQueryWrapper.in(AuthRecordIndex :: getStatus,statusList);
        }
        if (authTagDateStart != null && authTagDateEnd != null){
            //增加日期范围请求参数
            lambdaEsQueryWrapper.ge(AuthRecordIndex::getAuthTagDate, DateUtil.formatDateTime(authTagDateStart))
                    .le(AuthRecordIndex::getAuthTagDate,DateUtil.formatDateTime(authTagDateEnd));
        }
        if (auditDateStart != null && auditDateEnd != null){
            //增加日期范围请求参数
            lambdaEsQueryWrapper.ge(AuthRecordIndex::getAuditDate, DateUtil.formatDateTime(auditDateStart))
                    .le(AuthRecordIndex::getAuditDate,DateUtil.formatDateTime(auditDateEnd));
        }
        if(StringUtils.isNotBlank(name)){
            lambdaEsQueryWrapper.like(AuthRecordIndex :: getName,name);
        }
        if(StringUtils.isNotBlank(operatorName)){
            lambdaEsQueryWrapper.like(AuthRecordIndex :: getOperatorName,operatorName);
        }

        if(StringUtils.isNotBlank(managerName)){
            lambdaEsQueryWrapper.like(AuthRecordIndex :: getManagerName,managerName);
        }
        lambdaEsQueryWrapper.notSelect(AuthRecordIndex :: getId);
        lambdaEsQueryWrapper.orderByDesc(AuthRecordIndex :: getAuditDate);
        PageVO<AuthRecordVO> page = new PageVO<>();
        Integer from = json.getInt("from",0);
        Integer size = json.getInt("size",10);
        page.setPage(from);
        page.setSize(size);
        try {
            EsPageInfo<AuthRecordIndex> authRecordEsEsPageInfo = authRecordEsMapper.pageQuery(lambdaEsQueryWrapper, from, size);
            lambdaEsQueryWrapper.orderByAsc(AuthRecordIndex::getId);
            List<AuthRecordIndex> list = authRecordEsEsPageInfo.getList();
            page.setTotal(authRecordEsEsPageInfo.getTotal());
            page.setContent(AuthConverter.INSTANCE.indexListToVoList(list));
        } catch (Exception e) {

            //如果不是 es查询异常 便往外抛
            if (!(e instanceof ElasticsearchStatusException)
                    || !((ElasticsearchStatusException) e).getDetailedMessage().contains(Constants.ES_SEARCH_PHASE_EXECUTION_EXCEPTION)) {
                throw e;
            }
            //查询es异常获取不到总数，重新查询
            Long count = authRecordEsMapper.selectCount(lambdaEsQueryWrapper);
            page.setTotal(count);
            AuthRecordQueryPageDTO queryPageQo = new AuthRecordQueryPageDTO();
            queryPageQo.setName(name);
            queryPageQo.setAuditType(auditType);
            queryPageQo.setManagerId(managerId);
            queryPageQo.setStatus(status);
            queryPageQo.setOperatorName(operatorName);
            queryPageQo.setManagerName(managerName);
            queryPageQo.setAuthTagDateStart(authTagDateStart);
            queryPageQo.setAuthTagDateEnd(authTagDateEnd);
            queryPageQo.setAuditDateStart(auditDateStart);
            queryPageQo.setAuditDateEnd(auditDateEnd);
            queryPageQo.setStatusList(statusList);
            queryPageQo.setPage(page.getPage());
            queryPageQo.setSize(page.getSize());
            List<AuthRecordESVO> authRecordESVOList = authRecordSearchIndexFeignClient.search(queryPageQo);
            List<AuthRecordIndex> list = AuthConverter.INSTANCE.esToDorisQueryPage(authRecordESVOList);
            page.setContent(AuthConverter.INSTANCE.indexListToVoList(list));
        }

        return RestResponse.success(page);
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.esIndexProperties.getMdAuditIndex();
    }
}
