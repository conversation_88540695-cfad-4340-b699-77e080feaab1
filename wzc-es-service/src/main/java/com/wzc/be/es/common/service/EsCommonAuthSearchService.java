/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.common.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.baidu.mapcloud.cloudnative.authentication.data.enums.AuthOptionsEnum;
import com.baidu.mapcloud.cloudnative.authentication.datapermission.core.entity.OtherQueryParam;
import com.baidu.mapcloud.cloudnative.authentication.datapermission.core.rule.UserPermissionRule;
import com.baidu.mapcloud.cloudnative.authentication.datapermission.enums.DataRuleValueEnum;
import com.wzc.be.es.common.enums.QueryTypeEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年10月13日 3:17 PM
 */
@Component
@Slf4j
public class EsCommonAuthSearchService {

    @Autowired
    private UserPermissionRule userPermissionRule;

    /**
     * 订单 运输计划 查询是否有数据权限校验
     *
     * @param requestJson    请求参数json
     * @param queryTypeEnums 查询类型
     */
    public JSONObject checkQueryAuth(JSONObject requestJson, QueryTypeEnums queryTypeEnums) {
        //是否有配置订单类型查询权限
        Boolean needOrderTypeQueryAuth = userPermissionRule.needAuth(DataRuleValueEnum.ORDER_TYPE, AuthOptionsEnum.SELECT);
        if (needOrderTypeQueryAuth) {
            //配置的查询订单类型集合
            List<String> orderTypeAuthList = userPermissionRule.getValueList(DataRuleValueEnum.ORDER_TYPE, AuthOptionsEnum.SELECT);
            //前端是否有传订单类型过滤
            Integer waybillType = requestJson.getInt("waybillType");
            if (ObjectUtil.isNotNull(waybillType)) {
                requestJson.set("waybillType", waybillType);
            } else {
                requestJson.set("orderTypeList", orderTypeAuthList);
            }
        }
        //是否有配置公司数据权限
        Boolean needCompanyQueryAuth = userPermissionRule.needAuth(DataRuleValueEnum.COMPANY_LIST, AuthOptionsEnum.SELECT);
        if (needCompanyQueryAuth) {
            List<String> companyList = userPermissionRule.getValueList(DataRuleValueEnum.COMPANY_LIST, AuthOptionsEnum.SELECT);
            requestJson.set("companyList", companyList);
        }
        //是否有配置创建用户数据权限
        Boolean needUserQueryAuth = userPermissionRule.needAuth(DataRuleValueEnum.USER_LIST, AuthOptionsEnum.SELECT);
        if (needUserQueryAuth) {
            Long creatorId = requestJson.getLong("creatorId");
            List<String> userIdList = userPermissionRule.getValueList(DataRuleValueEnum.USER_LIST, AuthOptionsEnum.SELECT);
            if (ObjectUtil.isNotNull(creatorId)) {
                requestJson.set("creatorId", creatorId);
            } else {
                requestJson.set("userIdList", userIdList);
            }
        }
        //是否有配置部门数据权限
        Boolean needDepIdQueryAuth = userPermissionRule.needAuth(DataRuleValueEnum.DEPARTMENT_LIST, AuthOptionsEnum.SELECT);
        if (needDepIdQueryAuth){
            List<String> depIdList = userPermissionRule.getValueList(DataRuleValueEnum.DEPARTMENT_LIST, AuthOptionsEnum.SELECT);
            if (CollectionUtil.isNotEmpty(depIdList)){
                requestJson.set("depIdList", depIdList);
            }
        }
        //是否有配置线路，物类权限
        Boolean otherAuth = userPermissionRule.needAddCompanyAndOther();
        if (otherAuth){
            List<OtherQueryParam> companyAndOtherList = userPermissionRule.getCompanyAndOther();
            requestJson.put("companyAndOtherList",companyAndOtherList);
        }
        //运输计划权限需要多判断来源
        if (queryTypeEnums == QueryTypeEnums.TRANSPORT) {
            Boolean needSourceAuth = userPermissionRule.needAuth(DataRuleValueEnum.SOURCE_TYPE, AuthOptionsEnum.SELECT);
            if (needSourceAuth) {
                List<String> sourceTypeList = userPermissionRule.getValueList(DataRuleValueEnum.SOURCE_TYPE, AuthOptionsEnum.SELECT);
                //前端或者app有带来源搜索则
                List<String> reqSourceTypeList = requestJson.getBeanList("sourceTypeList", String.class);
                if (CollectionUtil.isNotEmpty(reqSourceTypeList)) {
                    //判断来源权限是否有包含请求来源参数 如果有包含则以前端传的参数来过滤数据
                    if (CollectionUtil.isNotEmpty(sourceTypeList) && CollectionUtil.containsAll(sourceTypeList, reqSourceTypeList)) {
                        requestJson.set("sourceTypeList", reqSourceTypeList);
                    }
                } else {
                    requestJson.set("sourceTypeList", sourceTypeList);
                }
            }
        }
        log.info("数据权限参数：{}", JSON.toJSONString(requestJson));
        return requestJson;
    }
}
