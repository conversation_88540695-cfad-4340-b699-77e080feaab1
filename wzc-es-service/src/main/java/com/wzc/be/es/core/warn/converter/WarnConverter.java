package com.wzc.be.es.core.warn.converter;

import com.wzc.be.data.data.es.warn.dto.CscWarnExceptionDTO;
import com.wzc.be.data.data.es.warn.dto.CscWarnExceptionEsPageDTO;
import com.wzc.be.es.data.warn.qo.CscWarnExceptionEsPageQO;
import com.wzc.be.es.data.warn.vo.CscWarnExceptionVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年07月13日 15:05
 */
@Mapper(componentModel = "spring")
public interface WarnConverter {

    WarnConverter INSTANCE = Mappers.getMapper(WarnConverter.class);

    CscWarnExceptionEsPageDTO exceptionQOToDTO(CscWarnExceptionEsPageQO queryPageQo);

    List<CscWarnExceptionVO> exceptionDTOToVO(List<CscWarnExceptionDTO> cscWarnExceptionDTOList);

}
