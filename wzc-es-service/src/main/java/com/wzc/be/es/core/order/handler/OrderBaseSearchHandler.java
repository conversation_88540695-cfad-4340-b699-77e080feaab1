package com.wzc.be.es.core.order.handler;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baidu.mapcloud.cloudnative.common.model.UserContext;
import com.google.common.collect.Lists;
import com.wzc.be.es.common.enums.DeletedEnums;
import com.wzc.be.es.common.enums.IsFixedEnums;
import com.wzc.be.es.common.enums.OrderSourceTypeEnums;
import com.wzc.be.es.common.enums.OrderStatusEnums;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.common.properties.CommonProperties;
import com.wzc.be.es.core.common.CommonRedisResultVO;
import com.wzc.be.es.core.order.index.OrderIndex;
import com.wzc.be.es.data.order.qo.OrderPageQO;
import com.wzc.common.user.utils.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.Objects.nonNull;

/**
 * 订单 原生es api接口 基本实现类 条件封装
 * <AUTHOR>
 */
@Slf4j
public class OrderBaseSearchHandler<Q,V> implements BaseSearchHandler<Q, V> {

    @Resource
    private CommonProperties commonProperties;

    /**
     * 条件封装返回
     * @param orderPageQo
     * @return
     */
    public BoolQueryBuilder getMainBoolQueryBuilder(OrderPageQO orderPageQo,List<CommonRedisResultVO> commonRedisResultVOList){
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        //第三方订单号
        String thirdNo = orderPageQo.getThirdNo();
        if (StrUtil.isNotEmpty(thirdNo)) {
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,thirdNo,true,StrUtil.split(FieldUtils.val(OrderIndex::getThirdNo),","));
        }

        //货主公司名称
        String companyName = orderPageQo.getCompanyName();
        if (StrUtil.isNotBlank(companyName)){
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,companyName,true,StrUtil.split(FieldUtils.val(OrderIndex::getCompanyName),","));
        }
        //货主公司id
        List<Long> ownerIdSList = orderPageQo.getOwnerIdSList();
        if (CollectionUtil.isNotEmpty(ownerIdSList)) {
            mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(OrderIndex::getCompanyId), ownerIdSList));
        }
        //发货单位
        String deliveryCustomer = orderPageQo.getDeliveryCustomer();
        if (StrUtil.isNotEmpty(deliveryCustomer)) {
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,deliveryCustomer,true,StrUtil.split("sender.customerName",","));
        }
        //发货地址
        String deliveryAddress = orderPageQo.getDeliveryAddress();
        if (StrUtil.isNotEmpty(deliveryAddress)) {
            List<String> columns = Lists.newArrayList();
            columns.add("sender.address");
            columns.add("sender.addressName");
            columns.add("sender.fullAddress");
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,deliveryAddress,true,columns);
        }
        //收货单位
        String receiptCustomer = orderPageQo.getReceiptCustomer();
        if (StrUtil.isNotEmpty(receiptCustomer)) {
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,receiptCustomer,true,StrUtil.split("receiver.customerName",","));
        }
        //收货人地址
        String receiptAddress = orderPageQo.getReceiptAddress();
        if (StrUtil.isNotEmpty(receiptAddress)) {
            List<String> columns = Lists.newArrayList();
            columns.add("receiver.address");
            columns.add("receiver.addressName");
            columns.add("receiver.fullAddress");
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,receiptAddress,true,columns);
        }
        //货物名称
        String itemNames = orderPageQo.getItemNames();
        if (StrUtil.isNotEmpty(itemNames)) {
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,itemNames,true,StrUtil.split("items.itemName",","));
        }
        //客户名称
        String customerName = orderPageQo.getCustomerName();
        if (StrUtil.isNotEmpty(customerName)) {
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,customerName,true,StrUtil.split(FieldUtils.val(OrderIndex::getCustomerName),","));
        }
        Integer businessType = orderPageQo.getBusinessType();
        if (nonNull(businessType)) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getBusinessType),businessType));
        }

        //订单类型集合
        List<String> businessTypeList = orderPageQo.getBusinessTypeList();
        if (CollectionUtil.isNotEmpty(businessTypeList)){
            mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(OrderIndex::getBusinessType),businessTypeList));
        }

        //订单状态：1 待审核,2 待确认,3 执行中,4 已完成,5 已暂停 6 已关闭, 7 已拒绝
        Integer queryStatus = orderPageQo.getStatus();
        if (nonNull(queryStatus)) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getStatus),queryStatus));
        }
        //运输模式
        Integer transportMode = orderPageQo.getTransportMode();
        if (nonNull(transportMode)) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getTransportType),transportMode));
        }

        //制单人id过滤
        Long creatorId = orderPageQo.getCreatorId();
        if (ObjectUtil.isNotNull(creatorId)){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getOwnerId),creatorId));
        }
        //制单人名称过滤
        String makerName = orderPageQo.getMakerName();
        if (StrUtil.isNotEmpty(makerName)){
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,makerName,true,StrUtil.split(FieldUtils.val(OrderIndex::getMakerName),","));
        }
        //托运公司名称过滤
        String shipperCompanyName = orderPageQo.getShipperCompanyName();
        if (StrUtil.isNotEmpty(shipperCompanyName)){
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,shipperCompanyName,true,StrUtil.split(FieldUtils.val(OrderIndex::getShipperCompanyName),","));
        }
        //订单编号
        String orderNo = orderPageQo.getOrderNo();
        if (StrUtil.isNotEmpty(orderNo)){
//            matchPhraseOrWildcardMustQuery(mainQueryBuilder,orderNo,true,StrUtil.split(FieldUtils.val(OrderIndex::getOrderNo)+".text",","));
            matchPhraseOrWildcardMustQueryByOrderList(mainQueryBuilder,orderNo,StrUtil.split(FieldUtils.val(OrderIndex::getOrderNo)+".text",","),orderPageQo.getOrderNoList());
        }
        //是否有删除的订单号
        if (CollectionUtil.isNotEmpty(commonRedisResultVOList)){
            List<String> deleteOrderNoList = commonRedisResultVOList.stream().filter(c -> ObjectUtil.equals(c.getDelFlag(), DeletedEnums.NO.getCode())).map(CommonRedisResultVO::getUnionNo).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(deleteOrderNoList)) {
                mainQueryBuilder.mustNot(QueryBuilders.termsQuery(FieldUtils.val(OrderIndex::getOrderNo), deleteOrderNoList));
            }
        }
        //是否有需要过滤的单号
        if (CollectionUtil.isNotEmpty(orderPageQo.getNoInOrderNoList())){
            mainQueryBuilder.mustNot(QueryBuilders.termsQuery(FieldUtils.val(OrderIndex::getOrderNo), orderPageQo.getNoInOrderNoList()));
        }

        //转网货过滤
        if (ObjectUtil.isNotNull(orderPageQo.getNetCargo())){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getNetCargo), orderPageQo.getNetCargo()));
        }
        //创建时间比较
        String startTime = orderPageQo.getStartTime();
        String endTime = orderPageQo.getEndTime();
        if (StrUtil.isNotEmpty(startTime)){
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(OrderIndex::getCreateTime))
                    .gte(startTime));
        }
        if (StrUtil.isNotEmpty(endTime)){
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(OrderIndex::getCreateTime))
                    .lte(endTime));
        }
        //二期订单号查询
        String waybillNoSecond = orderPageQo.getWaybillNoSecond();
        if (StrUtil.isNotBlank(waybillNoSecond)){
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,waybillNoSecond,true,StrUtil.split(FieldUtils.val(OrderIndex::getWaybillNoSecond),","));
        }
        //是否查询转包待确认订单
        Boolean querySubtractConfirmOrders = orderPageQo.getQuerySubtractConfirmOrders();
        if (querySubtractConfirmOrders){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getSourceType),OrderSourceTypeEnums.SUBCONTRACT.getCode()));
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getStatus), OrderStatusEnums.WAITING_CONFIRM.getCode()));
        }
        //是否查询二期同步数据 1.是 2.否
        Integer isSync = orderPageQo.getIsSync();
        if (ObjectUtil.isNotNull(isSync)){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getIsSync), isSync));
        }
        //是否竞价立项使用  可指派量不为0  收货地址不为空
        Integer isBidding = orderPageQo.getIsBidding();
        if (ObjectUtil.isNotNull(isBidding) && "1".equals(isBidding+"")){
            log.info("抢单竞价订单查询");
            //过滤线路拆分 共享订单来源
            mainQueryBuilder.mustNot(QueryBuilders.termsQuery(FieldUtils.val(OrderIndex::getSourceType), Stream.of(OrderSourceTypeEnums.SHARED.getCode()).collect(Collectors.toList())));
            //过滤非固定流向数据
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getIsFixed), isBidding));
            mainQueryBuilder.must(QueryBuilders.existsQuery(FieldUtils.val(OrderIndex::getReceiver)));
            mainQueryBuilder.must(QueryBuilders.existsQuery("items.itemWeight"));
            mainQueryBuilder.must(QueryBuilders.existsQuery("items.assignedWeight"));
            Script script = new Script(ScriptType.INLINE, "painless", "doc['items.itemWeight'].value - doc['items.assignedWeight'].value > 0", Collections.emptyMap());
            mainQueryBuilder.must(QueryBuilders.scriptQuery(script));
        }else{
            //过滤线路拆分 共享订单来源
            mainQueryBuilder.mustNot(QueryBuilders.termsQuery(FieldUtils.val(OrderIndex::getSourceType), Stream.of(OrderSourceTypeEnums.LINE_SPLIT.getCode(),OrderSourceTypeEnums.SHARED.getCode()).collect(Collectors.toList())));
        }

        //隐藏状态 0-不隐藏 1-隐藏
        Integer hideStatus = orderPageQo.getHideStatus();
        if(nonNull(hideStatus) && hideStatus == 0){
            mainQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getHideStatus), 1));
        }

        //需要过滤的订单来源类型
        Integer sourceType = orderPageQo.getSourceType();
        if(ObjectUtil.isNotNull(sourceType)){
            mainQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getSourceType),sourceType));
        }
        //是否开启校验载重设备功能 1是，2否，默认否
        Integer loadDeviceCheck = orderPageQo.getLoadDeviceCheck();
        if(ObjectUtil.isNotNull(loadDeviceCheck)){
            if(2==loadDeviceCheck){
                //兼容旧数据
                BoolQueryBuilder loadDeviceCheckBoolQuery = QueryBuilders.boolQuery();
                loadDeviceCheckBoolQuery.should(QueryBuilders.boolQuery()
                        .mustNot(QueryBuilders.existsQuery(FieldUtils.val(OrderIndex::getLoadDeviceCheck))));
                loadDeviceCheckBoolQuery.should(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getLoadDeviceCheck), loadDeviceCheck));
                mainQueryBuilder.must(loadDeviceCheckBoolQuery);
            }else{
                mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getLoadDeviceCheck), loadDeviceCheck));
            }
        }

        // 合同签署要求
        Integer contractRequire = orderPageQo.getContractRequire();
        if (ObjectUtil.isNotNull(contractRequire)) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getContractRequire), contractRequire));
        }
        // 合同状态
        Integer contractStatus = orderPageQo.getContractStatus();
        if (ObjectUtil.isNotNull(contractStatus)) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getContractStatus), contractStatus));
        }
        // 合同签约方式
        Integer contractSignMethod = orderPageQo.getContractSignMethod();
        if (ObjectUtil.isNotNull(contractSignMethod)) {
            mainQueryBuilder.must(QueryBuilders.termQuery("contractRelation.contractSignMethod", contractSignMethod));
        }
        // 合同编号
        String contractNo = orderPageQo.getContractNo();
        if (StrUtil.isNotEmpty(contractNo)) {
            matchPhraseOrWildcardMustQuery(mainQueryBuilder, contractNo, true, StrUtil.split("contractRelation.contractNo", ","));
        }
        // 是否存在合同
        Boolean hasContract = orderPageQo.getHasContract();
        if (BooleanUtil.isTrue(hasContract)) {
            mainQueryBuilder.must(QueryBuilders.existsQuery("contractRelation.contractNo"));
        }

        //数据权限判断
        authCheck(mainQueryBuilder,orderPageQo);
        return mainQueryBuilder;
    }

    private void authCheck(BoolQueryBuilder mainQueryBuilder,OrderPageQO orderPageQo){
        String userCompanyId = String.valueOf(UserContext.companyId().get());

        //过滤运营端审批决绝的数据
        boolean operation = UserContext.isOperation();
        log.info("是否是运营端访问：{}",operation);
        if(operation && !String.valueOf(commonProperties.getSuperAdminCompanyId()).equals(userCompanyId)){
            //查找isShowAdmin字段为空或者为1的数据
            BoolQueryBuilder orQueryBuilder = QueryBuilders.boolQuery();
            orQueryBuilder.should(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getIsShowAdmin), IsFixedEnums.YES.getCode()));
            orQueryBuilder.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("isShowAdmin")));
            mainQueryBuilder.must(orQueryBuilder);
        }

        List<String> companyList = orderPageQo.getCompanyList();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        // 平台 固定公司 超级管理员
        if(operation && String.valueOf(commonProperties.getSuperAdminCompanyId()).equals(userCompanyId)){
            if(UserContext.isPlatformAdmin()){
                //平台超管，无需鉴权
                log.info("订单列表查询-当前登陆角色为平台超级管理员无需数据权限");
            }else{
                log.info("订单列表查询-当前登陆角色为平台员工");
                //平台员工登录，需要校验平台员工公司数据权限
                authQueryCondition(mainQueryBuilder,orderPageQo,userCompanyId);
            }
            return;
        }else if(operation){
            // 托运公司 正常登陆公司
            log.info("订单列表查询-当前登陆角色为托运公司");
            boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getCompanyId), userCompanyId));
            boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getShipperCompanyId), userCompanyId));
            mainQueryBuilder.must(boolQuery);
        }else if(UserUtils.isIdentityShipper()){
            // 货主登录
            log.info("订单列表查询-当前登陆角色为货主公司");
            boolQuery.should(QueryBuilders.termsQuery(FieldUtils.val(OrderIndex::getCompanyId), companyList));
            mainQueryBuilder.must(boolQuery);
        }
        authQueryCondition(mainQueryBuilder,orderPageQo,userCompanyId);
    }

    private void authQueryCondition(BoolQueryBuilder mainQueryBuilder,OrderPageQO orderPageQo,String userCompanyId){
        List<String> companyList = orderPageQo.getCompanyList();
        //数据权限 订单类型集合
        List<String> orderTypeList = orderPageQo.getOrderTypeList();
        if (CollectionUtil.isNotEmpty(orderTypeList) && !orderTypeList.contains(BigDecimal.ZERO)){
            BoolQueryBuilder orderTypeBoolQuery = QueryBuilders.boolQuery();
            orderTypeBoolQuery.should(QueryBuilders.boolQuery()
                    .mustNot(QueryBuilders.existsQuery(FieldUtils.val(OrderIndex::getWaybillType))));
            orderTypeBoolQuery.should(QueryBuilders.termsQuery(FieldUtils.val(OrderIndex::getWaybillType), orderTypeList));
            mainQueryBuilder.must(orderTypeBoolQuery);
        }else {
            //运单类型,其他-全部,1-销售运单,2-采购运单,3-调拨运单
            Integer waybillType = orderPageQo.getWaybillType();
            if (nonNull(waybillType)) {
                mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getWaybillType),waybillType));
            }
        }

        // 数据权限配置的公司id集合 （配置当前公司限制）
        BoolQueryBuilder authBoolQuery = QueryBuilders.boolQuery();
        if (CollectionUtil.isNotEmpty(companyList) && UserContext.companyId().isPresent() &&
                companyList.contains(userCompanyId) && !String.valueOf(commonProperties.getSuperAdminCompanyId()).equals(userCompanyId) && CollectionUtil.isNotEmpty(companyList) && !companyList.contains("-1")) {
            BoolQueryBuilder innerQuery = QueryBuilders.boolQuery();
            BoolQueryBuilder companyIdShouldQuery = QueryBuilders.boolQuery();
            companyIdShouldQuery.should(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getCompanyId), userCompanyId));

            innerQuery.must(companyIdShouldQuery);
            // 是否需要关联用户数据过滤
            List<String> userIdList = orderPageQo.getUserIdList();
            if (CollectionUtil.isNotEmpty(userIdList)){
                innerQuery.must(QueryBuilders.termsQuery(FieldUtils.val(OrderIndex::getOwnerId), userIdList));
            }
            // 数据权限部门id过滤
            List<Long> depIdList = orderPageQo.getDepIdList();
            if (CollectionUtil.isNotEmpty(depIdList)){
                innerQuery.must(QueryBuilders.termsQuery(FieldUtils.val(OrderIndex::getOwnerDepartment), depIdList));
            }
            // 过滤当前公司的预料信息
            List<OrderPageQO.EsOtherQueryParam> companyAndOtherList = orderPageQo.getCompanyAndOtherList();
            if (CollectionUtil.isNotEmpty(companyAndOtherList)) {
                companyAndOtherList.stream().filter(
                        param -> Objects.equals(userCompanyId, param.getCompanyId())
                ).forEach(a -> {
                    if (CollectionUtil.isNotEmpty(a.getMaterialSpecies())){
                        innerQuery.must(QueryBuilders.termsQuery("items.speciesId", a.getMaterialSpecies()));
                    }
                });
            }
            authBoolQuery.should(innerQuery);
        }
        // 公司物料线路权限配置 （配置其他公司限制）
        List<OrderPageQO.EsOtherQueryParam> companyAndOtherList = orderPageQo.getCompanyAndOtherList();
        if (CollectionUtil.isNotEmpty(companyAndOtherList) && CollectionUtil.isNotEmpty(companyList) && !companyList.contains("-1")) {
            companyAndOtherList.stream().filter(
                    param -> !Objects.equals(userCompanyId, param.getCompanyId())
            ).forEach(a -> {
                BoolQueryBuilder innerQuery = QueryBuilders.boolQuery()
                        .must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getCompanyId), a.getCompanyId()));
                if (CollectionUtil.isNotEmpty(a.getMaterialSpecies())){
                    innerQuery.must(QueryBuilders.termsQuery("items.speciesId", a.getMaterialSpecies()));
                }
                authBoolQuery.should(innerQuery);
            });
        }
        mainQueryBuilder.must(authBoolQuery);
    }

    private void matchPhraseOrWildcardMustQueryByOrderList(BoolQueryBuilder mainBoolQueryBuilder, String keyword, List<String> columns, List<String> orderNoList) {
        //添加查询根订单号集合逻辑
        //单字段匹配
        if (columns.size() == 1){
            String column = columns.get(0);
            if (keyword.length() > 1) {
                if(CollectionUtil.isNotEmpty(orderNoList)){
                    BoolQueryBuilder orQueryBuilder = new BoolQueryBuilder();
                    orQueryBuilder.should(QueryBuilders.matchPhraseQuery(column, keyword));
                    orderNoList.forEach(x -> orQueryBuilder.should(QueryBuilders.matchPhraseQuery(column, x)));
                    mainBoolQueryBuilder.must(orQueryBuilder);
                }else{
                    mainBoolQueryBuilder.must(QueryBuilders.matchPhraseQuery(column, keyword));
                }

            } else {
                if(CollectionUtil.isNotEmpty(orderNoList)){
                    BoolQueryBuilder orQueryBuilder = new BoolQueryBuilder();
                    orQueryBuilder.should(QueryBuilders.wildcardQuery(column, wildcardsExp(keyword)));
                    orderNoList.forEach(x -> orQueryBuilder.should(QueryBuilders.wildcardQuery(column, wildcardsExp(x))));
                }else{
                    mainBoolQueryBuilder.must(QueryBuilders.wildcardQuery(column, wildcardsExp(keyword)));
                }

            }
        }
        //多字段走or匹配
        else {
            BoolQueryBuilder innerBoolQueryBuilder = QueryBuilders.boolQuery();
            if (keyword.length() > 1) {
                for (String column:columns){
                    innerBoolQueryBuilder.should(getMatchPhraseQuery(column, keyword));
                    if(CollectionUtil.isNotEmpty(orderNoList)){
                        orderNoList.forEach(x -> innerBoolQueryBuilder.should(getMatchPhraseQuery(column, x)));
                    }
                }
            } else {
                for (String column:columns){
                    innerBoolQueryBuilder.should(getWildcardQuery(column, keyword));
                    if(CollectionUtil.isNotEmpty(orderNoList)){
                        orderNoList.forEach(x -> innerBoolQueryBuilder.should(getWildcardQuery(column, x)));
                    }
                }
            }
            mainBoolQueryBuilder.must(innerBoolQueryBuilder);
        }
    }

    /**
     * 条件封装返回
     * @param orderPageQo
     * @return
     */
    public BoolQueryBuilder getMainBoolQueryBuilderSzy(OrderPageQO orderPageQo,List<CommonRedisResultVO> commonRedisResultVOList){
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        //第三方订单号
        String thirdNo = orderPageQo.getThirdNo();
        if (StrUtil.isNotEmpty(thirdNo)) {
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,thirdNo,true,StrUtil.split(FieldUtils.val(OrderIndex::getThirdNo),","));
        }
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        //公司id 托运公司id判断  companyId = or shipperCompanyId =
        Long companyId = orderPageQo.getCompanyId();
        //数据权限配置的公司id集合
        List<String> companyList = orderPageQo.getCompanyList();
        if (CollectionUtil.isNotEmpty(companyList)){
            boolQuery.should(QueryBuilders.termsQuery(FieldUtils.val(OrderIndex::getCompanyId), companyList));
            boolQuery.should(QueryBuilders.termsQuery(FieldUtils.val(OrderIndex::getShipperCompanyId), companyList));
            mainQueryBuilder.must(boolQuery);
        }else {
            if (nonNull(companyId)) {
                boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getCompanyId), companyId));
                boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getShipperCompanyId), companyId));
                mainQueryBuilder.must(boolQuery);
            }
        }
        //发货单位
        String deliveryCustomer = orderPageQo.getDeliveryCustomer();
        if (StrUtil.isNotEmpty(deliveryCustomer)) {
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,deliveryCustomer,true,StrUtil.split("sender.customerName",","));
        }
        //发货地址
        String deliveryAddress = orderPageQo.getDeliveryAddress();
        if (StrUtil.isNotEmpty(deliveryAddress)) {
            List<String> columns = Lists.newArrayList();
            columns.add("sender.address");
            columns.add("sender.addressName");
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,deliveryAddress,true,columns);
        }
        //收货单位
        String receiptCustomer = orderPageQo.getReceiptCustomer();
        if (StrUtil.isNotEmpty(receiptCustomer)) {
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,receiptCustomer,true,StrUtil.split("receiver.customerName",","));
        }
        //收货人地址
        String receiptAddress = orderPageQo.getReceiptAddress();
        if (StrUtil.isNotEmpty(receiptAddress)) {
            List<String> columns = Lists.newArrayList();
            columns.add("receiver.address");
            columns.add("receiver.addressName");
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,receiptAddress,true,columns);
        }
        //货物名称
        String itemNames = orderPageQo.getItemNames();
        if (StrUtil.isNotEmpty(itemNames)) {
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,itemNames,true,StrUtil.split("items.itemName",","));
        }
        //客户名称
        String customerName = orderPageQo.getCustomerName();
        if (StrUtil.isNotEmpty(customerName)) {
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,customerName,true,StrUtil.split(FieldUtils.val(OrderIndex::getCustomerName),","));
        }
        Integer businessType = orderPageQo.getBusinessType();
        if (nonNull(businessType)) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getBusinessType),businessType));
        }

        //数据权限 订单类型集合
        List<String> businessTypeList = orderPageQo.getBusinessTypeList();
        if (CollectionUtil.isNotEmpty(businessTypeList)){
            mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(OrderIndex::getBusinessType),businessTypeList));
        }

        //订单状态：1 待审核,2 待确认,3 执行中,4 已完成,5 已暂停 6 已关闭, 7 已拒绝
        Integer queryStatus = orderPageQo.getStatus();
        if (nonNull(queryStatus)) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getStatus),queryStatus));
        }
        //订单来源
        Integer sourceType = orderPageQo.getSourceType();
        if (nonNull(sourceType)) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getSourceType),sourceType));
        }
        //运输模式
        Integer transportMode = orderPageQo.getTransportMode();
        if (nonNull(transportMode)) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getTransportType),transportMode));
        }
        //数据权限 订单类型集合
        List<String> orderTypeList = orderPageQo.getOrderTypeList();
        if (CollectionUtil.isNotEmpty(orderTypeList)){
            BoolQueryBuilder orderTypeBoolQuery = QueryBuilders.boolQuery();
            orderTypeBoolQuery.should(QueryBuilders.boolQuery()
                    .mustNot(QueryBuilders.existsQuery(FieldUtils.val(OrderIndex::getWaybillType))));
            orderTypeBoolQuery.should(QueryBuilders.termsQuery(FieldUtils.val(OrderIndex::getWaybillType), orderTypeList));
            mainQueryBuilder.must(orderTypeBoolQuery);
        }else {
            //运单类型,其他-全部,1-销售运单,2-采购运单,3-调拨运单
            Integer waybillType = orderPageQo.getWaybillType();
            if (nonNull(waybillType)) {
                mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getWaybillType),waybillType));
            }
        }
        //制单人id过滤
        Long creatorId = orderPageQo.getCreatorId();
        if (ObjectUtil.isNotNull(creatorId)){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getOwnerId),creatorId));
        }
        //制单人名称过滤
        String makerName = orderPageQo.getMakerName();
        if (StrUtil.isNotEmpty(makerName)){
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,makerName,true,StrUtil.split(FieldUtils.val(OrderIndex::getMakerName),","));
        }
        //订单编号
        String orderNo = orderPageQo.getOrderNo();
        if (StrUtil.isNotEmpty(orderNo)){
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,orderNo,true,StrUtil.split(FieldUtils.val(OrderIndex::getOrderNo)+".text",","));
        }
        //是否有删除的订单号
        if (CollectionUtil.isNotEmpty(commonRedisResultVOList)){
            List<String> deleteOrderNoList = commonRedisResultVOList.stream().filter(c -> ObjectUtil.equals(c.getDelFlag(), DeletedEnums.NO.getCode())).map(CommonRedisResultVO::getUnionNo).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(deleteOrderNoList)) {
                mainQueryBuilder.mustNot(QueryBuilders.termsQuery(FieldUtils.val(OrderIndex::getOrderNo), deleteOrderNoList));
            }
        }
        //是否有需要过滤的单号
        if (CollectionUtil.isNotEmpty(orderPageQo.getNoInOrderNoList())){
            mainQueryBuilder.mustNot(QueryBuilders.termsQuery(FieldUtils.val(OrderIndex::getOrderNo), orderPageQo.getNoInOrderNoList()));
        }
        //转网货过滤
        if (ObjectUtil.isNotNull(orderPageQo.getNetCargo())){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getNetCargo), orderPageQo.getNetCargo()));
        }
        //创建时间比较
        String startTime = orderPageQo.getStartTime();
        String endTime = orderPageQo.getEndTime();
        if (StrUtil.isNotEmpty(startTime)){
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(OrderIndex::getCreateTime))
                    .gte(startTime));
        }
        if (StrUtil.isNotEmpty(endTime)){
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(OrderIndex::getCreateTime))
                    .lte(endTime));
        }
        //二期订单号查询
        String waybillNoSecond = orderPageQo.getWaybillNoSecond();
        if (StrUtil.isNotBlank(waybillNoSecond)){
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,waybillNoSecond,true,StrUtil.split(FieldUtils.val(OrderIndex::getWaybillNoSecond),","));
        }
        //是否查询转包待确认订单
        Boolean querySubtractConfirmOrders = orderPageQo.getQuerySubtractConfirmOrders();
        if (querySubtractConfirmOrders){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getSourceType),OrderSourceTypeEnums.SUBCONTRACT.getCode()));
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getStatus), OrderStatusEnums.WAITING_CONFIRM.getCode()));
        }
        //是否查询二期同步数据 1.是 2.否
        Integer isSync = orderPageQo.getIsSync();
        if (ObjectUtil.isNotNull(isSync)){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getIsSync), isSync));
        }
        //是否竞价立项使用  可指派量不为0  收货地址不为空
        Integer isBidding = orderPageQo.getIsBidding();
        if (ObjectUtil.isNotNull(isBidding) && "1".equals(isBidding+"")){
            log.info("抢单竞价订单查询");
            //过滤非固定流向数据
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getIsFixed), isBidding));
            mainQueryBuilder.must(QueryBuilders.existsQuery(FieldUtils.val(OrderIndex::getReceiver)));
            mainQueryBuilder.must(QueryBuilders.existsQuery("items.itemWeight"));
            mainQueryBuilder.must(QueryBuilders.existsQuery("items.assignedWeight"));
            Script script = new Script(ScriptType.INLINE, "painless", "doc['items.itemWeight'].value - doc['items.assignedWeight'].value > 0", Collections.emptyMap());
            mainQueryBuilder.must(QueryBuilders.scriptQuery(script) );
            //根据OrderIndex的isAutoStop判断，当isAutoStop为2时，过滤掉当前时间不在订单开始时间到订单结束时间之间的数据
            mainQueryBuilder.must(QueryBuilders.scriptQuery(new Script(ScriptType.INLINE, "painless",
                    "doc['isAutoStop'].value == 2 ? doc['createTime'].value.getTime() < doc['endTime'].value.getTime() : true", Collections.emptyMap())));

        }

        //隐藏状态 0-不隐藏 1-隐藏
        Integer hideStatus = orderPageQo.getHideStatus();
        if(nonNull(hideStatus) && hideStatus == 0){
            mainQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getHideStatus), 1));
        }

        //是否开启校验载重设备功能 1是，2否，默认否
        Integer loadDeviceCheck = orderPageQo.getLoadDeviceCheck();
        if(ObjectUtil.isNotNull(loadDeviceCheck)){
            if(2==loadDeviceCheck){
                //兼容旧数据
                BoolQueryBuilder loadDeviceCheckBoolQuery = QueryBuilders.boolQuery();
                loadDeviceCheckBoolQuery.should(QueryBuilders.boolQuery()
                        .mustNot(QueryBuilders.existsQuery(FieldUtils.val(OrderIndex::getLoadDeviceCheck))));
                loadDeviceCheckBoolQuery.should(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getLoadDeviceCheck), loadDeviceCheck));
                mainQueryBuilder.must(loadDeviceCheckBoolQuery);
            }else{
                mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(OrderIndex::getLoadDeviceCheck), loadDeviceCheck));
            }
        }
        return mainQueryBuilder;
    }
}
