package com.wzc.be.es.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SettlementStatusEnums {

    /**
     * 结算状态（0未结算 1已结算）
     */
    UNSETTLED(0, "未结算", ""),
    HAVE_ALREADY_SETTLED(1, "已结算", "alSettleCount"),
    RETURNED(2, "已退回", ""),
    ABROGATED(3, "已作废", ""),
    ;

    private final Integer code;
    private final String name;
    private final String fieldName;
}
