package com.wzc.be.es.core.business.handler;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.UserContext;
import com.wzc.be.es.common.enums.*;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.common.properties.CommonProperties;
import com.wzc.be.es.common.service.EsCommonAuthSearchService;
import com.wzc.be.es.core.business.index.BusinessIndex;
import com.wzc.be.es.data.business.qo.BmsEsBusinessQueryPageQO;
import com.wzc.be.es.data.order.qo.OrderPageQO;
import com.wzc.common.user.utils.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class BmsBusinessBaseSearchHandler<Q, V> implements BaseSearchHandler<Q, V> {

    @Resource
    private CommonProperties commonProperties;

    @Resource
    private EsCommonAuthSearchService esCommonAuthSearchService;

    /**
     * 条件封装返回
     *
     * @param qo
     * @return
     */
    public BoolQueryBuilder getMainBoolQueryBuilder(BmsEsBusinessQueryPageQO qo) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 运单号或子运单号
        String subWaybillNo = qo.getSubWaybillNo();
        if (StrUtil.isNotEmpty(subWaybillNo)) {
            BoolQueryBuilder subWaybillNoQuery = QueryBuilders.boolQuery();
            subWaybillNoQuery.should(QueryBuilders.matchPhraseQuery(FieldUtils.val(BusinessIndex::getDispatchNo), subWaybillNo));
            subWaybillNoQuery.should(QueryBuilders.matchPhraseQuery(FieldUtils.val(BusinessIndex::getSubWaybillNo), subWaybillNo));
            boolQueryBuilder.must(subWaybillNoQuery);
        }
        // 业务类型
        Integer businessType = qo.getBusinessType();
        if (ObjectUtil.isNotNull(businessType)) {
            boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getBusinessType), businessType));
        }
        // 订单类型
        Integer orderType = qo.getQueryOrderType();
        if (ObjectUtil.isNotNull(orderType)) {
            boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getOrderType), orderType));
        }
        // 对账状态
        Integer rcStatus = qo.getRcStatus();
        if (ObjectUtil.isNotNull(rcStatus)) {
            // 货主登录查询货主对账状态
            if (UserUtils.isShipperPc()) {
                boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getRcStatus), rcStatus));
            }
            // 平台登录
            if (UserUtils.isOperatePc()) {
                // 平台自营平台对上或平台总包查询货主对账状态
                if ((ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isTrue(qo.getIsShipperUp())) || ObjectUtil.equals(BusinessTypeEnums.PLATFORM_PACKAGE.getCode(), businessType)) {
                    boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getRcStatus), rcStatus));
                }
                // 平台自营平台对下查询平台对账状态
                if (ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType)) {
                    boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperRcStatus), rcStatus));
                }
            }
        }
        // 对账时间
        String rcStartTime = qo.getRcStartTime();
        String rcEndTime = qo.getRcEndTime();
        if (StrUtil.isNotEmpty(rcStartTime)) {
            rcStartTime = LocalDateTimeUtil.format(LocalDateTimeUtil.parse(rcStartTime, DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATE_PATTERN);
            // 货主登录
            if (UserUtils.isShipperPc()) {
                // 货主自营和平台总包查询承运商发起对账日期
                BoolQueryBuilder HZZYOrPTZBRcTimeBoolQueryBuilder = QueryBuilders.boolQuery();
                HZZYOrPTZBRcTimeBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getReconciliationDate)).gte(rcStartTime));
                HZZYOrPTZBRcTimeBoolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getBusinessType), CollectionUtil.newArrayList(BusinessTypeEnums.OWNER_OPERATED.getCode(), BusinessTypeEnums.PLATFORM_PACKAGE.getCode())));
                // 平台自营查询平台发起对账日期
                BoolQueryBuilder PTZYRcTimeBoolQueryBuilder = QueryBuilders.boolQuery();
                PTZYRcTimeBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getShipperReconciliationDate)).gte(rcStartTime));
                PTZYRcTimeBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getBusinessType), BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode()));
                // 拼接查询条件
                BoolQueryBuilder shipperRcTimeBoolQueryBuilder = QueryBuilders.boolQuery();
                shipperRcTimeBoolQueryBuilder.should(HZZYOrPTZBRcTimeBoolQueryBuilder);
                shipperRcTimeBoolQueryBuilder.should(PTZYRcTimeBoolQueryBuilder);
                boolQueryBuilder.must(shipperRcTimeBoolQueryBuilder);
            }
            // 平台登录
            if (UserUtils.isOperatePc()) {
                // 平台自营平台对上查询平台发起对账日期
                if (ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isTrue(qo.getIsShipperUp())) {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getShipperReconciliationDate)).gte(rcStartTime));
                }
                // 平台自营平台对下查询承运商发起对账日期
                if (ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isFalse(qo.getIsShipperUp())) {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getReconciliationDate)).gte(rcStartTime));
                }
                // 平台总包查询承运商发起对账日期
                if (ObjectUtil.equals(BusinessTypeEnums.PLATFORM_PACKAGE.getCode(), businessType)) {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getReconciliationDate)).gte(rcStartTime));
                }
            }
        }
        if (StrUtil.isNotEmpty(rcEndTime)) {
            rcEndTime = LocalDateTimeUtil.format(LocalDateTimeUtil.parse(rcEndTime, DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATE_PATTERN);
            // 货主登录
            if (UserUtils.isShipperPc()) {
                // 货主自营和平台总包查询承运商发起对账日期
                BoolQueryBuilder HZZYOrPTZBRcTimeBoolQueryBuilder = QueryBuilders.boolQuery();
                HZZYOrPTZBRcTimeBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getReconciliationDate)).lte(rcEndTime));
                HZZYOrPTZBRcTimeBoolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getBusinessType), CollectionUtil.newArrayList(BusinessTypeEnums.OWNER_OPERATED.getCode(), BusinessTypeEnums.PLATFORM_PACKAGE.getCode())));
                // 平台自营查询平台发起对账日期
                BoolQueryBuilder PTZYRcTimeBoolQueryBuilder = QueryBuilders.boolQuery();
                PTZYRcTimeBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getShipperReconciliationDate)).lte(rcEndTime));
                PTZYRcTimeBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getBusinessType), BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode()));
                // 拼接查询条件
                BoolQueryBuilder shipperRcTimeBoolQueryBuilder = QueryBuilders.boolQuery();
                shipperRcTimeBoolQueryBuilder.should(HZZYOrPTZBRcTimeBoolQueryBuilder);
                shipperRcTimeBoolQueryBuilder.should(PTZYRcTimeBoolQueryBuilder);
                boolQueryBuilder.must(shipperRcTimeBoolQueryBuilder);
            }
            // 平台登录
            if (UserUtils.isOperatePc()) {
                // 平台自营平台对上查询平台发起对账日期
                if (ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isTrue(qo.getIsShipperUp())) {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getShipperReconciliationDate)).lte(rcEndTime));
                }
                // 平台自营平台对下查询承运商发起对账日期
                if (ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isFalse(qo.getIsShipperUp())) {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getReconciliationDate)).lte(rcEndTime));
                }
                // 平台总包查询承运商发起对账日期
                if (ObjectUtil.equals(BusinessTypeEnums.PLATFORM_PACKAGE.getCode(), businessType)) {
                    boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getReconciliationDate)).lte(rcEndTime));
                }
            }
        }
        // 审核状态
        Integer auditStatus = qo.getAuditStatus();
        if (ObjectUtil.isNotNull(auditStatus)) {
            if (UserUtils.isShipperPc()) {
                BoolQueryBuilder auditStatusBoolQueryBuilder = QueryBuilders.boolQuery();
                // 不存在转包关系查询货主审核状态
                BoolQueryBuilder auditStatusNormalBoolQueryBuilder = QueryBuilders.boolQuery();
                auditStatusNormalBoolQueryBuilder.mustNot(QueryBuilders.existsQuery(FieldUtils.val(BusinessIndex::getRelation)));
                auditStatusNormalBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getCompanyId), qo.getCompanyId()));
                auditStatusNormalBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getApAuditStatus), auditStatus));
                BoolQueryBuilder auditStatusSubcontractBoolQueryBuilder = QueryBuilders.boolQuery();
                // 存在转包关系查询上游货主审核状态
                auditStatusSubcontractBoolQueryBuilder.must(QueryBuilders.existsQuery(FieldUtils.val(BusinessIndex::getRelation)));
                auditStatusSubcontractBoolQueryBuilder.must(QueryBuilders.termQuery("relation.tempIdConcatStatus.keyword", qo.getCompanyId() + StrUtil.DASHED + auditStatus));
                // 拼接条件查询
                auditStatusBoolQueryBuilder.should(auditStatusNormalBoolQueryBuilder);
                auditStatusBoolQueryBuilder.should(auditStatusSubcontractBoolQueryBuilder);
                boolQueryBuilder.must(auditStatusBoolQueryBuilder);
            }
            if (UserUtils.isCarrierPc()) {
                boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getArAuditStatus), auditStatus));
            }
        }
        // 货主审核状态
        Integer apAuditStatus = qo.getApAuditStatus();
        if (ObjectUtil.isNotNull(apAuditStatus)) {
            boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getApAuditStatus), apAuditStatus));
        }
        // 平台审核状态
        Integer shipperAuditStatus = qo.getShipperAuditStatus();
        if (ObjectUtil.isNotNull(shipperAuditStatus)) {
            // 平台自营平台对上查询平台对上审核状态
            if ((ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isTrue(qo.getIsShipperUp()))) {
                boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperAuditStatusUp), shipperAuditStatus));
            }
            // 平台自营平台对下查询平台对下审核状态
            if ((ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isFalse(qo.getIsShipperUp()))) {
                boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperAuditStatus), shipperAuditStatus));
            }
            // 平台总包查询平台对上审核状态和平台对下审核状态
            if (ObjectUtil.equals(BusinessTypeEnums.PLATFORM_PACKAGE.getCode(), businessType)) {
                boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperAuditStatusUp), shipperAuditStatus));
                boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperAuditStatus), shipperAuditStatus));
            }
        }
        // 审核时间
        String auditStartTime = qo.getAuditStartTime();
        String auditEndTime = qo.getAuditEndTime();
        if (StrUtil.isNotEmpty(auditStartTime) && StrUtil.isNotEmpty(auditEndTime)) {
            BoolQueryBuilder auditTimeBoolQueryBuilder = QueryBuilders.boolQuery();
            // 不存在转包关系查询货主审核时间
            BoolQueryBuilder auditTimeNormalBoolQueryBuilder = QueryBuilders.boolQuery();
            auditTimeNormalBoolQueryBuilder.mustNot(QueryBuilders.existsQuery(FieldUtils.val(BusinessIndex::getRelation)));
            auditTimeNormalBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getCompanyId), qo.getCompanyId()));
            auditTimeNormalBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getApAuditTime)).gte(auditStartTime));
            auditTimeNormalBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getApAuditTime)).lte(auditEndTime));
            // 存在转包关系查询上游货主审核时间
            BoolQueryBuilder auditTimeSubcontractBoolQueryBuilder = QueryBuilders.boolQuery();
            auditTimeSubcontractBoolQueryBuilder.must(QueryBuilders.existsQuery(FieldUtils.val(BusinessIndex::getRelation)));
            auditTimeSubcontractBoolQueryBuilder.must(QueryBuilders.termQuery("relation.apCompanyId", qo.getCompanyId()));
            auditTimeSubcontractBoolQueryBuilder.must(QueryBuilders.rangeQuery("relation.apAuditTime").gte(auditStartTime));
            auditTimeSubcontractBoolQueryBuilder.must(QueryBuilders.rangeQuery("relation.apAuditTime").lte(auditEndTime));
            // 拼接条件查询
            auditTimeBoolQueryBuilder.should(auditTimeNormalBoolQueryBuilder);
            auditTimeBoolQueryBuilder.should(auditTimeSubcontractBoolQueryBuilder);
            boolQueryBuilder.must(auditTimeBoolQueryBuilder);
        }
        // 货主审核时间
        String apAuditStartTime = qo.getApAuditStartTime();
        String apAuditEndTime = qo.getApAuditEndTime();
        if (StrUtil.isNotEmpty(apAuditStartTime)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getApAuditTime)).gte(apAuditStartTime));
        }
        if (StrUtil.isNotEmpty(apAuditEndTime)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getApAuditTime)).lte(apAuditEndTime));
        }
        // 平台审核时间
        String shipperAuditStartTime = qo.getShipperAuditStartTime();
        String shipperAuditEndTime = qo.getShipperAuditEndTime();
        if (StrUtil.isNotEmpty(shipperAuditStartTime)) {
            // 平台自营平台对上查询平台对上审核时间
            if ((ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isTrue(qo.getIsShipperUp()))) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getShipperAuditTimeUp)).gte(shipperAuditStartTime));
            }
            // 平台自营平台对下查询平台对下审核时间
            if ((ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isFalse(qo.getIsShipperUp()))) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getShipperAuditTime)).gte(shipperAuditStartTime));
            }
            // 平台总包查询平台对上审核状态和平台对下审核时间
            if (ObjectUtil.equals(BusinessTypeEnums.PLATFORM_PACKAGE.getCode(), businessType)) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getShipperAuditTimeUp)).gte(shipperAuditStartTime));
                boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getShipperAuditTime)).gte(shipperAuditStartTime));
            }
        }
        if (StrUtil.isNotEmpty(shipperAuditEndTime)) {
            // 平台自营平台对上查询平台对上审核时间
            if ((ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isTrue(qo.getIsShipperUp()))) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getShipperAuditTimeUp)).lte(shipperAuditEndTime));
            }
            // 平台自营平台对下查询平台对下审核时间
            if ((ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isFalse(qo.getIsShipperUp()))) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getShipperAuditTime)).lte(shipperAuditEndTime));
            }
            // 平台总包查询平台对上审核状态和平台对下审核时间
            if (ObjectUtil.equals(BusinessTypeEnums.PLATFORM_PACKAGE.getCode(), businessType)) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getShipperAuditTimeUp)).lte(shipperAuditEndTime));
                boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getShipperAuditTime)).lte(shipperAuditEndTime));
            }
        }
        // 发货单位
        String fmCustomerName = qo.getFmCustomerName();
        if (StrUtil.isNotEmpty(fmCustomerName)) {
            matchPhraseOrWildcardMustQuery(boolQueryBuilder, fmCustomerName, true, CollectionUtil.newArrayList(FieldUtils.val(BusinessIndex::getFmCustomerName)));
        }
        // 收货单位
        String toCustomerName = qo.getToCustomerName();
        if (StrUtil.isNotEmpty(toCustomerName)) {
            matchPhraseOrWildcardMustQuery(boolQueryBuilder, toCustomerName, true, CollectionUtil.newArrayList(FieldUtils.val(BusinessIndex::getToCustomerName)));
        }
        // 发货地址
        String fmAddress = qo.getFmAddress();
        if (StrUtil.isNotEmpty(fmAddress)) {
            matchPhraseOrWildcardMustQuery(boolQueryBuilder, fmAddress, true, CollectionUtil.newArrayList(FieldUtils.val(BusinessIndex::getFmAddress)));
        }
        // 收货地址
        String toAddress = qo.getToAddress();
        if (StrUtil.isNotEmpty(toAddress)) {
            matchPhraseOrWildcardMustQuery(boolQueryBuilder, toAddress, true, CollectionUtil.newArrayList(FieldUtils.val(BusinessIndex::getToAddress)));
        }
        // 货主单价
        BigDecimal hzPriceMin = qo.getHzPriceMin();
        BigDecimal hzPriceMax = qo.getHzPriceMax();
        if (ObjectUtil.isNotNull(hzPriceMin) && ObjectUtil.isNotNull(hzPriceMax)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("items.hzPrice").gte(hzPriceMin).lte(hzPriceMax));
        }else {
            if (ObjectUtil.isNotNull(hzPriceMin)) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("items.hzPrice").gte(hzPriceMin));
            }
            if (ObjectUtil.isNotNull(hzPriceMax)) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("items.hzPrice").lte(hzPriceMax));
            }
        }
        // 承运商单价
        BigDecimal ptPriceMin = qo.getPtPriceMin();
        BigDecimal ptPriceMax = qo.getPtPriceMax();
        if (ObjectUtil.isNotNull(ptPriceMin) && ObjectUtil.isNotNull(ptPriceMax)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery("items.ptPrice").gte(ptPriceMin).lte(ptPriceMax));
        }else {
            if (ObjectUtil.isNotNull(ptPriceMin)) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("items.ptPrice").gte(ptPriceMin));
            }
            if (ObjectUtil.isNotNull(ptPriceMax)) {
                boolQueryBuilder.must(QueryBuilders.rangeQuery("items.ptPrice").lte(ptPriceMax));
            }
        }
        // 运输计划编号
        String transportNo = qo.getTransportNo();
        if (StrUtil.isNotEmpty(transportNo)) {
            matchPhraseOrWildcardMustQuery(boolQueryBuilder, transportNo, true, CollectionUtil.newArrayList(FieldUtils.val(BusinessIndex::getTransportNo) + ".text"));
        }
        // 司机名称
        String driverName = qo.getDriverName();
        if (StrUtil.isNotEmpty(driverName)) {
            matchPhraseOrWildcardMustQuery(boolQueryBuilder, driverName, true, CollectionUtil.newArrayList(FieldUtils.val(BusinessIndex::getDriverName)));
        }
        // 车牌号
        String carNo = qo.getCarNo();
        if (StrUtil.isNotEmpty(carNo)) {
            matchPhraseOrWildcardMustQuery(boolQueryBuilder, carNo, true, CollectionUtil.newArrayList(FieldUtils.val(BusinessIndex::getCarNo)));
        }
        // 订单号
        String orderNo = qo.getOrderNo();
        if (StrUtil.isNotEmpty(orderNo)) {
            matchPhraseOrWildcardMustQuery(boolQueryBuilder, orderNo, true, CollectionUtil.newArrayList(FieldUtils.val(BusinessIndex::getOrderNo)));
        }
        // 创建时间
        String startTime = qo.getStartTime();
        String endTime = qo.getEndTime();
        if (StrUtil.isNotEmpty(startTime)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getCreateTime)).gte(startTime));
        }
        if (StrUtil.isNotEmpty(endTime)) {
            boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getCreateTime)).lte(endTime));
        }
        // 时间状态和运输时间
        if (ObjectUtil.isNotNull(qo.getClockInNode())) {
            switch (qo.getClockInNode()) {
                case 1:
                    if (StrUtil.isNotEmpty(qo.getTransportStartTime()) && StrUtil.isNotEmpty(qo.getTransportEndTime())) {
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getSentCarTime)).gte(qo.getTransportStartTime()));
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getSentCarTime)).lte(qo.getTransportEndTime()));
                    }
                    break;
                case 2:
                    if (StrUtil.isNotEmpty(qo.getTransportStartTime()) && StrUtil.isNotEmpty(qo.getTransportEndTime())) {
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getWaybillStartTime)).gte(qo.getTransportStartTime()));
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getWaybillStartTime)).lte(qo.getTransportEndTime()));
                    }
                    break;
                case 3:
                    if (StrUtil.isNotEmpty(qo.getTransportStartTime()) && StrUtil.isNotEmpty(qo.getTransportEndTime())) {
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getLoadSignInTime)).gte(qo.getTransportStartTime()));
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getLoadSignInTime)).lte(qo.getTransportEndTime()));
                    }
                    break;
                case 4:
                    if (StrUtil.isNotEmpty(qo.getTransportStartTime()) && StrUtil.isNotEmpty(qo.getTransportEndTime())) {
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getLoadConfirmTime)).gte(qo.getTransportStartTime()));
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getLoadConfirmTime)).lte(qo.getTransportEndTime()));
                    }
                    break;
                case 5:
                    if (StrUtil.isNotEmpty(qo.getTransportStartTime()) && StrUtil.isNotEmpty(qo.getTransportEndTime())) {
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getUnloadSignInTime)).gte(qo.getTransportStartTime()));
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getUnloadSignInTime)).lte(qo.getTransportEndTime()));
                    }
                    break;
                case 6:
                    if (StrUtil.isNotEmpty(qo.getTransportStartTime()) && StrUtil.isNotEmpty(qo.getTransportEndTime())) {
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getUnloadConfirmTime)).gte(qo.getTransportStartTime()));
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getUnloadConfirmTime)).lte(qo.getTransportEndTime()));
                    }
                    break;
                case 7:
                    if (StrUtil.isNotEmpty(qo.getTransportStartTime()) && StrUtil.isNotEmpty(qo.getTransportEndTime())) {
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getLoadFirstTime)).gte(qo.getTransportStartTime()));
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getLoadFirstTime)).lte(qo.getTransportEndTime()));
                    }
                    break;
                case 8:
                    if (StrUtil.isNotEmpty(qo.getTransportStartTime()) && StrUtil.isNotEmpty(qo.getTransportEndTime())) {
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getLoadSecondTime)).gte(qo.getTransportStartTime()));
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getLoadSecondTime)).lte(qo.getTransportEndTime()));
                    }
                    break;
                case 9:
                    if (StrUtil.isNotEmpty(qo.getTransportStartTime()) && StrUtil.isNotEmpty(qo.getTransportEndTime())) {
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getUnloadFirstTime)).gte(qo.getTransportStartTime()));
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getUnloadFirstTime)).lte(qo.getTransportEndTime()));
                    }
                    break;
                case 10:
                    if (StrUtil.isNotEmpty(qo.getTransportStartTime()) && StrUtil.isNotEmpty(qo.getTransportEndTime())) {
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getUnloadSecondTime)).gte(qo.getTransportStartTime()));
                        boolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getUnloadSecondTime)).lte(qo.getTransportEndTime()));
                    }
                    break;
            }
        }
        // 分页查询tab类型
        Integer queryTabType = qo.getQueryTabType();
        if (ObjectUtil.isNotNull(queryTabType)) {
            // 货主登录和平台登录待审核tab
            if (ObjectUtil.equals(BusinessQueryTabTypeEnums.UN_AUDIT.getCode(), queryTabType)) {
                // 货主登录
                if (UserUtils.isShipperPc()) {
                    BoolQueryBuilder unAuditBoolQueryBuilder = QueryBuilders.boolQuery();
                    // 不存在转包关系查询货主审核状态为待审核的对账单
                    BoolQueryBuilder unAuditNormalBoolQueryBuilder = QueryBuilders.boolQuery();
                    unAuditNormalBoolQueryBuilder.mustNot(QueryBuilders.existsQuery(FieldUtils.val(BusinessIndex::getRelation)));
                    unAuditNormalBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getCompanyId), qo.getCompanyId()));
                    unAuditNormalBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getApAuditStatus), AuditStatusEnums.TO_BE_REVIEWED.getCode()));
                    // 存在转包关系
                    BoolQueryBuilder unAuditSubcontractBoolQueryBuilder = QueryBuilders.boolQuery();
                    unAuditSubcontractBoolQueryBuilder.must(QueryBuilders.existsQuery(FieldUtils.val(BusinessIndex::getRelation)));
                    // 下游货主查询货主审核状态为待审核的对账单
                    BoolQueryBuilder unAuditSubcontractIsShipperBoolQueryBuilder = QueryBuilders.boolQuery();
                    unAuditSubcontractIsShipperBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getCompanyId), qo.getCompanyId()));
                    unAuditSubcontractIsShipperBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getApAuditStatus), AuditStatusEnums.TO_BE_REVIEWED.getCode()));
                    // 上游货主查询上游货主审核状态为待审核的对账单
                    BoolQueryBuilder unAuditSubcontractIsShipperUpBoolQueryBuilder = QueryBuilders.boolQuery();
                    unAuditSubcontractIsShipperUpBoolQueryBuilder.must(QueryBuilders.termQuery("relation.tempIdConcatStatus.keyword", qo.getCompanyId() + StrUtil.DASHED + AuditStatusEnums.TO_BE_REVIEWED.getCode()));
                    // 拼接条件查询
                    BoolQueryBuilder unAuditSubcontractShipperBoolQueryBuilder = QueryBuilders.boolQuery();
                    unAuditSubcontractShipperBoolQueryBuilder.should(unAuditSubcontractIsShipperBoolQueryBuilder);
                    unAuditSubcontractShipperBoolQueryBuilder.should(unAuditSubcontractIsShipperUpBoolQueryBuilder);
                    unAuditSubcontractBoolQueryBuilder.must(unAuditSubcontractShipperBoolQueryBuilder);
                    // 拼接条件查询
                    unAuditBoolQueryBuilder.should(unAuditNormalBoolQueryBuilder);
                    unAuditBoolQueryBuilder.should(unAuditSubcontractBoolQueryBuilder);
                    boolQueryBuilder.must(unAuditBoolQueryBuilder);
                }
                // 平台登录
                if (UserUtils.isOperatePc()) {
                    // 平台自营平台对上查询平台对上审核状态为待审核的对账单
                    if ((ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isTrue(qo.getIsShipperUp()))) {
                        boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperAuditStatusUp), AuditStatusEnums.TO_BE_REVIEWED.getCode()));
                    }
                    // 平台自营平台对下查询平台对下审核状态为审核通过的对账单
                    if ((ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isFalse(qo.getIsShipperUp()))) {
                        boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperAuditStatus), AuditStatusEnums.TO_BE_REVIEWED.getCode()));
                    }
                    // 平台总包查询平台对上审核状态和平台对下审核状态为待审核的对账单
                    if (ObjectUtil.equals(BusinessTypeEnums.PLATFORM_PACKAGE.getCode(), businessType)) {
                        boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperAuditStatusUp), AuditStatusEnums.TO_BE_REVIEWED.getCode()));
                        boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperAuditStatus), AuditStatusEnums.TO_BE_REVIEWED.getCode()));
                    }
                }
            }
            // 货主登录和平台登录已审核tab
            if (ObjectUtil.equals(BusinessQueryTabTypeEnums.AUDIT.getCode(), queryTabType)) {
                // 货主登录
                if (UserUtils.isShipperPc()) {
                    BoolQueryBuilder auditBoolQueryBuilder = QueryBuilders.boolQuery();
                    // 不存在转包关系查询货主审核状态为审核通过或已退回的对账单
                    BoolQueryBuilder auditNormalBoolQueryBuilder = QueryBuilders.boolQuery();
                    auditNormalBoolQueryBuilder.mustNot(QueryBuilders.existsQuery(FieldUtils.val(BusinessIndex::getRelation)));
                    auditNormalBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getCompanyId), qo.getCompanyId()));
                    auditNormalBoolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getApAuditStatus), CollectionUtil.newArrayList(AuditStatusEnums.PASS_THE_EXAMINATION.getCode(), AuditStatusEnums.RETURNED.getCode())));
                    // 存在转包关系
                    BoolQueryBuilder auditSubcontractBoolQueryBuilder = QueryBuilders.boolQuery();
                    auditSubcontractBoolQueryBuilder.must(QueryBuilders.existsQuery(FieldUtils.val(BusinessIndex::getRelation)));
                    // 下游货主查询货主审核状态为审核通过或已退回的对账单
                    BoolQueryBuilder auditSubcontractIsShipperBoolQueryBuilder = QueryBuilders.boolQuery();
                    auditSubcontractIsShipperBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getCompanyId), qo.getCompanyId()));
                    auditSubcontractIsShipperBoolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getApAuditStatus), CollectionUtil.newArrayList(AuditStatusEnums.PASS_THE_EXAMINATION.getCode(), AuditStatusEnums.RETURNED.getCode())));
                    // 上游货主查询上游货主审核状态为已审核或已退回的对账单
                    BoolQueryBuilder auditSubcontractIsShipperUpBoolQueryBuilder = QueryBuilders.boolQuery();
                    auditSubcontractIsShipperUpBoolQueryBuilder.must(QueryBuilders.termsQuery("relation.tempIdConcatStatus.keyword", qo.getCompanyId() + StrUtil.DASHED + AuditStatusEnums.PASS_THE_EXAMINATION.getCode(), qo.getCompanyId() + StrUtil.DASHED + AuditStatusEnums.RETURNED.getCode()));
                    // 拼接条件查询
                    BoolQueryBuilder auditSubcontractShipperBoolQueryBuilder = QueryBuilders.boolQuery();
                    auditSubcontractShipperBoolQueryBuilder.should(auditSubcontractIsShipperBoolQueryBuilder);
                    auditSubcontractShipperBoolQueryBuilder.should(auditSubcontractIsShipperUpBoolQueryBuilder);
                    auditSubcontractBoolQueryBuilder.must(auditSubcontractShipperBoolQueryBuilder);
                    // 拼接条件查询
                    auditBoolQueryBuilder.should(auditNormalBoolQueryBuilder);
                    auditBoolQueryBuilder.should(auditSubcontractBoolQueryBuilder);
                    boolQueryBuilder.must(auditBoolQueryBuilder);
                }
                // 平台登录
                if (UserUtils.isOperatePc()) {
                    // 平台自营平台对上查询平台对上审核状态为审核通过或已退回的对账单
                    if ((ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isTrue(qo.getIsShipperUp()))) {
                        BoolQueryBuilder adminLoginShipperUpAuditBuilder = QueryBuilders.boolQuery();
                        adminLoginShipperUpAuditBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getShipperAuditStatusUp), CollectionUtil.newArrayList(AuditStatusEnums.PASS_THE_EXAMINATION.getCode(), AuditStatusEnums.RETURNED.getCode())));
                        boolQueryBuilder.must(adminLoginShipperUpAuditBuilder);
                    }
                    // 平台自营平台对下查询平台对下审核状态为审核通过或已退回的对账单
                    if ((ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isFalse(qo.getIsShipperUp()))) {
                        BoolQueryBuilder adminLoginShipperAuditBuilder = QueryBuilders.boolQuery();
                        adminLoginShipperAuditBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getShipperAuditStatus), CollectionUtil.newArrayList(AuditStatusEnums.PASS_THE_EXAMINATION.getCode(), AuditStatusEnums.RETURNED.getCode())));
                        boolQueryBuilder.must(adminLoginShipperAuditBuilder);
                    }
                    // 平台总包查询平台对上审核状态和平台对下审核状态为审核通过的对账单
                    if (ObjectUtil.equals(BusinessTypeEnums.PLATFORM_PACKAGE.getCode(), businessType)) {
                        BoolQueryBuilder adminLoginAllAuditBuilder = QueryBuilders.boolQuery();
                        adminLoginAllAuditBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getShipperAuditStatusUp), CollectionUtil.newArrayList(AuditStatusEnums.PASS_THE_EXAMINATION.getCode(), AuditStatusEnums.RETURNED.getCode())));
                        adminLoginAllAuditBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getShipperAuditStatus), CollectionUtil.newArrayList(AuditStatusEnums.PASS_THE_EXAMINATION.getCode(), AuditStatusEnums.RETURNED.getCode())));
                        boolQueryBuilder.must(adminLoginAllAuditBuilder);
                    }
                }
            }
            // 平台登录和承运商登录未对账tab
            if (ObjectUtil.equals(BusinessQueryTabTypeEnums.UN_RC.getCode(), queryTabType)) {
                // 平台登录平台自营平台对上查询货主对账状态为未对账的对账单
                if (UserUtils.isOperatePc() && ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isTrue(qo.getIsShipperUp())) {
                    boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getRcStatus), RcStatusEnums.OUTSTANDING_RECONCILIATION.getCode()));
                    boolQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getIsAbnormal), AbnormalEnums.YES.getCode()));
                }
                // 承运商登录
                if (UserUtils.isCarrierPc()) {
                    // 平台自营查询平台对账状态为未对账的对账单
                    BoolQueryBuilder carrierLoginPTZYRcUnSettleBuilder = QueryBuilders.boolQuery();
                    carrierLoginPTZYRcUnSettleBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getBusinessType), BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode()));
                    carrierLoginPTZYRcUnSettleBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperRcStatus), RcStatusEnums.OUTSTANDING_RECONCILIATION.getCode()));
                    // 货主自营或平台总包查询货主对账状态为未对账的对账单
                    BoolQueryBuilder carrierLoginHZZYOrPTZBRcUnSettleBuilder = QueryBuilders.boolQuery();
                    carrierLoginHZZYOrPTZBRcUnSettleBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getBusinessType), CollectionUtil.newArrayList(BusinessTypeEnums.OWNER_OPERATED.getCode(), BusinessTypeEnums.PLATFORM_PACKAGE.getCode())));
                    carrierLoginHZZYOrPTZBRcUnSettleBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getRcStatus), RcStatusEnums.OUTSTANDING_RECONCILIATION.getCode()));
                    // 拼接条件查询
                    BoolQueryBuilder carrierLoginRcUnSettleBuilder = QueryBuilders.boolQuery();
                    carrierLoginRcUnSettleBuilder.should(carrierLoginPTZYRcUnSettleBuilder);
                    carrierLoginRcUnSettleBuilder.should(carrierLoginHZZYOrPTZBRcUnSettleBuilder);
                    boolQueryBuilder.must(carrierLoginRcUnSettleBuilder);
                    boolQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getIsAbnormalCarrier), AbnormalEnums.YES.getCode()));
                }
            }
            // 平台登录和承运商登录已对账未结算tab
            if (ObjectUtil.equals(BusinessQueryTabTypeEnums.RC_UN_SETTLE.getCode(), queryTabType)) {
                // 平台登录平台自营平台对上查询货主对账状态为已对账和结算对上状态为未结算或已退回或已作废的对账单
                if (UserUtils.isOperatePc() && ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isTrue(qo.getIsShipperUp())) {
                    boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getRcStatus), RcStatusEnums.HAVE_RECONCILED.getCode()));
                    boolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getSettlementStatusUp), CollectionUtil.newArrayList(SettlementStatusEnums.UNSETTLED.getCode(), SettlementStatusEnums.RETURNED.getCode(), SettlementStatusEnums.ABROGATED.getCode())));
                    //gs 推送不包含推送中的
                    boolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getPushGsStatusUp), CollectionUtil.newArrayList(PushGsStatusEnums.TO_BE_PUSHED.getCode(),
                            PushGsStatusEnums.PUSH_SUCCESS.getCode(), PushGsStatusEnums.PUSH_ERROR.getCode(), PushGsStatusEnums.RETURN_OF_FIRST_INSTANCE.getCode(), PushGsStatusEnums.AUDIT_ROLLBACK.getCode())));
                    boolQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getIsAbnormal), AbnormalEnums.YES.getCode()));
                }
                // 承运商登录
                if (UserUtils.isCarrierPc()) {
                    // 平台自营查询平台对账状态为已对账和结算对下状态为未结算或已退回或已作废的对账单
                    BoolQueryBuilder carrierLoginPTZYRcUnSettleBuilder = QueryBuilders.boolQuery();
                    carrierLoginPTZYRcUnSettleBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getBusinessType), BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode()));
                    carrierLoginPTZYRcUnSettleBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperRcStatus), RcStatusEnums.HAVE_RECONCILED.getCode()));
                    carrierLoginPTZYRcUnSettleBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getSettlementStatus), CollectionUtil.newArrayList(SettlementStatusEnums.UNSETTLED.getCode(), SettlementStatusEnums.RETURNED.getCode(), SettlementStatusEnums.ABROGATED.getCode())));
                    // 货主自营或平台总包查询货主对账状态为已对账和结算对下状态为未结算或已退回或已作废的对账单
                    BoolQueryBuilder carrierLoginHZZYOrPTZBRcUnSettleBuilder = QueryBuilders.boolQuery();
                    carrierLoginHZZYOrPTZBRcUnSettleBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getBusinessType), CollectionUtil.newArrayList(BusinessTypeEnums.OWNER_OPERATED.getCode(), BusinessTypeEnums.PLATFORM_PACKAGE.getCode())));
                    carrierLoginHZZYOrPTZBRcUnSettleBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getRcStatus), RcStatusEnums.HAVE_RECONCILED.getCode()));
                    carrierLoginHZZYOrPTZBRcUnSettleBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getSettlementStatus), CollectionUtil.newArrayList(SettlementStatusEnums.UNSETTLED.getCode(), SettlementStatusEnums.RETURNED.getCode(), SettlementStatusEnums.ABROGATED.getCode())));
                    // 拼接条件查询
                    BoolQueryBuilder carrierLoginRcUnSettleBuilder = QueryBuilders.boolQuery();
                    carrierLoginRcUnSettleBuilder.should(carrierLoginPTZYRcUnSettleBuilder);
                    carrierLoginRcUnSettleBuilder.should(carrierLoginHZZYOrPTZBRcUnSettleBuilder);
                    boolQueryBuilder.must(carrierLoginRcUnSettleBuilder);
                    boolQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getIsAbnormalCarrier), AbnormalEnums.YES.getCode()));
                }
            }
            //平台登录已对账制证中tab （仅限平台自己对账发起）
            if (ObjectUtil.equals(BusinessQueryTabTypeEnums.RC_UN_SETTLE_AUDIT.getCode(), queryTabType) && UserUtils.isOperatePc()) {
                // 平台登录平台自营平台对上查询货主对账状态为已对账和结算对上状态为未结算或已退回或已作废的对账单
                if (UserUtils.isOperatePc() && ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isTrue(qo.getIsShipperUp())) {
                    boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getRcStatus), RcStatusEnums.HAVE_RECONCILED.getCode()));
                    boolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getSettlementStatusUp), CollectionUtil.newArrayList(SettlementStatusEnums.UNSETTLED.getCode(), SettlementStatusEnums.RETURNED.getCode(), SettlementStatusEnums.ABROGATED.getCode())));
                    //gs 推送只包含推送中的
                    boolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getPushGsStatusUp), CollectionUtil.newArrayList(PushGsStatusEnums.PUSHING.getCode())));
                    boolQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getIsAbnormal), AbnormalEnums.YES.getCode()));
                }
            }
            // 平台登录和承运商登录已结算tab
            if (ObjectUtil.equals(BusinessQueryTabTypeEnums.SETTLE.getCode(), queryTabType)) {
                // 平台登录平台自营平台对上查询结算对上状态为已结算的对账单
                if (UserUtils.isOperatePc() && ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isTrue(qo.getIsShipperUp())) {
                    boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getSettlementStatusUp), SettlementStatusEnums.HAVE_ALREADY_SETTLED.getCode()));
                    boolQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getIsAbnormal), AbnormalEnums.YES.getCode()));
                }
                // 承运商登录查询结算对下状态为已结算的对账单
                if (UserUtils.isCarrierPc()) {
                    boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getSettlementStatus), SettlementStatusEnums.HAVE_ALREADY_SETTLED.getCode()));
                    boolQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getIsAbnormalCarrier), AbnormalEnums.YES.getCode()));
                }
            }
            // 平台登录和承运商登录异常tab
            if (ObjectUtil.equals(BusinessQueryTabTypeEnums.EXCEPTION.getCode(), queryTabType)) {
                // 平台登录平台自营平台对上查询货主对账状态为未对账的对账单
                if (UserUtils.isOperatePc() && ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isTrue(qo.getIsShipperUp())) {
                    boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getIsAbnormal), AbnormalEnums.YES.getCode()));
                }
                // 承运商登录
                if (UserUtils.isCarrierPc()) {
                    boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getIsAbnormalCarrier), AbnormalEnums.YES.getCode()));
                }
            }
        }
        // 应收结算单号
        String arSettlementNo = qo.getArSettlementNo();
        if (StrUtil.isNotEmpty(arSettlementNo)) {
            // 货主登录查询兼容应收应付结算单号
            if (UserUtils.isShipperPc()) {
                List<String> settlementNos = CollectionUtil.newArrayList(FieldUtils.val(BusinessIndex::getApSettlementNo), FieldUtils.val(BusinessIndex::getArSettlementNo));
                settlementNos.addAll(StrUtil.split("relation.arSettlementNo", ","));
                matchPhraseOrWildcardMustQuery(boolQueryBuilder, arSettlementNo, true, settlementNos);
            } else {
                matchPhraseOrWildcardMustQuery(boolQueryBuilder, arSettlementNo, true, CollectionUtil.newArrayList(FieldUtils.val(BusinessIndex::getArSettlementNo)));
            }
        }
        // 应付结算单号
        String apSettlementNo = qo.getApSettlementNo();
        if (StrUtil.isNotEmpty(apSettlementNo)) {
            // 货主登录查询兼容应收应付结算单号
            if (UserUtils.isShipperPc()) {
                List<String> settlementNos = CollectionUtil.newArrayList(FieldUtils.val(BusinessIndex::getApSettlementNo), FieldUtils.val(BusinessIndex::getArSettlementNo));
                settlementNos.addAll(StrUtil.split("relation.arSettlementNo", ","));
                matchPhraseOrWildcardMustQuery(boolQueryBuilder, apSettlementNo, true, settlementNos);
            } else {
                matchPhraseOrWildcardMustQuery(boolQueryBuilder, apSettlementNo, true, CollectionUtil.newArrayList(FieldUtils.val(BusinessIndex::getApSettlementNo)));
            }
        }
        // 结算状态
        Integer settlementStatus = qo.getSettlementStatus();
        if (ObjectUtil.isNotNull(settlementStatus)) {
            // 货主登录
            if (UserUtils.isShipperPc()) {
                // 货主自营或平台总包查询对下结算状态
                BoolQueryBuilder settlementHZZYPTZBStatusBuilder = QueryBuilders.boolQuery();
                settlementHZZYPTZBStatusBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getBusinessType), CollectionUtil.newArrayList(BusinessTypeEnums.OWNER_OPERATED.getCode(), BusinessTypeEnums.PLATFORM_PACKAGE.getCode())));
                settlementHZZYPTZBStatusBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getSettlementStatus), settlementStatus));
                // 平台自营查询对上结算状态
                BoolQueryBuilder settlementPTZYStatusBuilder = QueryBuilders.boolQuery();
                settlementPTZYStatusBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getBusinessType), BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode()));
                settlementPTZYStatusBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getSettlementStatusUp), settlementStatus));
                // 拼接条件查询
                BoolQueryBuilder settlementStatusBuilder = QueryBuilders.boolQuery();
                settlementStatusBuilder.should(settlementHZZYPTZBStatusBuilder);
                settlementStatusBuilder.should(settlementPTZYStatusBuilder);
                boolQueryBuilder.must(settlementStatusBuilder);
            }
            // 平台登录
            if (UserUtils.isOperatePc()) {
                // 平台自营平台对上查询对上结算状态
                if ((ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isTrue(qo.getIsShipperUp()))) {
                    boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getSettlementStatusUp), settlementStatus));
                }
                // 平台自营平台对下查询对下结算状态
                if ((ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isFalse(qo.getIsShipperUp()))) {
                    boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getSettlementStatus), settlementStatus));
                }
                // 平台总包查询对下结算状态
                if (ObjectUtil.equals(BusinessTypeEnums.PLATFORM_PACKAGE.getCode(), businessType)) {
                    boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getSettlementStatus), settlementStatus));
                }
            }
            // 承运商登录查询对下结算状态
            if (UserUtils.isCarrierPc()) {
                boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getSettlementStatus), settlementStatus));
            }
        }
        // 付款方名称
        String apCompanyName = qo.getApCompanyName();
        if (StrUtil.isNotEmpty(apCompanyName)) {
            matchPhraseOrWildcardMustQuery(boolQueryBuilder, apCompanyName, true, CollectionUtil.newArrayList(FieldUtils.val(BusinessIndex::getApCompanyName)));
        }
        // 收款方名称
        String arCompanyName = qo.getArCompanyName();
        if (StrUtil.isNotEmpty(arCompanyName)) {
            matchPhraseOrWildcardMustQuery(boolQueryBuilder, arCompanyName, true, CollectionUtil.newArrayList(FieldUtils.val(BusinessIndex::getArCompanyName)));
        }
        // 货物名称
        String itemName = qo.getItemName();
        if (StrUtil.isNotEmpty(itemName)) {
            matchPhraseOrWildcardMustQuery(boolQueryBuilder, itemName, true, StrUtil.split("items.itemName", ","));
        }
        // 运输方式
        Integer transportType = qo.getTransportType();
        if (ObjectUtil.isNotNull(transportType)) {
            boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getTransportType), transportType));
        }
        // 是否调量
        Integer isAdjustWeight = qo.getIsAdjustWeight();
        if (ObjectUtil.isNotNull(isAdjustWeight)) {
            if (UserUtils.isShipperPc()) {
                BoolQueryBuilder isAdjustWeightBoolQueryBuilder = new BoolQueryBuilder();
                isAdjustWeightBoolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getBusinessType), CollectionUtil.newArrayList(BusinessTypeEnums.OWNER_OPERATED.getCode(), BusinessTypeEnums.PLATFORM_PACKAGE.getCode())));
                isAdjustWeightBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getIsAdjustWeight), isAdjustWeight));
                BoolQueryBuilder shipperIsAdjustWeightBoolQueryBuilder = new BoolQueryBuilder();
                shipperIsAdjustWeightBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getBusinessType), BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode()));
                shipperIsAdjustWeightBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperIsAdjustWeight), isAdjustWeight));
                BoolQueryBuilder ownerIsAdjustWeightBoolQueryBuilder = new BoolQueryBuilder();
                ownerIsAdjustWeightBoolQueryBuilder.should(isAdjustWeightBoolQueryBuilder);
                ownerIsAdjustWeightBoolQueryBuilder.should(shipperIsAdjustWeightBoolQueryBuilder);
                boolQueryBuilder.must(ownerIsAdjustWeightBoolQueryBuilder);
            }
            if (UserUtils.isOperatePc()) {
                if (ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isTrue(qo.getIsShipperUp())) {
                    boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperIsAdjustWeight), isAdjustWeight));
                } else {
                    boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getIsAdjustWeight), isAdjustWeight));
                }
            }
            if (UserUtils.isCarrierPc()) {
                boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getIsAdjustWeight), isAdjustWeight));
            }
        }
        // 推送gs状态平台自营都传成pushGsStatus，平台总包应付推送状态传pushGsStatus
        Integer pushGsStatus = qo.getPushGsStatus();
        if (ObjectUtil.isNotNull(pushGsStatus)) {
            //平台自营对上的推送状态查询PushGsStatusUp
            if (UserUtils.isOperatePc() && ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isTrue(qo.getIsShipperUp())) {
                boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getPushGsStatusUp), pushGsStatus));
            }else {
                //平台自营的对下推送状态取PushGsStatus，平台总包的对下推送状态取PushGsStatus
                boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getPushGsStatus), pushGsStatus));
            }
          /*  if (UserUtils.isCarrierPc()) {
                boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getPushGsStatus), pushGsStatus));
            }*/
        }
        // 推送gs状态(平台总包应收推送状态)
        Integer pushGsStatusUp = qo.getPushGsStatusUp();
        if (ObjectUtil.isNotNull(pushGsStatusUp)) {
            //平台总包对上的推送状态查询PushGsStatusUp
            if (ObjectUtil.equals(BusinessTypeEnums.PLATFORM_PACKAGE.getCode(), businessType)) {
                boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getPushGsStatusUp), pushGsStatusUp));
            }
        }
        // 过滤子运单号
        List<String> filterDispatchNos = qo.getFilterDispatchNos();
        if (CollectionUtil.isNotEmpty(filterDispatchNos)) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getDispatchNo) + ".keyword", filterDispatchNos));
        }
        // 货主登录
        Long companyId = qo.getCompanyId();
        if (ObjectUtil.isNotNull(companyId)) {
            // 不存在转包关系查询当前货主公司ID
            BoolQueryBuilder companyIdNormalBoolQueryBuilder = QueryBuilders.boolQuery();
            companyIdNormalBoolQueryBuilder.mustNot(QueryBuilders.existsQuery(FieldUtils.val(BusinessIndex::getRelation)));
            companyIdNormalBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getCompanyId), companyId));
            // 存在转包关系查询当前货主公司ID或上游货主ID
            BoolQueryBuilder companyIdSubcontractBoolQueryBuilder = QueryBuilders.boolQuery();
            companyIdSubcontractBoolQueryBuilder.must(QueryBuilders.existsQuery(FieldUtils.val(BusinessIndex::getRelation)));
            companyIdSubcontractBoolQueryBuilder.should(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getCompanyId), companyId));
            companyIdSubcontractBoolQueryBuilder.should(QueryBuilders.termQuery("relation.apCompanyId", companyId));
            // 拼接条件查询
            BoolQueryBuilder companyIdBoolQueryBuilder = QueryBuilders.boolQuery();
            companyIdBoolQueryBuilder.should(companyIdNormalBoolQueryBuilder);
            companyIdBoolQueryBuilder.should(companyIdSubcontractBoolQueryBuilder);
            boolQueryBuilder.must(companyIdBoolQueryBuilder);
        }
        // 平台登录
        Long shipperCompanyId = qo.getShipperCompanyId();
        if (ObjectUtil.isNotNull(shipperCompanyId)) {
            boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperCompanyId), shipperCompanyId));
        }
        // 承运商登录
        Long carrierId = qo.getCarrierId();
        if (ObjectUtil.isNotNull(carrierId)) {
            boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getCarrierId), carrierId));
        }
        // 货主登录
        if (UserUtils.isShipperPc()) {
            // 货主自营
            BoolQueryBuilder HZZYBoolQueryBuilder = QueryBuilders.boolQuery();
            // 不存在转包关系查询承运商审核状态为审核通过的对账单
            BoolQueryBuilder HZZYNormalBoolQueryBuilder = QueryBuilders.boolQuery();
            HZZYNormalBoolQueryBuilder.mustNot(QueryBuilders.existsQuery(FieldUtils.val(BusinessIndex::getRelation)));
            HZZYNormalBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getCompanyId), qo.getCompanyId()));
            HZZYNormalBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getArAuditStatus), AuditStatusEnums.PASS_THE_EXAMINATION.getCode()));
            HZZYNormalBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getBusinessType), BusinessTypeEnums.OWNER_OPERATED.getCode()));
            // 存在转包关系查询
            BoolQueryBuilder HZZYSubcontractBoolQueryBuilder = QueryBuilders.boolQuery();
            HZZYSubcontractBoolQueryBuilder.must(QueryBuilders.existsQuery(FieldUtils.val(BusinessIndex::getRelation)));
            // 下游货主查询承运商审核状态为审核通过的对账单
            BoolQueryBuilder HZZYSubcontractIsShipperBoolQueryBuilder = QueryBuilders.boolQuery();
            HZZYSubcontractIsShipperBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getCompanyId), companyId));
            HZZYSubcontractIsShipperBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getArAuditStatus), AuditStatusEnums.PASS_THE_EXAMINATION.getCode()));
            HZZYSubcontractIsShipperBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getBusinessType), BusinessTypeEnums.OWNER_OPERATED.getCode()));
            // 上游货主查询上游货主审核状态为待审核并且货主审核状态和承运商审核状态都为审核通过的对账单
            BoolQueryBuilder HZZYSubcontractIsShipperUpBoolQueryBuilder = QueryBuilders.boolQuery();
            HZZYSubcontractIsShipperUpBoolQueryBuilder.must(QueryBuilders.termQuery("relation.apCompanyId", companyId));
            HZZYSubcontractIsShipperUpBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getApAuditStatus), AuditStatusEnums.PASS_THE_EXAMINATION.getCode()));
            HZZYSubcontractIsShipperUpBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getArAuditStatus), AuditStatusEnums.PASS_THE_EXAMINATION.getCode()));
            HZZYSubcontractIsShipperUpBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getBusinessType), BusinessTypeEnums.OWNER_OPERATED.getCode()));
            // 拼接条件查询
            BoolQueryBuilder HZZYSubcontractShipperBoolQueryBuilder = QueryBuilders.boolQuery();
            HZZYSubcontractShipperBoolQueryBuilder.should(HZZYSubcontractIsShipperBoolQueryBuilder);
            HZZYSubcontractShipperBoolQueryBuilder.should(HZZYSubcontractIsShipperUpBoolQueryBuilder);
            HZZYSubcontractBoolQueryBuilder.must(HZZYSubcontractShipperBoolQueryBuilder);
            // 拼接条件查询
            HZZYBoolQueryBuilder.should(HZZYNormalBoolQueryBuilder);
            HZZYBoolQueryBuilder.should(HZZYSubcontractBoolQueryBuilder);
            // 平台自营
            BoolQueryBuilder PTZYBoolQueryBuilder = QueryBuilders.boolQuery();
            // 不存在转包关系查询平台对上审核状态为审核通过的对账单
            BoolQueryBuilder PTZYNormalBoolQueryBuilder = QueryBuilders.boolQuery();
            PTZYNormalBoolQueryBuilder.mustNot(QueryBuilders.existsQuery(FieldUtils.val(BusinessIndex::getRelation)));
            PTZYNormalBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getCompanyId), qo.getCompanyId()));
            PTZYNormalBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperAuditStatusUp), AuditStatusEnums.PASS_THE_EXAMINATION.getCode()));
            PTZYNormalBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getBusinessType), BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode()));
            // 存在转包关系
            BoolQueryBuilder PTZYSubcontractBoolQueryBuilder = QueryBuilders.boolQuery();
            PTZYSubcontractBoolQueryBuilder.must(QueryBuilders.existsQuery(FieldUtils.val(BusinessIndex::getRelation)));
            // 下游货主查询上游货主审核状态为待审核并且并且货主审核状态和平台对上审核状态都为审核通过的对账单
            BoolQueryBuilder PTZYSubcontractIsShipperBoolQueryBuilder = QueryBuilders.boolQuery();
            PTZYSubcontractIsShipperBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getCompanyId), companyId));
            PTZYSubcontractIsShipperBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperAuditStatusUp), AuditStatusEnums.PASS_THE_EXAMINATION.getCode()));
            PTZYSubcontractIsShipperBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getBusinessType), BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode()));
            // 上游货主查询上游货主审核状态为待审核并且并且货主审核状态和平台对上审核状态都为审核通过的对账单
            BoolQueryBuilder PTZYSubcontractIsShipperUpBoolQueryBuilder = QueryBuilders.boolQuery();
            PTZYSubcontractIsShipperUpBoolQueryBuilder.must(QueryBuilders.termQuery("relation.apCompanyId", companyId));
            PTZYSubcontractIsShipperUpBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getApAuditStatus), AuditStatusEnums.PASS_THE_EXAMINATION.getCode()));
            PTZYSubcontractIsShipperUpBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperAuditStatusUp), AuditStatusEnums.PASS_THE_EXAMINATION.getCode()));
            PTZYSubcontractIsShipperUpBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getBusinessType), BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode()));
            // 拼接条件查询
            BoolQueryBuilder PTZYSubcontractShipperBoolQueryBuilder = QueryBuilders.boolQuery();
            PTZYSubcontractShipperBoolQueryBuilder.should(PTZYSubcontractIsShipperBoolQueryBuilder);
            PTZYSubcontractShipperBoolQueryBuilder.should(PTZYSubcontractIsShipperUpBoolQueryBuilder);
            PTZYSubcontractBoolQueryBuilder.must(PTZYSubcontractShipperBoolQueryBuilder);
            // 拼接条件查询
            PTZYBoolQueryBuilder.should(PTZYNormalBoolQueryBuilder);
            PTZYBoolQueryBuilder.should(PTZYSubcontractBoolQueryBuilder);
            // 平台总包
            BoolQueryBuilder PTZBBoolQueryBuilder = QueryBuilders.boolQuery();
            // 不存在转包关系查询承运商审核状态和平台对上审核状态和平台对下审核状态为审核通过的对账单
            BoolQueryBuilder PTZBNormalBoolQueryBuilder = QueryBuilders.boolQuery();
            PTZBNormalBoolQueryBuilder.mustNot(QueryBuilders.existsQuery(FieldUtils.val(BusinessIndex::getRelation)));
            PTZBNormalBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getCompanyId), qo.getCompanyId()));
            PTZBNormalBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getArAuditStatus), AuditStatusEnums.PASS_THE_EXAMINATION.getCode()));
            PTZBNormalBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperAuditStatus), AuditStatusEnums.PASS_THE_EXAMINATION.getCode()));
            PTZBNormalBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperAuditStatusUp), AuditStatusEnums.PASS_THE_EXAMINATION.getCode()));
            // 存在转包关系
            BoolQueryBuilder PTZBSubcontractBoolQueryBuilder = QueryBuilders.boolQuery();
            PTZBSubcontractBoolQueryBuilder.must(QueryBuilders.existsQuery(FieldUtils.val(BusinessIndex::getRelation)));
            // 下游货主查询查询承运商审核状态和平台对上审核状态和平台对下审核状态为审核通过的对账单
            BoolQueryBuilder PTZBSubcontractIsShipperBoolQueryBuilder = QueryBuilders.boolQuery();
            PTZBSubcontractIsShipperBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getCompanyId), companyId));
            PTZBSubcontractIsShipperBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getArAuditStatus), AuditStatusEnums.PASS_THE_EXAMINATION.getCode()));
            PTZBSubcontractIsShipperBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperAuditStatus), AuditStatusEnums.PASS_THE_EXAMINATION.getCode()));
            PTZBSubcontractIsShipperBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperAuditStatusUp), AuditStatusEnums.PASS_THE_EXAMINATION.getCode()));
            PTZBSubcontractIsShipperBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getBusinessType), BusinessTypeEnums.PLATFORM_PACKAGE.getCode()));
            // 上游货主查询上游货主审核状态为待审核并且货主审核状态和平台对上审核状态和平台对下审核状态和承运商审核状态都为审核通过的对账单
            BoolQueryBuilder PTZBSubcontractIsShipperUpBoolQueryBuilder = QueryBuilders.boolQuery();
            PTZBSubcontractIsShipperUpBoolQueryBuilder.must(QueryBuilders.termQuery("relation.apCompanyId", companyId));
            PTZBSubcontractIsShipperUpBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getArAuditStatus), AuditStatusEnums.PASS_THE_EXAMINATION.getCode()));
            PTZBSubcontractIsShipperUpBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperAuditStatus), AuditStatusEnums.PASS_THE_EXAMINATION.getCode()));
            PTZBSubcontractIsShipperUpBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperAuditStatusUp), AuditStatusEnums.PASS_THE_EXAMINATION.getCode()));
            PTZBSubcontractIsShipperUpBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getApAuditStatus), AuditStatusEnums.PASS_THE_EXAMINATION.getCode()));
            PTZBSubcontractIsShipperUpBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getBusinessType), BusinessTypeEnums.PLATFORM_PACKAGE.getCode()));
            // 拼接条件查询
            BoolQueryBuilder PTZBSubcontractShipperBoolQueryBuilder = QueryBuilders.boolQuery();
            PTZBSubcontractShipperBoolQueryBuilder.should(PTZBSubcontractIsShipperUpBoolQueryBuilder);
            PTZBSubcontractShipperBoolQueryBuilder.should(PTZBSubcontractIsShipperBoolQueryBuilder);
            PTZBSubcontractBoolQueryBuilder.must(PTZBSubcontractShipperBoolQueryBuilder);
            // 拼接条件查询
            PTZBBoolQueryBuilder.should(PTZBNormalBoolQueryBuilder);
            PTZBBoolQueryBuilder.should(PTZBSubcontractBoolQueryBuilder);
            // 拼接条件查询
            BoolQueryBuilder shipperLoginBoolQueryBuilder = QueryBuilders.boolQuery();
            shipperLoginBoolQueryBuilder.should(HZZYBoolQueryBuilder);
            shipperLoginBoolQueryBuilder.should(PTZYBoolQueryBuilder);
            shipperLoginBoolQueryBuilder.should(PTZBBoolQueryBuilder);
            boolQueryBuilder.must(shipperLoginBoolQueryBuilder);
        }
        // 平台登录
        if (UserUtils.isOperatePc()) {
            // 平台自营对下查询承运商审核状态为审核通过的对账单
            if (ObjectUtil.equals(BusinessTypeEnums.PLATFORM_SELF_SUPPORT.getCode(), businessType) && BooleanUtil.isFalse(qo.getIsShipperUp())) {
                boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getArAuditStatus), AuditStatusEnums.PASS_THE_EXAMINATION.getCode()));
            }
            // 平台总包查询承运商审核状态为审核通过的对账单
            if (ObjectUtil.equals(BusinessTypeEnums.PLATFORM_PACKAGE.getCode(), businessType)) {
                boolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getArAuditStatus), AuditStatusEnums.PASS_THE_EXAMINATION.getCode()));
            }
        }
        // 运单来源
        if(Objects.nonNull(qo.getDispatchSource())){
            switch (qo.getDispatchSource()){
                case 0:
                    //0我找车系统指派
                    BoolQueryBuilder boolQuery0 = QueryBuilders.boolQuery();
                    boolQuery0.should(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getDispatchSource),0));
                    boolQuery0.should(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getDispatchSource),1));
                    boolQuery0.should(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getDispatchSource),2));
                    boolQuery0.should(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getDispatchSource),7));
                    boolQueryBuilder.must(boolQuery0);
                    break;
                case 3:
                    //3竞价
                    BoolQueryBuilder boolQuery3 = QueryBuilders.boolQuery();
                    boolQuery3.should(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getDispatchSource),3));
                    boolQuery3.should(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getDispatchSource),5));
                    boolQuery3.should(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getDispatchSource),8));
                    boolQueryBuilder.must(boolQuery3);
                    break;
                case 4:
                    //4抢单
                    BoolQueryBuilder boolQuery4 = QueryBuilders.boolQuery();
                    boolQuery4.should(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getDispatchSource),4));
                    boolQuery4.should(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getDispatchSource),6));
                    boolQuery4.should(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getDispatchSource),9));
                    boolQueryBuilder.must(boolQuery4);
                    break;
            }
        }
        authCheck(boolQueryBuilder,qo);
        return boolQueryBuilder;
    }

    private void authCheck(BoolQueryBuilder mainBoolQueryBuilder, BmsEsBusinessQueryPageQO qo){
        JSONObject jsonObject = esCommonAuthSearchService.checkQueryAuth(JSONUtil.parseObj(qo), QueryTypeEnums.ORDER);
        qo = JSONUtil.toBean(jsonObject,BmsEsBusinessQueryPageQO.class);

        List<String> companyList = qo.getCompanyList();
        //承运商id
        Long carrierId = qo.getCarrierId();
        String userCompanyId = String.valueOf(UserContext.companyId().get());
        BoolQueryBuilder companyIdShouldBoolQuery = QueryBuilders.boolQuery();
        // 平台 固定公司 超级管理员
        if(UserContext.isOperation() && String.valueOf(commonProperties.getSuperAdminCompanyId()).equals(userCompanyId)){
            if(UserContext.isPlatformAdmin()){
                //平台超管，无需鉴权
                log.info("对账单列表查询-当前登陆角色为平台超级管理员无需数据权限");
            }else{
                log.info("对账单列表查询-当前登陆角色为平台员工");
                //平台员工登录，需要校验平台员工公司数据权限
                authQueryCondition(mainBoolQueryBuilder,qo,companyList);
            }
            return;
        }else if(UserContext.isOperation()){
            // 托运公司 正常登陆公司
            log.info("对账单列表查询-当前登陆角色为托运公司");
            companyIdShouldBoolQuery.should(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getCompanyId), userCompanyId));
            companyIdShouldBoolQuery.should(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getShipperCompanyId), userCompanyId));
            mainBoolQueryBuilder.must(companyIdShouldBoolQuery);
        }else if(UserUtils.isIdentityShipper()){
            // 货主登录
            log.info("对账单列表查询-当前登陆角色为货主公司");
            companyIdShouldBoolQuery.should(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getCompanyId), companyList));
            companyIdShouldBoolQuery.should(QueryBuilders.termsQuery("relation.apCompanyId", companyList));
            mainBoolQueryBuilder.must(companyIdShouldBoolQuery);
        }else if(UserUtils.isIdentityCarrier()){
            //承运商登录
            log.info("对账单列表查询-当前登陆角色为承运商公司");
            mainBoolQueryBuilder.must(QueryBuilders.existsQuery(FieldUtils.val(BusinessIndex::getCarrierId)));
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getCarrierId),carrierId));
        }
        authQueryCondition(mainBoolQueryBuilder,qo,companyList);
    }

    /**
     * 数据权限条件
     * @param mainBoolQueryBuilder
     * @param companyList
     * @param qo
     */
    private void authQueryCondition(BoolQueryBuilder mainBoolQueryBuilder,BmsEsBusinessQueryPageQO qo,List<String> companyList){
        //承运商id
        Long carrierId = qo.getCarrierId();
        // 货主公司id
        Long companyId = qo.getCompanyId();


        //数据权限 订单类型集合
        List<String> orderTypeList = qo.getOrderTypeList();
        if (CollectionUtil.isNotEmpty(orderTypeList)){
            BoolQueryBuilder orderTypeQueryBuilder = QueryBuilders.boolQuery()
                    .should(QueryBuilders.boolQuery()
                            .mustNot(QueryBuilders.existsQuery(FieldUtils.val(BusinessIndex::getOrderType))));
            orderTypeQueryBuilder.should(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getOrderType), orderTypeList));
            mainBoolQueryBuilder.must(orderTypeQueryBuilder);
        }

        // 数据权限配置的公司id集合 （配置当前公司限制）
        BoolQueryBuilder authBoolQuery = QueryBuilders.boolQuery();
        String userCompanyId = String.valueOf(UserContext.companyId().get());
        if (CollectionUtil.isNotEmpty(companyList) && UserContext.companyId().isPresent()
                && companyList.contains(String.valueOf(UserContext.companyId().get())) && !String.valueOf(commonProperties.getSuperAdminCompanyId()).equals(userCompanyId)
                && CollectionUtil.isNotEmpty(companyList) && !companyList.contains("-1")) {
            BoolQueryBuilder innerQuery = QueryBuilders.boolQuery();
            if (ObjectUtil.isNotNull(companyId)){
                BoolQueryBuilder companyIdShouldQuery = QueryBuilders.boolQuery();
                companyIdShouldQuery.should(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getCompanyId), companyId));
                companyIdShouldQuery.should(QueryBuilders.termQuery("relation.apCompanyId", companyId));
                innerQuery.must(companyIdShouldQuery);
            }
            if (ObjectUtil.isNotNull(carrierId)){
                innerQuery.must(
                        QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getCarrierId), carrierId)
                );
            }
            //是否需要关联用户数据过滤 承运商不需要这个条件
//            if (!UserContext.isCarrier()){
//                List<String> userIdList = qo.getUserIdList();
//                if (CollectionUtil.isNotEmpty(userIdList)) {
//                    innerQuery.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getCreateId), userIdList));
//                }
                //数据权限部门id过滤
//                List<Long> depIdList = qo.getDepIdList();
//                if (CollectionUtil.isNotEmpty(depIdList)){
//                    innerQuery.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getOwnerDepartment),depIdList));
//                }
//            }
            // 过滤当前公司的物料信息
//            List<OrderPageQO.EsOtherQueryParam> companyAndOtherList = qo.getCompanyAndOtherList();
//            if (CollectionUtil.isNotEmpty(companyAndOtherList)) {
//                companyAndOtherList.stream().filter(
//                        param -> Objects.equals(userCompanyId, param.getCompanyId())
//                ).forEach(a -> {
//                    if (CollectionUtil.isNotEmpty(a.getMaterialSpecies())){
//                        innerQuery.must(QueryBuilders.termsQuery("items.speciesId", a.getMaterialSpecies()));
//                    }
////                    if (CollectionUtil.isNotEmpty(a.getRoute())){
////                        innerQuery.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getTrackRouteId),a.getRoute()));
////                    }
//                });
//            }
            if(ObjectUtil.isNotNull(companyId) || ObjectUtil.isNotNull(carrierId)){
                authBoolQuery.should(innerQuery);
            }
        }

        //公司物料线路权限配置 （配置其他公司限制）
        List<OrderPageQO.EsOtherQueryParam> companyAndOtherList = qo.getCompanyAndOtherList();
        if (CollectionUtil.isNotEmpty(companyAndOtherList) && CollectionUtil.isNotEmpty(companyList) && !companyList.contains("-1")) {
            companyAndOtherList.stream().filter(
                    param -> !Objects.equals(userCompanyId, param.getCompanyId())
            ).forEach(a -> {
                BoolQueryBuilder innerQuery = QueryBuilders.boolQuery()
                        .must(QueryBuilders.termQuery(FieldUtils.val(BusinessIndex::getCompanyId), a.getCompanyId()));
                innerQuery.should(QueryBuilders.termQuery("relation.apCompanyId", a.getCompanyId()));
//                if (CollectionUtil.isNotEmpty(a.getMaterialSpecies())){
//                    innerQuery.must(QueryBuilders.termsQuery("items.speciesId", a.getMaterialSpecies()));
//                }
//                if (CollectionUtil.isNotEmpty(a.getRoute())){
//                    innerQuery.must(QueryBuilders.termsQuery(FieldUtils.val(BusinessIndex::getTrackRouteId),a.getRoute()));
//                }
                authBoolQuery.should(innerQuery);
            });
        }
        mainBoolQueryBuilder.must(authBoolQuery);
    }
}
