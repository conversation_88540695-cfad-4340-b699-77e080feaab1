package com.wzc.be.es.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/8/24 22:21
 */
@Getter
@AllArgsConstructor
public enum TocPayStatusEnums {

    /**
     * toc支付状态
     */
    pay_not(0, "未支付"),
    pay_on(1, "支付中"),
    pay_already(2, "已支付"),
    pay_over(3, "支付完成"),
    pay_error(4, "支付异常")
    ;

    private final Integer code;

    private final String name;


}
