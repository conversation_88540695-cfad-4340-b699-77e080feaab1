package com.wzc.be.es.adapter.job;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.wzc.be.es.adapter.job.bo.EsToKafkaBO;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.properties.ColdDataOfflineProperties;
import com.wzc.be.es.core.kafka.KafkaProducter;
import com.wzc.be.es.core.order.index.OrderIndex;
import com.wzc.be.es.core.transport.index.TransportIndex;
import com.wzc.be.es.core.warn.index.CscWarnExceptionIndex;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.search.*;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * ES冷数据下线Job
 * 按照指定顺序查询1年前的数据，先发送到Kafka再删除，确保数据一致性
 *
 * <AUTHOR> Assistant
 * @since 2025/02/18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EsColdDataOfflineJob {

    private final KafkaProducter kafkaProducter;
    private final RestHighLevelClient restHighLevelClient;
    private final ColdDataOfflineProperties coldDataProperties;
    private final EsIndexProperties esIndexProperties;

    /**
     * 冷数据下线主任务
     * 按照指定顺序处理各个索引的1年前数据
     */
    @XxlJob("esColdDataOffline")
    public ReturnT<String> esColdDataOffline() {
        if (!coldDataProperties.getEnabled()) {
            log.info("冷数据下线功能已禁用，跳过执行");
            return ReturnT.SUCCESS;
        }

        log.info("开始执行ES冷数据下线任务");

        // 计算保留时间阈值
        String retentionThreshold = LocalDateTime.now().minusMonths(coldDataProperties.getRetentionMonths())
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        log.info("冷数据下线时间阈值: {} (保留{}个月)", retentionThreshold, coldDataProperties.getRetentionMonths());

        // 按照指定顺序处理索引
        List<IndexConfig> indexConfigs = Arrays.asList(
            new IndexConfig(esIndexProperties.getOrderIndex(), FieldUtils.val(OrderIndex::getCreateTime), "订单索引"),
            new IndexConfig(esIndexProperties.getTransportIndex(), FieldUtils.val(TransportIndex::getCreateTime), "运输计划索引"),
            new IndexConfig(esIndexProperties.getGroupTransportIndex(), FieldUtils.val(TransportIndex::getCreateTime), "分组运输索引"),
            new IndexConfig(esIndexProperties.getSubWaybillIndex(), FieldUtils.val(SubWaybillIndex::getCreateTime), "子运单索引"),
            new IndexConfig(esIndexProperties.getCscWarnExceptionIndex(), FieldUtils.val(CscWarnExceptionIndex::getUpdateTime), "异常预警索引")
        );

        int totalProcessed = 0;
        int totalDeleted = 0;

        for (IndexConfig config : indexConfigs) {
            try {
                log.info("开始处理索引: {} ({})", config.indexName, config.description);

                ProcessResult result = processColdDataForIndex(config, retentionThreshold);
                totalProcessed += result.processedCount;
                totalDeleted += result.deletedCount;

                log.info("索引 {} 处理完成，处理数据: {} 条，删除数据: {} 条",
                    config.indexName, result.processedCount, result.deletedCount);

                // 索引间间隔，避免对ES造成过大压力
                Thread.sleep(coldDataProperties.getIndexIntervalMs());

            } catch (Exception e) {
                log.error("处理索引 {} 时发生异常", config.indexName, e);
                // 继续处理下一个索引，不中断整个流程
            }
        }

        log.info("ES冷数据下线任务完成，总计处理: {} 条，总计删除: {} 条", totalProcessed, totalDeleted);
        return ReturnT.SUCCESS;
    }

    /**
     * 处理单个索引的冷数据
     */
    private ProcessResult processColdDataForIndex(IndexConfig config, String retentionThreshold) throws Exception {
        // 构建查询条件
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.sort(SortBuilders.fieldSort(config.timeField).order(SortOrder.ASC));

        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.rangeQuery(config.timeField).lt(retentionThreshold));

        // 为订单索引添加业务状态检查
        if (esIndexProperties.getOrderIndex().equals(config.indexName)) {
            addOrderBusinessStatusFilter(queryBuilder);
        }

        // 为子运单索引添加业务状态检查
        if (esIndexProperties.getSubWaybillIndex().equals(config.indexName)) {
            addSubWaybillBusinessStatusFilter(queryBuilder);
        }

        searchSourceBuilder.query(queryBuilder);
        searchSourceBuilder.timeout(new TimeValue(coldDataProperties.getQueryTimeoutSeconds(), TimeUnit.SECONDS));
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.size(coldDataProperties.getPageSize());

        return searchAndProcessRecursively(searchSourceBuilder, config);
    }

    /**
     * 为订单索引添加业务状态过滤条件
     * 订单必须已经签收，且如果有结算，需要结算完成才能被删除
     */
    private void addOrderBusinessStatusFilter(BoolQueryBuilder queryBuilder) {
        // 订单状态：已完成
        queryBuilder.must(QueryBuilders.termQuery("status", coldDataProperties.getOrderCompletedStatus()));

        // 如果订单有结算信息，则必须结算完成
        // 使用bool查询：要么没有结算信息，要么结算状态为已完成
        BoolQueryBuilder settlementQuery = QueryBuilders.boolQuery();

        // 情况1：没有结算信息（结算状态为空或0）
        BoolQueryBuilder noSettlementQuery = QueryBuilders.boolQuery();
        noSettlementQuery.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("settlementStatus")));
        noSettlementQuery.should(QueryBuilders.termQuery("settlementStatus", 0));

        // 情况2：有结算且已完成（已支付）
        BoolQueryBuilder completedSettlementQuery = QueryBuilders.boolQuery();
        completedSettlementQuery.must(QueryBuilders.termQuery("settlementStatus", coldDataProperties.getOrderSettlementCompletedStatus()));

        settlementQuery.should(noSettlementQuery);
        settlementQuery.should(completedSettlementQuery);

        queryBuilder.must(settlementQuery);

        log.debug("为订单索引添加业务状态过滤：订单状态={}，结算状态=无结算或{}",
            coldDataProperties.getOrderCompletedStatus(), coldDataProperties.getOrderSettlementCompletedStatus());
    }

    /**
     * 为子运单索引添加业务状态过滤条件
     * 子运单必须已经签收完成，且结算状态为已完成才能被删除
     */
    private void addSubWaybillBusinessStatusFilter(BoolQueryBuilder queryBuilder) {
        // 子运单状态：已完成
        queryBuilder.must(QueryBuilders.termQuery("subWaybillStatus", coldDataProperties.getSubWaybillCompletedStatus()));

        // 必须有货主签收时间（表示已签收）
        queryBuilder.must(QueryBuilders.existsQuery("ownerSignTime"));

        // 结算状态检查：对上和对下结算都必须完成
        Integer settlementCompletedStatus = coldDataProperties.getSubWaybillSettlementCompletedStatus();

        // 对上结算状态：已支付或无需结算
        BoolQueryBuilder upSettlementQuery = QueryBuilders.boolQuery();
        upSettlementQuery.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("completeStatus")));
        upSettlementQuery.should(QueryBuilders.termQuery("completeStatus", settlementCompletedStatus));

        // 对下结算状态：已支付或无需结算
        BoolQueryBuilder downSettlementQuery = QueryBuilders.boolQuery();
        downSettlementQuery.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("completeDownStatus")));
        downSettlementQuery.should(QueryBuilders.termQuery("completeDownStatus", settlementCompletedStatus));

        queryBuilder.must(upSettlementQuery);
        queryBuilder.must(downSettlementQuery);

        log.debug("为子运单索引添加业务状态过滤：子运单状态={}，已签收，结算状态={}",
            coldDataProperties.getSubWaybillCompletedStatus(), settlementCompletedStatus);
    }

    /**
     * 递归搜索并处理数据
     */
    private ProcessResult searchAndProcessRecursively(SearchSourceBuilder sourceBuilder, IndexConfig config) throws Exception {
        SearchRequest searchRequest = new SearchRequest(config.indexName);
        searchRequest.source(sourceBuilder);
        searchRequest.scroll(TimeValue.timeValueMinutes(coldDataProperties.getScrollTimeoutMinutes()));

        int processedCount = 0;
        int deletedCount = 0;
        List<String> idsToDelete = new ArrayList<>();

        try {
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            String scrollId = searchResponse.getScrollId();
            SearchHit[] hits = searchResponse.getHits().getHits();

            // 处理第一批数据
            ProcessBatchResult batchResult = processBatch(hits, config, idsToDelete);
            processedCount += batchResult.processedCount;
            deletedCount += batchResult.deletedCount;

            // 处理后续批次
            while (hits.length > 0) {
                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                scrollRequest.scroll(TimeValue.timeValueMinutes(coldDataProperties.getScrollTimeoutMinutes()));

                Thread.sleep(coldDataProperties.getBatchIntervalMs()); // 避免对ES造成过大压力
                
                searchResponse = restHighLevelClient.scroll(scrollRequest, RequestOptions.DEFAULT);
                scrollId = searchResponse.getScrollId();
                hits = searchResponse.getHits().getHits();

                if (hits.length > 0) {
                    batchResult = processBatch(hits, config, idsToDelete);
                    processedCount += batchResult.processedCount;
                    deletedCount += batchResult.deletedCount;
                }
            }

            // 处理剩余的删除ID
            if (!idsToDelete.isEmpty()) {
                deletedCount += bulkDeleteDocuments(config.indexName, idsToDelete);
            }

            // 清理scroll
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);
            restHighLevelClient.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);

        } catch (Exception e) {
            log.error("处理索引 {} 的冷数据时发生异常", config.indexName, e);
            throw e;
        }

        return new ProcessResult(processedCount, deletedCount);
    }

    /**
     * 处理单批数据
     */
    private ProcessBatchResult processBatch(SearchHit[] hits, IndexConfig config, List<String> idsToDelete) {
        List<Map<String, Object>> dataList = new ArrayList<>();
        
        for (SearchHit hit : hits) {
            Map<String, Object> sourceAsMap = hit.getSourceAsMap();
            sourceAsMap.put("id", hit.getId());
            sourceAsMap.put("indexName", config.indexName);
            sourceAsMap.put("operation", "OFFLINE"); // 标记为下线操作
            sourceAsMap.put("offlineTime", DateUtil.now()); // 下线时间
            
            dataList.add(sourceAsMap);
            idsToDelete.add(hit.getId());
        }
        int processedCount = 0;
        int deletedCount = 0;
        if (!dataList.isEmpty()) {
            // 先发送到Kafka
            sendDataToKafka(dataList);
            processedCount = dataList.size();
            // 当累积到批量删除大小时，执行删除
            if (idsToDelete.size() >= coldDataProperties.getDeleteBatchSize()) {
                deletedCount = bulkDeleteDocuments(config.indexName, new ArrayList<>(idsToDelete));
                idsToDelete.clear();
            }
        }

        return new ProcessBatchResult(processedCount, deletedCount);
    }

    /**
     * 发送数据到Kafka
     */
    private void sendDataToKafka(List<Map<String, Object>> dataList) {
        try {
            List<List<Map<String, Object>>> partitions = Lists.partition(dataList, 1000);
            
            for (List<Map<String, Object>> partition : partitions) {
                EsToKafkaBO esToKafkaBO = new EsToKafkaBO();
                esToKafkaBO.setJsonStr(JSONUtil.toJsonStr(partition));
                esToKafkaBO.setTopic(coldDataProperties.getEsColdDataOfflineTopic());

                kafkaProducter.sendEsDataToKafka(esToKafkaBO);
                log.debug("发送 {} 条冷数据到Kafka topic: {}", partition.size(), coldDataProperties.getEsColdDataOfflineTopic());
            }
        } catch (Exception e) {
            log.error("发送冷数据到Kafka失败", e);
            throw new RuntimeException("发送冷数据到Kafka失败", e);
        }
    }

    /**
     * 批量删除文档
     */
    private int bulkDeleteDocuments(String indexName, List<String> documentIds) {
        if (documentIds.isEmpty()) {
            return 0;
        }

        try {
            BulkRequest bulkRequest = new BulkRequest();
            
            for (String documentId : documentIds) {
                DeleteRequest deleteRequest = new DeleteRequest(indexName, documentId);
                bulkRequest.add(deleteRequest);
            }

            BulkResponse bulkResponse = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
            
            if (bulkResponse.hasFailures()) {
                log.warn("批量删除部分失败: {}", bulkResponse.buildFailureMessage());
            }
            
            int deletedCount = documentIds.size() - (bulkResponse.hasFailures() ? 
                (int) Arrays.stream(bulkResponse.getItems()).filter(item -> item.isFailed()).count() : 0);
            
            log.info("从索引 {} 批量删除 {} 条冷数据", indexName, deletedCount);
            return deletedCount;
            
        } catch (Exception e) {
            log.error("批量删除索引 {} 的文档失败", indexName, e);
            return 0;
        }
    }

    /**
     * 索引配置类
     */
    private static class IndexConfig {
        final String indexName;
        final String timeField;
        final String description;

        IndexConfig(String indexName, String timeField, String description) {
            this.indexName = indexName;
            this.timeField = timeField;
            this.description = description;
        }
    }

    /**
     * 处理结果类
     */
    private static class ProcessResult {
        final int processedCount;
        final int deletedCount;

        ProcessResult(int processedCount, int deletedCount) {
            this.processedCount = processedCount;
            this.deletedCount = deletedCount;
        }
    }

    /**
     * 批处理结果类
     */
    private static class ProcessBatchResult {
        final int processedCount;
        final int deletedCount;

        ProcessBatchResult(int processedCount, int deletedCount) {
            this.processedCount = processedCount;
            this.deletedCount = deletedCount;
        }
    }
}
