package com.wzc.be.es.core.transport.handler;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baidu.mapcloud.cloudnative.common.model.UserContext;
import com.google.common.collect.Lists;
import com.wzc.be.es.common.enums.ContractStatusEnums;
import com.wzc.be.es.common.enums.DeletedEnums;
import com.wzc.be.es.common.enums.TransportSearchTypeEnums;
import com.wzc.be.es.common.enums.TransportStatusEnums;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.common.properties.CommonProperties;
import com.wzc.be.es.common.properties.KafkaProperties;
import com.wzc.be.es.core.common.CommonRedisResultVO;
import com.wzc.be.es.core.order.index.OrderIndex;
import com.wzc.be.es.core.transport.index.TransportIndex;
import com.wzc.be.es.data.order.qo.OrderPageQO;
import com.wzc.be.es.data.transport.qo.TransportQueryPageQO;
import com.wzc.common.user.utils.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.wzc.be.es.common.enums.TransportStatusEnums.APPLY_END;
import static java.util.Objects.nonNull;

/**
 * 运输计划 原生es api接口 基本实现类 条件封装
 * <AUTHOR>
 */
@Slf4j
public class TransportBaseSearchHandler<Q,V> implements BaseSearchHandler<Q, V> {

    @Resource
    private CommonProperties commonProperties;

    /**
     * 条件封装返回
     * @param transportQueryPageQo transportQueryPageQo
     * @return
     */
    public BoolQueryBuilder getMainBoolQueryBuilder(TransportQueryPageQO transportQueryPageQo,List<CommonRedisResultVO> commonRedisResultVOList){
        BoolQueryBuilder mainBoolQueryBuilder = QueryBuilders.boolQuery();

        if (UserContext.isCarrier()){
            //承运商端 已删除的状态过滤掉
            mainBoolQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getStatus), 8));
            //是否有删除的订单号
            if (CollectionUtil.isNotEmpty(commonRedisResultVOList)){
                List<String> deleteNoList = commonRedisResultVOList.stream().filter(c -> ObjectUtil.equals(c.getDelFlag(), DeletedEnums.NO.getCode())).map(CommonRedisResultVO::getUnionNo).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(deleteNoList)) {
                    mainBoolQueryBuilder.mustNot(QueryBuilders.termsQuery(FieldUtils.val(TransportIndex::getTransportNo), deleteNoList));
                }
            }

            // 承运商APP，进行中的状态返回剩余量不为0的数据
            if(ObjectUtil.equals(TransportStatusEnums.RUNNING.getCode(), transportQueryPageQo.getStatus()) && UserUtils.isCarrierApp()){
                mainBoolQueryBuilder.must(QueryBuilders.existsQuery("items.itemWeight"));
                mainBoolQueryBuilder.must(QueryBuilders.existsQuery("items.deliveryWeight"));
                Script script = new Script(ScriptType.INLINE, "painless", "doc['items.itemWeight'].value - doc['items.deliveryWeight'].value > 0", Collections.emptyMap());
                mainBoolQueryBuilder.must(QueryBuilders.scriptQuery(script));
            }
        }
        if(UserContext.isShipper()){
            //货主端 已删除的状态过滤掉
            if (CollectionUtil.isNotEmpty(commonRedisResultVOList)){
                List<String> deleteNoList = commonRedisResultVOList.stream().filter(c -> ObjectUtil.equals(c.getDelFlag(), DeletedEnums.NO.getCode())).map(CommonRedisResultVO::getUnionNo).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(deleteNoList)) {
                    mainBoolQueryBuilder.mustNot(QueryBuilders.termsQuery(FieldUtils.val(TransportIndex::getTransportNo), deleteNoList));
                }
            }
        }
        // 运输计划编号不为空
        mainBoolQueryBuilder.must(QueryBuilders.existsQuery(FieldUtils.val(TransportIndex::getTransportNo)));
        //发货地址
        String deliveryAddress = transportQueryPageQo.getDeliveryAddress();
        if (StrUtil.isNotEmpty(deliveryAddress)){
            List<String> columns = Lists.newArrayList();
            columns.add("sender.address");
            columns.add("sender.addressName");
            columns.add("sender.fullAddress");
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,deliveryAddress,true,columns);
        }
        //收货人地址
        String receiptAddress = transportQueryPageQo.getReceiptAddress();
        if (StrUtil.isNotEmpty(receiptAddress)){
            List<String> columns = Lists.newArrayList();
            columns.add("receiver.address");
            columns.add("receiver.addressName");
            columns.add("receiver.fullAddress");
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,receiptAddress,true,columns);
        }
        //货物名称
        String itemNames = transportQueryPageQo.getItemNames();
        if (StrUtil.isNotEmpty(itemNames)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,itemNames,true,StrUtil.split("items.itemName",","));
        }
        //货主名称
        String ownerName = transportQueryPageQo.getOwnerName();
        if (StrUtil.isNotEmpty(ownerName)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,ownerName,true,StrUtil.split(FieldUtils.val(TransportIndex::getCompanyName),","));
        }
        //订单号集合
        List<String> orderNoList = transportQueryPageQo.getOrderNoList();
        if (CollectionUtil.isNotEmpty(orderNoList)){
            mainBoolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(TransportIndex::getOrderNo)+".keyword",orderNoList));
        }else {
            //订单号
            String orderNo = transportQueryPageQo.getOrderNo();
            if (StrUtil.isNotEmpty(orderNo)) {
                matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,orderNo,true,StrUtil.split(FieldUtils.val(TransportIndex::getOrderNo),","));
            }
        }
        //货主公司id
        List<Long> ownerIdSList = transportQueryPageQo.getOwnerIdSList();
        if (CollectionUtil.isNotEmpty(ownerIdSList)) {
            mainBoolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(TransportIndex::getCompanyId), ownerIdSList));
        }
        //运输计划编号
        String transportNo = transportQueryPageQo.getTransportNo();
        if (StrUtil.isNotEmpty(transportNo)) {
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,transportNo,true,StrUtil.split(FieldUtils.val(TransportIndex::getTransportNo)+".text",","));
        }
        //运输计划编号集合查询
        List<String> transportNoList = transportQueryPageQo.getTransportNoList();
        if (CollectionUtil.isNotEmpty(transportNoList)){
            mainBoolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(TransportIndex::getTransportNo),transportNoList));
        }
        //承运商
        String carrierName = transportQueryPageQo.getCarrierName();
        if (StrUtil.isNotEmpty(carrierName)){
            mainBoolQueryBuilder.must(QueryBuilders.existsQuery(FieldUtils.val(TransportIndex::getCarrierName)));
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,carrierName,true,StrUtil.split(FieldUtils.val(TransportIndex::getCarrierName),","));
        }

        //搜索类型  1, "全部"  2, "货物名称搜索"  3, "货主名称搜索" 4, "装货地搜索" 5, "收货地搜索" 6, "运输计划编号搜索"
        Integer searchType = transportQueryPageQo.getSearchType();
        TransportSearchTypeEnums transportSearchTypeEnums = EnumUtil.getBy(TransportSearchTypeEnums::getCode, searchType);
        //搜索关键字
        String keyword = transportQueryPageQo.getKeyword();
        if (StrUtil.isNotEmpty(keyword)) {
            //搜索字段名称
            List<String> columnNames = Lists.newArrayList();
            boolean canMatchPhraseQuery = true;
            if (ObjectUtil.isNotNull(transportSearchTypeEnums)) {
                switch (transportSearchTypeEnums) {
                    //查询全部
                    case ALL:
                        allSearch(keyword, mainBoolQueryBuilder);
                        break;
                    //货主名称搜索
                    case HZ:
                        columnNames.add(FieldUtils.val(TransportIndex::getCompanyName));
                        break;
                    //货物名称搜索
                    case GOODS_NAME:
                        columnNames.add("items.itemName");
                        break;
                    //发货地搜索
                    case SEND_ADDR:
                        columnNames.add("sender.address");
                        columnNames.add("sender.addressName");
                        columnNames.add("sender.fullAddress");
                        break;
                    //收货地搜索
                    case RECEIVE_ADDR:
                        columnNames.add("receiver.address");
                        columnNames.add("receiver.addressName");
                        columnNames.add("receiver.fullAddress");
                        break;
                    //运输计划编号搜索
                    case TRANSPORT_NO:
                        columnNames.add(FieldUtils.val(TransportIndex::getTransportNo));
                        canMatchPhraseQuery = false;
                        break;
                    //订单编号搜索
                    case ORDER_NO:
                        columnNames.add(FieldUtils.val(TransportIndex::getOrderNo));
                        break;
                    //三方订单编号搜索
                    case THIRD_NO:
                        columnNames.add(FieldUtils.val(TransportIndex::getThirdNo));
                        break;
                    //运输计划编号搜索
                    case CYS:
                        columnNames.add(FieldUtils.val(TransportIndex::getCarrierName));
                        break;
                    //合同编号搜索
                    case CONTRACT_NO:
                        columnNames.addAll(StrUtil.split("contractRelation.contractNo", ","));
                        break;
                    default:
                        break;
                }
            }
            if (CollectionUtil.isNotEmpty(columnNames)) {
                matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder, keyword, canMatchPhraseQuery, columnNames);
            }
        }

        //运输计划状态：1 待接单,2 已拒绝,3 进行中,4 已完成,5 暂停 6已取消
        List<Integer> statusList = transportQueryPageQo.getStatusList();
        if (CollectionUtil.isNotEmpty(statusList)){
            if (statusList.contains(APPLY_END.getCode())){
                BoolQueryBuilder statusQueryBuilder = QueryBuilders.boolQuery()
                        .should(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getEndStatus),TransportStatusEnums.RUNNING.getCode()));
                statusQueryBuilder.should(QueryBuilders.termsQuery(FieldUtils.val(TransportIndex::getStatus), statusList));
                mainBoolQueryBuilder.must(statusQueryBuilder);
            }else {
                mainBoolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(TransportIndex::getStatus),statusList));
            }
        }else {
            Integer status = transportQueryPageQo.getStatus();
            if (nonNull(status)) {
                //状态为7查询的为停运状态为已申请的运输计划
                if (Objects.equals(status, APPLY_END.getCode())){
                    mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getEndStatus), TransportStatusEnums.RUNNING.getCode()));
                }else {
                    mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getStatus), status));
                }
            }
        }

        //来源
        Integer sourceType = transportQueryPageQo.getSourceType();
        if (Objects.nonNull(sourceType)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getSourceType),sourceType));
        }

        //创建时间比较
        String startTime = transportQueryPageQo.getStartTime();
        String endTime = transportQueryPageQo.getEndTime();
        if (StrUtil.isNotEmpty(startTime)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TransportIndex::getCreateTime))
                    .gte(startTime));
        }
        if (StrUtil.isNotEmpty(endTime)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TransportIndex::getCreateTime))
                    .lte(endTime));
        }
        //有效期比较 比较索引中的endTime
        String validate = transportQueryPageQo.getValidate();
        if (StrUtil.isNotEmpty(validate)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TransportIndex::getEndTime))
                    .lte(validate));
        }

        //是否有需要过滤的单号
        if (CollectionUtil.isNotEmpty(transportQueryPageQo.getNoInTransportNoList())){
            mainBoolQueryBuilder.mustNot(QueryBuilders.termsQuery(FieldUtils.val(TransportIndex::getTransportNo), transportQueryPageQo.getNoInTransportNoList()));
        }

        //过滤转网数据
        if (ObjectUtil.isNotNull(transportQueryPageQo.getNetCargo())){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getNetCargo), transportQueryPageQo.getNetCargo()));
        }
        //是否过滤二，三期数据
        Integer isSync = transportQueryPageQo.getIsSync();
        if (ObjectUtil.isNotNull(isSync)){
            BoolQueryBuilder isSyncQueryBuilder = QueryBuilders.boolQuery();
            BoolQueryBuilder normalTransportBoolQueryBuilder = QueryBuilders.boolQuery();
            normalTransportBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getAssignType), 2));
            normalTransportBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getIsSync), isSync));
            BoolQueryBuilder shareTransportBoolQueryBuilder = QueryBuilders.boolQuery();
            shareTransportBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getAssignType), 1));
            isSyncQueryBuilder.should(normalTransportBoolQueryBuilder);
            isSyncQueryBuilder.should(shareTransportBoolQueryBuilder);
            mainBoolQueryBuilder.must(isSyncQueryBuilder);
        }

        //货源单号
        if (ObjectUtil.isNotNull(transportQueryPageQo.getGoodsNo())){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,transportQueryPageQo.getGoodsNo(),true,StrUtil.split(FieldUtils.val(TransportIndex::getGoodsNo),","));
        }

        //运输类型
        if (ObjectUtil.isNotNull(transportQueryPageQo.getTransportType())){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getTransportType), transportQueryPageQo.getTransportType()));
        }

        // 合同签署要求
        Integer contractRequire = transportQueryPageQo.getContractRequire();
        if (ObjectUtil.isNotNull(contractRequire)) {
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getContractRequire), contractRequire));
        }
        // 合同状态
        Integer contractStatus = transportQueryPageQo.getContractStatus();
        if (ObjectUtil.isNotNull(contractStatus)) {
            if ( ContractStatusEnums.NO.getCode().equals(contractStatus)) {
                mainBoolQueryBuilder.must(QueryBuilders.boolQuery().mustNot(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getContractStatus), ContractStatusEnums.YES.getCode())));
            } else {
                mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getContractStatus), contractStatus));
            }
        }
        // 合同签约方式
        Integer contractSignMethod = transportQueryPageQo.getContractSignMethod();
        if (ObjectUtil.isNotNull(contractSignMethod)) {
            mainBoolQueryBuilder.must(QueryBuilders.termQuery("contractRelation.contractSignMethod", contractSignMethod));
        }
        // 合同编号
        String contractNo = transportQueryPageQo.getContractNo();
        if (StrUtil.isNotEmpty(contractNo)) {
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder, contractNo, true, StrUtil.split("contractRelation.contractNo", ","));
        }
        // 是否存在合同
        Boolean hasContract = transportQueryPageQo.getHasContract();
        if (BooleanUtil.isTrue(hasContract)) {
            mainBoolQueryBuilder.must(QueryBuilders.existsQuery("contractRelation.contractNo"));
        }

        // 接单时间
        String acceptTime = transportQueryPageQo.getAcceptTime();
        if (StrUtil.isNotBlank(acceptTime)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TransportIndex::getTransAcceptTime))
                    .gte(acceptTime));
        }
        // 订单类型
        Integer orderType = transportQueryPageQo.getQueryOrderType();
        if (ObjectUtil.isNotNull(orderType)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getOrderType), orderType));
        }
        // 业务类型
        Integer businessType = transportQueryPageQo.getBusinessType();
        if (ObjectUtil.isNotNull(businessType)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getBusinessType), businessType));
        }

        //是否开启校验载重设备功能 1是，2否，默认否
        Integer loadDeviceCheck = transportQueryPageQo.getLoadDeviceCheck();
        if(ObjectUtil.isNotNull(loadDeviceCheck)){
            if(2==loadDeviceCheck){
                //兼容旧数据
                BoolQueryBuilder loadDeviceCheckBoolQuery = QueryBuilders.boolQuery();
                loadDeviceCheckBoolQuery.should(QueryBuilders.boolQuery()
                        .mustNot(QueryBuilders.existsQuery(FieldUtils.val(TransportIndex::getLoadDeviceCheck))));
                loadDeviceCheckBoolQuery.should(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getLoadDeviceCheck), loadDeviceCheck));
                mainBoolQueryBuilder.must(loadDeviceCheckBoolQuery);
            }else{
                mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getLoadDeviceCheck), loadDeviceCheck));
            }
        }

        //数据权限过滤
        authCheck(mainBoolQueryBuilder,transportQueryPageQo,transportQueryPageQo.getCompanyList());

        if (StringUtils.isNotBlank(transportQueryPageQo.getAllWord())) {
            BoolQueryBuilder innerBoolQueryBuilder = QueryBuilders.boolQuery();
            innerBoolQueryBuilder.should(getWildcardQuery(FieldUtils.val(TransportIndex::getTransportNo), keyword));
            innerBoolQueryBuilder.should(getWildcardQuery(FieldUtils.val(TransportIndex::getCarrierName), keyword));
            mainBoolQueryBuilder.must(innerBoolQueryBuilder);
        }

        return mainBoolQueryBuilder;
    }

    private void authCheck(BoolQueryBuilder mainBoolQueryBuilder,TransportQueryPageQO transportQueryPageQo,List<String> companyList){
        //承运商id
        Long carrierId = transportQueryPageQo.getCarrierId();
        String userCompanyId = String.valueOf(UserContext.companyId().get());
        BoolQueryBuilder companyIdShouldBoolQuery = QueryBuilders.boolQuery();
        // 平台 固定公司 超级管理员
        if(UserContext.isOperation() && String.valueOf(commonProperties.getSuperAdminCompanyId()).equals(userCompanyId) && !transportQueryPageQo.getIsFromJob()){
            if(UserContext.isPlatformAdmin()){
                //平台超管，无需鉴权
                log.info("运输计划列表查询-当前登陆角色为平台超级管理员无需数据权限");
            }else{
                log.info("运输计划列表查询-当前登陆角色为平台员工");
                //平台员工登录，需要校验平台员工公司数据权限
                authQueryCondition(mainBoolQueryBuilder,transportQueryPageQo,companyList);
            }
            return;
        }else if(UserContext.isOperation()){
            // 托运公司 正常登陆公司
            log.info("运输计划列表查询-当前登陆角色为托运公司");
            companyIdShouldBoolQuery.should(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getCompanyId), userCompanyId));
            companyIdShouldBoolQuery.should(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getShipperCompanyId), userCompanyId));
            mainBoolQueryBuilder.must(companyIdShouldBoolQuery);
        }else if(UserUtils.isIdentityShipper()){
            // 货主登录
            log.info("运输计划列表查询-当前登陆角色为货主公司");
            companyIdShouldBoolQuery.should(QueryBuilders.termsQuery(FieldUtils.val(TransportIndex::getCompanyId), companyList));
            mainBoolQueryBuilder.must(companyIdShouldBoolQuery);
        }else if(UserUtils.isIdentityCarrier()){
            //承运商登录
            log.info("运输计划列表查询-当前登陆角色为承运商公司");
            mainBoolQueryBuilder.must(QueryBuilders.existsQuery(FieldUtils.val(TransportIndex::getCarrierId)));
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getCarrierId),carrierId));
        }
        if(!transportQueryPageQo.getIsFromJob()){
            authQueryCondition(mainBoolQueryBuilder,transportQueryPageQo,companyList);
        }
    }

    /**
     * 数据权限条件
     * @param transportQueryPageQo
     * @param mainBoolQueryBuilder
     * @param companyList
     */
    private void authQueryCondition(BoolQueryBuilder mainBoolQueryBuilder,TransportQueryPageQO transportQueryPageQo,List<String> companyList){
        //承运商id
        Long carrierId = transportQueryPageQo.getCarrierId();
        // 货主公司id
        Long companyId = transportQueryPageQo.getCompanyId();

        //数据权限 运输计划来源集合
        List<String> sourceTypeList = transportQueryPageQo.getSourceTypeList();
        if (CollectionUtil.isNotEmpty(sourceTypeList)){
            mainBoolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(TransportIndex::getSourceType),sourceTypeList));
        }

        //数据权限 订单类型集合
        List<String> orderTypeList = transportQueryPageQo.getOrderTypeList();
        if (CollectionUtil.isNotEmpty(orderTypeList)){
            BoolQueryBuilder orderTypeQueryBuilder = QueryBuilders.boolQuery()
                    .should(QueryBuilders.boolQuery()
                            .mustNot(QueryBuilders.existsQuery(FieldUtils.val(TransportIndex::getOrderType))));
            orderTypeQueryBuilder.should(QueryBuilders.termsQuery(FieldUtils.val(TransportIndex::getOrderType), orderTypeList));
            mainBoolQueryBuilder.must(orderTypeQueryBuilder);
        }

        // 数据权限配置的公司id集合 （配置当前公司限制）
        BoolQueryBuilder authBoolQuery = QueryBuilders.boolQuery();
        String userCompanyId = String.valueOf(UserContext.companyId().get());
        if (CollectionUtil.isNotEmpty(companyList) && UserContext.companyId().isPresent()
                && companyList.contains(String.valueOf(UserContext.companyId().get())) && !String.valueOf(commonProperties.getSuperAdminCompanyId()).equals(userCompanyId)
                && CollectionUtil.isNotEmpty(companyList) && !companyList.contains("-1")) {
            BoolQueryBuilder innerQuery = QueryBuilders.boolQuery();
            if (ObjectUtil.isNotNull(companyId)){
                BoolQueryBuilder companyIdShouldQuery = QueryBuilders.boolQuery();
                companyIdShouldQuery.should(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getCompanyId), companyId));
                innerQuery.must(companyIdShouldQuery);
            }
            if (ObjectUtil.isNotNull(carrierId)){
                innerQuery.must(
                        QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getCarrierId), carrierId)
                );
            }
            //是否需要关联用户数据过滤 承运商不需要这个条件
            if (!UserContext.isCarrier()){
                List<String> userIdList = transportQueryPageQo.getUserIdList();
                if (CollectionUtil.isNotEmpty(userIdList)) {
                    innerQuery.must(QueryBuilders.termsQuery(FieldUtils.val(TransportIndex::getCreateId), userIdList));
                }
                //数据权限部门id过滤
                List<Long> depIdList = transportQueryPageQo.getDepIdList();
                if (CollectionUtil.isNotEmpty(depIdList)){
                    innerQuery.must(QueryBuilders.termsQuery(FieldUtils.val(TransportIndex::getOwnerDepartment),depIdList));
                }
            }
            // 过滤当前公司的物料信息
            List<OrderPageQO.EsOtherQueryParam> companyAndOtherList = transportQueryPageQo.getCompanyAndOtherList();
            if (CollectionUtil.isNotEmpty(companyAndOtherList)) {
                companyAndOtherList.stream().filter(
                        param -> Objects.equals(userCompanyId, param.getCompanyId())
                ).forEach(a -> {
                    if (CollectionUtil.isNotEmpty(a.getMaterialSpecies())){
                        innerQuery.must(QueryBuilders.termsQuery("items.speciesId", a.getMaterialSpecies()));
                    }
                    if (CollectionUtil.isNotEmpty(a.getRoute())){
                        innerQuery.must(QueryBuilders.termsQuery(FieldUtils.val(TransportIndex::getTrackRouteId),a.getRoute()));
                    }
                });
            }
            authBoolQuery.should(innerQuery);
        }

        //公司物料线路权限配置 （配置其他公司限制）
        List<OrderPageQO.EsOtherQueryParam> companyAndOtherList = transportQueryPageQo.getCompanyAndOtherList();
        if (CollectionUtil.isNotEmpty(companyAndOtherList) && CollectionUtil.isNotEmpty(companyList) && !companyList.contains("-1")) {
            companyAndOtherList.stream().filter(
                    param -> !Objects.equals(userCompanyId, param.getCompanyId())
            ).forEach(a -> {
                BoolQueryBuilder innerQuery = QueryBuilders.boolQuery()
                        .must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getCompanyId), a.getCompanyId()));
                if (CollectionUtil.isNotEmpty(a.getMaterialSpecies())){
                    innerQuery.must(QueryBuilders.termsQuery("items.speciesId", a.getMaterialSpecies()));
                }
                if (CollectionUtil.isNotEmpty(a.getRoute())){
                    innerQuery.must(QueryBuilders.termsQuery(FieldUtils.val(TransportIndex::getTrackRouteId),a.getRoute()));
                }
                authBoolQuery.should(innerQuery);
            });
        }
        mainBoolQueryBuilder.must(authBoolQuery);
    }

    /**
     * 全部类型的搜索 货主 收发地 运输计划编号 货物名称 都进行模糊匹配
     *
     * @param keyword 搜索关键字
     */
    private void allSearch(String keyword, BoolQueryBuilder mainBoolQueryBuilder) {
        BoolQueryBuilder innerBoolQueryBuilder = QueryBuilders.boolQuery();
        if (keyword.length() > 1){
            innerBoolQueryBuilder.should(getMatchPhraseQuery("items.itemName", keyword));
            innerBoolQueryBuilder.should(getMatchPhraseQuery("companyName", keyword));
            innerBoolQueryBuilder.should(getMatchPhraseQuery("sender.address", keyword));
            innerBoolQueryBuilder.should(getMatchPhraseQuery("sender.addressName", keyword));
            innerBoolQueryBuilder.should(getMatchPhraseQuery("sender.fullAddress", keyword));
            innerBoolQueryBuilder.should(getMatchPhraseQuery("receiver.address", keyword));
            innerBoolQueryBuilder.should(getMatchPhraseQuery("receiver.addressName", keyword));
            innerBoolQueryBuilder.should(getMatchPhraseQuery("receiver.fullAddress", keyword));
            innerBoolQueryBuilder.should(getMatchPhraseQuery("orderNo", keyword));
            innerBoolQueryBuilder.should(getMatchPhraseQuery("thirdNo", keyword));
            innerBoolQueryBuilder.should(getMatchPhraseQuery("carrierName", keyword));
        }else {
            innerBoolQueryBuilder.should(getWildcardQuery("items.itemName", keyword));
            innerBoolQueryBuilder.should(getWildcardQuery("companyName", keyword));
            innerBoolQueryBuilder.should(getWildcardQuery("sender.address", keyword));
            innerBoolQueryBuilder.should(getWildcardQuery("sender.addressName", keyword));
            innerBoolQueryBuilder.should(getWildcardQuery("sender.fullAddress", keyword));
            innerBoolQueryBuilder.should(getWildcardQuery("receiver.address", keyword));
            innerBoolQueryBuilder.should(getWildcardQuery("receiver.addressName", keyword));
            innerBoolQueryBuilder.should(getWildcardQuery("receiver.fullAddress", keyword));
            innerBoolQueryBuilder.should(getMatchPhraseQuery("orderNo", keyword));
            innerBoolQueryBuilder.should(getMatchPhraseQuery("thirdNo", keyword));
            innerBoolQueryBuilder.should(getMatchPhraseQuery("carrierName", keyword));
        }
        innerBoolQueryBuilder.should(getWildcardQuery(FieldUtils.val(TransportIndex::getTransportNo), keyword));
        mainBoolQueryBuilder.must(innerBoolQueryBuilder);
    }
}
