package com.wzc.be.es.adapter.client.waybill.feign;


import com.wzc.be.data.api.es.DwdOmsSubWaybillIndexApi;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * @ClassName WaybillDorisSearchFeign
 * <AUTHOR>
 * @Date 2024/2/1 16:50
 * @Description
 * @Version 1.0
 */
@FeignClient(name = "WaybillDorisSearchFeign", url = "${base3.data.url}")
public interface WaybillDorisSearchFeign extends DwdOmsSubWaybillIndexApi {
}
