package com.wzc.be.es.core.business.service;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.common.service.EsBaseSearchService;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.data.business.qo.BmsEsBusinessQueryPageQO;
import com.wzc.be.es.data.business.vo.BmsBusinessEsVO;
import com.wzc.be.es.data.business.vo.BmsEsAuditStatusCountVO;
import com.wzc.be.es.data.business.vo.BmsEsRcStatusCountVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class EsBmsBusinessSearchService extends EsBaseSearchService {

    /**
     * 对账单分页查询
     *
     * @param qo
     * @return
     */
    public RestResponse<EsPageVO<BmsBusinessEsVO>> page(BmsEsBusinessQueryPageQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.BUSINESS_RC_YD_PAGE.getType());
        return searchHandler.search(qo);
    }

    /**
     * 对账单对账状态统计
     *
     * @param qo
     * @return
     */
    public RestResponse<BmsEsRcStatusCountVO> rcStatusCount(BmsEsBusinessQueryPageQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.RECONCILIATION_STATUS_COUNT.getType());
        return searchHandler.search(qo);
    }

    /**
     * 对账单审核状态统计
     *
     * @param qo
     * @return
     */
    public RestResponse<BmsEsAuditStatusCountVO> rcAuditStatusCount(BmsEsBusinessQueryPageQO qo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.RC_AUDIT_STATUS_COUNT.getType());
        return searchHandler.search(qo);
    }
}
