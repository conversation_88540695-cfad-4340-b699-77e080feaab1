package com.wzc.be.es.core.business.handler;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.wzc.be.es.common.enums.ComplianceEnums;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.core.business.index.TocBusinessIndex;
import com.wzc.be.es.core.common.CommonRedisResultVO;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.data.business.qo.TocBusinessEsPageQO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;

import java.util.List;

/**
 * 网货对账单 原生es api接口 基本实现类 条件封装
 * <AUTHOR>
 */
@Slf4j
public class TocBusinessBaseSearchHandler<Q,V> implements BaseSearchHandler<Q, V> {

    /**
     * 条件封装返回
     * @param queryPageQo
     * @return
     */
    public BoolQueryBuilder getMainBoolQueryBuilder(TocBusinessEsPageQO queryPageQo, List<CommonRedisResultVO> commonRedisResultVOList){
        BoolQueryBuilder mainBoolQueryBuilder = QueryBuilders.boolQuery();
        //订单号
        String orderNo = queryPageQo.getOrderNo();
        if (StrUtil.isNotEmpty(orderNo)) {
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,orderNo,true,List.of(FieldUtils.val(TocBusinessIndex::getOrderNo)));
        }
        //运输计划编号
        String transportNo = queryPageQo.getTransportNo();
        if (StrUtil.isNotEmpty(transportNo)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,transportNo,true,List.of(FieldUtils.val(TocBusinessIndex::getTransportNo)));
        }
        //运单号
        String dispatchNo = queryPageQo.getDispatchNo();
        if (StrUtil.isNotEmpty(dispatchNo)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,dispatchNo,true,List.of(FieldUtils.val(TocBusinessIndex::getDispatchNo)));
        }
        //子运单号
        String subWaybillNo = queryPageQo.getSubWaybillNo();
        if (StrUtil.isNotEmpty(subWaybillNo)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,subWaybillNo,true,List.of(FieldUtils.val(TocBusinessIndex::getSubWaybillNo)));
        }
        //发布方id
        Long apCompanyId = queryPageQo.getApCompanyId();
        if (ObjectUtil.isNotNull(apCompanyId)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getApCompanyId),apCompanyId));
        }
        //发布方类型
        Integer apCompanyType = queryPageQo.getApCompanyType();
        if (ObjectUtil.isNotNull(apCompanyType)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getApCompanyType),apCompanyType));
        }
        //对账状态
        Integer rcStatus = queryPageQo.getRcStatus();
        if (ObjectUtil.isNotNull(rcStatus)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getRcStatus),rcStatus));
        }
        //平台登录
        Long shipperCompanyId = queryPageQo.getShipperCompanyId();
        if (ObjectUtil.isNotNull(shipperCompanyId)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getShipperCompanyId),shipperCompanyId));
        }
//        //平台审核状态
//        Integer shipperAuditStatus = queryPageQo.getShipperAuditStatus();
//        if (ObjectUtil.isNotNull(shipperAuditStatus)){
//            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getShipperAuditStatus),shipperAuditStatus));
//        }
        //派车时间比较
        String dispatchTimeStart = queryPageQo.getDispatchTimeStart();
        String dispatchTimeEnd = queryPageQo.getDispatchTimeEnd();
        if (StrUtil.isNotEmpty(dispatchTimeStart)) {
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getDispatchTime))
                    .gte(dispatchTimeStart));
        }
        if (StrUtil.isNotEmpty(dispatchTimeEnd)) {
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getDispatchTime))
                    .lte(dispatchTimeEnd));
        }
        //司机运输时间
        String transportTimeStart = queryPageQo.getTransportTimeStart();
        String transportTimeEnd = queryPageQo.getTransportTimeEnd();
        if (StrUtil.isNotBlank(transportTimeStart)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getTransportTime))
                    .gte(transportTimeStart));
        }
        if (StrUtil.isNotBlank(transportTimeEnd)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getTransportTime))
                    .lte(transportTimeEnd));
        }
        //司机卸货时间
        String unloadTimeStart = queryPageQo.getUnloadTimeStart();
        String unloadTimeEnd = queryPageQo.getUnloadTimeEnd();
        if (StrUtil.isNotBlank(unloadTimeStart)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getUnloadTime))
                    .gte(unloadTimeStart));
        }
        if (StrUtil.isNotBlank(unloadTimeEnd)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getUnloadTime))
                    .lte(unloadTimeEnd));
        }
        //车牌号
        String carNo = queryPageQo.getCarNo();
        if (StrUtil.isNotEmpty(carNo)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,carNo,true,List.of(FieldUtils.val(TocBusinessIndex::getCarNo)));
        }
        //发货单位
        String fmCustomerName = queryPageQo.getFmCustomerName();
        if (StrUtil.isNotEmpty(fmCustomerName)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,fmCustomerName,true,List.of(FieldUtils.val(TocBusinessIndex::getFmCustomerName)));
        }
        //收货单位
        String toCustomerName = queryPageQo.getToCustomerName();
        if (StrUtil.isNotEmpty(toCustomerName)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,toCustomerName,true,List.of(FieldUtils.val(TocBusinessIndex::getToCustomerName)));
        }
        //服务方姓名
        String arCompanyName = queryPageQo.getArCompanyName();
        if (StrUtil.isNotBlank(arCompanyName)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,arCompanyName,true,List.of(FieldUtils.val(TocBusinessIndex::getArCompanyName)));
        }
        //发布方姓名
        String apCompanyName = queryPageQo.getApCompanyName();
        if (StrUtil.isNotBlank(apCompanyName)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,apCompanyName,true,List.of(FieldUtils.val(TocBusinessIndex::getApCompanyName)));
        }
        //货物名称
        String itemName = queryPageQo.getItemName();
        if (StrUtil.isNotBlank(itemName)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,itemName,true,StrUtil.split("items.itemName",","));
        }
        //支付方式
        Integer paymentMethod = queryPageQo.getPaymentMethod();
        if (ObjectUtil.isNotNull(paymentMethod)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getPaymentMethod),paymentMethod));
        }
        //发货地址
        String fmAddress = queryPageQo.getFmAddress();
        if (StrUtil.isNotBlank(fmAddress)){
            List<String> columns = Lists.newArrayList();
            columns.add(FieldUtils.val(TocBusinessIndex::getFmAddress));
            columns.add(FieldUtils.val(TocBusinessIndex::getFmAddressName));
            columns.add(FieldUtils.val(TocBusinessIndex::getFmFullAddress));
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,fmAddress,true,columns);
        }
        //收货地址
        String toAddress = queryPageQo.getToAddress();
        if (StrUtil.isNotBlank(toAddress)){
            List<String> columns = Lists.newArrayList();
            columns.add(FieldUtils.val(TocBusinessIndex::getToAddress));
            columns.add(FieldUtils.val(TocBusinessIndex::getToAddressName));
            columns.add(FieldUtils.val(TocBusinessIndex::getToFullAddress));
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,toAddress,true,columns);
        }
        //导出的时候是否有选中单号
        List<String> exportSubWaybillNoList = queryPageQo.getExportSubWaybillNoList();
        if (CollectionUtil.isNotEmpty(exportSubWaybillNoList)){
            mainBoolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(TocBusinessIndex::getSubWaybillNo),exportSubWaybillNoList));
        }
        //支付状态
        Integer payStatus = queryPageQo.getPayStatus();
        if (ObjectUtil.isNotNull(payStatus)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getPayStatus),payStatus));
        }
        //操作人
        String payOperatorName = queryPageQo.getPayOperatorName();
        if (StrUtil.isNotBlank(payOperatorName)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getPayOperatorName)+ ".keyword",payOperatorName));
        }
        return mainBoolQueryBuilder;
    }

    /**
     * 条件封装返回
     *
     * @Param [queryPageQo, commonRedisResultVOList]
     * @return org.elasticsearch.index.query.BoolQueryBuilder
     **/
    public BoolQueryBuilder getMainBoolQueryBuilderBefore(TocBusinessEsPageQO queryPageQo, List<CommonRedisResultVO> commonRedisResultVOList){
        BoolQueryBuilder mainBoolQueryBuilder = QueryBuilders.boolQuery();
        // 订单号
        if (StringUtils.isNotBlank(queryPageQo.getOrderNo())) {
            mainBoolQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getOrderNo), wildcardsExp(queryPageQo.getOrderNo())));
        }
        //运输计划编号
        String transportNo = queryPageQo.getTransportNo();
        if (StrUtil.isNotEmpty(transportNo)){
            mainBoolQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(TocBusinessIndex::getTransportNo), wildcardsExp(transportNo)));
        }
        //运单号
        String dispatchNo = queryPageQo.getDispatchNo();
        if (StrUtil.isNotEmpty(dispatchNo)){
            mainBoolQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(TocBusinessIndex::getDispatchNo), wildcardsExp(dispatchNo)));
        }
        //子运单号
        String subWaybillNo = queryPageQo.getSubWaybillNo();
        if (StrUtil.isNotEmpty(subWaybillNo)){
            mainBoolQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(TocBusinessIndex::getSubWaybillNo), wildcardsExp(subWaybillNo)));
        }
        //发布方id
        Long apCompanyId = queryPageQo.getApCompanyId();
        if (ObjectUtil.isNotNull(apCompanyId)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getApCompanyId), apCompanyId));
        }
        //发布方类型
        Integer apCompanyType = queryPageQo.getApCompanyType();
        if (ObjectUtil.isNotNull(apCompanyType)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getApCompanyType),apCompanyType));
        }
        //对账状态
        Integer rcStatus = queryPageQo.getRcStatus();
        if (ObjectUtil.isNotNull(rcStatus)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getRcStatus),rcStatus));
        }
        //平台登录
        Long shipperCompanyId = queryPageQo.getShipperCompanyId();
        if (ObjectUtil.isNotNull(shipperCompanyId)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getShipperCompanyId),shipperCompanyId));
        }
        //平台审核状态
        Integer shipperAuditStatus = queryPageQo.getShipperAuditStatus();
        if (ObjectUtil.isNotNull(shipperAuditStatus)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getShipperAuditStatus),shipperAuditStatus));
        }
        //派车时间比较
        String dispatchTimeStart = queryPageQo.getDispatchTimeStart();
        String dispatchTimeEnd = queryPageQo.getDispatchTimeEnd();
        if (StrUtil.isNotEmpty(dispatchTimeStart)) {
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getDispatchTime))
                    .gte(dispatchTimeStart));
        }
        if (StrUtil.isNotEmpty(dispatchTimeEnd)) {
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getDispatchTime))
                    .lte(dispatchTimeEnd));
        }
        //司机运输时间
        String transportTimeStart = queryPageQo.getTransportTimeStart();
        String transportTimeEnd = queryPageQo.getTransportTimeEnd();
        if (StrUtil.isNotBlank(transportTimeStart)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getTransportTime))
                    .gte(transportTimeStart));
        }
        if (StrUtil.isNotBlank(transportTimeEnd)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getTransportTime))
                    .lte(transportTimeEnd));
        }
        //司机卸货时间
        String unloadTimeStart = queryPageQo.getUnloadTimeStart();
        String unloadTimeEnd = queryPageQo.getUnloadTimeEnd();
        if (StrUtil.isNotBlank(unloadTimeStart)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getUnloadTime))
                    .gte(unloadTimeStart));
        }
        if (StrUtil.isNotBlank(unloadTimeEnd)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getUnloadTime))
                    .lte(unloadTimeEnd));
        }
        //车牌号
        String carNo = queryPageQo.getCarNo();
        if (StrUtil.isNotEmpty(carNo)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getCarNo), carNo));
        }
        //发货单位
        String fmCustomerName = queryPageQo.getFmCustomerName();
        if (StrUtil.isNotEmpty(fmCustomerName)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getFmCustomerName), fmCustomerName));
        }
        //收货单位
        String toCustomerName = queryPageQo.getToCustomerName();
        if (StrUtil.isNotEmpty(toCustomerName)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getToCustomerName), toCustomerName));
        }
        //服务方姓名
        String arCompanyName = queryPageQo.getArCompanyName();
        if (StrUtil.isNotBlank(arCompanyName)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getArCompanyName), arCompanyName));
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,arCompanyName,true,List.of(FieldUtils.val(TocBusinessIndex::getArCompanyName)));
        }
        //发布方姓名
        String apCompanyName = queryPageQo.getApCompanyName();
        if (StrUtil.isNotBlank(apCompanyName)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,apCompanyName,true,List.of(FieldUtils.val(TocBusinessIndex::getApCompanyName)));
        }
        //货物名称
        String itemName = queryPageQo.getItemName();
        if (StrUtil.isNotBlank(itemName)){
            mainBoolQueryBuilder.must(QueryBuilders.wildcardQuery("items.itemName", wildcardsExp(itemName)));
        }
        //支付方式
        Integer paymentMethod = queryPageQo.getPaymentMethod();
        if (ObjectUtil.isNotNull(paymentMethod)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getPaymentMethod),paymentMethod));
        }
        //发货地址
        String fmAddress = queryPageQo.getFmAddress();
        if (StrUtil.isNotBlank(fmAddress)){
            List<String> columns = Lists.newArrayList();
            columns.add(FieldUtils.val(TocBusinessIndex::getFmAddress));
            columns.add(FieldUtils.val(TocBusinessIndex::getFmAddressName));
            columns.add(FieldUtils.val(TocBusinessIndex::getFmFullAddress));
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,fmAddress,true,columns);
        }
        //收货地址
        String toAddress = queryPageQo.getToAddress();
        if (StrUtil.isNotBlank(toAddress)){
            List<String> columns = Lists.newArrayList();
            columns.add(FieldUtils.val(TocBusinessIndex::getToAddress));
            columns.add(FieldUtils.val(TocBusinessIndex::getToAddressName));
            columns.add(FieldUtils.val(TocBusinessIndex::getToFullAddress));
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,toAddress,true,columns);
        }
        //导出的时候是否有选中单号
        List<String> exportSubWaybillNoList = queryPageQo.getExportSubWaybillNoList();
        if (CollectionUtil.isNotEmpty(exportSubWaybillNoList)){
            mainBoolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(TocBusinessIndex::getSubWaybillNo),exportSubWaybillNoList));
        }
        //支付状态
        Integer payStatus = queryPageQo.getPayStatus();
        if (ObjectUtil.isNotNull(payStatus)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getPayStatus),payStatus));
        }
        //操作人
        String payOperatorName = queryPageQo.getPayOperatorName();
        if (StrUtil.isNotBlank(payOperatorName)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TocBusinessIndex::getPayOperatorName)+ ".keyword",payOperatorName));
        }
        return mainBoolQueryBuilder;
    }
}
