package com.wzc.be.es.core.order.converter;

import com.wzc.be.data.data.es.order.dto.OrderIndexDTO;
import com.wzc.be.data.data.es.order.dto.OrderPageDTO;
import com.wzc.be.es.core.order.index.OrderIndex;
import com.wzc.be.es.data.order.qo.OrderPageQO;
import com.wzc.be.es.data.order.vo.OrderIndexVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年07月13日 15:05
 */
@Mapper(componentModel = "spring")
public interface OrderConverter {

    OrderConverter INSTANCE = Mappers.getMapper(OrderConverter.class);

    List<OrderIndexVO> indexListToVoList(List<OrderIndex> orderIndexList);

    OrderPageDTO qoToDTO(OrderPageQO orderPageQo);

    List<OrderIndex> dtoListToVOList(List<OrderIndexDTO> orderIndexDTOList);

}
