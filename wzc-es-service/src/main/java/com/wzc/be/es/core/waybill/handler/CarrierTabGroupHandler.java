package com.wzc.be.es.core.waybill.handler;


import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.collection.CollectionUtil;
import com.baidu.mapcloud.cloudnative.authentication.data.enums.AuthOptionsEnum;
import com.baidu.mapcloud.cloudnative.authentication.datapermission.core.entity.OtherQueryParam;
import com.baidu.mapcloud.cloudnative.authentication.datapermission.core.rule.UserPermissionRule;
import com.baidu.mapcloud.cloudnative.authentication.datapermission.enums.DataRuleValueEnum;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.baidu.mapcloud.cloudnative.common.model.UserContext;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.enums.SubWaybillStatusEnum;
import com.wzc.be.es.common.enums.WaybillCompleteStatusEnum;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.core.waybill.mapper.SubWaybillListMapper;
import com.wzc.be.es.data.waybill.enums.CarrierTabTypeEnum;
import com.wzc.be.es.data.waybill.qo.CarrierTabGroupCountQO;
import com.wzc.be.es.data.waybill.vo.CarrierTabGroupCountVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.ExistsQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 承运商列表tab页统计
 *
 * <AUTHOR> Wei
 */
@Slf4j
@Component
public class CarrierTabGroupHandler implements BaseSearchHandler<CarrierTabGroupCountQO, CarrierTabGroupCountVO> {

    @Value("${carrierTabGroup.search.type:carrierTabGroupHandler}")
    private String searchType;

    @Resource
    private EsIndexProperties esIndexProperties;

    @Resource
    protected UserPermissionRule userPermissionRule;

    @Resource
    private RestHighLevelClient restHighLevelClient;


    @Override
    public RestResponse<CarrierTabGroupCountVO> search(CarrierTabGroupCountQO qo) {
        try {


            SearchRequest searchRequest = new SearchRequest(esIndexProperties.getSubWaybillIndex());
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.from(0);
            searchSourceBuilder.size(2);
            searchSourceBuilder.trackTotalHits(true);

            BoolQueryBuilder mainQueryBuilder0 = this.getMainBoolQueryBuilder(qo);
            searchSourceBuilder.query(mainQueryBuilder0);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse0 = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits0 = searchResponse0.getHits();
            TotalHits status0 = searchHits0.getTotalHits();

            BoolQueryBuilder mainQueryBuilder1 = this.getMainBoolQueryBuilder(qo);
            mainQueryBuilder1.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_START.getCode()));
            searchSourceBuilder.query(mainQueryBuilder1);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse1 = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits1 = searchResponse1.getHits();
            TotalHits status1 = searchHits1.getTotalHits();

            BoolQueryBuilder mainQueryBuilder2 = this.getMainBoolQueryBuilder(qo);
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_LOAD.getCode()));
            boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_UNLOAD.getCode()));
            boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_LOAD_SIGN.getCode()));
            boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_UNLOAD_SIGN.getCode()));
            mainQueryBuilder2.must(boolQuery);
            searchSourceBuilder.query(mainQueryBuilder2);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse2 = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits2 = searchResponse2.getHits();
            TotalHits status2 = searchHits2.getTotalHits();

            BoolQueryBuilder mainQueryBuilder3 = this.getMainBoolQueryBuilder(qo);
            mainQueryBuilder3.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_SIGN.getCode()));
            searchSourceBuilder.query(mainQueryBuilder3);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse3 = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits3 = searchResponse3.getHits();
            TotalHits status3 = searchHits3.getTotalHits();

            BoolQueryBuilder mainQueryBuilder4 = this.getMainBoolQueryBuilder(qo);
            mainQueryBuilder4.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.FINISH.getCode()));
            searchSourceBuilder.query(mainQueryBuilder4);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse4 = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits4 = searchResponse4.getHits();
            TotalHits status4 = searchHits4.getTotalHits();

            BoolQueryBuilder mainQueryBuilder5 = this.getMainBoolQueryBuilder(qo);
            mainQueryBuilder5.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.CANCELLED.getCode()));
            searchSourceBuilder.query(mainQueryBuilder5);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse5 = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits5 = searchResponse5.getHits();
            TotalHits status5 = searchHits5.getTotalHits();

            BoolQueryBuilder mainQueryBuilder6 = this.getMainBoolQueryBuilder(qo);
            mainQueryBuilder6.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCompleteStatus), WaybillCompleteStatusEnum.UNRECONCILED.getCode()));
            searchSourceBuilder.query(mainQueryBuilder6);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse6 = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits6 = searchResponse6.getHits();
            TotalHits status6 = searchHits6.getTotalHits();

            BoolQueryBuilder mainQueryBuilder8 = this.getMainBoolQueryBuilder(qo);
            mainQueryBuilder8.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_ASSIGN.getCode()));
            searchSourceBuilder.query(mainQueryBuilder8);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse8 = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits8 = searchResponse8.getHits();
            TotalHits status8 = searchHits8.getTotalHits();

            CarrierTabGroupCountVO countVO = new CarrierTabGroupCountVO();
            countVO.setStatusCount0(status0.value);
            countVO.setStatusCount1(status1.value);
            countVO.setStatusCount2(status2.value);
            countVO.setStatusCount3(status3.value);
            countVO.setStatusCount4(status4.value);
            countVO.setStatusCount5(status5.value);
            countVO.setStatusCount6(status6.value);
            countVO.setStatusCount8(status8.value);
            return RestResponse.success(countVO);
        } catch (Exception e) {
            log.error("承运商运单列表tab统计异常", e);
        }
        return RestResponse.success(new CarrierTabGroupCountVO());
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return esIndexProperties.getSubWaybillIndex();
    }

    public BoolQueryBuilder getMainBoolQueryBuilder(CarrierTabGroupCountQO qo) {
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        // 子运单号字段必须存在
        ExistsQueryBuilder fieldName = QueryBuilders.existsQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillNo));
        mainQueryBuilder.must(fieldName);

        if(qo.getIsSync() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getIsSync), qo.getIsSync()));
        }
        CarrierTabTypeEnum tabType = qo.getTabType();
        if (null != tabType) {
            switch (tabType) {
                case STATUS_1:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_START.getCode()));
                    break;
                case STATUS_2:
                    BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                    boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_LOAD.getCode()));
                    boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_UNLOAD.getCode()));
                    boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_LOAD_SIGN.getCode()));
                    boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_UNLOAD_SIGN.getCode()));
                    mainQueryBuilder.must(boolQuery);
                    break;
                case STATUS_3:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_SIGN.getCode()));
                    break;
                case STATUS_4:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.FINISH.getCode()));
                    break;
                case STATUS_5:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.CANCELLED.getCode()));
                    break;
                case STATUS_6:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCompleteStatus), WaybillCompleteStatusEnum.UNRECONCILED.getCode()));
                    break;
                case STATUS_7:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), 8));
                    mainQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getIsEvaluate), 1));
                    break;
                case STATUS_8:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_ASSIGN.getCode()));
                    break;
                default:
                    break;

            }
        }

        if(qo.getZbStatus() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getZbStatus), qo.getZbStatus()));
        }

        if (CollectionUtils.isNotEmpty(qo.getPlanOrderNoList())) {
            mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getParentNo), qo.getPlanOrderNoList()));
        }else {
            //mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCarrierCompanyId), qo.getCarrierCompanyId()));
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCarrierCompanyId), qo.getCarrierCompanyId()));
            boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getFbfId), qo.getCarrierCompanyId()));
            mainQueryBuilder.must(boolQuery);
        }

        // 状态
        if (CollectionUtils.isNotEmpty(qo.getStatus())) {
            mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), qo.getStatus()));
        }

        // 派车单号
        if (StringUtils.isNotBlank(qo.getWaybillNo())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getWaybillNo), wildcardsExp(qo.getWaybillNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getWaybillNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getWaybillNo)));
        }

        // 车牌号
        if (StringUtils.isNotBlank(qo.getCarNo())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getCarNo), wildcardsExp(qo.getCarNo())));
        }
        matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getCarNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getCarNo)));

        // 司机姓名
        if (StringUtils.isNotBlank(qo.getDriverName())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getDriverName), wildcardsExp(qo.getDriverName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getDriverName(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getDriverName)));
        }

        // 船员姓名
        if (StringUtils.isNotBlank(qo.getZtDriverName())){
//            mainQueryBuilder.must(getMatchPhraseQuery("ztDriverName",qo.getZtDriverName()));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getZtDriverName(),true, CollectionUtil.newArrayList("ztDriverName"));
        }

        // 订单号
        if (StringUtils.isNotBlank(qo.getOrderNo())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getOrderNo), wildcardsExp(qo.getOrderNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getOrderNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getOrderNo)));
        }

        // goodsName
        if (StringUtils.isNotBlank(qo.getGoodsName())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery("items.itemName", wildcardsExp(qo.getGoodsName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getGoodsName(),true, CollectionUtil.newArrayList("items.itemName"));
        }

        //运输模式
        if(qo.getTransportType() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getTransportType), qo.getTransportType()));
        }

        // 查询时间类型 1创建时间 2开启运输 3装货签到 4装货确认 5卸货签到 6卸货确认 7货主签收
        if(qo.getDateType() != null){
            switch (qo.getDateType()){
                case 1:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getCreateTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getCreateTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 2:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getTransStartTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getTransStartTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 3:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadSignInTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadSignInTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 4:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadConfirmTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadConfirmTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 5:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getUnloadSignInTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getUnloadSignInTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 6:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getTransEndTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getTransEndTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 7:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getOwnerSignTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getOwnerSignTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 8:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getOrderType),1));
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadFirstTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadFirstTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 9:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getOrderType),1));
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadSecondTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadSecondTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 10:
                    mainQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getOrderType),1));
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getUnloadFirstTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getUnloadFirstTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 11:
                    mainQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getOrderType),1));
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getUnloadSecondTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getUnloadSecondTime)).lte(qo.getEndTime()));
                    }
                    break;
            }

        }

        // 运单来源
        if(Objects.nonNull(qo.getDispatchSourceNew())){
            switch (qo.getDispatchSourceNew()){
                case 0://0我找车系统指派
                    BoolQueryBuilder boolQuery0 = QueryBuilders.boolQuery();
                    boolQuery0.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),0));
                    boolQuery0.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),1));
                    boolQuery0.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),2));
                    boolQuery0.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),7));
                    mainQueryBuilder.must(boolQuery0);
                    break;
                case 3://3竞价
                    BoolQueryBuilder boolQuery3 = QueryBuilders.boolQuery();
                    boolQuery3.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),3));
                    boolQuery3.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),5));
                    boolQuery3.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),8));
                    mainQueryBuilder.must(boolQuery3);
                    break;
                case 4://4抢单
                    BoolQueryBuilder boolQuery4 = QueryBuilders.boolQuery();
                    boolQuery4.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),4));
                    boolQuery4.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),6));
                    boolQuery4.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),9));
                    mainQueryBuilder.must(boolQuery4);
                    break;
            }
        }


        // 发货商
        if(StringUtils.isNotBlank(qo.getFromCustomerName())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery("sender.customerName",wildcardsExp(qo.getFromCustomerName())));
        }
        matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getFromCustomerName(),true, CollectionUtil.newArrayList("sender.customerName"));

        // 收货商
        if(StringUtils.isNotBlank(qo.getToCustomerName())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery("receiver.customerName",wildcardsExp(qo.getToCustomerName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getToCustomerName(),true, CollectionUtil.newArrayList("receiver.customerName"));
        }

        // 发货地址
        if(StringUtils.isNotBlank(qo.getFromAddress())){
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//            boolQuery.should(QueryBuilders.wildcardQuery("sender.address",wildcardsExp(qo.getFromAddress())));
//            boolQuery.should(QueryBuilders.wildcardQuery("sender.fullAddress",wildcardsExp(qo.getFromAddress())));
            matchPhraseOrWildcardMustQuery(boolQuery,qo.getFromAddress(),true, CollectionUtil.newArrayList("sender.address","sender.fullAddress"));
            mainQueryBuilder.must(boolQuery);
        }

        // 收货地址
        if(StringUtils.isNotBlank(qo.getToAddress())){
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//            boolQuery.should(QueryBuilders.wildcardQuery("receiver.address",wildcardsExp(qo.getToAddress())));
//            boolQuery.should(QueryBuilders.wildcardQuery("receiver.fullAddress",wildcardsExp(qo.getToAddress())));
            matchPhraseOrWildcardMustQuery(boolQuery,qo.getToAddress(),true, CollectionUtil.newArrayList("receiver.address","receiver.fullAddress"));
            mainQueryBuilder.must(boolQuery);
        }

        // 第三方单号
        if(StringUtils.isNotBlank(qo.getThirdOrderNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getThirdOrderNo),wildcardsExp(qo.getThirdOrderNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getThirdOrderNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getThirdOrderNo)));
        }

        // 业务类型
        if(qo.getBusinessType() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getBusinessType),qo.getBusinessType()));
        }

        // 订单类型
        if(qo.getWaybillType() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getOrderType),qo.getWaybillType()));
        }

        // 北斗是否正常
        if(qo.getBdNormal() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getBdNormal),qo.getBdNormal()));
        }

        // 自动签收
        if(qo.getAutoSign() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getAutoSign),qo.getAutoSign()));
        }

        // shipperName-货主名称
        if (StringUtils.isNotBlank(qo.getShipperName())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getShipperCompanyName), wildcardsExp(qo.getShipperName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getShipperName(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getShipperCompanyName)));
        }

        // 结算状态(对上)
        if(Objects.nonNull(qo.getCompleteStatus())){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCompleteStatus),qo.getCompleteStatus()));
        }
        // 结算状态(对下)
        if(Objects.nonNull(qo.getCompleteDownStatus())){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCompleteDownStatus),qo.getCompleteDownStatus()));
        }

        // 应付结算单号
        if(StringUtils.isNotBlank(qo.getPayStatementNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getPayStatementNo),wildcardsExp(qo.getPayStatementNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getPayStatementNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getPayStatementNo)));
        }

        // 应收结算单号
        if(StringUtils.isNotBlank(qo.getReStatementNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getReStatementNo),wildcardsExp(qo.getReStatementNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getReStatementNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getReStatementNo)));
        }

        // 应付单号
        if(StringUtils.isNotBlank(qo.getPayOrderNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getPayOrderNo),wildcardsExp(qo.getPayOrderNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getPayOrderNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getPayOrderNo)));
        }

        // 应收单号
        if(StringUtils.isNotBlank(qo.getReOrderNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getReOrderNo),wildcardsExp(qo.getReOrderNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getReOrderNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getReOrderNo)));
        }

        // 付款申请单号
        if(StringUtils.isNotBlank(qo.getPayApplyNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getPayApplyNo),wildcardsExp(qo.getPayApplyNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getPayApplyNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getPayApplyNo)));
        }

        // 收款单号
        if(StringUtils.isNotBlank(qo.getReceiptNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getReceiptNo),wildcardsExp(qo.getReceiptNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getReceiptNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getReceiptNo)));
        }

        // 货源编号
        if(StringUtils.isNotBlank(qo.getGoodsNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery("goodsNo.keyword",wildcardsExp(qo.getGoodsNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getGoodsNo(),true, CollectionUtil.newArrayList("goodsNo"));
        }

        BoolQueryBuilder authBoolQuery = QueryBuilders.boolQuery();
        Boolean needAddCompanyAndOther = userPermissionRule.needAddCompanyAndOther();
        String userCompanyId = String.valueOf(UserContext.companyId());
        List<String> orderType = userPermissionRule.getValueList(DataRuleValueEnum.ORDER_TYPE, AuthOptionsEnum.SELECT);
        if (CollectionUtil.isNotEmpty(orderType)) {
            mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getOrderType), orderType));
        }
        List<String> sourceType = userPermissionRule.getValueList(DataRuleValueEnum.SOURCE_TYPE, AuthOptionsEnum.SELECT);
        if (CollectionUtil.isNotEmpty(sourceType)) {
            mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getSourceType), sourceType));
        }
        if (needAddCompanyAndOther) {
            BoolQueryBuilder innerQuery = QueryBuilders.boolQuery();

            List<OtherQueryParam> companyAndOther = userPermissionRule.getCompanyAndOther();
            // 当前公司物料信息
            if (CollectionUtil.isNotEmpty(companyAndOther)) {
                companyAndOther.stream().filter(
                        param -> Objects.equals(userCompanyId, param.getCompanyId())
                ).forEach(queryParam -> {
                    // 物料
                    if (CollectionUtil.isNotEmpty(queryParam.getMaterialSpecies())) {
                        List<String> materialSpecies = queryParam.getMaterialSpecies().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());;
                        if(CollectionUtils.isNotEmpty(materialSpecies)){
                            innerQuery.must(QueryBuilders.termsQuery("items.speciesId", materialSpecies));
                        }
                    }
                    // 路线
                    if (CollectionUtil.isNotEmpty(queryParam.getRoute())) {
                        List<String> routes = queryParam.getRoute().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());;
                        if(CollectionUtils.isNotEmpty(routes)){
                            innerQuery.must(QueryBuilders.termsQuery("trackRouteId", routes));
                        }
                    }
                });
                authBoolQuery.should(innerQuery);
            }

            if (CollectionUtil.isNotEmpty(companyAndOther)) {
                companyAndOther.stream().filter(
                        param -> !Objects.equals(userCompanyId, param.getCompanyId())
                ).forEach(queryParam -> {
                    BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                    // 货主公司
                    shouldQuery.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCarrierCompanyId), queryParam.getCompanyId()));
                    // 物料
                    if (CollectionUtil.isNotEmpty(queryParam.getMaterialSpecies())) {
                        List<String> materialSpecies = queryParam.getMaterialSpecies().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());;
                        if(CollectionUtils.isNotEmpty(materialSpecies)){
                            shouldQuery.must(QueryBuilders.termsQuery("items.speciesId", materialSpecies));
                        }
                    }
                    // 路线
                    if (CollectionUtil.isNotEmpty(queryParam.getRoute())) {
                        List<String> routes = queryParam.getRoute().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());;
                       if(CollectionUtils.isNotEmpty(routes)){
                           shouldQuery.must(QueryBuilders.termsQuery("trackRouteId", routes));
                       }
                    }
                    authBoolQuery.should(shouldQuery);
                });
            }
        }
        mainQueryBuilder.must(authBoolQuery);
        return mainQueryBuilder;
    }
}
