package com.wzc.be.es.core.dispatch.converter;

import com.wzc.be.es.core.dispatch.index.DispatchStatusIndex;
import com.wzc.be.es.data.dispatch.vo.DispatchStatusVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年07月13日 15:05
 */
@Mapper(componentModel = "spring")
public interface DispatchConverter {

    DispatchConverter INSTANCE = Mappers.getMapper(DispatchConverter.class);

    List<DispatchStatusVO> indexListToVoList(List<DispatchStatusIndex> dispatchStatusIndexList);

}
