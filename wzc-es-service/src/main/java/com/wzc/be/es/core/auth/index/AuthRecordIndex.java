package com.wzc.be.es.core.auth.index;

import cn.easyes.annotation.IndexField;
import cn.easyes.annotation.rely.FieldType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.Date;

/**
 * @ClassName AuthRecordEs
 * @Description
 * <AUTHOR>
 * @Date 2023/7/6 13:49
 * @Version 1.0
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AuthRecordIndex {

    @IndexField(fieldType = FieldType.LONG)
    private Long id;

    //名称
    @IndexField(fieldType = FieldType.KEYWORD)
    private String name;



    //类型
    private Integer auditType;


    //管理员id
    private Long managerId;


    //数据来源id
    private Long dataId;


    //审核状态 1:待审核，2：审核未通过，3：审核已通过，4：认证过期
    private Integer status;


    //提交认证时间

    private String authTagDate;


    //审核人
    private Long operatorId;

    //车辆注册人
    private Long registerID;


    //审核人
    @IndexField(fieldType = FieldType.KEYWORD)
    private String operatorName;


    //管理员
    private String managerName;

    //审核时间
    private String auditDate;

    //审核原因
    @IndexField(fieldType = FieldType.TEXT)
    private String auditComments;


    private Integer auditFlag;



}
