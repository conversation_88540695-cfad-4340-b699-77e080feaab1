package com.wzc.be.es.core.waybill.handler;


import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import cn.hutool.core.collection.CollectionUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.core.waybill.mapper.SubWaybillListMapper;
import com.wzc.be.es.data.waybill.qo.CheckActivateWaybillQO;
import com.wzc.be.es.data.waybill.qo.CheckDispatchWaybillQO;
import com.wzc.be.es.data.waybill.vo.WaybillQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 司机已派车成功的运单查询
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CheckDispatchWaybillHandler implements BaseSearchHandler<CheckDispatchWaybillQO, List<WaybillQueryVO>> {

    @Value("${checkActivateWaybill.search.type:checkDispatchWaybillHandler}")
    private String searchType;

    @Value("${waybill.search.index:sub_waybill_index01}")
    private String index;

    @Resource
    private SubWaybillListMapper subWaybillListMapper;

    @Override
    public RestResponse<List<WaybillQueryVO>> search(CheckDispatchWaybillQO qo) {
        LambdaEsQueryWrapper<SubWaybillIndex> queryWrapper = new LambdaEsQueryWrapper<>();
        if (qo.getDriverId() != null) {
            queryWrapper.eq(SubWaybillIndex::getDriverId, qo.getDriverId());
        }

        if (qo.getCarId() != null) {
            queryWrapper.eq(SubWaybillIndex::getCarId, qo.getCarId());
        }
        //子运单状态
        if (CollectionUtil.isNotEmpty(qo.getSubWaybillStatusList())) {
            queryWrapper.in(SubWaybillIndex::getSubWaybillStatus, qo.getSubWaybillStatusList());
        }
        //对上结算状态
        if (CollectionUtil.isNotEmpty(qo.getCompleteStatusList())) {
            queryWrapper.notIn(SubWaybillIndex::getCompleteStatus, qo.getCompleteStatusList());
        }
        //对上结算状态
        if (CollectionUtil.isNotEmpty(qo.getCompleteDownStatusList())) {
            queryWrapper.notIn(SubWaybillIndex::getCompleteDownStatus, qo.getCompleteDownStatusList());
        }

        queryWrapper.index(getIndex());
        queryWrapper.limit(1);
        List<SubWaybillIndex> subWaybillIndexList = subWaybillListMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(subWaybillIndexList)) {
            return RestResponse.success();
        }
        List<WaybillQueryVO> waybillQueryVOList = new ArrayList<>();
        subWaybillIndexList.forEach(obj -> {
            WaybillQueryVO record = new WaybillQueryVO();
            record.setWaybillNo(obj.getWaybillNo());
            record.setWaybillStatus(obj.getSubWaybillStatus());
            record.setStatus(obj.getStatus());
            record.setCompleteStatus(obj.getCompleteStatus());
            record.setCarrierCompanyId(obj.getCarrierCompanyId());
            record.setCompanyId(obj.getCompanyId());
            record.setDriverId(obj.getDriverId());
            record.setDriverName(obj.getDriverName());
            record.setCarId(obj.getCarId());
            record.setCarNo(obj.getCarNo());
            record.setCreateTime(obj.getCreateTime());
            record.setBusinessType(obj.getBusinessType());
            record.setSettleStatus(obj.getCompleteStatus());
            record.setSubWaybillNo(obj.getSubWaybillNo());
            waybillQueryVOList.add(record);
        });
        return RestResponse.success(waybillQueryVOList);
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.index;
    }
}
