package com.wzc.be.es.core.waybill.handler;


import cn.easyes.core.biz.EsPageInfo;
import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.data.data.waybill.model.dto.WaybillQueryEntityDTO;
import com.wzc.be.es.adapter.client.waybill.WaybillDorisFeignClient;
import com.wzc.be.es.common.constant.Constants;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.commons.PageVO;
import com.wzc.be.es.core.waybill.converter.DorisQueryWaybillConverter;
import com.wzc.be.es.core.waybill.mapper.SubWaybillListMapper;
import com.wzc.be.es.data.waybill.enums.DispatchQueryEnums;
import com.wzc.be.es.data.waybill.enums.InspectorListTabEnum;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.data.waybill.qo.InspectorListQo;
import com.wzc.be.es.data.waybill.vo.InspectorWaybillListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.ElasticsearchException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 小程序验货人运单列表
 *
 * <AUTHOR> Wei
 */
@Slf4j
@Component
public class InspectorWaybillQueryListHandler implements BaseSearchHandler<InspectorListQo, PageVO<InspectorWaybillListVO>> {

    @Value("${inspectorWaybillQuery.search.type:inspectorWaybill}")
    private String searchType;

    @Value("${sub.waybill.search.index:sub_waybill_index01}")
    private String index;

    @Resource
    private SubWaybillListMapper subWaybillListMapper;

    @Resource
    private WaybillDorisFeignClient waybillDorisFeignClient;


    @Override
    public RestResponse<PageVO<InspectorWaybillListVO>> search(InspectorListQo qo) {

        LambdaEsQueryWrapper<SubWaybillIndex> queryWrapper = new LambdaEsQueryWrapper<>();
        this.genSearch(queryWrapper, qo);
        queryWrapper.index(index);
        List<InspectorWaybillListVO> list = new ArrayList<>();
        PageVO<InspectorWaybillListVO> page = new PageVO<>();
        page.setPage(qo.getPage());
        page.setSize(qo.getSize());
        try {
            EsPageInfo<SubWaybillIndex> pageInfo = subWaybillListMapper.pageQuery(queryWrapper, qo.getPage(), qo.getSize());
            page.setTotal(pageInfo.getTotal());
            if (CollectionUtils.isEmpty(pageInfo.getList())) {
                return RestResponse.success(page);
            }
            for (SubWaybillIndex item : pageInfo.getList()) {
                InspectorWaybillListVO record = new InspectorWaybillListVO();
                record.setDispatchCarNo(item.getWaybillNo());
                record.setSubWaybillNo(item.getSubWaybillNo());
                record.setCarNo(item.getCarNo());
                record.setSubWaybillStatus(item.getSubWaybillStatus());
                // 收发货地址 取值逻辑：poi地址>完整地址
                InspectorWaybillListVO.AddressInfo senderInfo = new InspectorWaybillListVO.AddressInfo();
                if (CollectionUtils.isNotEmpty(item.getSender())) {
                    for (SubWaybillIndex.Address address : item.getSender()) {
                        senderInfo.setPoiName(address.getAddress());
                        senderInfo.setFullAddress(address.getFullAddress());
                    }
                }
                record.setSenderInfo(senderInfo);

                InspectorWaybillListVO.AddressInfo receiverInfo = new InspectorWaybillListVO.AddressInfo();
                if (CollectionUtils.isNotEmpty(item.getReceiver())) {
                    for (SubWaybillIndex.Address address : item.getReceiver()) {
                        receiverInfo.setPoiName(address.getAddress());
                        receiverInfo.setFullAddress(address.getFullAddress());
                    }
                }
                record.setReceiverInfo(receiverInfo);
                record.setSubWaybillStatus(item.getSubWaybillStatus());
                record.setCompanyName(item.getCarrierCompanyName());
                record.setCompanyId(item.getCarrierCompanyId());
                record.setCarId(item.getCarId().toString());
                record.setDriverId(item.getDriverId().toString());
                record.setDriverName(item.getDriverName());
                list.add(record);
            }
        }catch (Exception e){
            //es查询异常 ，查询doris
            log.info("小程序验货人运单列表查询es异常:{},开始查询doris",e);
            //如果不是 es查询异常 便往外抛
            Throwable root = ExceptionUtil.getRootCause(e);
            if(!(root instanceof ElasticsearchException)
                    || !((ElasticsearchException) root).getDetailedMessage().contains(Constants.ES_RESULT_WINDOW_IS_TOO_LARGE)){
                throw new RuntimeException(e);
            }
            //查询es异常获取不到总数，重新查询
            page.setTotal(subWaybillListMapper.selectCount(queryWrapper));
            List<WaybillQueryEntityDTO> dtoList = new ArrayList<>();
            try {
//                dtoList = waybillDorisFeignClient.inspectorWaybillQueryListHandlerSearch(DorisQueryWaybillConverter.INSTANCE.esInspectorListQOToDorisInspectorListQO(qo));
            }catch (Exception exception){
                log.error("异常运单相关查询doris异常：{}",e);
                return RestResponse.success(page);
            }
            if(CollectionUtils.isNotEmpty(dtoList)){
                for(WaybillQueryEntityDTO dto:dtoList){
                    InspectorWaybillListVO record = new InspectorWaybillListVO();
                    record.setDispatchCarNo(dto.getWaybillNo());
                    record.setSubWaybillNo(dto.getSubWaybillNo());
                    record.setCarNo(dto.getCarNo());
                    record.setSubWaybillStatus(dto.getSubWaybillStatus());
                    // 收发货地址 取值逻辑：poi地址>完整地址
                    if(StringUtils.isNotBlank(dto.getSenderInfo())){
                        List<SubWaybillIndex.Address> senderList = JSONUtil.toList(dto.getSenderInfo(),SubWaybillIndex.Address.class);
                        InspectorWaybillListVO.AddressInfo senderInfo = new InspectorWaybillListVO.AddressInfo();
                        if (CollectionUtils.isNotEmpty(senderList)) {
                            for (SubWaybillIndex.Address address : senderList) {
                                senderInfo.setPoiName(address.getAddress());
                                senderInfo.setFullAddress(address.getFullAddress());
                            }
                        }
                        record.setSenderInfo(senderInfo);
                    }

                    if(StringUtils.isNotBlank(dto.getReceiverInfo())){
                        List<SubWaybillIndex.Address> receiverList = JSONUtil.toList(dto.getReceiverInfo(),SubWaybillIndex.Address.class);
                        InspectorWaybillListVO.AddressInfo receiverInfo = new InspectorWaybillListVO.AddressInfo();
                        if (CollectionUtils.isNotEmpty(receiverList)) {
                            for (SubWaybillIndex.Address address : receiverList) {
                                receiverInfo.setPoiName(address.getAddress());
                                receiverInfo.setFullAddress(address.getFullAddress());
                            }
                        }
                        record.setReceiverInfo(receiverInfo);
                    }
                    record.setSubWaybillStatus(dto.getSubWaybillStatus());
                    record.setCompanyName(dto.getCarrierCompanyName());
                    record.setCompanyId(dto.getCarrierCompanyId());
                    record.setCarId(dto.getCarId().toString());
                    record.setDriverId(dto.getDriverId().toString());
                    record.setDriverName(dto.getDriverName());
                    list.add(record);
                }
            }
        }
        page.setContent(list);
        //是否还有下一页 true = 有 false = 无
        page.setHasNext(qo.getPage() + qo.getSize() < page.getTotal());
        return RestResponse.success(page);
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return index;
    }

    private void genSearch(LambdaEsQueryWrapper<SubWaybillIndex> wrapper, InspectorListQo qo) {
        wrapper.isNotNull(SubWaybillIndex::getSubWaybillNo);
        String startTime = qo.getStartTime();
        String endTime = qo.getEndTime();
        if(StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)){
            wrapper.ge(SubWaybillIndex::getCreateTime, startTime);
            wrapper.le(SubWaybillIndex::getCreateTime, endTime);
        }

        String phone = qo.getPhone();
        if (StringUtils.isNotBlank(phone)) {
            wrapper.eq("receiver.phone", phone);
        }

        InspectorListTabEnum tabType = qo.getTabType();
        switch (tabType){
            case ALL:
                // 包含全部运单的状态：待签收、待卸货、待装货、已完成
                wrapper.and(i -> i.eq(SubWaybillIndex::getSubWaybillStatus, 3) // 待装货
                        .or().eq(SubWaybillIndex::getSubWaybillStatus, 6) // 待卸货
                        .or().eq(SubWaybillIndex::getSubWaybillStatus, 5) // 待卸货签到
                        .or().eq(SubWaybillIndex::getSubWaybillStatus, 7)// 待签收
                        .or().eq(SubWaybillIndex::getSubWaybillStatus, 8) //已完成
                );
                break;
            case WAIT_SIGN:
                // 待签收运单：司机已完成全部运输节点、收货人未签收|验货的运单
                wrapper.eq(SubWaybillIndex::getSubWaybillStatus, 6);
                break;
        }

        DispatchQueryEnums queryType = qo.getQueryType();
        if (queryType != null) {
            String queryValue = qo.getQueryValue();
            switch (queryType) {
                case ALL:
                    wrapper.and(i -> i.like("carNo", queryValue)
                            .or().like("sender.fullAddress", queryValue)
                            .or().like("receiver.fullAddress", queryValue)
                            .or().like("items.itemName", queryValue)
                            .or().like("waybillNo", queryValue)
                    );
                    break;
                case CAR_NO:
                    wrapper.like(SubWaybillIndex::getCarNo, queryValue);
                    break;
                case FORM_ADDRESS:
                    wrapper.like("sender.fullAddress", queryValue);
                    break;
                case TO_ADDRESS:
                    wrapper.like("receiver.fullAddress", queryValue);
                    break;
                case CARRIER:
                    wrapper.like("shipperCompanyName", queryValue);
                    break;
                case MATERIAL_NAME:
                    wrapper.like("items.itemName", queryValue);
                    break;
                case DISPATCH_CAR_NO:
                    wrapper.like("waybillNo", queryValue);
                    break;
                default:
                    break;
            }
        }

    }

}
