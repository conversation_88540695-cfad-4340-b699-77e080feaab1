package com.wzc.be.es.core.waybill.converter;


import com.wzc.be.data.data.waybill.model.dto.WaybillQueryEntityDTO;
import com.wzc.be.data.data.waybill.model.qo.*;
import com.wzc.be.es.data.waybill.qo.InspectorListQo;
import com.wzc.be.es.data.waybill.vo.CarrierWaybillListVO;
import com.wzc.be.es.data.waybill.vo.SubWaybillVO;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.factory.Mappers;


/**
 * @ClassName DorisQueryWaybillConverter
 * <AUTHOR>
 * @Date 2024/2/2 9:39
 * @Description
 * @Version 1.0
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface DorisQueryWaybillConverter {

    DorisQueryWaybillConverter INSTANCE = Mappers.getMapper(DorisQueryWaybillConverter.class);

//    CarrierAppOrderListQO carrierAppOrderListQOToDataCarrierAppOrderListQO(com.wzc.be.es.data.waybill.qo.CarrierAppOrderListQO qo);

//    CarrierWaybillListQO esCarrierWaybillListQOToDorisCarrierWaybillListQO(com.wzc.be.es.data.waybill.qo.CarrierWaybillListQO qo);

    CarrierWaybillListVO dorisEntityToCarrierWaybillListVo(WaybillQueryEntityDTO dto);

    ExceptionWaybillListQO esExceptionQOToDorisExceptionQO(com.wzc.be.es.data.waybill.qo.ExceptionWaybillListQO qo);

    SubWaybillVO dorisEntityToSubWaybillVO(WaybillQueryEntityDTO dto);

//    InspectorListQO esInspectorListQOToDorisInspectorListQO(InspectorListQo qo);

//    OwnerWaybillListQO esOwnerWaybillListQOToDorisOwnerWaybillListQO(com.wzc.be.es.data.waybill.qo.OwnerWaybillListQO qo);

//    DriverWaybillListQO esDriverWaybillListQOToDorisDriverWaybillListQO(com.wzc.be.es.data.waybill.qo.DriverWaybillListQO qo);
}
