package com.wzc.be.es.core.waybill.index;


import cn.easyes.annotation.IndexField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 子运单索引
 *
 * <AUTHOR> <PERSON>
 */
@Data
public class SubWaybillIndex {

    @Schema(description = "id")
    private String id;

    /**
     * 运输计划单号
     */
    @Schema(description = "运输计划单号")
    private String parentNo;

    /**
     * 货主公司
     */
    @Schema(description = "货主公司ID")
    private Long shipperCompanyId;

    /**
     * 货主公司
     */
    @Schema(description = "货主公司名称")
    private String shipperCompanyName;

    /**
     * 承运商公司
     */
    @Schema(description = "承运商公司ID")
    private Long carrierCompanyId;

    /**
     * 承运商公司
     */
    @Schema(description = "承运商公司名称")
    private String carrierCompanyName;

    /**
     * 托运公司
     */
    @Schema(description = "托运公司")
    private Long companyId;

    /**
     * 订单号
     */
    @Schema(description = "订单号")
    private String orderNo;

    /**
     * 派车时间
     */
    @Schema(description = "派车时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 子运单号
     */
    @Schema(description = "子运单号")
    private String subWaybillNo;

    /**
     * 子运单状态
     */
    @Schema(description = "子运单状态")
    private Integer subWaybillStatus;

    /**
     * 派车单状态
     */
    @Schema(description = "派车单状态")
    private Integer status;

    /**
     * 派车单号
     */
    @Schema(description = "派车单号")
    private String waybillNo;

    /**
     * 司机id
     */
    @Schema(description = "司机id")
    private Long driverId;

    /**
     * 司机姓名
     */
    @Schema(description = "司机姓名")
    private String driverName;

    @Schema(description = "船员名称")
    private String ztDriverName;


    /**
     * 司机手机号
     */
    @Schema(description = "司机手机号")
    private String driverPhone;

    /**
     * 车辆id
     */
    @Schema(description = "车辆id")
    private Long carId;

    /**
     * 车牌号
     */
    @Schema(description = "车牌号")
    private String carNo;


    /**
     * 发货地址
     */
    @Schema(description = "发货地址")
    private List<Address> sender;

    /**
     * 收货地址
     */
    @Schema(description = "收货地址")
    private List<Address> receiver;

    /**
     * 货物信息
     */
    @Schema(description = "货物信息")
    private List<Item> items;
    /**
     * 货物信息
     */
    @Schema(description = "货物信息")
    private List<SubWaybillIndex.BmsItem> bmsItems;

    /**
     * 结算状态(对上) 1运输未完成 3待结算 4已结算 5已支付 6未对账 7已对账未结算 8已结算待支付
     */
    @Schema(description = "结算状态(对上) 1运输未完成 3待结算 4已结算 5已支付 6未对账 7已对账未结算 8已结算待支付")
    private Integer completeStatus;

    /**
     * 结算状态(对下) 1运输未完成 3待结算 4已结算 5已支付 6未对账 7已对账未结算 8已结算待支付
     */
    @Schema(description = "结算状态(对下) 1运输未完成 3待结算 4已结算 5已支付 6未对账 7已对账未结算 8已结算待支付")
    private Integer completeDownStatus;

    /**
     * 监管异常 1是 0否
     */
    @Schema(description = "监管异常 1是 0否")
    private Integer excepSupervise;

    /**
     * 签收异常 1是 0否
     */
    @Schema(description = "签收异常 1是 0否")
    private Integer excepSign;

    /**
     * 在途异常 1是 0否
     */
    @Schema(description = "在途异常 1是 0否")
    private Integer excepTrans;

    /**
     * 是否存在司机上报异常 1存在 0 不存在
     */
    @Schema(description = "是否存在司机上报异常 1存在 0 不存在")
    private Integer excepReport;

    /**
     * 在途异常发生时间
     */
    @Schema(description = "在途异常发生时间")
    private String transExcepTime;


    /**
     * 签收异常发生时间
     */
    @Schema(description = "签收异常发生时间")
    private String signExcepTime;

    /**
     * 监管上报异常发生时间
     */
    @Schema(description = "监管上报异常发生时间")
    private String superviseExcepTime;

    /**
     * 司机异常上报时间
     */
    @Schema(description = "司机异常上报时间")
    private String reportExcepTime;

    /**
     * 在途异常处理情况 0或null表示有全部处理 1表示有待处理异常
     */
    @Schema(description = "在途异常处理情况 0或null表示有全部处理 1表示有待处理异常")
    private Integer transProcess;

    /**
     * 司机异常上报处理情况 0或null表示有全部处理 1表示有待处理异常
     */
    @Schema(description = "司机异常上报处理情况 0或null表示有全部处理 1表示有待处理异常")
    private Integer signProcess;

    /**
     * 监管上报异异常处理情况 0或null表示有全部处理 1表示有待处理异常
     */
    @Schema(description = "监管上报异异常处理情况 0或null表示有全部处理 1表示有待处理异常")
    private Integer superviseProcess;

    /**
     * 司机异常上报处理情况 0或null表示有全部处理 1表示有待处理异常
     */
    @Schema(description = "司机异常上报处理情况 0或null表示有全部处理 1表示有待处理异常")
    private Integer reportProcess;

    /**
     * 二期派车单号
     */
    @Schema(description = "二期派车单号")
    private String oldWaybillNo;

    /**
     * 二期子运单号
     */
    @Schema(description = "二期子运单号")
    private String oldSubWaybillNo;


    @Schema(description = "订单类型（2.采购订单，1.销售订单，3.调拨订单）")
    private Integer orderType;

    @Schema(description = "来源类型 1、手动创建 2、竞价货源 3、抢单货源")
    private Integer sourceType;

    @Schema(description = "订单创建人")
    private Long shipperUserId;

    @Schema(description = "订单部门ID")
    private Long shipperUserDepartment;

    @Schema(description = "线路id")
    private String trackRouteId;

    @Schema(description = "支付状态0-未支付 1-已支付")
    private Integer payStatus;

    @Schema(description = "车辆上报状态1-未上报 2-上报中 3-已上报 4-上报失败")
    private Integer carReportStatus;

    @Schema(description = "司机上报状态1-未上报 2-已上报 3-上报失败 4-上报中")
    private Integer driverReportStatus;

    /**
     * 装货上报节点
     */
    @Schema(description = "装货上报节点")
    private LocationUpload loadUploadNode;

    /**
     * 卸货上报节点
     */
    @Schema(description = "卸货上报节点")
    private LocationUpload unloadUploadNode;

    /**
     * 监管运单上报状态0-未上报 1-已上报 2-上报中
     */
    @Schema(description = "监管运单上报状态0-未上报 1-已上报 2-上报中")
    private Integer reportWaybillStatus;

    /**
     * 监管流水上报状态0-未上报 1-已上报 2-上报中
     */
    @Schema(description = "监管流水上报状态0-未上报 1-已上报 2-上报中")
    private Integer reportRecordStatus;

    /**
     * 支付状态 0.未支付 1.支付中 2.已支付 3.支付完成 4.支付异常
     */
    @Schema(description = "支付状态 0.未支付 1.支付中 2.已支付 3.支付完成 4.支付异常")
    private Integer tocPayStatus;

    /**
     * 发布方名称
     */
    @Schema(description = "发布方名称")
    private String apCompanyName;

    /**
     * 服务方名称
     */
    @Schema(description = "服务方名称")
    private String arCompanyName;

    /**
     * 业务类型 1货主自营 2平台总包 3平台自营 4网络货运
     */
    @Schema(description = "业务类型 1货主自营 2平台总包 3平台自营 4网络货运")
    private Integer businessType;

    /**
     * 开启运输时间
     */
    @Schema(description = "开启运输时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date transStartTime;

    /**
     * 卸货确认时间
     */
    @Schema(description = "卸货确认时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date transEndTime;

    /**
     * 装货签到时间
     */
    @Schema(description = "装货签到时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date loadSignInTime;

    /**
     * 装货确认时间
     */
    @Schema(description = "装货确认时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date loadConfirmTime;

    /**
     * 卸货签到时间
     */
    @Schema(description = "卸货签到时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date unloadSignInTime;

    /**
     * 货主签收时间
     */
    @Schema(description = "货主签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date ownerSignTime;

    /**
     * 第三方订单号
     */
    @Schema(description = "第三方订单号")
    private String thirdOrderNo;

    /**
     * 北斗是否正常
     * 司机装货确认时，获取在24小时内是否有轨迹点，设置到运单上
     */
    @Schema(description = "北斗是否正常 1是 2否")
    private Integer bdNormal;

    /**
     * 自动签收 1是 2否
     */
    @Schema(description = "自动签收 1是 2否")
    private Integer autoSign;

    /**
     * 是否网络货运 1是 2否
     */
    @Schema(description = "是否网络货运 1是 2否")
    private Integer zbStatus;

    /**
     * 收发货商(收发货人信息里的单位信息)
     */
    @Schema(description = "收发货商(收发货人信息里的单位信息)")
    private String customerName;

    /**
     * 服务方id
     */
    @Schema(description = "服务方id")
    private Long carCaptainId;

    /**
     * 服务方名称
     */
    @Schema(description = "服务方名称")
    private String carCaptainName;

    /**
     * 发布方id
     */
    @Schema(description = "发布方id")
    private Long fbfId;

    /**
     * 发布方名称
     */
    @Schema(description = "发布方名称")
    private String fbfName;

    /**
     * 线路轨迹是否合规(1-是 2-否)
     */
    @Schema(description = "线路轨迹是否合规(1-是 2-否)")
    private Integer isLineLegal;

    /**
     * 时间是否合规(1-是 2-否)
     */
    @Schema(description = "时间是否合规(1-是 2-否)")
    private Integer isTimeLegal;

    /**
     * 榜单是否合规(1-是 2-否)
     */
    @Schema(description = "榜单是否合规(1-是 2-否)")
    private Integer isPoundLegal;

    /**
     * 系统审核状态（1-通过、2-未通过）
     */
    @Schema(description = "系统审核状态（1-通过、2-未通过）")
    private Integer systemCheckStatus;

    /**
     * 人工审核状态（0-未审核、1-通过、2-未通过）
     */
    @Schema(description = "人工审核状态（0-未审核、1-通过、2-未通过）")
    private Integer manualCheckStatus;

    /**
     * 人工审核时间
     */
    @Schema(description = "人工审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date manualCheckTime;

    /**
     * 车主声明 0无需上传 1已上传
     */
    @Schema(description = "车主声明 0无需上传 1已上传")
    @IndexField("czStatement")
    private Integer czStatement;

    /**
     * 车队长收入合规性(1-是 2-否)
     */
    @Schema(description = "车队长收入合规性(1-是 2-否)")
    @IndexField("czStatement")
    private Integer cdzIncomeLegal;

    /**
     * 装货场景照片是否合规(1-是 2-否)
     */
    @Schema(description = "装货场景照片是否合规(1-是 2-否)")
    private Integer isLoadImgLegal;

    /**
     * 卸货场景照片是否合规(1-是 2-否)
     */
    @Schema(description = "卸货场景照片是否合规(1-是 2-否)")
    private Integer isUnLoadImgLegal;

    /**
     * 是否同步二期数据 1是 2否
     */
    @Schema(description = "是否同步二期数据 1是 2否")
    private Integer isSync;

    /**
     * 运输类型 1-汽运 2-船运
     */
    @Schema(description = "运输类型 1-汽运 2-船运")
    private Integer transportType;

    /**
     * 货源编号
     */
    @Schema(description = "货源编号")
    private String goodsNo;

    /**
     * 冻结类型 1现金 2授信
     */
    @Schema(description = "冻结类型 1现金 2授信")
    private Integer freezeType;

    /**
     * 是否已被其他运单关联 0否 1是
     */
    @Schema(description = "是否已被其他运单关联 0否 1是")
    private Integer isRelevance;

    /**
     * 派车类型 1承运商派车 2货主派车
     */
    @Schema(description = "派车类型 1承运商派车 2货主派车")
    private Integer dispatchCarType;

    /**
     * 发布方签收时间
     */
    @Schema(description = "发布方签收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date fbfSignTime;
    ;

    /**
     * 合同编号
     */
    @Schema(description = "合同编号")
    private String contractNo;

    /**
     * 是否需要合同 0否 1是
     */
    @Schema(description = "是否需要合同 0否 1是")
    private Integer needContract;

    /**
     * 签订合同 0未签订，1已签订
     */
    @Schema(description = "签订合同 0未签订，1已签订")
    private Integer signContract;

    /**
     * 合同状态 0-待生成 1-待签约 30-已签约 101-线下签约
     */
    @Schema(description = "合同状态 0-待生成 1-待签约 30-已签约 101-线下签约")
    private Integer contractStatus;

    /**
     * 轮次
     */
    @Schema(description = "轮次")
    private Integer round;

    /**
     * 评价 0待评价 1已评价
     */
    @Schema(description = "评价 0待评价 1已评价")
    private Integer isEvaluate;

    /**
     * 运单审核状态 0-待审核，1-已通过，2-已拒绝
     */
    @Schema(description = "运单审核状态 0-待审核，1-已通过，2-已拒绝")
    private Integer dispatchCheckStatus;

    /**
     * 超时未开启 0正常 1异常
     */
    @Schema(description = "超时未签收 0正常 1异常")
    private Integer overtimeUnStart;

    /**
     * 超时未签收 0正常 1异常
     */
    @Schema(description = "超时未签收 0正常 1异常")
    private Integer overtimeUnSign;

    /**
     * 运单合规性状态 1-司机签收 2-司机待上传 3-司机已上传 4-发布方待确认 5-发布方已确认 6-人工审核通过
     * 非网货的此字段为null
     */
    @Schema(description = "运单合规性状态 1-司机签收 2-司机待上传 3-司机已上传 4-发布方待确认 5-发布方已确认 6-人工审核通过")
    private Integer dispatchComplianceType;

    /**
     * 装货一次过磅时间
     */
    @Schema(description = "装货一次过磅时间")
    private Date loadFirstTime;

    /**
     * 装货二次过磅时间
     */
    @Schema(description = "装货二次过磅时间")
    private Date loadSecondTime;

    /**
     * 卸货一次过磅时间
     */
    @Schema(description = "卸货一次过磅时间")
    private Date unloadFirstTime;

    /**
     * 卸货二次过磅时间
     */
    @Schema(description = "卸货二次过磅时间")
    private Date unloadSecondTime;

    /**
     * 运单来源 0我找车系统 1易思推送(第三方) 2易思推送(商混EBC) 3竞价 4抢单 5第三方竞价 6第三方抢单 7第三方转包 8第三方转包竞价 9第三方转包抢单
     */
    @Schema(description = "运单来源 0我找车系统 1易思推送(第三方) 2易思推送(商混EBC) 3竞价 4抢单 5第三方竞价 6第三方抢单 7第三方转包 8第三方转包竞价 9第三方转包抢单")
    private Integer dispatchSource;


    /**
     * 应付结算单号
     */
    @Schema(description = "应付结算单号")
    private String payStatementNo;

    /**
     * 应收结算单号
     */
    @Schema(description = "应收结算单号")
    private String reStatementNo;

    /**
     * 应付单号
     */
    @Schema(description = "应付单号")
    private String payOrderNo;

    /**
     * 应收单号
     */
    @Schema(description = "应收单号")
    private String reOrderNo;

    /**
     * 付款申请单号
     */
    @Schema(description = "付款申请单号")
    private String payApplyNo;

    /**
     * 收款单号
     */
    @Schema(description = "收款单号")
    private String receiptNo;


    /**
     * 地址信息
     */
    @Schema(description = "地址信息")
    @Data
    public static class Address implements Serializable {

        /**
         * POI
         */
        private String address;

        /**
         * 省
         */
        private String province;

        /**
         * 市
         */
        private String city;

        /**
         * 手机号
         */
        private String phone;

        /**
         * 区
         */
        private String district;

        /**
         * 完整地址
         */
        private String fullAddress;

        /**
         * id
         */
        private Long id;

        /**
         * 类型
         */
        private Integer type;

        /**
         * 收发货商
         */
        @Schema(description = "收发货商")
        private String customerName;

        /**
         * 收货商
         */
        @Schema(description = "收货商")
        private String toCustomerName;
    }

    /**
     * 车辆上报状态
     */
    @Schema(description = "车辆上报状态")
    @Data
    public static class LocationUpload implements Serializable {

        /**
         * 主键id
         */
        @Schema(description = "")
        private Long id;

        /**
         * 打卡节点
         */
        @Schema(description = "")
        private Integer clockInNode;

        /**
         * 监管上报状态
         */
        @Schema(description = "")
        private Integer uploadSuccess;

        /**
         * 订单号
         */
        @Schema(description = "")
        private String waybillNo;

    }

    /**
     * 货物
     */
    @Schema(description = "货物")
    @Data
    public static class Item implements Serializable {
        @Schema(description = "物料id")
        private String itemId;

        @Schema(description = "物料名称")
        private String itemName;

        @Schema(description = "id")
        private String id;

        @Schema(description = "物类id")
        private String speciesId;

        @Schema(description = "承运商实际结算量")
        private String cysActualSettleWeight;

        @Schema(description = "承运商实际结算价格")
        private String cysActualSettlePrice;

        @Schema(description = "货主实际结算量")
        private String hzActualSettleWeight;

        @Schema(description = "货主实际结算价格")
        private String hzActualSettlePrice;
    }

    /**
     * BMS货物
     */
    @Schema(description = "BMS货物")
    @Data
    public static class BmsItem{
        @Schema(description = "物料id")
        private String itemId;

        @Schema(description = "承运商实际结算量")
        private String cysActualSettleWeight;

        @Schema(description = "承运商实际结算价格")
        private String cysActualSettlePrice;

        @Schema(description = "货主实际结算量")
        private String hzActualSettleWeight;

        @Schema(description = "货主实际结算价格")
        private String hzActualSettlePrice;
    }
}
