package com.wzc.be.es.core.business.converter;

import com.wzc.be.data.data.es.toc.model.TocRdBusinessPageQO;
import com.wzc.be.data.data.es.toc.model.TocRdBusinessVO;
import com.wzc.be.es.data.business.qo.TocBusinessEsPageQO;
import com.wzc.be.es.data.business.vo.TocEsBusinessVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author：chenxingguang
 * @Date：2024/2/4 16:42
 * @Version：1.0.0
 */
@Mapper(componentModel = "spring")
public interface TocRdConverter {

    TocRdConverter INSTANCE = Mappers.getMapper(TocRdConverter.class);

    TocRdBusinessPageQO esToDorisQueryDto(TocBusinessEsPageQO queryPageQo);

    List<TocEsBusinessVO> esToDorisQueryPageDto(List<TocRdBusinessVO> tocEsBusinessVOList);
}
