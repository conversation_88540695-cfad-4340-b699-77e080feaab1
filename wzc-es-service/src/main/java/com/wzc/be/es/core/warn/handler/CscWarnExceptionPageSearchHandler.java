/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.core.warn.handler;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.google.common.collect.Lists;
import com.wzc.be.es.adapter.data.DwdCscWarnExceptionIndexClient;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.constant.Constants;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.common.enums.OperateExceptionEnums;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.core.common.CommonRedisResultVO;
import com.wzc.be.es.core.warn.index.CscWarnExceptionIndex;
import com.wzc.be.es.data.business.vo.BusinessEsVO;
import com.wzc.be.es.data.warn.qo.CscWarnExceptionEsPageQO;
import com.wzc.be.es.data.warn.vo.CscWarnExceptionVO;
import com.wzc.common.user.utils.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.wzc.be.es.common.constant.Constants.UPDATE_INDEX_REDIS_KEY;

/**
 * <AUTHOR>
 * @date 2023年12月25日
 * 异常运单
 */
@Slf4j
@Component
public class CscWarnExceptionPageSearchHandler implements BaseSearchHandler<CscWarnExceptionEsPageQO, EsPageVO<CscWarnExceptionVO>> {

    @Resource
    private EsIndexProperties esIndexProperties;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private DwdCscWarnExceptionIndexClient dwdCscWarnExceptionIndexClient;

    @Override
    public RestResponse<EsPageVO<CscWarnExceptionVO>> search(CscWarnExceptionEsPageQO queryPageQo) {
        log.info("csc异常运单模糊查询 search param is : {}", JSONUtil.toJsonStr(queryPageQo));
        try {
            SearchRequest searchRequest = new SearchRequest(getIndex());

            SearchSourceBuilder sourceBuilder = generateSearchSourceBuilder(queryPageQo);
            searchRequest.source(sourceBuilder);

            Integer size = queryPageQo.getSize();
            List<CommonRedisResultVO> commonRedisUpdateResultVOList = null;
            List<CscWarnExceptionVO> list = null;
            long count = 0;
            try {
                SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
                SearchHits searchHits = searchResponse.getHits();
                SearchHit[] hitResult = searchHits.getHits();
                JSONArray array = new JSONArray();
                Stream.of(hitResult).forEach(itemHit -> {
                    Map<String, Object> sourceAsMap = itemHit.getSourceAsMap();
                    array.add(sourceAsMap);
                });
                list = JSONUtil.toList(array, CscWarnExceptionVO.class);
                TotalHits totalHits = searchHits.getTotalHits();
                count = totalHits.value;
            }catch (Exception e){
                log.error("查询csc异常运单失败，开始查询doris",e);
                //如果不是 es查询异常 便往外抛
                Throwable throwable = ExceptionUtil.getRootCause(e);
                if(!(throwable instanceof ElasticsearchException)
                        || !((ElasticsearchException) throwable).getDetailedMessage().contains(Constants.ES_RESULT_WINDOW_IS_TOO_LARGE)){
                    throw new Exception(e);
                }
                CountRequest countRequest = new CountRequest(getIndex());
                countRequest.source(sourceBuilder);
                CountResponse countResponse = restHighLevelClient.count(countRequest, RequestOptions.DEFAULT);
                count = countResponse.getCount();
                list = dwdCscWarnExceptionIndexClient.search(queryPageQo);
            }

            if(OperateExceptionEnums.NO_HANDLE.getCode().equals(queryPageQo.getOperateException())){
                RMapCache<String, String> updateMapCache = redissonClient.getMapCache(UPDATE_INDEX_REDIS_KEY + esIndexProperties.getCscWarnExceptionIndex() + ":" + UserUtils.getCompanyId());
                commonRedisUpdateResultVOList = updateMapCache.readAllEntrySet().stream().map(e -> JSONUtil.toBean(e.getValue(), CommonRedisResultVO.class))
                        .collect(Collectors.toList());
                //查询是否有状态更新的redis缓存 有几个变化 查询就多查几条
                size += commonRedisUpdateResultVOList.size();
                queryPageQo.setSize(size);
            }

            if (CollectionUtil.isNotEmpty(commonRedisUpdateResultVOList)) {
                //过滤变化前状态的订单编号集合 符合则删除
                List<CommonRedisResultVO> removeList = commonRedisUpdateResultVOList.stream().filter(c -> ObjectUtil.equals(c.getStatus(), OperateExceptionEnums.NO_HANDLE)).collect(Collectors.toList());
                list.removeIf(p1 -> removeList.stream().anyMatch(p2 ->
                        p1.getId().equals(p2.getUnionNo())));
            }

            List<CscWarnExceptionVO> subList = list.stream().limit(size).collect(Collectors.toList());

            EsPageVO<CscWarnExceptionVO> esPageRes = new EsPageVO<>();
            esPageRes.setTotal(count);
            esPageRes.setList(subList);
            return RestResponse.success(esPageRes);
        } catch (Exception e) {
            log.error("csc异常运单模糊查询异常.search error:", e);
        }
        return RestResponse.error(ExcepitonEnum.ERROR_UNKNOW);
    }

    /**
     * 查询条件组装
     *
     * @param queryPageQo 请求参数
     * @return LambdaEsQueryWrapper
     */
    private SearchSourceBuilder generateSearchSourceBuilder(CscWarnExceptionEsPageQO queryPageQo) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder mainBoolQueryBuilder = QueryBuilders.boolQuery();

        Long companyId = queryPageQo.getCompanyId();
        if (ObjectUtil.isNotNull(companyId)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(CscWarnExceptionIndex::getCompanyId),companyId));
        }

        //过滤司机上报异常类型 0-司机上报异常\r\n1-停留预警\r\n2-偏离预警\r\n3-离线预警\r\n4-高危围栏预警\r\n5-司机签收量与地磅量差异异常\r\n6-围栏外签收异常\r\n7-收货地两公里外签收异常\r\n8-进出厂磅单不齐全异常\r\n9-轨迹缺失异常\r\n10-轨迹不完整异常\r\n11-超速异常\r\n12-超载异常\r\n13-运输时长异常
        mainBoolQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(CscWarnExceptionIndex::getExceptionType),0));

        //运单号
        String dispatchNo = queryPageQo.getDispatchCarNo();
        if (StrUtil.isNotEmpty(dispatchNo)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,dispatchNo,true,List.of(FieldUtils.val(CscWarnExceptionIndex::getDispatchCarNo)));
        }
        //子运单号
        String subWaybillNo = queryPageQo.getSubWaybillNo();
        if (StrUtil.isNotEmpty(subWaybillNo)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,subWaybillNo,true,List.of(FieldUtils.val(CscWarnExceptionIndex::getSubWaybillNo)));
        }
        //公司名称
        String companyName = queryPageQo.getCompanyName();
        if (StrUtil.isNotBlank(companyName)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,companyName,true,List.of(FieldUtils.val(CscWarnExceptionIndex::getCompanyName)));
        }
        //司机姓名
        String driverName = queryPageQo.getDriverName();
        if (StrUtil.isNotBlank(driverName)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,driverName,true,List.of(FieldUtils.val(CscWarnExceptionIndex::getDriverName)));
        }
        //车牌号
        String carNo = queryPageQo.getCarNo();
        if (StrUtil.isNotBlank(carNo)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,carNo,true,List.of(FieldUtils.val(CscWarnExceptionIndex::getCarNo)));
        }
        //承运商名称
        String carrierName = queryPageQo.getCarrierName();
        if (StrUtil.isNotBlank(carrierName)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,carrierName,true,List.of(FieldUtils.val(CscWarnExceptionIndex::getCarrierName)));
        }
        //发货地址
        String fmAddress = queryPageQo.getFmAddress();
        if (StrUtil.isNotEmpty(fmAddress)) {
            List<String> columns = Lists.newArrayList();
            columns.add("sender.address");
            columns.add("sender.addressName");
            columns.add("sender.fullAddress");
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,fmAddress,true,columns);
        }
        //收货人地址
        String toAddress = queryPageQo.getToAddress();
        if (StrUtil.isNotEmpty(toAddress)) {
            List<String> columns = Lists.newArrayList();
            columns.add("receiver.address");
            columns.add("receiver.addressName");
            columns.add("receiver.fullAddress");
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,toAddress,true,columns);
        }
        //异常类型
        List<Integer> exceptionTypeList = queryPageQo.getExceptionTypeList();
        if (CollectionUtil.isNotEmpty(exceptionTypeList)){
            mainBoolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(CscWarnExceptionIndex::getExceptionType),exceptionTypeList));
        }
        //预警时间比较
        String exceptionTimeStart = queryPageQo.getExceptionStartTime();
        String exceptionTimeEnd = queryPageQo.getExceptionEndTime();
        if (StrUtil.isNotEmpty(exceptionTimeStart)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(CscWarnExceptionIndex::getExceptionTime))
                    .gte(exceptionTimeStart));
        }
        if (StrUtil.isNotEmpty(exceptionTimeEnd)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(CscWarnExceptionIndex::getExceptionTime))
                    .lte(exceptionTimeEnd));
        }
        //处理状态
        Integer operateException = queryPageQo.getOperateException();
        if (ObjectUtil.isNotNull(operateException)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(CscWarnExceptionIndex::getOperateException),operateException));
        }
        //处理人
        String operatorName = queryPageQo.getOperatorName();
        if (StrUtil.isNotBlank(operatorName)){
            matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder,operatorName,true,List.of(FieldUtils.val(CscWarnExceptionIndex::getOperatorName)));
        }
        //处理时间比较
        String operateExceptionTimeStart = queryPageQo.getOperateExceptionStartTime();
        String operateExceptionTimeEnd = queryPageQo.getOperateExceptionEndTime();
        if (StrUtil.isNotEmpty(operateExceptionTimeStart)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(CscWarnExceptionIndex::getOperateExceptionTime))
                    .gte(operateExceptionTimeStart));
        }
        if (StrUtil.isNotEmpty(operateExceptionTimeEnd)){
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(CscWarnExceptionIndex::getOperateExceptionTime))
                    .lte(operateExceptionTimeEnd));
        }
        //异常处理结果
        if(ObjectUtil.isNotNull(queryPageQo.getOperateType())){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(CscWarnExceptionIndex::getOperateType),queryPageQo.getOperateType()));
        }
        //订单类型
        if(ObjectUtil.isNotNull(queryPageQo.getWaybillType())){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(CscWarnExceptionIndex::getOrderType),queryPageQo.getWaybillType()));
        }

        Integer from = queryPageQo.getPage();
        Integer size = queryPageQo.getSize();

        int fromElasticsearch = (from == 0 ? from : from - 1) * size;
        sourceBuilder.size(size);
        sourceBuilder.from(fromElasticsearch);
        sourceBuilder.query(mainBoolQueryBuilder);
        //默认倒序排序
        SortOrder sortOrder = SortOrder.DESC;
        sourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(CscWarnExceptionIndex::getUpdateTime)).order(sortOrder));
        sourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
        sourceBuilder.trackTotalHits(true);

        return sourceBuilder;
    }

    @Override
    public String getSearchType() {
        return SearchTypeEnums.CSC_WARN_EXCEPTION_PAGE.getType();
    }

    @Override
    public String getIndex() {
        return esIndexProperties.getCscWarnExceptionIndex();
    }


}
