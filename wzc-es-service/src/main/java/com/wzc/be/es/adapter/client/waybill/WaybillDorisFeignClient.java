package com.wzc.be.es.adapter.client.waybill;


import com.wzc.be.data.data.waybill.model.dto.WaybillQueryEntityDTO;
import com.wzc.be.data.data.waybill.model.qo.*;
import com.wzc.be.es.adapter.client.waybill.feign.WaybillDorisSearchFeign;
import com.wzc.common.core.api.utils.ApiResultUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName WaybillDorisFeignClient
 * <AUTHOR>
 * @Date 2024/2/1 18:53
 * @Description
 * @Version 1.0
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class WaybillDorisFeignClient {
    private final WaybillDorisSearchFeign waybillDorisSearchFeign;

    /***
     *@Description 承运商APP运单列表Doris查询
     *@Param [com.wzc.be.data.data.waybill.model.qo.CarrierWaybillListQO]
     *@Return com.baidu.mapcloud.cloudnative.common.model.RestResponse<java.util.List<com.wzc.be.data.data.waybill.model.vo.CarrierWaybillListVO>>
     *@Date 2024/2/1 10:38
     *<AUTHOR>
     **/
    public List<WaybillQueryEntityDTO> carrierAppOrderListSearch(CarrierAppOrderListQO qo){
        return ApiResultUtils.getDataIgnoreNull(waybillDorisSearchFeign.carrierAppOrderListSearch(qo));
    }

    /***
     *@Description 承运商PC运单列表Doris查询
     *@Param [com.wzc.be.data.data.waybill.model.qo.CarrierWaybillListQO]
     *@Return com.baidu.mapcloud.cloudnative.common.model.RestResponse<java.util.List<com.wzc.be.data.data.waybill.model.vo.CarrierWaybillListVO>>
     *@Date 2024/2/1 10:38
     *<AUTHOR>
     **/
    public List<WaybillQueryEntityDTO> carrierWaybillQueryAfterHandlerSearch(CarrierWaybillListQO qo){
        return ApiResultUtils.getDataIgnoreNull(waybillDorisSearchFeign.carrierWaybillQueryAfterHandlerSearch(qo));
    }

    /****
     *@Description 承运商PC运单列表Doris查询
     *@Param [com.wzc.be.data.data.waybill.model.qo.CarrierWaybillListQO]
     *@Return com.baidu.mapcloud.cloudnative.common.model.RestResponse<java.util.List<com.wzc.be.data.data.waybill.model.vo.CarrierWaybillListVO>>
     *@Date 2024/2/1 10:39
     *<AUTHOR>
     **/
    public List<WaybillQueryEntityDTO> carrierWaybillQueryListHandlerSearch(CarrierWaybillListQO qo){
        return ApiResultUtils.getDataIgnoreNull(waybillDorisSearchFeign.carrierWaybillQueryListHandlerSearch(qo));
    }

    /***
     *@Description 异常运单相关列表Doris查询
     *@Param [com.wzc.be.data.data.waybill.model.qo.ExceptionWaybillListQO]
     *@Return com.baidu.mapcloud.cloudnative.common.model.RestResponse<java.util.List<com.wzc.be.data.data.waybill.model.vo.SubWaybillVO>>
     *@Date 2024/2/1 10:39
     *<AUTHOR>
     **/
    public List<WaybillQueryEntityDTO> exceptionWaybillHandlerSearch(ExceptionWaybillListQO qo){
        return ApiResultUtils.getDataIgnoreNull(waybillDorisSearchFeign.exceptionWaybillHandlerSearch(qo));
    }

    /***
     *@Description 小程序验货人运单列表Doris查询
     *@Param [com.wzc.be.data.data.waybill.model.qo.InspectorListQO]
     *@Return com.baidu.mapcloud.cloudnative.common.model.RestResponse<java.util.List<com.wzc.be.data.data.waybill.model.vo.InspectorWaybillListVO>>
     *@Date 2024/2/1 10:39
     *<AUTHOR>
     **/
    public List<WaybillQueryEntityDTO> inspectorWaybillQueryListHandlerSearch(InspectorListQO qo){
        return ApiResultUtils.getDataIgnoreNull(waybillDorisSearchFeign.inspectorWaybillQueryListHandlerSearch(qo));
    }

    /***
     *@Description 货主PC运单列表Doris查询
     *@Param [com.wzc.be.data.data.waybill.model.qo.OwnerWaybillListQO]
     *@Return com.baidu.mapcloud.cloudnative.common.model.RestResponse<java.util.List<com.wzc.be.data.data.waybill.model.vo.SubWaybillVO>>
     *@Date 2024/2/1 10:39
     *<AUTHOR>
     **/
    public List<WaybillQueryEntityDTO> ownerWaybillQueryListAfterHandlerSearch(OwnerWaybillListQO qo){
        return ApiResultUtils.getDataIgnoreNull(waybillDorisSearchFeign.ownerWaybillQueryListAfterHandlerSearch(qo));
    }

    /***
     *@Description 货主PC运单列表Doris查询
     *@Param [com.wzc.be.data.data.waybill.model.qo.OwnerWaybillListQO]
     *@Return com.baidu.mapcloud.cloudnative.common.model.RestResponse<java.util.List<com.wzc.be.data.data.waybill.model.vo.SubWaybillVO>>
     *@Date 2024/2/1 10:39
     *<AUTHOR>
     **/
    public List<WaybillQueryEntityDTO> ownerWaybillQueryListHandlerSearch(OwnerWaybillListQO qo){
        return ApiResultUtils.getDataIgnoreNull(waybillDorisSearchFeign.ownerWaybillQueryListAfterHandlerSearch(qo));
    }

    /***
     *@Description 司机运单列表Doris查询
     *@Param [com.wzc.be.data.data.waybill.model.qo.DriverWaybillListQO]
     *@Return com.baidu.mapcloud.cloudnative.common.model.RestResponse<java.util.List<com.wzc.be.data.data.waybill.model.vo.WaybillQueryVO>>
     *@Date 2024/2/1 10:39
     *<AUTHOR>
     **/
    public List<WaybillQueryEntityDTO> waybillQueryListHandlerSearch(DriverWaybillListQO qo){
        return ApiResultUtils.getDataIgnoreNull(waybillDorisSearchFeign.waybillQueryListHandlerSearch(qo));
    }
}
