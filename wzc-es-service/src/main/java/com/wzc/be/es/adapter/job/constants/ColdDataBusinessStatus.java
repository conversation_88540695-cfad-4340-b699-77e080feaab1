package com.wzc.be.es.adapter.job.constants;

/**
 * 冷数据业务状态常量
 * 定义冷数据迁移时的业务状态判断条件
 *
 * <AUTHOR> Assistant
 * @since 2025/02/18
 */
public class ColdDataBusinessStatus {

    /**
     * 订单状态常量
     */
    public static class OrderStatus {
        /** 草稿 */
        public static final int DRAFT = 1;
        /** 待派车 */
        public static final int PENDING_DISPATCH = 2;
        /** 运输中 */
        public static final int IN_TRANSIT = 3;
        /** 已完成 - 可以迁移到冷数据 */
        public static final int COMPLETED = 6;
        /** 已取消 */
        public static final int CANCELLED = 7;
    }

    /**
     * 子运单状态常量
     */
    public static class SubWaybillStatus {
        /** 待派车 */
        public static final int PENDING_DISPATCH = 1;
        /** 已派车 */
        public static final int DISPATCHED = 2;
        /** 运输中 */
        public static final int IN_TRANSIT = 3;
        /** 已完成 - 可以迁移到冷数据 */
        public static final int COMPLETED = 6;
        /** 已取消 */
        public static final int CANCELLED = 7;
    }

    /**
     * 结算状态常量
     * 对应 completeStatus 和 completeDownStatus 字段
     */
    public static class SettlementStatus {
        /** 无需结算 */
        public static final int NO_SETTLEMENT = 0;
        /** 运输未完成 */
        public static final int TRANSPORT_INCOMPLETE = 1;
        /** 待结算 */
        public static final int PENDING_SETTLEMENT = 3;
        /** 已结算 */
        public static final int SETTLED = 4;
        /** 已支付 - 可以迁移到冷数据 */
        public static final int PAID = 5;
        /** 未对账 */
        public static final int NOT_RECONCILED = 6;
        /** 已对账未结算 */
        public static final int RECONCILED_NOT_SETTLED = 7;
        /** 已结算待支付 */
        public static final int SETTLED_PENDING_PAYMENT = 8;
    }

    /**
     * 冷数据迁移业务规则说明
     */
    public static class MigrationRules {
        
        /**
         * 订单迁移规则
         * - 订单状态必须为已完成 (status = 6)
         * - 创建时间超过保留期限
         */
        public static final String ORDER_RULE = "订单状态=已完成(6) AND 创建时间 < 保留期限";
        
        /**
         * 子运单迁移规则
         * - 子运单状态必须为已完成 (subWaybillStatus = 6)
         * - 必须已签收 (ownerSignTime 不为空)
         * - 对上结算状态为无需结算(0)或已支付(5)
         * - 对下结算状态为无需结算(0)或已支付(5)
         * - 创建时间超过保留期限
         */
        public static final String SUB_WAYBILL_RULE = 
            "子运单状态=已完成(6) AND 已签收 AND (对上结算=无需结算(0)或已支付(5)) AND (对下结算=无需结算(0)或已支付(5)) AND 创建时间 < 保留期限";
        
        /**
         * 运输计划迁移规则
         * - 仅基于时间过滤，无业务状态限制
         * - 创建时间超过保留期限
         */
        public static final String TRANSPORT_RULE = "创建时间 < 保留期限";
        
        /**
         * 分组运输迁移规则
         * - 仅基于时间过滤，无业务状态限制
         * - 创建时间超过保留期限
         */
        public static final String GROUP_TRANSPORT_RULE = "创建时间 < 保留期限";
        
        /**
         * 异常预警迁移规则
         * - 仅基于时间过滤，无业务状态限制
         * - 更新时间超过保留期限
         */
        public static final String WARN_EXCEPTION_RULE = "更新时间 < 保留期限";
    }

    /**
     * 判断订单是否可以迁移到冷数据
     * @param orderStatus 订单状态
     * @return 是否可以迁移
     */
    public static boolean canMigrateOrder(Integer orderStatus) {
        return OrderStatus.COMPLETED == orderStatus;
    }

    /**
     * 判断子运单是否可以迁移到冷数据
     * @param subWaybillStatus 子运单状态
     * @param ownerSignTime 货主签收时间
     * @param completeStatus 对上结算状态
     * @param completeDownStatus 对下结算状态
     * @return 是否可以迁移
     */
    public static boolean canMigrateSubWaybill(Integer subWaybillStatus, Object ownerSignTime, 
                                               Integer completeStatus, Integer completeDownStatus) {
        // 1. 子运单必须已完成
        if (!SubWaybillStatus.COMPLETED.equals(subWaybillStatus)) {
            return false;
        }
        
        // 2. 必须已签收
        if (ownerSignTime == null) {
            return false;
        }
        
        // 3. 对上结算状态检查：无需结算或已支付
        if (completeStatus != null && completeStatus > SettlementStatus.NO_SETTLEMENT) {
            if (!SettlementStatus.PAID.equals(completeStatus)) {
                return false;
            }
        }
        
        // 4. 对下结算状态检查：无需结算或已支付
        if (completeDownStatus != null && completeDownStatus > SettlementStatus.NO_SETTLEMENT) {
            if (!SettlementStatus.PAID.equals(completeDownStatus)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 获取业务状态描述
     * @param indexName 索引名称
     * @return 业务状态描述
     */
    public static String getBusinessRuleDescription(String indexName) {
        if (indexName.contains("order_index")) {
            return MigrationRules.ORDER_RULE;
        } else if (indexName.contains("sub_waybill_index")) {
            return MigrationRules.SUB_WAYBILL_RULE;
        } else if (indexName.contains("transport_index")) {
            return MigrationRules.TRANSPORT_RULE;
        } else if (indexName.contains("group_transport_index")) {
            return MigrationRules.GROUP_TRANSPORT_RULE;
        } else if (indexName.contains("warn_exception_index")) {
            return MigrationRules.WARN_EXCEPTION_RULE;
        } else {
            return "仅基于时间过滤";
        }
    }
}
