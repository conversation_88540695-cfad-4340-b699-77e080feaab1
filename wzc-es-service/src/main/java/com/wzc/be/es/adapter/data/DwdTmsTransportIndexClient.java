package com.wzc.be.es.adapter.data;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.data.data.es.transport.dto.TransportEsDTO;
import com.wzc.be.data.data.es.transport.dto.TransportGroupDTO;
import com.wzc.be.data.data.es.transport.dto.TransportQueryPageDTO;
import com.wzc.be.es.adapter.data.feign.DwdTmsGroupTransportIndexFeign;
import com.wzc.be.es.adapter.data.feign.DwdTmsTransportIndexFeign;
import com.wzc.be.es.common.utils.DataUtils;
import com.wzc.be.es.core.common.CommonRedisResultVO;
import com.wzc.be.es.core.transport.converter.TransportConverter;
import com.wzc.be.es.core.transport.index.TransportIndex;
import com.wzc.be.es.data.transport.qo.TransportQueryPageQO;
import com.wzc.be.es.data.transport.vo.TransportEsVO;
import com.wzc.be.es.data.transport.vo.TransportGroupVO;
import com.wzc.common.core.api.utils.ApiResultUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class DwdTmsTransportIndexClient {

    private final DwdTmsTransportIndexFeign dwdTmsTransportIndexFeign;

    private final DwdTmsGroupTransportIndexFeign dwdTmsGroupTransportIndexFeign;

    public List<TransportEsVO> appSearch(@RequestBody TransportQueryPageQO transportQueryPageQo){
        TransportQueryPageDTO transportQueryPageDTO = TransportConverter.INSTANCE.pageQOToDTO(transportQueryPageQo);
        RestResponse<List<TransportEsDTO>> search = dwdTmsTransportIndexFeign.appSearch(transportQueryPageDTO);
        List<TransportEsDTO> transportEsDTOList = ApiResultUtils.getDataOrThrow(search);
        return TransportConverter.INSTANCE.transportDTOListToVO(transportEsDTOList);
    }

    public List<TransportEsVO> search(@RequestBody TransportQueryPageQO transportQueryPageQo,List<CommonRedisResultVO> commonRedisResultVOList){
        TransportQueryPageDTO transportQueryPageDTO = TransportConverter.INSTANCE.pageQOToDTO(transportQueryPageQo);
        transportQueryPageDTO.setCommonRedisResultVOList(DataUtils.esRedisResultVOToDataDTO(commonRedisResultVOList));
        RestResponse<List<TransportEsDTO>> search = dwdTmsTransportIndexFeign.search(transportQueryPageDTO);
        List<TransportEsDTO> transportEsDTOList = ApiResultUtils.getDataOrThrow(search);
        return TransportConverter.INSTANCE.transportDTOListToVO(transportEsDTOList);
    }

    public List<TransportIndex> groupSearch(@RequestBody TransportQueryPageQO transportQueryPageQo, List<CommonRedisResultVO> commonRedisResultVOList){
        TransportQueryPageDTO transportQueryPageDTO = TransportConverter.INSTANCE.pageQOToDTO(transportQueryPageQo);
        RestResponse<List<TransportGroupDTO>> search = dwdTmsGroupTransportIndexFeign.groupTransport(transportQueryPageDTO);
        List<TransportGroupDTO> transportGroupDTO = ApiResultUtils.getDataOrThrow(search);
        return TransportConverter.INSTANCE.groupDTOListToIndexList(transportGroupDTO);
    }

}
