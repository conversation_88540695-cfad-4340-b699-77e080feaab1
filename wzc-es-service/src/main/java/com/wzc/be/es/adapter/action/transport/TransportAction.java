package com.wzc.be.es.adapter.action.transport;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.api.transport.EsTransportSearchApi;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.core.transport.service.EsTransportSearchService;
import com.wzc.be.es.data.transport.qo.TransportQueryPageQO;
import com.wzc.be.es.data.transport.vo.TransportEsVO;
import com.wzc.be.es.data.transport.vo.TransportGroupVO;
import com.wzc.be.es.data.transport.vo.TransportStatusCountVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年08月11日
 */
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class TransportAction implements EsTransportSearchApi {

    private final EsTransportSearchService transportSearchService;


    @Override
    public RestResponse<EsPageVO<TransportEsVO>> transportSearch(TransportQueryPageQO requestPageQo) {
        return transportSearchService.transportSearch(requestPageQo);
    }

    @Override
    public RestResponse<EsPageVO<TransportGroupVO>> transportGroupSearch(TransportQueryPageQO requestPageQo) {
        return transportSearchService.transportGroupSearch(requestPageQo);
    }

    @Override
    public RestResponse<EsPageVO<TransportGroupVO>> transportGroupNoSearch(TransportQueryPageQO requestPageQo) {
        return transportSearchService.transportGroupNoSearch(requestPageQo);
    }

    @Override
    public RestResponse<EsPageVO<TransportEsVO>> appTransportSearch(TransportQueryPageQO requestPageQo) {
        return transportSearchService.appTransportSearch(requestPageQo);
    }

    @Override
    public RestResponse<TransportStatusCountVO> transportCount(TransportQueryPageQO requestPageQo) {
        return transportSearchService.transportCount(requestPageQo);
    }

    @Override
    public RestResponse<List<TransportEsVO>> transportListSearch(TransportQueryPageQO queryPageQO) {
        return transportSearchService.transportList(queryPageQO);
    }

    @Override
    public RestResponse<Boolean> transportUpdate(TransportQueryPageQO queryPageQO) {
        return transportSearchService.transportUpdate(queryPageQO);
    }
}
