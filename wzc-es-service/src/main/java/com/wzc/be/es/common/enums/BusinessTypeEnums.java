package com.wzc.be.es.common.enums;

import com.baidu.mapcloud.cloudnative.common.model.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BusinessTypeEnums implements IEnum {

    /**
     * 业务类型（1货主自营 2平台总包 3平台自营 4网络货运）
     */
    OWNER_OPERATED(1, "货主自营"),
    PLATFORM_PACKAGE(2, "平台总包"),
    PLATFORM_SELF_SUPPORT(3, "平台自营"),
    NETWORK_FREIGHT(4, "网络货运"),
    ;

    private final Integer code;
    private final String name;
}
