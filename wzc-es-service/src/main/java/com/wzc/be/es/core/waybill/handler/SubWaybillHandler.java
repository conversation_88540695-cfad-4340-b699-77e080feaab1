package com.wzc.be.es.core.waybill.handler;

import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import cn.hutool.core.util.StrUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.core.waybill.converter.WaybillConverter;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.core.waybill.index.WaybillQueryIndex;
import com.wzc.be.es.core.waybill.mapper.SubWaybillListMapper;
import com.wzc.be.es.core.waybill.mapper.WaybillQueryListMapper;
import com.wzc.be.es.data.waybill.qo.OmsSearchWaybillQO;
import com.wzc.be.es.data.waybill.qo.SubWaybillQO;
import com.wzc.be.es.data.waybill.vo.SubWaybillVO;
import com.wzc.be.es.data.waybill.vo.WaybillQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class SubWaybillHandler implements BaseSearchHandler<SubWaybillQO, List<SubWaybillVO>> {

    @Value("${subWaybill.search.type:subWaybillHandler}")
    private String searchType;

    @Resource
    private EsIndexProperties esIndexProperties;

    @Resource
    private SubWaybillListMapper subWaybillListMapper;

    @Override
    public RestResponse<List<SubWaybillVO>> search(SubWaybillQO qo) {
        LambdaEsQueryWrapper<SubWaybillIndex> wrapper = new LambdaEsQueryWrapper<>();
        this.genSearch(wrapper, qo);
        wrapper.index(esIndexProperties.getSubWaybillIndex());
        List<SubWaybillIndex> list = subWaybillListMapper.selectList(wrapper);
        List<SubWaybillVO> data = WaybillConverter.INSTANCE.toSubWaybillVOList(list);
        return RestResponse.success(data);
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.esIndexProperties.getSubWaybillIndex();
    }

    /**
     * 构建查询条件
     */
    private void genSearch(LambdaEsQueryWrapper<SubWaybillIndex> wrapper, SubWaybillQO qo) {
        wrapper.isNotNull(SubWaybillIndex::getSubWaybillNo);
        if(CollectionUtils.isNotEmpty(qo.getParentNoList())){
            wrapper.in(SubWaybillIndex::getParentNo, qo.getParentNoList());
        }

        if(StringUtils.isNotBlank(qo.getSubWaybillNo())){
            wrapper.eq(SubWaybillIndex::getSubWaybillNo, qo.getSubWaybillNo());
        }

        if(CollectionUtils.isNotEmpty(qo.getSubWaybillNoList())){
            wrapper.in(SubWaybillIndex::getSubWaybillNo, qo.getSubWaybillNoList());
        }

        if (StrUtil.isNotEmpty(qo.getDispatchCarNo())){
            wrapper.eq(SubWaybillIndex::getWaybillNo,qo.getDispatchCarNo());
        }

        if(CollectionUtils.isNotEmpty(qo.getDispatchCarNoList())){
            wrapper.in(SubWaybillIndex::getWaybillNo, qo.getDispatchCarNoList());
        }
    }
}
