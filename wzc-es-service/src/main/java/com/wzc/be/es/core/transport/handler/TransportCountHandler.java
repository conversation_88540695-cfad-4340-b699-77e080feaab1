package com.wzc.be.es.core.transport.handler;

import cn.easyes.core.toolkit.FieldUtils;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.data.waybill.qo.OrderCountQO;
import com.wzc.be.es.data.waybill.qo.TransportCountQO;
import com.wzc.be.es.data.waybill.vo.OrderCountVO;
import com.wzc.be.es.data.waybill.vo.TransportCountVO;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.ParsedCardinality;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class TransportCountHandler implements BaseSearchHandler<TransportCountQO, List<TransportCountVO>> {

    @Value("${planOrderCount.search.type:transportNoCount}")
    private String searchType;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private EsIndexProperties esIndexProperties;

    @Override
    public RestResponse<List<TransportCountVO>> search(TransportCountQO qo) {
        // 创建搜索请求
        SearchRequest searchRequest = new SearchRequest(esIndexProperties.getSubWaybillIndex());
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder mainBoolQueryBuilder = QueryBuilders.boolQuery();
        mainBoolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getParentNo),qo.getTransportNo()));
        // 构建查询条件（可选）
        // 这里我们假设要对 "category" 字段进行分组统计
        searchSourceBuilder.query(mainBoolQueryBuilder);
        // 构建分组聚合
        // 使用 Terms 聚合按 "category" 字段进行分组
        searchSourceBuilder.aggregation(
                AggregationBuilders.terms("group_by_category").field("parentNo")
                        // 在每个分组上添加基数聚合，统计每个分组中 "subCategory" 字段的唯一值数量
                        .subAggregation(AggregationBuilders.cardinality("distinct_subCategories").field("subWaybillNo")).size(Integer.MAX_VALUE
        ));
        // 设置返回结果大小（可选）
        searchSourceBuilder.size(50); // 不需要返回文档，只返回聚合结果
        // 设置排序（可选）
        // 如果需要对结果进行排序，可以在这里设置
        // 将 SearchSourceBuilder 添加到 SearchRequest
        searchRequest.source(searchSourceBuilder);
        // 执行搜索请求
        SearchResponse searchResponse = null;
        try {
            searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("查询sub_waybill_index01异常",e);
        }
        // 解析聚合结果
        Terms groupByCategory = searchResponse.getAggregations().get("group_by_category");
        List<TransportCountVO> resultList = new ArrayList<>();
        for (Terms.Bucket bucket : groupByCategory.getBuckets()) {
            // 获取分组字段值
            String transportNo = bucket.getKeyAsString();
            // 获取基数聚合结果，即每个分组中 "subCategory" 字段的唯一值数量
            long dispatchCount = ((ParsedCardinality) bucket.getAggregations().get("distinct_subCategories")).getValue();
            TransportCountVO transportCountVO = new TransportCountVO();
            transportCountVO.setTransportNo(transportNo);
            transportCountVO.setDispatchCount(dispatchCount);
            resultList.add(transportCountVO);
        }
        return RestResponse.success(resultList);
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.esIndexProperties.getSubWaybillIndex();
    }

}
