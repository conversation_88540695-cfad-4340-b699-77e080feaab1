package com.wzc.be.es.adapter.action.netcargo;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.api.netcargo.EsNetCargoSearchApi;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.core.business.service.EsBmsBusinessSearchService;
import com.wzc.be.es.core.netcargo.service.NetCargoSearchService;
import com.wzc.be.es.data.business.qo.BmsEsBusinessQueryPageQO;
import com.wzc.be.es.data.business.vo.BmsEsAuditStatusCountVO;
import com.wzc.be.es.data.business.vo.BmsEsRcStatusCountVO;
import com.wzc.be.es.data.netcargo.qo.NetCargoEsQO;
import com.wzc.be.es.data.netcargo.vo.NetCargoCountEsVO;
import com.wzc.be.es.data.netcargo.vo.NetCargoEsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class NetCargoAction implements EsNetCargoSearchApi {

    private final NetCargoSearchService netCargoSearchService;

    /**
     * 发单管理分页查询
     *
     * @param qo
     * @return
     */
    @Override
    public RestResponse<EsPageVO<NetCargoEsVO>> page(NetCargoEsQO qo) {
        return netCargoSearchService.page(qo);
    }

    /**
     * 发单管理状态统计
     *
     * @param qo
     * @return
     */
    @Override
    public RestResponse<NetCargoCountEsVO> count(NetCargoEsQO qo) {
        return netCargoSearchService.count(qo);
    }
}
