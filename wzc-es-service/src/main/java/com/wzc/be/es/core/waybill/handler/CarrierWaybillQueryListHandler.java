package com.wzc.be.es.core.waybill.handler;


import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baidu.mapcloud.cloudnative.authentication.data.enums.AuthOptionsEnum;
import com.baidu.mapcloud.cloudnative.authentication.datapermission.core.entity.OtherQueryParam;
import com.baidu.mapcloud.cloudnative.authentication.datapermission.core.rule.UserPermissionRule;
import com.baidu.mapcloud.cloudnative.authentication.datapermission.enums.DataRuleValueEnum;
import com.baidu.mapcloud.cloudnative.common.model.PageOrderType;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.baidu.mapcloud.cloudnative.common.model.UserContext;
import com.wzc.be.data.data.waybill.model.dto.WaybillQueryEntityDTO;
import com.wzc.be.es.adapter.client.waybill.WaybillDorisFeignClient;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.constant.Constants;
import com.wzc.be.es.common.enums.SubWaybillStatusEnum;
import com.wzc.be.es.common.enums.WaybillCompleteStatusEnum;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.commons.PageVO;
import com.wzc.be.es.core.waybill.converter.DorisQueryWaybillConverter;
import com.wzc.be.es.core.waybill.converter.WaybillConverter;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.data.waybill.enums.CarrierTabTypeEnum;
import com.wzc.be.es.data.waybill.qo.CarrierWaybillListQO;
import com.wzc.be.es.data.waybill.vo.CarrierWaybillListVO;
import com.wzc.common.user.utils.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.ExistsQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 承运商PC运单列表
 *
 * <AUTHOR> Wei
 */
@Slf4j
@Component
public class CarrierWaybillQueryListHandler implements BaseSearchHandler<CarrierWaybillListQO, PageVO<CarrierWaybillListVO>> {

    @Value("${carrierWaybillQuery.search.type:carrierWaybillQueryListHandler}")
    private String searchType;

    @Resource
    private EsIndexProperties esIndexProperties;

    @Resource
    protected UserPermissionRule userPermissionRule;

    @Resource
    private RestHighLevelClient restHighLevelClient;

    @Override
    public RestResponse<PageVO<CarrierWaybillListVO>> search(CarrierWaybillListQO qo) {
        log.info("承运商PC运单列表查询参数:{}", JSON.toJSONString(qo));
        PageVO<CarrierWaybillListVO> page = new PageVO<>();
        page.setPage(qo.getPage());
        page.setSize(qo.getSize());
        try {


            // 构建搜索请求
            SearchRequest searchRequest = new SearchRequest(esIndexProperties.getSubWaybillIndex());
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            // 排序
            PageOrderType orderType = qo.getOrderType();
            if(orderType != null){
                switch (orderType){
                    case ASC:
                        searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(SubWaybillIndex::getCreateTime)).order(SortOrder.ASC));
                        break;
                    case DESC:
                        searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(SubWaybillIndex::getCreateTime)).order(SortOrder.DESC));
                        break;
                }
            }

            BoolQueryBuilder mainQueryBuilder = this.getMainBoolQueryBuilder(qo);
            Integer size = qo.getSize();
            Integer from = (qo.getPage() - 1) * size;

            searchSourceBuilder.from(from);
            searchSourceBuilder.size(size);
            searchSourceBuilder.query(mainQueryBuilder);

            searchSourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
            searchSourceBuilder.trackTotalHits(true);
            searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(SubWaybillIndex::getCreateTime)).order(SortOrder.DESC));
            searchRequest.source(searchSourceBuilder);
            log.info("DSL:{}",searchSourceBuilder.toString());
            List<CarrierWaybillListVO> list = new ArrayList<>();
            try {
                SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
                SearchHits searchHits = searchResponse.getHits();
                SearchHit[] hitResult = searchHits.getHits();
                JSONArray array = new JSONArray();
                Stream.of(hitResult).forEach(itemHit -> {
                    Map<String, Object> sourceAsMap = itemHit.getSourceAsMap();
                    array.add(sourceAsMap);
                });
                TotalHits totalHits = searchHits.getTotalHits();
                List<SubWaybillIndex> data = JSONUtil.toList(array.toJSONString(0), SubWaybillIndex.class);


                if (CollectionUtils.isEmpty(data)) {
                    return RestResponse.success(page);
                }

                for (SubWaybillIndex subWaybill : data) {
                    CarrierWaybillListVO record = WaybillConverter.INSTANCE.toCarrierWaybillListVO(subWaybill);
                    record.setWaybillNo(subWaybill.getWaybillNo());
                    record.setTagList(new ArrayList<>());
                    record.setCarNo(subWaybill.getCarNo());
                    record.setDriverName(subWaybill.getDriverName());
                    record.setZtDriverName(subWaybill.getZtDriverName());
                    record.setShipperName(subWaybill.getShipperCompanyName());
                    record.setStatus(subWaybill.getSubWaybillStatus());

                    List<SubWaybillIndex.Address> sender = subWaybill.getSender();
                    // 收发货地址 取值逻辑：poi地址>完整地址
                    if (CollectionUtils.isNotEmpty(sender)) {
                        for (SubWaybillIndex.Address address : sender) {
                            record.setSenderAddress(StringUtils.isNotBlank(address.getAddress()) ? address.getAddress() : address.getFullAddress());
                        }
                    }

                    List<SubWaybillIndex.Address> receiver = subWaybill.getReceiver();
                    // 收发货地址 取值逻辑：poi地址>完整地址
                    if (CollectionUtils.isNotEmpty(receiver)) {
                        for (SubWaybillIndex.Address address : receiver) {
                            record.setReceiverAddress(StringUtils.isNotBlank(address.getAddress()) ? address.getAddress() : address.getFullAddress());
                        }
                    }

                    record.setSettleStatus(subWaybill.getCompleteStatus());
                    record.setCompleteStatus(subWaybill.getCompleteStatus());
                    record.setCompleteDownStatus(subWaybill.getCompleteDownStatus());
                    record.setDispatchDate(subWaybill.getCreateTime());
                    record.setPlanOrderNo(subWaybill.getParentNo());
                    record.setOrderNo(subWaybill.getOrderNo());
                    record.setSubWaybillNo(subWaybill.getSubWaybillNo());
                    record.setMerge(0);

                    record.setOldWaybillNo(subWaybill.getOldWaybillNo());
                    record.setOldSubWaybillNo(subWaybill.getOldSubWaybillNo());
                    record.setWaybillStartTime(subWaybill.getTransStartTime());
                    record.setUnloadConfirmTime(subWaybill.getTransEndTime());
                    record.setUnloadSignTime(subWaybill.getUnloadSignInTime());
                    record.setLoadSignTime(subWaybill.getLoadSignInTime());
                    record.setTransportType(subWaybill.getTransportType());
                    record.setIsEvaluate(Objects.isNull(subWaybill.getIsEvaluate()) ? 0 : subWaybill.getIsEvaluate());

                    record.setPayStatementNo(subWaybill.getPayStatementNo());
                    record.setReStatementNo(subWaybill.getReStatementNo());
                    record.setPayOrderNo(subWaybill.getPayOrderNo());
                    record.setReOrderNo(subWaybill.getReOrderNo());
                    record.setPayApplyNo(subWaybill.getPayApplyNo());
                    record.setReceiptNo(subWaybill.getReceiptNo());

                    if(CollectionUtils.isNotEmpty(subWaybill.getItems())){
                        List<CarrierWaybillListVO.GoodsInfo> goodsInfoList = new ArrayList<>();
                        Map<String,SubWaybillIndex.BmsItem> bmsItemMap = new HashMap<>();
                        if(CollectionUtils.isNotEmpty(subWaybill.getBmsItems())){
                            bmsItemMap = subWaybill.getBmsItems().stream().collect(Collectors.toMap(SubWaybillIndex.BmsItem::getItemId, Function.identity(),(o1,o2) -> o1));
                        }
                        for (SubWaybillIndex.Item item : subWaybill.getItems()) {
                            CarrierWaybillListVO.GoodsInfo goodsInfo = new CarrierWaybillListVO.GoodsInfo();
                            goodsInfo.setId(item.getId());
                            goodsInfo.setItemId(item.getItemId());
                            goodsInfo.setGoodsName(item.getItemName());
                            SubWaybillIndex.BmsItem bmsItem = bmsItemMap.get(item.getItemId());
                            if(Objects.nonNull(bmsItem)){
                                goodsInfo.setHzActualSettleWeight(bmsItem.getHzActualSettleWeight());
                                goodsInfo.setHzActualSettlePrice(bmsItem.getHzActualSettlePrice());
                                goodsInfo.setCysActualSettlePrice(bmsItem.getCysActualSettlePrice());
                                goodsInfo.setCysActualSettleWeight(bmsItem.getCysActualSettleWeight());
                            }else {
                                goodsInfo.setHzActualSettleWeight("0");
                                goodsInfo.setHzActualSettlePrice("0");
                                goodsInfo.setCysActualSettlePrice("0");
                                goodsInfo.setCysActualSettleWeight("0");
                            }
                            goodsInfoList.add(goodsInfo);
                        }
                        record.setGoodsInfoList(goodsInfoList);
                    }

                    list.add(record);
                }
                page.setTotal(totalHits.value);
            }catch (Exception e){
                log.info("承运商PC运单列表查询es异常:{},开始查询doris",e);
                //如果不是 es查询异常 便往外抛
                Throwable root = ExceptionUtil.getRootCause(e);
                if(!(root instanceof ElasticsearchException)
                        || !((ElasticsearchException) root).getDetailedMessage().contains(Constants.ES_RESULT_WINDOW_IS_TOO_LARGE)){
                    throw new Exception(e);
                }
                //查询es异常获取不到总数，重新查询
                CountRequest countRequest = new CountRequest(getIndex());
                countRequest.source(searchSourceBuilder);
                CountResponse countResponse = restHighLevelClient.count(countRequest, RequestOptions.DEFAULT);
                page.setTotal(countResponse.getCount());
                List<WaybillQueryEntityDTO> waybillQueryEntityDTOList  = new ArrayList<>();
                if(CollectionUtil.isNotEmpty(waybillQueryEntityDTOList)){
                    for(WaybillQueryEntityDTO dto:waybillQueryEntityDTOList){
                        CarrierWaybillListVO record = DorisQueryWaybillConverter.INSTANCE.dorisEntityToCarrierWaybillListVo(dto);
                        record.setWaybillNo(dto.getWaybillNo());
                        record.setTagList(new ArrayList<>());
                        record.setCarNo(dto.getCarNo());
                        record.setDriverName(dto.getDriverName());
                        record.setShipperName(dto.getShipperCompanyName());
                        record.setStatus(dto.getSubWaybillStatus());

                        if(StringUtils.isNotBlank(dto.getSenderInfo())){
                            List<SubWaybillIndex.Address> sender = JSONUtil.toList(dto.getSenderInfo(),SubWaybillIndex.Address.class);
                            // 收发货地址 取值逻辑：poi地址>完整地址
                            if (CollectionUtils.isNotEmpty(sender)) {
                                for (SubWaybillIndex.Address address : sender) {
                                    record.setSenderAddress(StringUtils.isNotBlank(address.getAddress()) ? address.getAddress() : address.getFullAddress());
                                }
                            }
                        }

                        if(StringUtils.isNotBlank(dto.getReceiverInfo())){
                            List<SubWaybillIndex.Address> receiver = JSONUtil.toList(dto.getReceiverInfo(),SubWaybillIndex.Address.class);
                            // 收发货地址 取值逻辑：poi地址>完整地址
                            if (CollectionUtils.isNotEmpty(receiver)) {
                                for (SubWaybillIndex.Address address : receiver) {
                                    record.setReceiverAddress(StringUtils.isNotBlank(address.getAddress()) ? address.getAddress() : address.getFullAddress());
                                }
                            }
                        }

                        record.setSettleStatus(dto.getCompleteStatus());
                        record.setDispatchDate(dto.getCreateTime());
                        record.setPlanOrderNo(dto.getParentNo());
                        record.setOrderNo(dto.getOrderNo());
                        record.setSubWaybillNo(dto.getSubWaybillNo());
                        record.setMerge(0);

                        record.setOldWaybillNo(dto.getOldWaybillNo());
                        record.setOldSubWaybillNo(dto.getOldSubWaybillNo());
                        record.setWaybillStartTime(dto.getTransStartTime());
                        record.setUnloadConfirmTime(dto.getTransEndTime());
                        record.setUnloadSignTime(dto.getUnloadSignInTime());
                        record.setLoadSignTime(dto.getLoadSignInTime());
                        list.add(record);
                    }
                }
            }

            page.setContent(list);
            return RestResponse.success(page);
        } catch (Exception e) {
            log.error("承运商运ES单列表查询异常", e);
        }
        return RestResponse.success(page);
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return esIndexProperties.getSubWaybillIndex();
    }

    public BoolQueryBuilder getMainBoolQueryBuilder(CarrierWaybillListQO qo) {
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        // 子运单号字段必须存在
        ExistsQueryBuilder fieldName = QueryBuilders.existsQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillNo));
        mainQueryBuilder.must(fieldName);

        if(qo.getIsSync() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getIsSync), qo.getIsSync()));
        }

        // app聚合搜索
        if(StringUtils.isNotBlank(qo.getAppSearchValue())){
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getCarNo), wildcardsExp(qo.getAppSearchValue())));
            boolQuery.should(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getDriverName), wildcardsExp(qo.getAppSearchValue())));
            mainQueryBuilder.must(boolQuery);
        }

        CarrierTabTypeEnum tabType = qo.getTabType();
        if (null != tabType) {
            switch (tabType) {
                case STATUS_1:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_START.getCode()));
                    break;
                case STATUS_2:
                    BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                    boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_LOAD.getCode()));
                    boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_UNLOAD.getCode()));
                    boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_LOAD_SIGN.getCode()));
                    boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_UNLOAD_SIGN.getCode()));
                    mainQueryBuilder.must(boolQuery);
                    break;
                case STATUS_3:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_SIGN.getCode()));
                    break;
                case STATUS_4:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.FINISH.getCode()));
                    break;
                case STATUS_5:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.CANCELLED.getCode()));
                    break;
                case STATUS_6:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCompleteStatus), WaybillCompleteStatusEnum.UNRECONCILED.getCode()));
                    break;
                case STATUS_7:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), 8));
                    mainQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getIsEvaluate), 1));
                    break;
                case STATUS_8:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_ASSIGN.getCode()));
                    break;
                default:
                    break;

            }
        }

        if (CollectionUtils.isNotEmpty(qo.getPlanOrderNoList())) {
            mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getParentNo), qo.getPlanOrderNoList()));
        }else {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCarrierCompanyId), qo.getCarrierCompanyId()));

            BoolQueryBuilder boolQuery1 = QueryBuilders.boolQuery();
            boolQuery1.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCarrierCompanyId), qo.getCarrierCompanyId()));
            boolQuery1.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getFbfId), qo.getCarrierCompanyId()));
            boolQuery1.mustNot(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCarrierCompanyId), 0));

            boolQuery.should(boolQuery1);

            mainQueryBuilder.must(boolQuery);
            //mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCarrierCompanyId), qo.getCarrierCompanyId()));
        }

        // 状态
        if (CollectionUtils.isNotEmpty(qo.getStatus())) {
            mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), qo.getStatus()));
        }

        // 派车单号
        if (StringUtils.isNotBlank(qo.getWaybillNo())) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//            boolQuery.should(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getWaybillNo), wildcardsExp(qo.getWaybillNo())));
//            boolQuery.should(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillNo), wildcardsExp(qo.getWaybillNo())));
//            boolQuery.should(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getOldWaybillNo), wildcardsExp(qo.getWaybillNo())));
//            boolQuery.should(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getOldSubWaybillNo), wildcardsExp(qo.getWaybillNo())));
            matchPhraseOrWildcardMustQuery(boolQuery,qo.getWaybillNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getWaybillNo),FieldUtils.val(SubWaybillIndex::getSubWaybillNo),FieldUtils.val(SubWaybillIndex::getOldWaybillNo),FieldUtils.val(SubWaybillIndex::getOldSubWaybillNo)));
            mainQueryBuilder.must(boolQuery);
        }

        // 派车单号集合
        if(CollectionUtils.isNotEmpty(qo.getSubWaybillNoList())){
            mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getWaybillNo),qo.getSubWaybillNoList()));
        }

        // 车牌号
        if (StringUtils.isNotBlank(qo.getCarNo())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getCarNo), wildcardsExp(qo.getCarNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getCarNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getCarNo)));
        }

        // 司机姓名
        if (StringUtils.isNotBlank(qo.getDriverName())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getDriverName), wildcardsExp(qo.getDriverName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getDriverName(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getDriverName)));
        }
        // 船员姓名
        if (StringUtils.isNotBlank(qo.getZtDriverName())){
//            mainQueryBuilder.must(getMatchPhraseQuery("ztDriverName",qo.getZtDriverName()));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getZtDriverName(),true, CollectionUtil.newArrayList("ztDriverName"));
        }

        // 订单号
        if (StringUtils.isNotBlank(qo.getOrderNo())) {
            if(qo.getReqSource()==1){
                mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getOrderNo), qo.getOrderNo()));
            }else{
                mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getOrderNo), wildcardsExp(qo.getOrderNo())));
            }

        }

        // goodsName
        if (StringUtils.isNotBlank(qo.getGoodsName())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery("items.itemName", wildcardsExp(qo.getGoodsName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getGoodsName(),true, CollectionUtil.newArrayList("items.itemName"));
        }

        // 查询时间类型 1创建时间 2开启运输 3装货签到 4装货确认 5卸货签到 6卸货确认 7货主签收
        if(qo.getDateType() != null){
            switch (qo.getDateType()){
                case 1:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getCreateTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getCreateTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 2:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getTransStartTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getTransStartTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 3:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadSignInTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadSignInTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 4:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadConfirmTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadConfirmTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 5:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getUnloadSignInTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getUnloadSignInTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 6:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getTransEndTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getTransEndTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 7:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getOwnerSignTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getOwnerSignTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 8:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getOrderType),1));
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadFirstTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadFirstTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 9:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getOrderType),1));
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadSecondTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadSecondTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 10:
                    mainQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getOrderType),1));
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getUnloadFirstTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getUnloadFirstTime)).lte(qo.getEndTime()));
                    }
                    break;
                case 11:
                    mainQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getOrderType),1));
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getUnloadSecondTime)).gte(qo.getStartTime()));
                        mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getUnloadSecondTime)).lte(qo.getEndTime()));
                    }
                    break;
            }

        }

        // 运单来源
        if(Objects.nonNull(qo.getDispatchSourceNew())){
            switch (qo.getDispatchSourceNew()){
                case 0://0我找车系统指派
                    BoolQueryBuilder boolQuery0 = QueryBuilders.boolQuery();
                    boolQuery0.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),0));
                    boolQuery0.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),1));
                    boolQuery0.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),2));
                    boolQuery0.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),7));
                    mainQueryBuilder.must(boolQuery0);
                    break;
                case 3://3竞价
                    BoolQueryBuilder boolQuery3 = QueryBuilders.boolQuery();
                    boolQuery3.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),3));
                    boolQuery3.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),5));
                    boolQuery3.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),8));
                    mainQueryBuilder.must(boolQuery3);
                    break;
                case 4://4抢单
                    BoolQueryBuilder boolQuery4 = QueryBuilders.boolQuery();
                    boolQuery4.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),4));
                    boolQuery4.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),6));
                    boolQuery4.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchSource),9));
                    mainQueryBuilder.must(boolQuery4);
                    break;
            }
        }

        // 发货商
        if(StringUtils.isNotBlank(qo.getFromCustomerName())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery("sender.customerName",wildcardsExp(qo.getFromCustomerName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getFromCustomerName(),true, CollectionUtil.newArrayList("sender.customerName"));
        }

        // 收货商
        if(StringUtils.isNotBlank(qo.getToCustomerName())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery("receiver.customerName",wildcardsExp(qo.getToCustomerName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getToCustomerName(),true, CollectionUtil.newArrayList("receiver.customerName"));
        }

        // 发货地址
        if(StringUtils.isNotBlank(qo.getFromAddress())){
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//            boolQuery.should(QueryBuilders.wildcardQuery("sender.address",wildcardsExp(qo.getFromAddress())));
//            boolQuery.should(QueryBuilders.wildcardQuery("sender.fullAddress",wildcardsExp(qo.getFromAddress())));
            matchPhraseOrWildcardMustQuery(boolQuery,qo.getFromAddress(),true, CollectionUtil.newArrayList("sender.address","sender.fullAddress"));
            mainQueryBuilder.must(boolQuery);
        }

        // 收货地址
        if(StringUtils.isNotBlank(qo.getToAddress())){
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//            boolQuery.should(QueryBuilders.wildcardQuery("receiver.address",wildcardsExp(qo.getToAddress())));
//            boolQuery.should(QueryBuilders.wildcardQuery("receiver.fullAddress",wildcardsExp(qo.getToAddress())));
            matchPhraseOrWildcardMustQuery(boolQuery,qo.getToAddress(),true, CollectionUtil.newArrayList("receiver.address","receiver.fullAddress"));
            mainQueryBuilder.must(boolQuery);
        }

        // 第三方单号
        if(StringUtils.isNotBlank(qo.getThirdOrderNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getThirdOrderNo),wildcardsExp(qo.getThirdOrderNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getThirdOrderNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getThirdOrderNo)));
        }


        // 业务类型
        if(qo.getBusinessType() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getBusinessType),qo.getBusinessType()));
        }

        // 订单类型
        if(qo.getWaybillType() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getOrderType),qo.getWaybillType()));
        }

        // 北斗是否正常
        if(qo.getBdNormal() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getBdNormal),qo.getBdNormal()));
        }

        // 自动签收
        if(qo.getAutoSign() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getAutoSign),qo.getAutoSign()));
        }

        // shipperName-货主名称
        if (StringUtils.isNotBlank(qo.getShipperName())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getShipperCompanyName), wildcardsExp(qo.getShipperName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getShipperName(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getShipperCompanyName)));
        }

        // 运输类型 1-汽运 2-船运
        if(qo.getTransportType() != null) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getTransportType), qo.getTransportType()));
        }

        if (StringUtils.isNotBlank(qo.getPlanOrderNo())) {
            if(qo.getReqSource()==1){
                mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getParentNo),qo.getPlanOrderNo()));
            }else{
                mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getParentNo),wildcardsExp(qo.getPlanOrderNo())));
            }
        }

        if(CollectionUtils.isNotEmpty(qo.getWaybillNolist())){
            if(qo.getReqSource()==1){
                mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getWaybillNo),qo.getWaybillNolist()));
            }
        }

        // 评价
        if(Objects.nonNull(qo.getIsEvaluate())){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getIsEvaluate),qo.getIsEvaluate()));
        }

        // 结算状态(对上)
        if(Objects.nonNull(qo.getCompleteStatus())){
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCompleteStatus),qo.getCompleteStatus()));
            boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCompleteDownStatus),qo.getCompleteStatus()));
            mainQueryBuilder.must(boolQuery);
        }
//        // 结算状态(对下)
//        if(Objects.nonNull(qo.getCompleteDownStatus())){
//            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCompleteDownStatus),qo.getCompleteDownStatus()));
//        }

        // 应付结算单号
        if(StringUtils.isNotBlank(qo.getPayStatementNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getPayStatementNo),wildcardsExp(qo.getPayStatementNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getPayStatementNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getPayStatementNo)));
        }

        // 应收结算单号
        if(StringUtils.isNotBlank(qo.getReStatementNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getReStatementNo),wildcardsExp(qo.getReStatementNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getReStatementNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getReStatementNo)));
        }

        // 应付单号
        if(StringUtils.isNotBlank(qo.getPayOrderNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getPayOrderNo),wildcardsExp(qo.getPayOrderNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getPayOrderNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getPayOrderNo)));
        }

        // 应收单号
        if(StringUtils.isNotBlank(qo.getReOrderNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getReOrderNo),wildcardsExp(qo.getReOrderNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getReOrderNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getReOrderNo)));
        }

        // 付款申请单号
        if(StringUtils.isNotBlank(qo.getPayApplyNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getPayApplyNo),wildcardsExp(qo.getPayApplyNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getPayApplyNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getPayApplyNo)));
        }

        // 收款单号
        if(StringUtils.isNotBlank(qo.getReceiptNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getReceiptNo),wildcardsExp(qo.getReceiptNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getReceiptNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getReceiptNo)));
        }

        // 货源编号
        if(StringUtils.isNotBlank(qo.getGoodsNo())){
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery("goodsNo.keyword",wildcardsExp(qo.getGoodsNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getGoodsNo(),true, CollectionUtil.newArrayList("goodsNo"));
        }

        if (ObjectUtil.isNotNull(UserUtils.getCompanyId())) {
            BoolQueryBuilder authBoolQuery = QueryBuilders.boolQuery();
            List<String> orderType = userPermissionRule.getValueList(DataRuleValueEnum.ORDER_TYPE, AuthOptionsEnum.SELECT);
            if (CollectionUtil.isNotEmpty(orderType)) {
                mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getOrderType), orderType));
            }
            List<String> sourceType = userPermissionRule.getValueList(DataRuleValueEnum.SOURCE_TYPE, AuthOptionsEnum.SELECT);
            if (CollectionUtil.isNotEmpty(sourceType)) {
                mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getSourceType), sourceType));
            }

            Boolean needAddCompanyAndOther = userPermissionRule.needAddCompanyAndOther();
            String userCompanyId = String.valueOf(ObjectUtil.isNull(qo.getCarrierCompanyId()) ? UserContext.companyId().get() : qo.getCarrierCompanyId());
            if (needAddCompanyAndOther) {
                BoolQueryBuilder innerQuery = QueryBuilders.boolQuery();
                List<OtherQueryParam> companyAndOther = userPermissionRule.getCompanyAndOther();
                // 当前公司物料信息
                if (CollectionUtil.isNotEmpty(companyAndOther)) {
                    companyAndOther.stream().filter(
                            param -> Objects.equals(userCompanyId, param.getCompanyId())
                    ).forEach(queryParam -> {
                        // 物料
                        if (CollectionUtil.isNotEmpty(queryParam.getMaterialSpecies())) {
                            List<String> materialSpecies = queryParam.getMaterialSpecies().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                            ;
                            if (CollectionUtils.isNotEmpty(materialSpecies)) {
                                innerQuery.must(QueryBuilders.termsQuery("items.speciesId", materialSpecies));
                            }
                        }
                        // 路线
                        if (CollectionUtil.isNotEmpty(queryParam.getRoute())) {
                            List<String> routes = queryParam.getRoute().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                            ;
                            if (CollectionUtils.isNotEmpty(routes)) {
                                innerQuery.must(QueryBuilders.termsQuery("trackRouteId", routes));
                            }
                        }
                    });
                    authBoolQuery.should(innerQuery);
                }

                if (CollectionUtil.isNotEmpty(companyAndOther)) {
                    companyAndOther.stream().filter(
                            param -> !Objects.equals(userCompanyId, param.getCompanyId())
                    ).forEach(queryParam -> {
                        BoolQueryBuilder shouldQuery = QueryBuilders.boolQuery();
                        // 货主公司
                        shouldQuery.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCarrierCompanyId), queryParam.getCompanyId()));
                        // 物料
                        if (CollectionUtil.isNotEmpty(queryParam.getMaterialSpecies())) {
                            List<String> materialSpecies = queryParam.getMaterialSpecies().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                            ;
                            if (CollectionUtils.isNotEmpty(materialSpecies)) {
                                shouldQuery.must(QueryBuilders.termsQuery("items.speciesId", materialSpecies));
                            }
                        }
                        // 路线
                        if (CollectionUtil.isNotEmpty(queryParam.getRoute())) {
                            List<String> routes = queryParam.getRoute().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                            ;
                            if (CollectionUtils.isNotEmpty(routes)) {
                                shouldQuery.must(QueryBuilders.termsQuery("trackRouteId", routes));
                            }
                        }
                        authBoolQuery.should(shouldQuery);
                    });
                }
            }
            mainQueryBuilder.must(authBoolQuery);
        }
        return mainQueryBuilder;
    }
}
