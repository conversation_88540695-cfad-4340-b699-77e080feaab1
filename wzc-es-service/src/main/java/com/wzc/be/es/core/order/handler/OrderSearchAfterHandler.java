/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.core.order.handler;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.common.enums.QueryTypeEnums;
import com.wzc.be.es.common.service.EsCommonAuthSearchService;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.core.order.converter.OrderConverter;
import com.wzc.be.es.core.order.index.OrderIndex;
import com.wzc.be.es.data.order.qo.OrderPageQO;
import com.wzc.be.es.data.order.vo.OrderIndexVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023年06月13日
 * 订单分页深度查询 导出用
 */
@Slf4j
@Component
public class OrderSearchAfterHandler extends OrderBaseSearchHandler<OrderPageQO, EsPageVO<OrderIndexVO>> {

    @Value("${order.after.page.search.type:orderSearchAfter}")
    private String searchType;

    @Value("${order.search.index:order_index}")
    private String index;

    @Resource
    private EsCommonAuthSearchService esCommonAuthSearchService;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Override
    public RestResponse<EsPageVO<OrderIndexVO>> search(OrderPageQO orderPageQo) {
        try {
            JSONObject jsonObject = esCommonAuthSearchService.checkQueryAuth(JSONUtil.parseObj(orderPageQo), QueryTypeEnums.ORDER);
            orderPageQo = JSONUtil.toBean(jsonObject,OrderPageQO.class);
            log.info("订单深度模糊搜索:{}", jsonObject.toJSONString(0));

            // 构建搜索请求
            SearchRequest searchRequest = new SearchRequest(index);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            BoolQueryBuilder mainQueryBuilder = getMainBoolQueryBuilder(orderPageQo,null);

            searchSourceBuilder.size(orderPageQo.getSize());
            searchSourceBuilder.query(mainQueryBuilder);
            //创建时间默认倒序排序
            SortOrder sortOrder = SortOrder.DESC;
            if (orderPageQo.getIsAsc()){
                sortOrder = SortOrder.ASC;
            }
            searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(OrderIndex::getCreateTime)).order(sortOrder));
            searchSourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
            searchSourceBuilder.trackTotalHits(true);
            if (ObjectUtil.isNotNull(orderPageQo.getLastSortValues())){
                searchSourceBuilder.searchAfter(orderPageQo.getLastSortValues());
            }
            searchRequest.source(searchSourceBuilder);

            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            SearchHit[] hitResult = searchHits.getHits();
            Object[] lastSortValues = hitResult[hitResult.length - 1].getSortValues();

            JSONArray array = new JSONArray();
            Stream.of(hitResult).forEach(itemHit -> {
                Map<String, Object> sourceAsMap = itemHit.getSourceAsMap();
                array.add(sourceAsMap);
            });
            TotalHits totalHits = searchHits.getTotalHits();

            List<OrderIndex> list = JSONUtil.toList(array.toJSONString(0),OrderIndex.class);
            EsPageVO<OrderIndexVO> esPageRes = new EsPageVO<>();
            esPageRes.setTotal(totalHits.value);
            esPageRes.setList(OrderConverter.INSTANCE.indexListToVoList(list));
            esPageRes.setLastSortValues(lastSortValues);
            return RestResponse.success(esPageRes);
        } catch (Exception e) {
            log.error("订单列表查询异常.search error", e);
        }
        return RestResponse.error(ExcepitonEnum.ERROR_UNKNOW);
    }

    @Override
    public String getSearchType() {
        return searchType;
    }

    @Override
    public String getIndex() {
        return index;
    }


}
