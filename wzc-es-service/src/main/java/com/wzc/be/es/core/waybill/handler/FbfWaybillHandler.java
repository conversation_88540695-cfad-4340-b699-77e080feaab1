package com.wzc.be.es.core.waybill.handler;

import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baidu.mapcloud.cloudnative.common.model.Pagination;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.data.data.waybill.enums.SubWaybillStatusEnum;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.enums.AuditStatusEnums;
import com.wzc.be.es.common.enums.ComplianceEnums;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.core.waybill.converter.WaybillConverter;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.core.waybill.mapper.SubWaybillListMapper;
import com.wzc.be.es.data.waybill.qo.FbfSignListQO;
import com.wzc.be.es.data.waybill.vo.FbfSignListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.ExistsQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * 网货发布方列表
 */
@Slf4j
@Component
public class FbfWaybillHandler implements BaseSearchHandler<FbfSignListQO, Pagination<FbfSignListVO>> {


    @Value("${fbfWaybill.search.type:fbfWaybillHandler}")
    private String searchType;

    @Resource
    private EsIndexProperties esIndexProperties;

    @Resource
    private SubWaybillListMapper subWaybillListMapper;

    @Resource
    private RestHighLevelClient restHighLevelClient;

    @Override
    public RestResponse<Pagination<FbfSignListVO>> search(FbfSignListQO req) {
        Pagination<FbfSignListVO> pagination = new Pagination<>();
        try {
            int size = req.getSize();
            int from = (req.getPage() - 1) * size;

            // 构建搜索请求
            SearchRequest searchRequest = new SearchRequest(esIndexProperties.getSubWaybillIndex());
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            searchSourceBuilder.from(from);
            searchSourceBuilder.size(size);
            searchSourceBuilder.trackTotalHits(true);

            BoolQueryBuilder mainQueryBuilder = genSearchSource(req);
            searchSourceBuilder.query(mainQueryBuilder);
            searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(SubWaybillIndex::getCreateTime)).order(SortOrder.DESC));
            searchRequest.source(searchSourceBuilder);
            log.info("网货-发布方签收列表请求参数:{}", searchSourceBuilder.toString());
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            TotalHits totalHits = searchHits.getTotalHits();
            SearchHit[] hitResult = searchHits.getHits();
            JSONArray array = new JSONArray();
            Stream.of(hitResult).forEach(itemHit -> {
                Map<String, Object> sourceAsMap = itemHit.getSourceAsMap();
                array.add(sourceAsMap);
            });
            List<SubWaybillIndex> data = JSONUtil.toList(array.toJSONString(0), SubWaybillIndex.class);

            pagination.setTotal(0L);
            if (CollectionUtils.isNotEmpty(data)) {
                List<FbfSignListVO> list = WaybillConverter.INSTANCE.toFbfWaybill(data);
                pagination.setContent(list);
                pagination.setTotal(totalHits.value);
            }
            pagination.setPage(req.getPage());
            pagination.setSize(req.getSize());
            log.info("网货-发布方签收列表响应结果：{}", JSON.toJSONString(pagination));
            return RestResponse.success(pagination);
        } catch (Exception e) {
            log.error("网货-发布方签收列表异常", e);
        }


        return RestResponse.success(pagination);
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.esIndexProperties.getSubWaybillIndex();
    }

    public BoolQueryBuilder genSearchSource(FbfSignListQO qo) {
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        // 子运单号字段必须存在
        ExistsQueryBuilder fieldName = QueryBuilders.existsQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillNo));
        mainQueryBuilder.must(fieldName);
        mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getZbStatus), 1));
        if (qo.getRequestSource() != null && qo.getRequestSource() == 3) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getBusinessType), 3));
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getZbStatus), 1));
        } else {
            if (qo.getFbfId() != null) {
                mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getFbfId), qo.getFbfId()));
            }
        }
        //已签收
        Integer auditTabStatus = 2;
        if (!auditTabStatus.equals(qo.getAuditTabStatus()) && Objects.nonNull(qo.getFbfSignStatus())) {
            //tob页面不等于已签收才加发布方签收条件
            mainQueryBuilder.must(QueryBuilders.termQuery("fbfSignStatus", qo.getFbfSignStatus()));
        }

        // 子运单状态
        if (CollectionUtil.isNotEmpty(qo.getSubWaybillStatusList())) {
            mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), qo.getSubWaybillStatusList()));
        } else {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_SIGN.getCode()));
            boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.FINISH.getCode()));
            mainQueryBuilder.must(boolQuery);
        }

        if (Objects.nonNull(qo.getRequestSource())) {
            // 运单派车类型
            if(qo.getRequestSource().equals(1)){
                mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchCarType), 2));
            }else if(qo.getRequestSource().equals(2)){
                mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchCarType), 1));
            }
        }

        // 派车单号
        if (StringUtils.isNotBlank(qo.getWaybillNo())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getWaybillNo), wildcardsExp(qo.getWaybillNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getWaybillNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getWaybillNo)));
        }
        // 派车单号集合
        if (CollectionUtils.isNotEmpty(qo.getWaybillNoList())) {
            mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getWaybillNo), qo.getWaybillNoList()));
        }
        // 订单号
        if (StringUtils.isNotBlank(qo.getOrderNo())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getOrderNo), wildcardsExp(qo.getOrderNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getOrderNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getOrderNo)));
        }
        // 子运单号
        if (StringUtils.isNotBlank(qo.getSubWaybillNo())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillNo), wildcardsExp(qo.getSubWaybillNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getSubWaybillNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getSubWaybillNo)));
        }
        // 服务方姓名
        if (StringUtils.isNotBlank(qo.getFwfName())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery("carCaptainName.keyword", wildcardsExp(qo.getFwfName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getFwfName(),true, CollectionUtil.newArrayList("carCaptainName"));
        }

        // 发布方
        if (StringUtils.isNotBlank(qo.getFbfName())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery("fbfName.keyword", wildcardsExp(qo.getFbfName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getFbfName(),true, CollectionUtil.newArrayList("fbfName"));
        }

        // 承运商
        if (StringUtils.isNotBlank(qo.getCarrierCompanyName())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getCarrierCompanyName), wildcardsExp(qo.getCarrierCompanyName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getCarrierCompanyName(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getCarrierCompanyName)));
        }

        // 车牌号
        if (StringUtils.isNotBlank(qo.getCarNo())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getCarNo), wildcardsExp(qo.getCarNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getCarNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getCarNo)));
        }
        // 货物名称
        if (StringUtils.isNotBlank(qo.getGoodsName())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery("items.itemName", wildcardsExp(qo.getGoodsName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getGoodsName(),true, CollectionUtil.newArrayList("items.itemName"));
        }
        // 发货商
        if (StringUtils.isNotBlank(qo.getFromCustomerName())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery("sender.customerName", wildcardsExp(qo.getFromCustomerName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getFromCustomerName(),true, CollectionUtil.newArrayList("sender.customerName"));
        }
        // 发货地址
        if (StringUtils.isNotBlank(qo.getFromAddress())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery("sender.fullAddress", wildcardsExp(qo.getFromAddress())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getFromAddress(),true, CollectionUtil.newArrayList("sender.fullAddress"));
        }
        // 收货商
        if (StringUtils.isNotBlank(qo.getToCustomerName())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery("receiver.customerName", wildcardsExp(qo.getToCustomerName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getToCustomerName(),true, CollectionUtil.newArrayList("receiver.customerName"));
        }
        // 收货地址
        if (StringUtils.isNotBlank(qo.getToAddress())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery("receiver.fullAddress", wildcardsExp(qo.getToAddress())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getToAddress(),true, CollectionUtil.newArrayList("receiver.fullAddress"));
        }

        if (StringUtils.isNotBlank(qo.getDispatchStartTime()) && StringUtils.isNotBlank(qo.getDispatchEndTime())) {
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getCreateTime)).gte(qo.getDispatchStartTime()));
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getCreateTime)).lte(qo.getDispatchEndTime()));
        }

        if (StringUtils.isNotBlank(qo.getTranStartTime()) && StringUtils.isNotBlank(qo.getTranEndTime())) {
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getTransStartTime)).gte(qo.getTranStartTime()));
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getTransStartTime)).lte(qo.getTranEndTime()));
        }

        if (StringUtils.isNotBlank(qo.getUnlLoadStartTime()) && StringUtils.isNotBlank(qo.getUnlLoadEndTime())) {
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getTransEndTime)).gte(qo.getUnlLoadStartTime()));
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getTransEndTime)).lte(qo.getUnlLoadEndTime()));
        }

        if (StringUtils.isNotBlank(qo.getLoadEndTime()) && StringUtils.isNotBlank(qo.getLoadStartTime())) {
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadConfirmTime)).gte(qo.getLoadStartTime()));
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getLoadConfirmTime)).lte(qo.getLoadEndTime()));
        }

        if (StringUtils.isNotBlank(qo.getManualCheckStartTime()) && StringUtils.isNotBlank(qo.getManualCheckEndTime())) {
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getManualCheckTime)).gte(qo.getManualCheckStartTime()));
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getManualCheckTime)).lte(qo.getManualCheckEndTime()));
        }


        // 车主声明
        if (qo.getCzStatement() != null) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCzStatement), qo.getCzStatement()));
        }

        // 线路轨迹是否合规
        if (qo.getIsLineLegal() != null) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getIsLineLegal), qo.getIsLineLegal()));
        }
        //支付类型
        if (!Objects.isNull(qo.getFreezeType())) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getFreezeType), qo.getFreezeType()));
        }

        // 时间是否合规
        if (qo.getIsTimeLegal() != null) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getIsTimeLegal), qo.getIsTimeLegal()));
        }

        // 榜单是否合规
        if (qo.getIsPoundLegal() != null) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getIsPoundLegal), qo.getIsPoundLegal()));
        }

        // 系统审核状态
        if (qo.getSystemCheckStatus() != null) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSystemCheckStatus), qo.getSystemCheckStatus()));
        }

        // 人工审核状态
        if (qo.getManualCheckStatus() != null) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getManualCheckStatus), qo.getManualCheckStatus()));
        }

        // 车队长收入合规性
        if (qo.getCdzIncomeLegal() != null) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCdzIncomeLegal), qo.getCdzIncomeLegal()));
        }

        // 装货场景照片是否合规
        if(qo.getIsLoadImgLegal() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getIsLoadImgLegal), qo.getIsLoadImgLegal()));
        }

        // 卸货场景照片是否合规
        if(qo.getIsUnLoadImgLegal() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getIsUnLoadImgLegal), qo.getIsUnLoadImgLegal()));
        }

        // tab切换
        if (qo.getAuditTabStatus() != null) {
            switch (qo.getAuditTabStatus()) {
                case 0:
                    BoolQueryBuilder boolQuery0 = QueryBuilders.boolQuery();
                    boolQuery0.must(QueryBuilders.termQuery("fbfSignStatus", 1));
                    boolQuery0.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSystemCheckStatus), 1));
                    boolQuery0.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getManualCheckStatus), 0));
                    mainQueryBuilder.must(boolQuery0);
                    break;
                case 1:
                    BoolQueryBuilder boolQuery1 = QueryBuilders.boolQuery();
                    boolQuery1.must(QueryBuilders.termQuery("fbfSignStatus", 1));
                    boolQuery1.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSystemCheckStatus), 2));
                    boolQuery1.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getManualCheckStatus), 0));
                    mainQueryBuilder.must(boolQuery1);
                    break;
                case 2:
                    BoolQueryBuilder boolQuery2 = QueryBuilders.boolQuery();
                    boolQuery2.mustNot(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getManualCheckStatus), 0));
                    mainQueryBuilder.must(boolQuery2);
                    break;
            }
        }

        // 发布方标签tab
        if(Objects.nonNull(qo.getFbfTabStatus())){
            List<Integer> signStatusList = CollectionUtil.newArrayList(SubWaybillStatusEnum.WAIT_SIGN.getCode(), SubWaybillStatusEnum.FINISH.getCode());
            mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), signStatusList));
            switch (qo.getFbfTabStatus()) {
                // 待签收：司机卸货确认后，系统自动判断合规性后单据列表或发布方签收后平台人工审核异常后回退至此列表；
                case 0:
                    List<Integer> noSignComplianceType = CollectionUtil.newArrayList(1, 3, 4);
                    List<Integer> noManualCheckStatus = CollectionUtil.newArrayList(0,2);
                    BoolQueryBuilder boolQuery0 = QueryBuilders.boolQuery();
                    boolQuery0.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getManualCheckStatus), noManualCheckStatus));
                    boolQuery0.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getDispatchComplianceType), noSignComplianceType));
                    mainQueryBuilder.must(boolQuery0);
                    break;
                // 待司机补录：如发布方发现运单存在上传图片异常，可回退司机进行图片重新上传，待司机重新上传单据列表；
                case 1:
                    BoolQueryBuilder boolQuery1 = QueryBuilders.boolQuery();
                    boolQuery1.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDispatchComplianceType), 2));
                    mainQueryBuilder.must(boolQuery1);
                    break;
                // 已签收：发布方签收后平台人工审核通过或待人工审核列表；
                case 2:
                    List<Integer> signComplianceType = CollectionUtil.newArrayList(5,6);
                    BoolQueryBuilder boolQuery2 = QueryBuilders.boolQuery();
                    boolQuery2.must(QueryBuilders.termQuery("fbfSignStatus", 1));
                    boolQuery2.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getDispatchComplianceType), signComplianceType));
                    boolQuery2.mustNot(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getManualCheckStatus), 2));
                    mainQueryBuilder.must(boolQuery2);
                    break;
            }
        }
        //是否查询异常
        boolean abnormal = qo.isAbnormal();
        if (abnormal){
            //时间合规性 榜单合规性 轨迹合规性 车队长收入合规性 有一个不合规的 或人工审核状态不通过的就过滤出来
            BoolQueryBuilder abnormalBuilder = QueryBuilders.boolQuery();
            mainQueryBuilder.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getIsTimeLegal), ComplianceEnums.NO.getCode()));
            mainQueryBuilder.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getIsPoundLegal), ComplianceEnums.NO.getCode()));
            mainQueryBuilder.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getIsLineLegal), ComplianceEnums.NO.getCode()));
            mainQueryBuilder.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCdzIncomeLegal), ComplianceEnums.NO.getCode()));
            mainQueryBuilder.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getManualCheckStatus), AuditStatusEnums.RETURNED.getCode()));
            mainQueryBuilder.must(abnormalBuilder);
        }

        Long companyId = qo.getCompanyId();
        if (ObjectUtil.isNotNull(companyId)) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCompanyId), companyId));
        }
        Long shipperCompanyId = qo.getShipperCompanyId();
        if (ObjectUtil.isNotNull(shipperCompanyId)) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getShipperCompanyId), shipperCompanyId));
        }
        Long carrierCompanyId = qo.getCarrierCompanyId();
        if (ObjectUtil.isNotNull(carrierCompanyId)) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCarrierCompanyId), carrierCompanyId));
        }

        return mainQueryBuilder;
    }

}
