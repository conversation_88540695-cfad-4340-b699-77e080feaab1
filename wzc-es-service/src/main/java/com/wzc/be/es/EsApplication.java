package com.wzc.be.es;

import cn.easyes.starter.register.EsMapperScan;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * <AUTHOR>
 */
@Slf4j
@SpringBootApplication
@EnableFeignClients
@EsMapperScan("com.wzc.be.es.core")
public class EsApplication {

    public static void main(String[] args) {
        log.info("开始启动base3.0 es服务");
        SpringApplication.run(EsApplication.class, args);
        log.info("启动完成base3.0 es服务");
    }
}
