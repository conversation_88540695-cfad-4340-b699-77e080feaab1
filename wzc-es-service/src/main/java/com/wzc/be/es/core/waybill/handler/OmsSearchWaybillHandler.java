package com.wzc.be.es.core.waybill.handler;

import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.enums.SubWaybillStatusEnum;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.core.waybill.mapper.SubWaybillListMapper;
import com.wzc.be.es.data.waybill.qo.OmsSearchWaybillQO;
import com.wzc.be.es.data.waybill.vo.WaybillQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class OmsSearchWaybillHandler implements BaseSearchHandler<OmsSearchWaybillQO, List<WaybillQueryVO>> {

    @Value("${omsSearchWaybill.search.type:omsSearchWaybillHandler}")
    private String searchType;

    @Resource
    private EsIndexProperties esIndexProperties;

    @Resource
    private SubWaybillListMapper subWaybillListMapper;

    @Override
    public RestResponse<List<WaybillQueryVO>> search(OmsSearchWaybillQO qo) {
        LambdaEsQueryWrapper<SubWaybillIndex> wrapper = new LambdaEsQueryWrapper<>();
        this.genSearch(wrapper, qo);
        wrapper.index(esIndexProperties.getSubWaybillIndex());
        List<SubWaybillIndex> list = subWaybillListMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return new RestResponse<>();
        }

        List<WaybillQueryVO> data = new ArrayList<>();
        for (SubWaybillIndex subWaybill : list) {
            WaybillQueryVO record = new WaybillQueryVO();
            record.setWaybillNo(subWaybill.getWaybillNo());
            record.setWaybillStatus(subWaybill.getSubWaybillStatus());
            record.setStatus(subWaybill.getStatus());
            record.setCompleteStatus(subWaybill.getCompleteStatus());
            record.setCarrierCompanyId(subWaybill.getCarrierCompanyId());
            record.setCompanyId(subWaybill.getCompanyId());
            record.setDriverId(subWaybill.getDriverId());
            record.setDriverName(subWaybill.getDriverName());
            record.setCarId(subWaybill.getCarId());
            record.setCarNo(subWaybill.getCarNo());
            record.setCreateTime(subWaybill.getCreateTime());
            data.add(record);
        }
        return RestResponse.success(data);
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.esIndexProperties.getSubWaybillIndex();
    }

    /**
     * 构建查询条件
     */
    private void genSearch(LambdaEsQueryWrapper<SubWaybillIndex> wrapper, OmsSearchWaybillQO qo) {
        wrapper.isNotNull(SubWaybillIndex::getSubWaybillNo);
        wrapper.groupBy(SubWaybillIndex::getWaybillNo);
        wrapper.ne(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.CANCELLED.getCode());
        wrapper.ge(SubWaybillIndex::getCreateTime, qo.getStartTime());
        wrapper.le(SubWaybillIndex::getCreateTime, qo.getEndTime());

        if (qo.getCarrierCompanyId() != null) {
            wrapper.eq(SubWaybillIndex::getCarrierCompanyId, qo.getCarrierCompanyId());
        }
    }
}
