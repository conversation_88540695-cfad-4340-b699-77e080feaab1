package com.wzc.be.es.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/8/24 22:21
 */
@Getter
@AllArgsConstructor
public enum TransportStatusEnums {

    /**
     * 运输计划状态
     */
    CREATED(1, "待接单","unConfirmCount"),
    REFUSE(2, "已拒绝","refuseCount"),
    RUNNING(3, "进行中","executionCount"),
    FINISH(4, "已完成","finishCount"),
    PAUSE(5, "暂停","stopCount"),
    CANCEL(6, "已取消","cancelCount"),

    APPLY_END(7, "申请停运",""),

    WAITING_DISPATCH(8, "待派车",""),

    DELETE(9, "已删除","deleteCount");


    private final Integer code;

    private final String name;

    /**
     * 字段名称
     */
    private final String fieldName;


}
