package com.wzc.be.es.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TransportSearchTypeEnums {

    /**
     * 运输计划app搜索类型枚举
     */
    ALL(1, "全部"),
    GOODS_NAME(2, "货物名称搜索"),
    HZ(3, "货主名称搜索"),
    SEND_ADDR(4, "装货地搜索"),
    RECEIVE_ADDR(5, "收货地搜索"),
    TRANSPORT_NO(6, "运输计划编号搜索"),
    CYS(7, "承运商名称搜索"),
    ORDER_NO(8, "订单号搜索"),
    THIRD_NO(9, "三方计划编号搜索"),
    CONTRACT_NO(10, "合同编号搜索"),
    ;


    private final Integer code;

    private final String name;

}
