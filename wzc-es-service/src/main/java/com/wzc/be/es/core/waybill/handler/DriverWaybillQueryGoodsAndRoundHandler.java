package com.wzc.be.es.core.waybill.handler;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.constant.Constants;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.core.waybill.converter.WaybillConverter;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.data.waybill.qo.DriverWaybillListQO;
import com.wzc.be.es.data.waybill.vo.WaybillQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class DriverWaybillQueryGoodsAndRoundHandler implements BaseSearchHandler<DriverWaybillListQO, List<WaybillQueryVO>> {

    private final String searchType = SearchTypeEnums.DRIVER_GRABBING_WAYBILL_LIST.getType();

    @Resource
    private EsIndexProperties esIndexProperties;

    @Resource
    private RestHighLevelClient restHighLevelClient;

    @Override
    public RestResponse<List<WaybillQueryVO>> search(DriverWaybillListQO qo) {
        log.info("司机ID范围查询ES查询参数:{}", JSON.toJSONString(qo));
        SearchRequest searchRequest = new SearchRequest(esIndexProperties.getSubWaybillIndex());
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        //TIAOJIAN
        mainQueryBuilder.must(QueryBuilders.matchQuery(FieldUtils.val(SubWaybillIndex::getGoodsNo), qo.getGoodsNo()));
        mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getDriverId), qo.getDriverIds()));
        mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getRound), qo.getRound()));

        searchSourceBuilder.query(mainQueryBuilder);
        searchRequest.source(searchSourceBuilder);
        log.info("司机抢单信息查询条件：{}", searchSourceBuilder);
        try {
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            SearchHit[] hitResult = searchHits.getHits();

            JSONArray array = new JSONArray();
            Stream.of(hitResult).forEach(itemHit -> {
                Map<String, Object> sourceAsMap = itemHit.getSourceAsMap();
                array.add(sourceAsMap);
            });
            List<SubWaybillIndex> listIndex = JSONUtil.toList(array.toJSONString(0), SubWaybillIndex.class);
            log.info("司机抢单信息查询结果：{}", JSON.toJSONString(listIndex));
            return RestResponse.success(WaybillConverter.INSTANCE.toWaybillQueryVO(listIndex));
        } catch (Exception e) {
            log.info("司机运单列表ES查询异常,开始查询doris", e);
            //如果不是 es查询异常 便往外抛
            Throwable root = ExceptionUtil.getRootCause(e);
            if (!(root instanceof ElasticsearchException) || !((ElasticsearchException) root).getDetailedMessage().contains(Constants.ES_RESULT_WINDOW_IS_TOO_LARGE)) {
                throw new RuntimeException(e);
            }
            //查询es异常获取不到总数，重新查询
            return RestResponse.success();
        }
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.esIndexProperties.getSubWaybillIndex();
    }
}
