package com.wzc.be.es.core.waybill.handler;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.authentication.datapermission.core.rule.UserPermissionRule;
import com.baidu.mapcloud.cloudnative.common.model.PageOrderType;
import com.baidu.mapcloud.cloudnative.common.model.Pagination;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.enums.SuperviseTrackEnum;
import com.wzc.be.es.common.enums.TocPayStatusEnums;
import com.wzc.be.es.common.enums.ZbStatusEnums;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.data.waybill.enums.*;
import com.wzc.be.es.data.waybill.qo.RepostStatusQO;
import com.wzc.be.es.data.waybill.qo.SuperviseOrderPageQO;
import com.wzc.be.es.data.waybill.vo.SuperviseOrderPageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.ExistsQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

@Slf4j
@Component
public class RepostStatusListHandler implements BaseSearchHandler<RepostStatusQO, Pagination<SuperviseOrderPageVO>> {


    @Value("${superviseWaybillQueryList.search.type:repostStatusListHandler}")
    private String searchType;

    @Resource
    private EsIndexProperties esIndexProperties;

    @Autowired
    protected UserPermissionRule userPermissionRule;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Override
    public RestResponse<Pagination<SuperviseOrderPageVO>> search(RepostStatusQO qo) {
        Pagination<SuperviseOrderPageVO> page = new Pagination<>();
        page.setPage(qo.getPage());
        page.setSize(qo.getSize());
        try {
            // 构建搜索请求
            SearchRequest searchRequest = new SearchRequest(esIndexProperties.getSubWaybillIndex());
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            // 排序方式
            searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(SubWaybillIndex::getCreateTime)).order(SortOrder.DESC));

            BoolQueryBuilder mainQueryBuilder = this.getMainBoolQueryBuilder(qo);
            Integer size = qo.getSize();
            Integer from = (qo.getPage() - 1) * size;

            searchSourceBuilder.from(from);
            searchSourceBuilder.size(size);
            searchSourceBuilder.query(mainQueryBuilder);

            searchSourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
            searchSourceBuilder.trackTotalHits(true);
            searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(SubWaybillIndex::getCreateTime)).order(SortOrder.DESC));
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            SearchHit[] hitResult = searchHits.getHits();
            JSONArray array = new JSONArray();
            Stream.of(hitResult).forEach(itemHit -> {
                Map<String, Object> sourceAsMap = itemHit.getSourceAsMap();
                array.add(sourceAsMap);
            });
            TotalHits totalHits = searchHits.getTotalHits();
            List<SubWaybillIndex> list = JSONUtil.toList(array.toJSONString(0), SubWaybillIndex.class);


            if (CollectionUtils.isEmpty(list)) {
                RestResponse.success(page);
            }
            List<SuperviseOrderPageVO> data = new ArrayList<>();
            for (SubWaybillIndex subWaybill : list) {
                SuperviseOrderPageVO superviseOrderPageVO = new SuperviseOrderPageVO();
                superviseOrderPageVO.setDispatchCarNo(subWaybill.getWaybillNo());
                superviseOrderPageVO.setSubWaybillNo(subWaybill.getSubWaybillNo());
                superviseOrderPageVO.setOrderNo(subWaybill.getOrderNo());
                SubWaybillIndex.LocationUpload loadUploadNode = subWaybill.getLoadUploadNode();
                if (!Objects.isNull(loadUploadNode)) {
                    superviseOrderPageVO.setLoadUploadStatus(LoadUploadStatusEnum.getEnum(loadUploadNode.getUploadSuccess()));
                }
                SubWaybillIndex.LocationUpload unloadUploadNode = subWaybill.getUnloadUploadNode();
                if (!Objects.isNull(unloadUploadNode)) {
                    superviseOrderPageVO.setUnloadUploadStatus(LoadUploadStatusEnum.getEnum(unloadUploadNode.getUploadSuccess()));
                }
                Integer tocPayStatus = subWaybill.getTocPayStatus();
                if (TocPayStatusEnums.pay_already.getCode().equals(tocPayStatus)) {
                    superviseOrderPageVO.setPayStatus(PayStatusEnum.ALREADY_PAY);
                } else {
                    superviseOrderPageVO.setPayStatus(PayStatusEnum.NOT_PAY);
                }

                superviseOrderPageVO.setDriverReportStatus(DriverReportStatusEnum.getEnum(subWaybill.getDriverReportStatus()));
                superviseOrderPageVO.setCarReportStatus(CarReportStatusEnum.getEnum(subWaybill.getCarReportStatus()));

                superviseOrderPageVO.setDriverName(subWaybill.getDriverName());
                superviseOrderPageVO.setCarNo(subWaybill.getCarNo());
                superviseOrderPageVO.setTrack(SuperviseTrackEnum.TRACK_YES.getCode());
                superviseOrderPageVO.setDriverPhone(subWaybill.getDriverPhone());
                if (CollectionUtils.isNotEmpty(subWaybill.getSender())) {
                    superviseOrderPageVO.setFromAddress(subWaybill.getSender().get(0).getAddress());
                }
                if (CollectionUtils.isNotEmpty(subWaybill.getReceiver())) {
                    superviseOrderPageVO.setToAddress(subWaybill.getReceiver().get(0).getAddress());
                }
                if (CollectionUtils.isNotEmpty(subWaybill.getItems())) {
                    superviseOrderPageVO.setMaterialName(subWaybill.getItems().get(0).getItemName());
                }
                superviseOrderPageVO.setCreateTime(subWaybill.getCreateTime());
                superviseOrderPageVO.setReportWaybillStatus(SuperviseReportStatusEnum.getEnum(subWaybill.getReportWaybillStatus()));
                superviseOrderPageVO.setReportRecordStatus(SuperviseReportStatusEnum.getEnum(subWaybill.getReportRecordStatus()));
                superviseOrderPageVO.setCompleteStatus(subWaybill.getCompleteStatus());
                data.add(superviseOrderPageVO);
            }
            page.setTotal(totalHits.value);
            page.setContent(data);
            long totalPage = page.getTotal() / qo.getSize();
            if (page.getTotal() % qo.getSize() != 0) {
                totalPage += 1;
            }
            page.setTotalPage((int) totalPage);
            //是否还有下一页 true = 有 false = 无
            page.setHasNext(qo.getPage() + qo.getSize() < page.getTotal());
            return RestResponse.success(page);
        } catch (Exception e) {
            log.error("修改车辆/司机上报状态查询异常", e);
        }
        return RestResponse.success(page);

    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return esIndexProperties.getSubWaybillIndex();
    }


    public BoolQueryBuilder getMainBoolQueryBuilder(RepostStatusQO qo) {
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        // 子运单号字段必须存在
        ExistsQueryBuilder fieldName = QueryBuilders.existsQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillNo));
        mainQueryBuilder.must(fieldName);
        //司机id
        if (ObjectUtil.isNotNull(qo.getDriverId())) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDriverId), qo.getDriverId()));
        }
        //车辆id
        if (ObjectUtil.isNotNull(qo.getCarId())) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCarId), qo.getCarId()));
        }
        //转网状态
        if (ObjectUtil.isNotNull(qo.getZbStatus())) {
            mainQueryBuilder.must(QueryBuilders.termQuery("zbStatus", qo.getZbStatus()));
        }
        if (ObjectUtil.isNotNull(qo.getCarReportStatus())) {
            mainQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCarReportStatus), qo.getCarReportStatus().getCode()));
        }
        if (ObjectUtil.isNotNull(qo.getDriverReportStatus())) {
            mainQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDriverReportStatus), qo.getDriverReportStatus().getCode()));
        }

        mainQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getReportWaybillStatus), SuperviseReportStatusEnum.report_status_1.getCode()));
        mainQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getReportRecordStatus), SuperviseReportStatusEnum.report_status_1.getCode()));
        return mainQueryBuilder;
    }
}
