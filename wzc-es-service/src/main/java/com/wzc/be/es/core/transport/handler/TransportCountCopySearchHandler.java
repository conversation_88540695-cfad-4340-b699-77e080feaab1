/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.core.transport.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.enums.QueryTypeEnums;
import com.wzc.be.es.common.enums.TransportStatusEnums;
import com.wzc.be.es.common.service.EsCommonAuthSearchService;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.core.common.CommonRedisResultVO;
import com.wzc.be.es.data.transport.qo.TransportQueryPageQO;
import com.wzc.be.es.data.transport.vo.TransportEsVO;
import com.wzc.be.es.data.transport.vo.TransportGroupVO;
import com.wzc.be.es.data.transport.vo.TransportStatusCountVO;
import com.wzc.common.user.utils.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.wzc.be.es.common.constant.Constants.UPDATE_INDEX_REDIS_KEY;
import static com.wzc.be.es.common.enums.TransportStatusEnums.APPLY_END;

/**
 * <AUTHOR>
 * @date 2023年07月06日
 * 运输计划状态统计查询
 */
@Slf4j
@Component
public class TransportCountCopySearchHandler extends TransportBaseSearchHandler<TransportQueryPageQO, TransportStatusCountVO> {

    @Value("${transport.count.search.type:transportCount}")
    private String searchType;
    @Resource
    private EsIndexProperties esIndexProperties;

    @Autowired
    private EsCommonAuthSearchService esCommonAuthSearchService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private TransportSearchHandler transportSearchHandler;

    @Autowired
    private TransportGroupNoSearchHandler transportGroupNoSearchHandler;

    @Override
    public RestResponse<TransportStatusCountVO> search(TransportQueryPageQO transportQueryPageQo) {
        JSONObject jsonObject = esCommonAuthSearchService.checkQueryAuth(JSONUtil.parseObj(transportQueryPageQo), QueryTypeEnums.TRANSPORT);
        transportQueryPageQo = JSONUtil.toBean(jsonObject, TransportQueryPageQO.class);
        log.info("运输计划状态统计模糊搜索:{}", jsonObject.toJSONString(0));
        TransportStatusCountVO transportStatusCountVO = new TransportStatusCountVO();
        //查询缓存里是否有变化的单号
        RMapCache<String, String> mapCache = redissonClient.getMapCache(UPDATE_INDEX_REDIS_KEY + esIndexProperties.getTransportIndex() + ":" + UserUtils.getCompanyId());
        List<CommonRedisResultVO> commonRedisResultVOList = mapCache.readAllEntrySet().stream().map(e -> JSONUtil.toBean(e.getValue(), CommonRedisResultVO.class)).collect(Collectors.toList());

        transportQueryPageQo.setNeedAuth(false);
        transportQueryPageQo.setSize(1);
        List<Integer> statusList = transportQueryPageQo.getStatusList();
        transportQueryPageQo.setStatusList(null);

        if (UserUtils.isIdentityCarrier()) {
            transportStatusCountVO = carrierStatusCount(transportQueryPageQo, commonRedisResultVOList,statusList);
        }
        if (UserUtils.isIdentityShipper() || UserUtils.isOperatePc()) {
            transportStatusCountVO = shipperOrManagementStatusCount(transportQueryPageQo,commonRedisResultVOList,statusList);
        }
        return RestResponse.success(transportStatusCountVO);

    }

    /**
     * 承运商端状态数量统计
     *
     * @return
     */
    private TransportStatusCountVO carrierStatusCount(TransportQueryPageQO transportQueryPageQo,
                                                      List<CommonRedisResultVO> commonRedisResultVOList,
                                                      List<Integer> statusList) {
        log.info("承运商端统计");
        TransportStatusCountVO transportStatusCountVO = new TransportStatusCountVO();
        Map<Integer, Integer> statusCountMap = new HashMap<>();
        AtomicReference<Integer> allCount = new AtomicReference<>(0);
        //统计待接单状态
        requestContentSet(transportQueryPageQo, TransportStatusEnums.CREATED.getCode(), commonRedisResultVOList);
        RestResponse<EsPageVO<TransportEsVO>> createdSearch = transportSearchHandler.search(transportQueryPageQo);
        if (createdSearch.getSuccess()) {
            Long createdTotal = createdSearch.getData().getTotal();
            transportStatusCountVO.setUnConfirmCount(longCountParseToInteger(createdTotal));
            statusCountMap.put(TransportStatusEnums.CREATED.getCode(), longCountParseToInteger(createdTotal));
        }
        //已拒绝状态统计
        requestContentSet(transportQueryPageQo, TransportStatusEnums.REFUSE.getCode(), commonRedisResultVOList);
        RestResponse<EsPageVO<TransportEsVO>> refuseSearch = transportSearchHandler.search(transportQueryPageQo);
        if (refuseSearch.getSuccess()) {
            Long refuseTotal = refuseSearch.getData().getTotal();
            transportStatusCountVO.setRefuseCount(longCountParseToInteger(refuseTotal));
            statusCountMap.put(TransportStatusEnums.REFUSE.getCode(), longCountParseToInteger(refuseTotal));
        }
        //执行中状态统计
        requestContentSet(transportQueryPageQo, TransportStatusEnums.RUNNING.getCode(), commonRedisResultVOList);
        RestResponse<EsPageVO<TransportEsVO>> runningSearch = transportSearchHandler.search(transportQueryPageQo);
        if (runningSearch.getSuccess()) {
            Long runningTotal = runningSearch.getData().getTotal();
            transportStatusCountVO.setExecutionCount(longCountParseToInteger(runningTotal));
            statusCountMap.put(TransportStatusEnums.RUNNING.getCode(), longCountParseToInteger(runningTotal));
        }
        //已暂停状态统计
        requestContentSet(transportQueryPageQo, TransportStatusEnums.PAUSE.getCode(), commonRedisResultVOList);
        RestResponse<EsPageVO<TransportEsVO>> pauseSearch = transportSearchHandler.search(transportQueryPageQo);
        if (pauseSearch.getSuccess()) {
            Long pauseTotal = pauseSearch.getData().getTotal();
            transportStatusCountVO.setStopCount(longCountParseToInteger(pauseTotal));
            statusCountMap.put(TransportStatusEnums.PAUSE.getCode(), longCountParseToInteger(pauseTotal));
        }
        //已完成状态统计
        requestContentSet(transportQueryPageQo, TransportStatusEnums.FINISH.getCode(), commonRedisResultVOList);
        RestResponse<EsPageVO<TransportEsVO>> finishSearch = transportSearchHandler.search(transportQueryPageQo);
        if (finishSearch.getSuccess()) {
            Long finishTotal = finishSearch.getData().getTotal();
            transportStatusCountVO.setFinishCount(longCountParseToInteger(finishTotal));
            statusCountMap.put(TransportStatusEnums.FINISH.getCode(), longCountParseToInteger(finishTotal));
        }
        //已取消统计
        requestContentSet(transportQueryPageQo, TransportStatusEnums.CANCEL.getCode(), commonRedisResultVOList);
        RestResponse<EsPageVO<TransportEsVO>> cancelSearch = transportSearchHandler.search(transportQueryPageQo);
        if (cancelSearch.getSuccess()) {
            Long cancelCount = cancelSearch.getData().getTotal();
            transportStatusCountVO.setCancelCount(longCountParseToInteger(cancelCount));
            statusCountMap.put(TransportStatusEnums.CANCEL.getCode(), longCountParseToInteger(cancelCount));
        }
        //停运申请统计
        requestContentSet(transportQueryPageQo, APPLY_END.getCode(), commonRedisResultVOList);
        RestResponse<EsPageVO<TransportEsVO>> endSearch = transportSearchHandler.search(transportQueryPageQo);
        if (endSearch.getSuccess()) {
            Long endTotal = endSearch.getData().getTotal();
            transportStatusCountVO.setEndCount(longCountParseToInteger(endTotal));
            statusCountMap.put(APPLY_END.getCode(), longCountParseToInteger(endTotal));
        }
        //已删除统计
        requestContentSet(transportQueryPageQo, 8, commonRedisResultVOList);
        RestResponse<EsPageVO<TransportEsVO>> deleteSearch = transportSearchHandler.search(transportQueryPageQo);
        if (deleteSearch.getSuccess()) {
            Long deleteTotal = deleteSearch.getData().getTotal();
            transportStatusCountVO.setDeleteCount(longCountParseToInteger(deleteTotal));
            statusCountMap.put(8, longCountParseToInteger(deleteTotal));
        }
        if(CollectionUtil.isNotEmpty(statusList)){
            statusList.forEach(status -> {
                allCount.updateAndGet(v -> v + Convert.toInt(statusCountMap.get(status),0));
            });
            log.info("承运商端统计allCount1:{}",allCount.get());
        }else {
            allCount.set(transportStatusCountVO.getUnConfirmCount() + transportStatusCountVO.getCancelCount() + transportStatusCountVO.getRefuseCount() + transportStatusCountVO.getExecutionCount()
                    + transportStatusCountVO.getStopCount() + transportStatusCountVO.getFinishCount() + transportStatusCountVO.getDeleteCount());
            log.info("承运商端统计allCount2:{}",allCount.get());
        }
        transportStatusCountVO.setAllCount(allCount.get());
        return transportStatusCountVO;
    }

    private Integer longCountParseToInteger(long count) {
        return Integer.parseInt(StrUtil.toString(count));
    }

    /**
     * 请求参数 状态 过滤单号设置
     *
     * @param transportQueryPageQo
     * @param transportStatus
     * @param commonRedisResultVOList
     */
    private void requestContentSet(TransportQueryPageQO transportQueryPageQo, Integer transportStatus, List<CommonRedisResultVO> commonRedisResultVOList) {
        // 不符合状态的运输计划编号集合
        Integer finalTransportStatus;
        //如果是停运申请对应查询的是已暂停状态
        if (ObjectUtil.equals(transportStatus,APPLY_END.getCode())){
            finalTransportStatus = TransportStatusEnums.PAUSE.getCode();
        } else {
            finalTransportStatus = transportStatus;
        }
        transportQueryPageQo.setStatus(transportStatus);
        List<String> noInTransportNoList = commonRedisResultVOList.stream().filter(c -> ObjectUtil.equals(c.getStatus(), finalTransportStatus)).map(CommonRedisResultVO::getUnionNo).collect(Collectors.toList());
        transportQueryPageQo.setNoInTransportNoList(noInTransportNoList);
    }

    /**
     * 货主端 运营端 状态数量统计
     *
     * @return
     */
    private TransportStatusCountVO shipperOrManagementStatusCount(TransportQueryPageQO transportQueryPageQo,
                                                                  List<CommonRedisResultVO> commonRedisResultVOList,
                                                                  List<Integer> statusList) {
        log.info("货主端 运营端统计");
        TransportStatusCountVO transportStatusCountVO = new TransportStatusCountVO();
        AtomicReference<Integer> allCount = new AtomicReference<>(0);
        Map<Integer, Integer> statusCountMap = new HashMap<>();
        //统计待接单状态
        requestContentSet(transportQueryPageQo, TransportStatusEnums.CREATED.getCode(), commonRedisResultVOList);
        RestResponse<EsPageVO<TransportGroupVO>> createdSearch = transportGroupNoSearchHandler.search(transportQueryPageQo);
        if (createdSearch.getSuccess()) {
            Long createdTotal = createdSearch.getData().getTotal();
            transportStatusCountVO.setUnConfirmCount(longCountParseToInteger(createdTotal));
            statusCountMap.put(TransportStatusEnums.CREATED.getCode(), longCountParseToInteger(createdTotal));
        }
        //已拒绝状态统计
        requestContentSet(transportQueryPageQo, TransportStatusEnums.REFUSE.getCode(), commonRedisResultVOList);
        RestResponse<EsPageVO<TransportGroupVO>> refuseSearch = transportGroupNoSearchHandler.search(transportQueryPageQo);
        if (refuseSearch.getSuccess()) {
            Long refuseTotal = refuseSearch.getData().getTotal();
            transportStatusCountVO.setRefuseCount(longCountParseToInteger(refuseTotal));
            statusCountMap.put(TransportStatusEnums.REFUSE.getCode(), longCountParseToInteger(refuseTotal));
        }
        //执行中状态统计
        requestContentSet(transportQueryPageQo, TransportStatusEnums.RUNNING.getCode(), commonRedisResultVOList);
        RestResponse<EsPageVO<TransportGroupVO>> runningSearch = transportGroupNoSearchHandler.search(transportQueryPageQo);
        if (runningSearch.getSuccess()) {
            Long runningTotal = runningSearch.getData().getTotal();
            transportStatusCountVO.setExecutionCount(longCountParseToInteger(runningTotal));
            statusCountMap.put(TransportStatusEnums.RUNNING.getCode(), longCountParseToInteger(runningTotal));
        }
        //已暂停状态统计
        requestContentSet(transportQueryPageQo, TransportStatusEnums.PAUSE.getCode(), commonRedisResultVOList);
        RestResponse<EsPageVO<TransportGroupVO>> pauseSearch = transportGroupNoSearchHandler.search(transportQueryPageQo);
        if (pauseSearch.getSuccess()) {
            Long pauseTotal = pauseSearch.getData().getTotal();
            transportStatusCountVO.setStopCount(longCountParseToInteger(pauseTotal));
            statusCountMap.put(TransportStatusEnums.PAUSE.getCode(), longCountParseToInteger(pauseTotal));
        }
        //已完成状态统计
        requestContentSet(transportQueryPageQo, TransportStatusEnums.FINISH.getCode(), commonRedisResultVOList);
        RestResponse<EsPageVO<TransportGroupVO>> finishSearch = transportGroupNoSearchHandler.search(transportQueryPageQo);
        if (finishSearch.getSuccess()) {
            Long finishTotal = finishSearch.getData().getTotal();
            transportStatusCountVO.setFinishCount(longCountParseToInteger(finishTotal));
            statusCountMap.put(TransportStatusEnums.FINISH.getCode(), longCountParseToInteger(finishTotal));
        }
        //已取消统计
        requestContentSet(transportQueryPageQo, TransportStatusEnums.CANCEL.getCode(), commonRedisResultVOList);
        RestResponse<EsPageVO<TransportGroupVO>> cancelSearch = transportGroupNoSearchHandler.search(transportQueryPageQo);
        if (cancelSearch.getSuccess()) {
            Long cancelCount = cancelSearch.getData().getTotal();
            transportStatusCountVO.setCancelCount(longCountParseToInteger(cancelCount));
            statusCountMap.put(TransportStatusEnums.CANCEL.getCode(), longCountParseToInteger(cancelCount));
        }
        //停运申请统计
        requestContentSet(transportQueryPageQo, APPLY_END.getCode(), commonRedisResultVOList);
        RestResponse<EsPageVO<TransportGroupVO>> endSearch = transportGroupNoSearchHandler.search(transportQueryPageQo);
        if (endSearch.getSuccess()) {
            Long endTotal = endSearch.getData().getTotal();
            transportStatusCountVO.setEndCount(longCountParseToInteger(endTotal));
            statusCountMap.put(APPLY_END.getCode(), longCountParseToInteger(endTotal));
        }
        //已删除统计
        requestContentSet(transportQueryPageQo, 8, commonRedisResultVOList);
        RestResponse<EsPageVO<TransportGroupVO>> deleteSearch = transportGroupNoSearchHandler.search(transportQueryPageQo);
        if (deleteSearch.getSuccess()) {
            Long deleteTotal = deleteSearch.getData().getTotal();
            transportStatusCountVO.setDeleteCount(longCountParseToInteger(deleteTotal));
            statusCountMap.put(8, longCountParseToInteger(deleteTotal));
        }
        if(CollectionUtil.isNotEmpty(statusList)){
            statusList.forEach(status -> {
                allCount.updateAndGet(v -> v + Convert.toInt(statusCountMap.get(status),0));
            });
            log.info("货主端、运营端统计allCount1:{}",allCount.get());
        }else {
            allCount.set(transportStatusCountVO.getUnConfirmCount() + transportStatusCountVO.getCancelCount() + transportStatusCountVO.getRefuseCount() + transportStatusCountVO.getExecutionCount()
                    + transportStatusCountVO.getStopCount() + transportStatusCountVO.getFinishCount() + transportStatusCountVO.getDeleteCount());
            log.info("货主端、运营端统计allCount2:{}",allCount.get());
        }
        transportStatusCountVO.setAllCount(allCount.get());
        return transportStatusCountVO;
    }

    @Override
    public String getSearchType() {
        return searchType;
    }

    @Override
    public String getIndex() {
        return esIndexProperties.getTransportIndex();
    }


}
