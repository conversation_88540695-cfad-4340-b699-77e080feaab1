package com.wzc.be.es.adapter.action.waybill;

import com.baidu.mapcloud.cloudnative.common.model.Pagination;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.api.waybill.EsWaybillSearchApi;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.commons.PageVO;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.core.waybill.service.EsWaybillSearchService;
import com.wzc.be.es.data.waybill.qo.SuperviseOrderPageQO;
import com.wzc.be.es.data.waybill.vo.SuperviseOrderPageVO;
import com.wzc.be.es.data.waybill.qo.*;
import com.wzc.be.es.data.waybill.vo.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023年08月11日
 */
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class WaybillAction implements EsWaybillSearchApi {

    private final EsWaybillSearchService esWaybillSearchService;

    @Override
    public RestResponse<PageVO<WaybillQueryVO>> waybillQuery(DriverWaybillListQO qo) {
        return esWaybillSearchService.waybillQuery(qo);
    }

    @Override
    public RestResponse<PageVO<CarrierWaybillListVO>> carrierWaybillList(CarrierWaybillListQO qo) {
        return esWaybillSearchService.carrierWaybillList(qo);
    }

    @Override
    public RestResponse<WaybillQueryVO> activateWaybill(CheckActivateWaybillQO qo) {
        return esWaybillSearchService.activateWaybill(qo);
    }

    @Override
    public RestResponse<List<WaybillQueryVO>> dispatchWaybill(CheckDispatchWaybillQO qo) {
        return esWaybillSearchService.dispatchWaybill(qo);
    }

    @Override
    public RestResponse<PageVO<InspectorWaybillListVO>> inspectorWaybillList(InspectorListQo qo) {
        return esWaybillSearchService.inspectorWaybillList(qo);
    }

    @Override
    public RestResponse<WaybillQueryVO> selectWaybillInfo(SelectWaybillInfoQO qo) {
        return esWaybillSearchService.selectWaybillInfo(qo);
    }

    @Override
    public RestResponse<CarrierTabGroupCountVO> carrierTabGroupCount(CarrierTabGroupCountQO qo) {
        return esWaybillSearchService.carrierTabGroupCount(qo);
    }

    /**
     * 承运商APP运单列表
     */
    @Override
    public RestResponse<PageVO<WaybillQueryVO>> carrierAppOrderList(CarrierAppOrderListQO qo) {
        return esWaybillSearchService.carrierAppOrderList(qo);
    }


    @Override
    public RestResponse<List<WaybillQueryVO>> omsSearchWaybill(OmsSearchWaybillQO qo) {
        return esWaybillSearchService.omsSearchWaybill(qo);
    }

    @Override
    public RestResponse<Pagination<SubWaybillVO>> ownerWaybillList(OwnerWaybillListQO qo) {
        Integer requestType = qo.getRequestType();
        if(Objects.isNull(requestType)){
            qo.setRequestType(1);
        }
        return esWaybillSearchService.ownerWaybillList(qo);
    }

    @Override
    public RestResponse<EsPageVO<SubWaybillVO>> ownerWaybillListAfter(OwnerWaybillListQO qo) {
        return esWaybillSearchService.ownerWaybillListAfter(qo);
    }

    @Override
    public RestResponse<EsPageVO<CarrierWaybillListVO>> carrierWaybillListAfter(CarrierWaybillListQO qo) {
        return esWaybillSearchService.carrierWaybillListAfter(qo);
    }

    @Override
    public RestResponse<CarrierTabGroupCountVO> ownerTabGroupCount(OwnerWaybillListQO qo) {
        return esWaybillSearchService.ownerTabGroupCount(qo);
    }

    @Override
    public RestResponse<Pagination<SubWaybillVO>> exceptionWaybillList(ExceptionWaybillListQO qo) {
        return esWaybillSearchService.exceptionWaybillList(qo);
    }

    @Override
    public RestResponse<List<SubWaybillVO>> subWaybillList(SubWaybillQO qo) {
        return esWaybillSearchService.subWaybillList(qo);
    }

    @Override
    public RestResponse<ExceptionCountVO> exceptionTabCount(ExceptionWaybillListQO qo) {
        return esWaybillSearchService.exceptionTabCount(qo);
    }

    @Override
    public RestResponse<Boolean> checkDriverWaybill(CheckDriverWaybillQO qo) {
        return esWaybillSearchService.checkDriverWaybill(qo);
    }

    @Override
    public RestResponse<List<OrderCountVO>> orderCount(OrderCountQO qo) {
        return esWaybillSearchService.orderCount(qo);
    }

    @Override
    public RestResponse<List<TransportCountVO>> transportCount(TransportCountQO qo) {
        return esWaybillSearchService.transportCount(qo);
    }

    /**
     * 公司运输统计
     *
     */
    @Override
    public RestResponse<WaybillStatisticsVO> waybillStatistics(WaybillStatisticsQO qo) {
        return esWaybillSearchService.waybillStatistics(qo);
    }

    /**
     * 网货发布方签收列表
     *
     * @param qo
     */
    @Override
    public RestResponse<Pagination<FbfSignListVO>> fbfWaybillList(FbfSignListQO qo) {
        return esWaybillSearchService.fbfWaybillList(qo);
    }

    /**
     * 网货发布方签收列表导出专用
     *
     * @param qo
     */
    @Override
    public RestResponse<EsPageVO<FbfSignListVO>> fbfWaybillListExport(FbfSignListQO qo) {
        return esWaybillSearchService.fbfWaybillListExport(qo);
    }

    /**
     * 根据货主公司id查询是否有运单
     */
    @Override
    public RestResponse<Boolean> ownerWaybill(OwnerWaybillQO qo) {
        return esWaybillSearchService.ownerWaybill(qo);
    }

    /**
     * 根据承运商公司id，运单号查询
     *
     * @param qo 请求参数
     */
    @Override
    public RestResponse<List<SubWaybillVO>> waybillQueryList(SubWaybillQO qo) {
        return esWaybillSearchService.selectByCompanyAndWaybillNo(qo);
    }

    /**
     * 根据运输计划查询派车单列表
     *
     * @param qo 请求参数
     */
    @Override
    public RestResponse<Pagination<PlanWaybillVO>> selectByPlanNo(PlanWaybillQO qo) {
        return esWaybillSearchService.selectByPlanNo(qo);
    }

    @Override
    public RestResponse<Pagination<SuperviseOrderPageVO>> superviseList(SuperviseOrderPageQO qo) {
        return esWaybillSearchService.superviseList(qo);
    }

    @Override
    public RestResponse<EsPageVO<SuperviseOrderPageVO>> superviseListExport(SuperviseOrderPageQO qo) {
        return esWaybillSearchService.superviseListExport(qo);
    }

    /**
     * 抢单相关运单查询
     * @param qo
     * @return
     */
    public RestResponse<EsPageVO<SubWaybillVO>> waybillQueryPage(@RequestBody SubWaybillQO qo){
        return esWaybillSearchService.selectWaybillPage(qo);
    }

    /**
     * 关联运单查询
     *
     */
    @Override
    public RestResponse<List<RelevanceWaybillVO>> relevanceWaybill(RelevanceWaybillQO qo) {
        return esWaybillSearchService.relevanceWaybill(qo);
    }

    /**
     * 打卡补录校验司机车辆是否在时间段内开启了运单
     *
     */
    @Override
    public RestResponse<Boolean> checkDriverAndCarWaybill(CheckDriverAndCarWaybillQO qo) {
        return esWaybillSearchService.checkDriverAndCarWaybill(qo);
    }

    @Override
    public RestResponse<List<WaybillQueryVO>> searchWaybillByGoodsNoAndRound(DriverWaybillListQO qo) {
        return esWaybillSearchService.searchWaybillByGoodsNoAndRound(qo);
    }

    /**
     * 查询发布方是否有待结算的运单
     *
     */
    @Override
    public RestResponse<Boolean> fbfCompleteStatusWaybill(FbfCompleteStatusWaybillQO qo) {
        return esWaybillSearchService.fbfCompleteStatusWaybill(qo);
    }

    @Override
    public RestResponse<Pagination<SuperviseOrderPageVO>> superviseTobList(SuperviseOrderPageQO requestPageQo) {
        return esWaybillSearchService.superviseTobList(requestPageQo);
    }


    @Override
    public RestResponse<Pagination<SuperviseOrderPageVO>> reportStatusList(RepostStatusQO repostStatusQO) {
        return esWaybillSearchService.reportStatusList(repostStatusQO);
    }
}
