package com.wzc.be.es.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AuditStatusEnums{

    /**
     * 收款方、托运方、付款方审核状态（0待审核 1审核通过 2已退回）
     */
    TO_BE_REVIEWED(0, "待审核","waitAudit"),
    PASS_THE_EXAMINATION(1, "审核通过","auditPass"),
    RETURNED(2, "已退回","auditReject"),
    ;

    private final Integer code;
    private final String name;
    private final String fieldName;
}
