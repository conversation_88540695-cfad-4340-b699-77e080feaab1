/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.common.handler;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchPhraseQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.WildcardQueryBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年07月06日
 */
public interface BaseSearchHandler<Q,V> {
    default RestResponse<V> search(Q req){
        return null;
    }

    default String getSearchType(){
        return null;
    }

    default String getIndex(){
        return null;
    }

    default String[] getIndices(){
        return null;
    }

    /**
     * 模糊搜索 通配符
     * @param value 搜索关键字
     * @return String
     */
    default String wildcardsExp(String value) {
        return "*" + value + "*";
    }

    /**
     * 字段可走模糊匹配或者分词匹配搜索
     *
     * @param mainBoolQueryBuilder 搜索构建
     * @param keyword              搜索关键字
     * @param canMatchPhraseQuery  是否可以走分词搜索
     * @param columns              搜索字段集合
     */
    default void matchPhraseOrWildcardMustQuery(BoolQueryBuilder mainBoolQueryBuilder, String keyword, boolean canMatchPhraseQuery, List<String> columns) {
        //单字段匹配
        if (columns.size() == 1){
            String column = columns.get(0);
            if (canMatchPhraseQuery) {
                if (keyword.length() > 1) {
                    mainBoolQueryBuilder.must(QueryBuilders.matchPhraseQuery(column, keyword));
                } else {
                    mainBoolQueryBuilder.must(QueryBuilders.wildcardQuery(column, wildcardsExp(keyword)));
                }
            } else {
                mainBoolQueryBuilder.must(QueryBuilders.wildcardQuery(column, wildcardsExp(keyword)));
            }
        }
        //多字段走or匹配
        else {
            BoolQueryBuilder innerBoolQueryBuilder = QueryBuilders.boolQuery();
            if (canMatchPhraseQuery){
                if (keyword.length() > 1) {
                    for (String column:columns){
                        innerBoolQueryBuilder.should(getMatchPhraseQuery(column, keyword));
                    }
                } else {
                    for (String column:columns){
                        innerBoolQueryBuilder.should(getWildcardQuery(column, keyword));
                    }
                }
            }else {
                for (String column:columns){
                    innerBoolQueryBuilder.should(getWildcardQuery(column, keyword));
                }
            }
            mainBoolQueryBuilder.must(innerBoolQueryBuilder);
        }

    }

    /**
     * 模糊搜索
     * @param fieldName 字段名称
     * @param wildcardExpression 搜索关键字
     * @return WildcardQueryBuilder 模糊构造
     */
    default WildcardQueryBuilder getWildcardQuery(String fieldName, String wildcardExpression) {
        return QueryBuilders.wildcardQuery(fieldName, wildcardsExp(wildcardExpression));
    }

    /**
     * 分词模糊搜索
     * @param fieldName 字段名称
     * @param keyword 搜索关键字
     * @return MatchPhraseQueryBuilder 模糊构造
     */
    default MatchPhraseQueryBuilder getMatchPhraseQuery(String fieldName, String keyword) {
        return QueryBuilders.matchPhraseQuery(fieldName, keyword);
    }


}
