package com.wzc.be.es.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/8/24 22:21
 */
@Getter
@AllArgsConstructor
public enum OrderStatusEnums {

    /**
     * 订单状态枚举
     */
    CREATED(1, "待审核","waitCheckCount"),
    RUNNING(3, "执行中","executionCount"),
    FINISH(4, "已完成","finishCount"),
    PAUSE(5, "已暂停","stopCount"),
    WAITING_CONFIRM(2, "待确认","");


    private final Integer code;

    private final String name;


    /**
     * 字段名称
     */
    private final String fieldName;

}
