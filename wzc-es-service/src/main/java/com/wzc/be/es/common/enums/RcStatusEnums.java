package com.wzc.be.es.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RcStatusEnums {

    /**
     * 对账状态（0未对账 1已对账）
     */
    OUTSTANDING_RECONCILIATION(0, "未对账","unReconciliationCount"),
    HAVE_RECONCILED(1, "已对账",""),
    HAVE_RECONCILED_UN_SETTLE(101, "已对账未结算","alReconciliationCount"),
    ;

    private final Integer code;
    private final String name;
    private final String fieldName;
}
