package com.wzc.be.es.core.waybill.handler;

import cn.easyes.core.biz.EsPageInfo;
import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import cn.hutool.core.util.ObjectUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.core.waybill.converter.WaybillConverter;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.core.waybill.mapper.SubWaybillListMapper;
import com.wzc.be.es.data.waybill.qo.SubWaybillQO;
import com.wzc.be.es.data.waybill.vo.SubWaybillVO;
import com.wzc.be.validation.asserts.AssertsUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class WaybillQueryHandler implements BaseSearchHandler<SubWaybillQO,List<SubWaybillVO>> {

    @Value("${waybillQueryHandler.search.type:waybillQueryHandler}")
    private String searchType;

    @Resource
    private EsIndexProperties esIndexProperties;

    @Resource
    private SubWaybillListMapper subWaybillListMapper;

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.esIndexProperties.getSubWaybillIndex();
    }


    @Override
    public RestResponse<List<SubWaybillVO>> search(SubWaybillQO qo) {
//        AssertsUtils.isBlank(qo.getUnLoadStartTime(), ExcepitonEnum.ERROR_UNKNOW,"卸货确认开始时间不能为空");
//        AssertsUtils.isBlank(qo.getUnLoadEndTime(), ExcepitonEnum.ERROR_UNKNOW,"卸货确认结束时间不能为空");
        LambdaEsQueryWrapper<SubWaybillIndex> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.index(esIndexProperties.getSubWaybillIndex());
        genSearch(wrapper,qo);
        List<SubWaybillIndex> subWaybillList = subWaybillListMapper.selectList(wrapper);
        List<SubWaybillVO> data = WaybillConverter.INSTANCE.toSubWaybillVO(subWaybillList);
        return RestResponse.success(data);
    }


    private void genSearch(LambdaEsQueryWrapper<SubWaybillIndex> wrapper,SubWaybillQO qo){
        wrapper.isNotNull(SubWaybillIndex::getSubWaybillNo);
        if(StringUtils.isNotEmpty(qo.getGoodsNo())){
            wrapper.eq("goodsNo.keyword",qo.getGoodsNo());
        }

        if(StringUtils.isNotEmpty(qo.getDriverName())){
            wrapper.like(SubWaybillIndex::getDriverName,qo.getDriverName());
        }

        if(CollectionUtils.isNotEmpty(qo.getCompanyIdList())){
            wrapper.in(SubWaybillIndex::getCarrierCompanyId,qo.getCompanyIdList());
        }

        if(CollectionUtils.isNotEmpty(qo.getDispatchCarNoList())){
            wrapper.in(SubWaybillIndex::getWaybillNo,qo.getDispatchCarNoList());
        }

        if(CollectionUtils.isNotEmpty(qo.getGoodsNos())){
            wrapper.in("goodsNo.keyword",qo.getGoodsNos());
        }

        if(CollectionUtils.isNotEmpty(qo.getStatus())){
            wrapper.in(SubWaybillIndex::getSubWaybillStatus,qo.getStatus());
        }
        if(ObjectUtil.isNotNull(qo.getIsSync())){
            wrapper.eq(SubWaybillIndex::getIsSync,qo.getIsSync());
        }
        if(StringUtils.isNotBlank(qo.getUnLoadStartTime()) && StringUtils.isNotBlank(qo.getUnLoadEndTime())){
            wrapper.ge(SubWaybillIndex::getTransEndTime, qo.getUnLoadStartTime());
            wrapper.le(SubWaybillIndex::getTransEndTime, qo.getUnLoadEndTime());
        }

        if(StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())){
            wrapper.ge(SubWaybillIndex::getTransStartTime, qo.getStartTime());
            wrapper.le(SubWaybillIndex::getTransStartTime, qo.getEndTime());
        }

        if(StringUtils.isNotBlank(qo.getCarrierCompanyName())){
            wrapper.like(SubWaybillIndex::getCarrierCompanyName,qo.getCarrierCompanyName());
        }

        if(StringUtils.isNotBlank(qo.getDispatchCarNo())){
            wrapper.like(SubWaybillIndex::getWaybillNo,qo.getDispatchCarNo());
        }
    }

}
