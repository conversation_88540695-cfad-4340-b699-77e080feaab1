package com.wzc.be.es.core.waybill.handler;


import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.core.waybill.converter.WaybillConverter;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.core.waybill.mapper.SubWaybillListMapper;
import com.wzc.be.es.data.waybill.qo.RelevanceWaybillQO;
import com.wzc.be.es.data.waybill.vo.RelevanceWaybillVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 关联运单-查询关联的运单
 * <AUTHOR> <PERSON>
 */
@Slf4j
@Component
public class RelevanceWaybillHandler implements BaseSearchHandler<RelevanceWaybillQO, List<RelevanceWaybillVO>> {

    @Value("${relevanceWaybill.search.type:relevanceWaybillHandler}")
    private String searchType;

    @Resource
    private EsIndexProperties esIndexProperties;

    @Resource
    private SubWaybillListMapper subWaybillListMapper;


    @Override
    public RestResponse<List<RelevanceWaybillVO>> search(RelevanceWaybillQO qo) {
        LambdaEsQueryWrapper<SubWaybillIndex> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.isNotNull(SubWaybillIndex::getSubWaybillNo);
        wrapper.eq(SubWaybillIndex::getIsRelevance,1);
        wrapper.eq("relevance",0);
        wrapper.ne(SubWaybillIndex::getSubWaybillStatus,9);
        if(StringUtils.isNotBlank(qo.getDriverPhone())){
            wrapper.eq(SubWaybillIndex::getDriverPhone,qo.getDriverPhone());
        }
        if(StringUtils.isNotBlank(qo.getCarNo())){
            wrapper.eq(SubWaybillIndex::getCarNo,qo.getCarNo());
        }
        if(StringUtils.isNotBlank(qo.getFromAddress())){
            wrapper.like("sender.fullAddress",qo.getFromAddress());
        }
        if(StringUtils.isNotBlank(qo.getToAddress())){
            wrapper.like("receiver.fullAddress",qo.getToAddress());
        }
        if(StringUtils.isNotBlank(qo.getFromCustomerName())){
            wrapper.eq("shipperCompanyName",qo.getFromCustomerName());
        }
        if(StringUtils.isNotBlank(qo.getToCustomerName())){
            wrapper.eq("shipperCompanyName",qo.getToCustomerName());
        }
        wrapper.eq(SubWaybillIndex::getOrderType,qo.getOrderType());
        wrapper.index(esIndexProperties.getSubWaybillIndex());
        List<SubWaybillIndex> subWaybillIndices = subWaybillListMapper.selectList(wrapper);
        if(CollectionUtils.isNotEmpty(subWaybillIndices)){
            List<RelevanceWaybillVO> data = WaybillConverter.INSTANCE.toRelevanceWaybillVO(subWaybillIndices);
            return RestResponse.success(data);
        }
        return RestResponse.success();
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.esIndexProperties.getSubWaybillIndex();
    }
}
