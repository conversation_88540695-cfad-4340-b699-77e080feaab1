package com.wzc.be.es.core.business.handler;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.enums.AuditStatusEnums;
import com.wzc.be.es.common.enums.BusinessQueryTabTypeEnums;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.common.enums.SettlementStatusEnums;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.core.business.index.BusinessIndex;
import com.wzc.be.es.core.common.CommonRedisResultVO;
import com.wzc.be.es.data.business.qo.BmsEsBusinessQueryPageQO;
import com.wzc.be.es.data.business.vo.BmsBusinessEsVO;
import com.wzc.common.user.utils.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.wzc.be.es.common.constant.Constants.UPDATE_INDEX_REDIS_KEY;

@Slf4j
@Component
public class BmsBusinessPageSearchHandler extends BmsBusinessBaseSearchHandler<BmsEsBusinessQueryPageQO, EsPageVO<BmsBusinessEsVO>> {

    @Resource
    private EsIndexProperties esIndexProperties;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    public RestResponse<EsPageVO<BmsBusinessEsVO>> search(BmsEsBusinessQueryPageQO qo) {
        log.info("对账单分页查询参数：{}", JSONUtil.toJsonStr(qo));
        try {
            Long companyId = null;
            if (UserUtils.isShipperPc()) {
                companyId = qo.getCompanyId();
            }
            if (UserUtils.isOperatePc()) {
                companyId = qo.getShipperCompanyId();
            }
            if (UserUtils.isIdentityCarrier()) {
                companyId = qo.getCarrierId();
            }
            Integer queryTabType = qo.getQueryTabType();
            Boolean allTab = ObjectUtil.isNotNull(queryTabType) && ObjectUtil.equals(BusinessQueryTabTypeEnums.ALL.getCode(), queryTabType);
            Boolean unAuditTab = ObjectUtil.isNotNull(queryTabType) && ObjectUtil.equals(BusinessQueryTabTypeEnums.UN_AUDIT.getCode(), queryTabType);
            Boolean rcUnSettleTab = ObjectUtil.isNotNull(queryTabType) && ObjectUtil.equals(BusinessQueryTabTypeEnums.RC_UN_SETTLE.getCode(), queryTabType);
            List<CommonRedisResultVO> commonRedisUpdateResultVOList = null;
            Integer size = qo.getSize();
            if (BooleanUtil.isTrue(allTab) || BooleanUtil.isTrue(unAuditTab) || BooleanUtil.isTrue(rcUnSettleTab)) {
                RMapCache<String, String> updateMapCache = redissonClient.getMapCache(UPDATE_INDEX_REDIS_KEY + esIndexProperties.getBmsBusinessIndex() + ":" + companyId);
                commonRedisUpdateResultVOList = updateMapCache.readAllEntrySet().stream().map(e -> JSONUtil.toBean(e.getValue(), CommonRedisResultVO.class)).collect(Collectors.toList());
                size += commonRedisUpdateResultVOList.size();
                qo.setSize(size);
            }
            SearchRequest searchRequest = new SearchRequest(esIndexProperties.getBmsBusinessIndex());
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            BoolQueryBuilder boolQueryBuilder = super.getMainBoolQueryBuilder(qo);
            Integer page = Convert.toInt(qo.getPage(), 1);
            int from = (page == 0 ? page : page - 1) * size;
            searchSourceBuilder.from(from);
            searchSourceBuilder.size(size);
            String[] fetchSourceFields = {FieldUtils.val(BusinessIndex::getBusinessId), FieldUtils.val(BusinessIndex::getDispatchNo)};
            searchSourceBuilder.fetchSource(fetchSourceFields, null);
            searchSourceBuilder.query(boolQueryBuilder);
            SortOrder sortOrder = SortOrder.DESC;
            if (ObjectUtil.isNotNull(queryTabType)) {
                // 未对账默认按单据生成时间排降序
                if (ObjectUtil.equals(BusinessQueryTabTypeEnums.UN_RC.getCode(), queryTabType)) {
                    searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(BusinessIndex::getCreateTime)).order(sortOrder));
                }
                // 已对账未结算和已结算默认按审核时间排降序
                if (CollectionUtil.newArrayList(BusinessQueryTabTypeEnums.RC_UN_SETTLE.getCode(), BusinessQueryTabTypeEnums.SETTLE.getCode()).contains(queryTabType)) {
                    // 平台登录取平台对上审核时间
                    if (UserUtils.isOperatePc()) {
                        searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(BusinessIndex::getShipperAuditTimeUp)).order(sortOrder));
                    }
                    // 承运商登录取承运商审核时间
                    if (UserUtils.isCarrierPc()) {
                        searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(BusinessIndex::getArAuditTime)).order(sortOrder));
                    }
                }
                // 全部和待审核和已审核默认按更新时间排降序
                if (CollectionUtil.newArrayList(BusinessQueryTabTypeEnums.ALL.getCode(), BusinessQueryTabTypeEnums.UN_AUDIT.getCode()).contains(queryTabType)) {
                    searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(BusinessIndex::getUpdateTime)).order(sortOrder));
                }
                //已审核默认按平台审核时间排降序
                if (ObjectUtil.equals(BusinessQueryTabTypeEnums.AUDIT.getCode(), queryTabType)) {
                    //货主登录取货主审核时间
                    if(UserUtils.isShipperPc()){
                        searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(BusinessIndex::getApAuditTime)).order(sortOrder));
                    }
                    if(UserUtils.isOperatePc()){
                        searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(BusinessIndex::getShipperAuditTime)).order(sortOrder));
                    }
                }
                //对账单排序增加单号排序，解决查询重复的问题
                searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(BusinessIndex::getDispatchNo)).order(sortOrder));
            }
            searchSourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
            searchSourceBuilder.trackTotalHits(true);
            searchRequest.source(searchSourceBuilder);
            log.info("对账单搜索参数:{}",searchSourceBuilder.toString());
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            SearchHit[] hitResult = searchHits.getHits();
            JSONArray array = new JSONArray();
            Stream.of(hitResult).forEach(itemHit -> {
                Map<String, Object> sourceAsMap = itemHit.getSourceAsMap();
                array.add(sourceAsMap);
            });
            TotalHits totalHits = searchHits.getTotalHits();
            List<BmsBusinessEsVO> list = JSONUtil.toList(array.toJSONString(0), BmsBusinessEsVO.class);
            if (CollectionUtil.isNotEmpty(commonRedisUpdateResultVOList)) {
                if (BooleanUtil.isTrue(allTab)) {
                    List<CommonRedisResultVO> removeList = commonRedisUpdateResultVOList.stream().filter(c -> ObjectUtil.equals(c.getAfterStatus(), AuditStatusEnums.RETURNED.getCode())).collect(Collectors.toList());
                    list.removeIf(es -> removeList.stream().anyMatch(redis -> es.getDispatchNo().equals(redis.getUnionNo())));
                }
                if (BooleanUtil.isTrue(unAuditTab)) {
                    List<CommonRedisResultVO> removeList = commonRedisUpdateResultVOList.stream().filter(c -> ObjectUtil.equals(c.getAfterStatus(), AuditStatusEnums.TO_BE_REVIEWED.getCode())).collect(Collectors.toList());
                    list.removeIf(es -> removeList.stream().anyMatch(redis -> es.getDispatchNo().equals(redis.getUnionNo())));
                }
                if (BooleanUtil.isTrue(rcUnSettleTab)) {
                    List<CommonRedisResultVO> removeList = commonRedisUpdateResultVOList.stream().filter(c -> ObjectUtil.equals(c.getAfterStatus(), SettlementStatusEnums.HAVE_ALREADY_SETTLED.getCode())).collect(Collectors.toList());
                    list.removeIf(es -> removeList.stream().anyMatch(redis -> es.getDispatchNo().equals(redis.getUnionNo())));
                }
            }
            EsPageVO<BmsBusinessEsVO> esPageRes = new EsPageVO<>();
            esPageRes.setTotal(totalHits.value);
            esPageRes.setList(list);
            return RestResponse.success(esPageRes);
        } catch (Exception e) {
            log.error("对账单分页查询异常：", e);
        }
        return RestResponse.error(ExcepitonEnum.ERROR_UNKNOW);
    }

    @Override
    public String getSearchType() {
        return SearchTypeEnums.BUSINESS_RC_YD_PAGE.getType();
    }

    @Override
    public String getIndex() {
        return esIndexProperties.getBmsBusinessIndex();
    }
}
