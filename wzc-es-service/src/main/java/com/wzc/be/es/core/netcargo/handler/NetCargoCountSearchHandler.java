package com.wzc.be.es.core.netcargo.handler;

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.data.netcargo.qo.NetCargoEsQO;
import com.wzc.be.es.data.netcargo.vo.NetCargoCountEsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class NetCargoCountSearchHandler extends NetCargoBaseSearchHandler<NetCargoEsQO, NetCargoCountEsVO> {

    @Value("${order.search.index:order_index}")
    private String orderIndex;

    @Value("${transport.search.index:transport_index}")
    private String transportIndex;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Override
    public RestResponse<NetCargoCountEsVO> search(NetCargoEsQO qo) {
        log.info("发单管理状态统计参数:{}", JSONUtil.toJsonStr(qo));
        SearchRequest searchRequest = new SearchRequest(orderIndex, transportIndex);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(0);
        searchSourceBuilder.timeout(new TimeValue(60, TimeUnit.SECONDS));
        searchSourceBuilder.trackTotalHits(true);
        NetCargoCountEsVO netCargoCountEsVO = new NetCargoCountEsVO();
        try {
            // 全部
            qo.setQueryTabType(0);
            BoolQueryBuilder noRcboolQueryBuilder = super.getMainBoolQueryBuilder(qo);
            searchSourceBuilder.query(noRcboolQueryBuilder);
            searchRequest.source(searchSourceBuilder);
            SearchResponse noRcSearchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            TotalHits noRcTotalHits = noRcSearchResponse.getHits().getTotalHits();
            netCargoCountEsVO.setAllCount(Convert.toInt(noRcTotalHits.value));
            // 待审核
            qo.setQueryTabType(1);
            BoolQueryBuilder rcNoSettleBoolQueryBuilder = super.getMainBoolQueryBuilder(qo);
            searchSourceBuilder.query(rcNoSettleBoolQueryBuilder);
            searchRequest.source(searchSourceBuilder);
            SearchResponse rcNoSettleSearchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            TotalHits rcNoSettleTotalHits = rcNoSettleSearchResponse.getHits().getTotalHits();
            netCargoCountEsVO.setToBeReviewedCount(Convert.toInt(rcNoSettleTotalHits));
            return RestResponse.success(netCargoCountEsVO);
        } catch (Exception e) {
            log.error("发单管理状态统计异常: ", e);
            return RestResponse.error(ExcepitonEnum.ERROR_UNKNOW);
        }
    }

    @Override
    public String getSearchType() {
        return SearchTypeEnums.NET_CARGO_COUNT.getType();
    }

    @Override
    public String[] getIndices() {
        return new String[]{orderIndex, transportIndex};
    }
}
