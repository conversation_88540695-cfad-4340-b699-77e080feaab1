package com.wzc.be.es.adapter.data;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.data.data.es.toc.model.TocRdBusinessPageQO;
import com.wzc.be.data.data.es.toc.model.TocRdBusinessVO;
import com.wzc.be.es.adapter.data.feign.TocRdBusinessIndexFeign;
import com.wzc.common.core.api.utils.ApiResultUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author：chenxingguang
 * @Date：2024/2/4 14:36
 * @Version：1.0.0
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class TocRdBusinessIndexFeignClient {

    private final TocRdBusinessIndexFeign tocRdBusinessIndexFeign;

    public List<TocRdBusinessVO> search(TocRdBusinessPageQO tocBusinessEsPageQO) {
        RestResponse<List<TocRdBusinessVO>> search = tocRdBusinessIndexFeign.search(tocBusinessEsPageQO);
        List<TocRdBusinessVO> esBusinessVOList = ApiResultUtils.getDataOrThrow(search);
        return esBusinessVOList;
    }
}
