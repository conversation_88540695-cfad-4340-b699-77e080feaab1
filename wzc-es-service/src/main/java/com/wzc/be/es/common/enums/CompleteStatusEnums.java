package com.wzc.be.es.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/8/24 22:21
 */
@Getter
@AllArgsConstructor
public enum CompleteStatusEnums {

    /**
     * 运输计划状态
     */
    TRANSPORT_NOT_COMPLETE(1, "运输未完成"),
    NOT_SETTLE(3, "待结算"),
    ALREADY_SETTLE(4, "已结算")
    ;

    private final Integer code;

    private final String name;


}
