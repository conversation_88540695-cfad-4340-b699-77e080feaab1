package com.wzc.be.es.adapter.data;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.data.data.es.reconciliation.model.ReconciliationGroupBusinessEsQueryPageQO;
import com.wzc.be.data.data.es.reconciliation.model.ReconciliationPageBusinessVO;
import com.wzc.be.es.adapter.data.feign.ReconciliationPageIndexFeign;
import com.wzc.common.core.api.utils.ApiResultUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author：chenxingguang
 * @Date：2024/2/4 14:34
 * @Version：1.0.0
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ReconciliationPageIndexFeignClient {

    private final ReconciliationPageIndexFeign reconciliationPageIndexFeign;

    public List<ReconciliationPageBusinessVO> search(ReconciliationGroupBusinessEsQueryPageQO businessEsQueryPageQO) {
        RestResponse<List<ReconciliationPageBusinessVO>> search = reconciliationPageIndexFeign.search(businessEsQueryPageQO);
        List<ReconciliationPageBusinessVO> bmsRcGroupVOS = ApiResultUtils.getDataOrThrow(search);
        return bmsRcGroupVOS;
    }
}
