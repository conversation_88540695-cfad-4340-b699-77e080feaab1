package com.wzc.be.es.core.transport.converter;

import com.wzc.be.data.data.es.transport.dto.TransportEsDTO;
import com.wzc.be.data.data.es.transport.dto.TransportGroupDTO;
import com.wzc.be.data.data.es.transport.dto.TransportQueryPageDTO;
import com.wzc.be.es.core.transport.index.TransportIndex;
import com.wzc.be.es.data.transport.qo.TransportQueryPageQO;
import com.wzc.be.es.data.transport.vo.TransportEsVO;
import com.wzc.be.es.data.transport.vo.TransportGroupVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年07月13日 15:05
 */
@Mapper(componentModel = "spring")
public interface TransportConverter {

    TransportConverter INSTANCE = Mappers.getMapper(TransportConverter.class);

    List<TransportEsVO> indexListToVoList(List<TransportIndex> transportIndexList);

    TransportQueryPageDTO pageQOToDTO(TransportQueryPageQO transportQueryPageQO);

    List<TransportEsVO> transportDTOListToVO(List<TransportEsDTO> transportEsDTOList);

    List<TransportGroupVO> groupTransportDTOListToVO(List<TransportGroupDTO> transportEsDTOList);

    List<TransportIndex> groupDTOListToIndexList (List<TransportGroupDTO> transportGroupDTOList);

    List<TransportGroupVO> indexListToGroupVOList(List<TransportIndex> list);

}
