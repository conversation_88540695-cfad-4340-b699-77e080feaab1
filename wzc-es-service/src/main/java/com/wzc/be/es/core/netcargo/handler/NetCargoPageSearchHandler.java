package com.wzc.be.es.core.netcargo.handler;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.core.order.index.OrderIndex;
import com.wzc.be.es.core.transport.index.TransportIndex;
import com.wzc.be.es.data.netcargo.qo.NetCargoEsQO;
import com.wzc.be.es.data.netcargo.vo.NetCargoEsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

@Slf4j
@Component
public class NetCargoPageSearchHandler extends NetCargoBaseSearchHandler<NetCargoEsQO, EsPageVO<NetCargoEsVO>> {

    @Resource
    private EsIndexProperties esIndexProperties;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Override
    public RestResponse<EsPageVO<NetCargoEsVO>> search(NetCargoEsQO qo) {
        log.info("发单管理分页查询参数：{}", JSONUtil.toJsonStr(qo));
        try {
            SearchRequest searchRequest = new SearchRequest(esIndexProperties.getOrderIndex(), esIndexProperties.getTransportIndex());
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            BoolQueryBuilder boolQueryBuilder = super.getMainBoolQueryBuilder(qo);
            int from = (qo.getPage() == 0 ? qo.getPage() : qo.getPage() - 1) * qo.getSize();
            searchSourceBuilder.from(from);
            searchSourceBuilder.size(qo.getSize());
            String[] fetchSourceFields = {FieldUtils.val(OrderIndex::getOrderNo), FieldUtils.val(TransportIndex::getTransportNo)};
            searchSourceBuilder.fetchSource(fetchSourceFields, null);
            searchSourceBuilder.query(boolQueryBuilder);
            SortOrder sortOrder = SortOrder.DESC;
            searchSourceBuilder.sort(SortBuilders.fieldSort("netCargoCreateTime").order(sortOrder));
            searchSourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
            searchSourceBuilder.trackTotalHits(true);
            searchRequest.source(searchSourceBuilder);
            log.info("发单管理搜索参数：{}", JSONUtil.toJsonStr(searchSourceBuilder));
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            SearchHit[] hitResult = searchHits.getHits();
            JSONArray array = new JSONArray();
            Stream.of(hitResult).forEach(itemHit -> {
                Map<String, Object> sourceAsMap = itemHit.getSourceAsMap();
                array.add(sourceAsMap);
            });
            List<NetCargoEsVO> list = JSONUtil.toList(array.toJSONString(0), NetCargoEsVO.class);
            EsPageVO<NetCargoEsVO> esPageRes = new EsPageVO<>();
            esPageRes.setList(list);
            TotalHits totalHits = searchHits.getTotalHits();
            esPageRes.setTotal(totalHits.value);
            return RestResponse.success(esPageRes);
        } catch (Exception e) {
            log.error("发单管理分页查询异常：", e);
        }
        return RestResponse.error(ExcepitonEnum.ERROR_UNKNOW);
    }

    @Override
    public String getSearchType() {
        return SearchTypeEnums.NET_CARGO_PAGE.getType();
    }

    @Override
    public String[] getIndices() {
        return new String[]{esIndexProperties.getOrderIndex(), esIndexProperties.getTransportIndex()};
    }
}
