/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.core.auth.service;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.handler.SearchHandler;
import com.wzc.be.es.common.service.EsBaseSearchService;
import com.wzc.be.es.commons.PageVO;
import com.wzc.be.es.data.auth.vo.AuthRecordVO;
import com.wzc.be.es.data.waybill.qo.RequestPageQO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022年10月13日 3:17 PM
 */
@Component
@Slf4j
public class EsAuthService extends EsBaseSearchService {

    /**
     * 审核记录
     * @param requestPageQo
     * @return
     */
    public RestResponse<PageVO<AuthRecordVO>> authRecordPageQuery(RequestPageQO requestPageQo) {
        String searchType = requestPageQo.getSearchType();
        SearchHandler searchHandler = getSearchHandler(searchType);
        return searchHandler.search( requestPageQo.getRequestJson());
    }

}
