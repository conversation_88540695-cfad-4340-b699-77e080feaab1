package com.wzc.be.es.adapter.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.admin.indices.alias.IndicesAliasesRequest;
import org.elasticsearch.action.admin.indices.alias.get.GetAliasesRequest;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.admin.indices.get.GetIndexRequest;
import org.elasticsearch.action.admin.indices.settings.put.UpdateSettingsRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 冷数据索引管理服务
 * 提供冷数据索引的创建、管理、查询等功能
 *
 * <AUTHOR> Assistant
 * @since 2025/02/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ColdDataIndexService {

    private final RestHighLevelClient restHighLevelClient;

    /**
     * 生成冷数据索引名称
     * @param originalIndexName 原索引名称
     * @param year 年份
     * @return 冷数据索引名称
     */
    public String generateColdIndexName(String originalIndexName, String year) {
        return originalIndexName + "_cold_" + year;
    }

    /**
     * 生成当前年份的冷数据索引名称
     */
    public String generateCurrentYearColdIndexName(String originalIndexName) {
        String currentYear = String.valueOf(LocalDateTime.now().getYear());
        return generateColdIndexName(originalIndexName, currentYear);
    }

    /**
     * 检查索引是否存在
     */
    public boolean indexExists(String indexName) {
        try {
            GetIndexRequest request = new GetIndexRequest(indexName);
            return restHighLevelClient.indices().exists(request, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("检查索引 {} 是否存在时发生异常", indexName, e);
            return false;
        }
    }

    /**
     * 获取索引的文档数量
     */
    public long getIndexDocumentCount(String indexName) {
        try {
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.size(0);
            searchSourceBuilder.trackTotalHits(true);
            searchSourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));

            SearchRequest searchRequest = new SearchRequest(indexName);
            searchRequest.source(searchSourceBuilder);

            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            return searchResponse.getHits().getTotalHits().value;
        } catch (Exception e) {
            log.error("获取索引 {} 文档数量时发生异常", indexName, e);
            return 0;
        }
    }

    /**
     * 获取指定原索引的所有冷数据索引
     */
    public List<String> getColdIndexes(String originalIndexName) {
        List<String> coldIndexes = new ArrayList<>();
        try {
            // 查找所有匹配的冷数据索引
            String pattern = originalIndexName + "_cold_*";
            GetIndexRequest request = new GetIndexRequest(pattern);
            
            if (restHighLevelClient.indices().exists(request, RequestOptions.DEFAULT)) {
                // 这里需要使用更复杂的逻辑来获取所有匹配的索引
                // 简化实现，可以根据年份范围生成可能的索引名称
                int currentYear = LocalDateTime.now().getYear();
                for (int year = currentYear - 10; year <= currentYear; year++) {
                    String coldIndexName = generateColdIndexName(originalIndexName, String.valueOf(year));
                    if (indexExists(coldIndexName)) {
                        coldIndexes.add(coldIndexName);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取原索引 {} 的冷数据索引列表时发生异常", originalIndexName, e);
        }
        return coldIndexes;
    }

    /**
     * 创建冷数据索引别名
     * 为便于查询，可以为所有冷数据索引创建一个统一的别名
     */
    public boolean createColdDataAlias(String originalIndexName) {
        try {
            String aliasName = originalIndexName + "_cold_all";
            List<String> coldIndexes = getColdIndexes(originalIndexName);
            
            if (coldIndexes.isEmpty()) {
                log.info("原索引 {} 没有冷数据索引，跳过别名创建", originalIndexName);
                return true;
            }

            IndicesAliasesRequest request = new IndicesAliasesRequest();
            for (String coldIndex : coldIndexes) {
                IndicesAliasesRequest.AliasActions aliasAction = 
                    new IndicesAliasesRequest.AliasActions(IndicesAliasesRequest.AliasActions.Type.ADD)
                        .index(coldIndex)
                        .alias(aliasName);
                request.addAliasAction(aliasAction);
            }

            AcknowledgedResponse response = restHighLevelClient.indices().updateAliases(request, RequestOptions.DEFAULT);
            
            if (response.isAcknowledged()) {
                log.info("成功为原索引 {} 创建冷数据别名: {}, 包含索引: {}", 
                    originalIndexName, aliasName, coldIndexes);
                return true;
            } else {
                log.warn("创建冷数据别名失败: {}", aliasName);
                return false;
            }
        } catch (Exception e) {
            log.error("创建原索引 {} 的冷数据别名时发生异常", originalIndexName, e);
            return false;
        }
    }

    /**
     * 优化冷数据索引设置
     * 对冷数据索引进行存储优化
     */
    public boolean optimizeColdIndexSettings(String coldIndexName) {
        try {
            UpdateSettingsRequest request = new UpdateSettingsRequest(coldIndexName);
            
            Settings.Builder settingsBuilder = Settings.builder()
                .put("index.refresh_interval", "60s") // 降低刷新频率
                .put("index.number_of_replicas", 0) // 移除副本以节省存储
                .put("index.blocks.write", true) // 设置为只读
                .put("index.merge.policy.max_merged_segment", "5gb") // 优化合并策略
                .put("index.codec", "best_compression"); // 使用最佳压缩

            request.settings(settingsBuilder);

            AcknowledgedResponse response = restHighLevelClient.indices().putSettings(request, RequestOptions.DEFAULT);
            
            if (response.isAcknowledged()) {
                log.info("成功优化冷数据索引设置: {}", coldIndexName);
                return true;
            } else {
                log.warn("优化冷数据索引设置失败: {}", coldIndexName);
                return false;
            }
        } catch (Exception e) {
            log.error("优化冷数据索引 {} 设置时发生异常", coldIndexName, e);
            return false;
        }
    }

    /**
     * 删除过期的冷数据索引
     * @param originalIndexName 原索引名称
     * @param retentionYears 保留年数
     */
    public int deleteExpiredColdIndexes(String originalIndexName, int retentionYears) {
        int deletedCount = 0;
        try {
            int currentYear = LocalDateTime.now().getYear();
            int expiredYear = currentYear - retentionYears;
            
            List<String> coldIndexes = getColdIndexes(originalIndexName);
            
            for (String coldIndex : coldIndexes) {
                // 从索引名称中提取年份
                String[] parts = coldIndex.split("_cold_");
                if (parts.length == 2) {
                    try {
                        int indexYear = Integer.parseInt(parts[1]);
                        if (indexYear <= expiredYear) {
                            if (deleteColdIndex(coldIndex)) {
                                deletedCount++;
                                log.info("删除过期冷数据索引: {} ({}年)", coldIndex, indexYear);
                            }
                        }
                    } catch (NumberFormatException e) {
                        log.warn("无法解析冷数据索引年份: {}", coldIndex);
                    }
                }
            }
            
            log.info("删除原索引 {} 的过期冷数据索引完成，删除数量: {}", originalIndexName, deletedCount);
            
        } catch (Exception e) {
            log.error("删除原索引 {} 的过期冷数据索引时发生异常", originalIndexName, e);
        }
        return deletedCount;
    }

    /**
     * 删除指定的冷数据索引
     */
    public boolean deleteColdIndex(String coldIndexName) {
        try {
            DeleteIndexRequest request = new DeleteIndexRequest(coldIndexName);
            AcknowledgedResponse response = restHighLevelClient.indices().delete(request, RequestOptions.DEFAULT);
            
            if (response.isAcknowledged()) {
                log.info("成功删除冷数据索引: {}", coldIndexName);
                return true;
            } else {
                log.warn("删除冷数据索引失败: {}", coldIndexName);
                return false;
            }
        } catch (Exception e) {
            log.error("删除冷数据索引 {} 时发生异常", coldIndexName, e);
            return false;
        }
    }

    /**
     * 获取冷数据索引统计信息
     */
    public ColdIndexStats getColdIndexStats(String originalIndexName) {
        ColdIndexStats stats = new ColdIndexStats();
        stats.originalIndexName = originalIndexName;
        stats.coldIndexes = new ArrayList<>();
        
        try {
            List<String> coldIndexes = getColdIndexes(originalIndexName);
            
            for (String coldIndex : coldIndexes) {
                ColdIndexInfo info = new ColdIndexInfo();
                info.indexName = coldIndex;
                info.documentCount = getIndexDocumentCount(coldIndex);
                info.exists = indexExists(coldIndex);
                
                // 从索引名称中提取年份
                String[] parts = coldIndex.split("_cold_");
                if (parts.length == 2) {
                    try {
                        info.year = Integer.parseInt(parts[1]);
                    } catch (NumberFormatException e) {
                        info.year = 0;
                    }
                }
                
                stats.coldIndexes.add(info);
                stats.totalDocuments += info.documentCount;
            }
            
            stats.totalIndexes = stats.coldIndexes.size();
            
        } catch (Exception e) {
            log.error("获取原索引 {} 的冷数据统计信息时发生异常", originalIndexName, e);
        }
        
        return stats;
    }

    /**
     * 冷数据索引统计信息
     */
    public static class ColdIndexStats {
        public String originalIndexName;
        public int totalIndexes;
        public long totalDocuments;
        public List<ColdIndexInfo> coldIndexes;
    }

    /**
     * 冷数据索引信息
     */
    public static class ColdIndexInfo {
        public String indexName;
        public int year;
        public long documentCount;
        public boolean exists;
    }
}
