package com.wzc.be.es.adapter.job;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.wzc.be.es.adapter.job.bo.BizDataSyncParamBO;
import com.wzc.be.es.adapter.job.bo.EsToKafkaBO;
import com.wzc.be.es.common.enums.SyncTypeEnums;
import com.wzc.be.es.common.properties.KafkaProperties;
import com.wzc.be.es.core.auth.index.AuthRecordIndex;
import com.wzc.be.es.core.business.index.BusinessIndex;
import com.wzc.be.es.core.business.index.TocBusinessIndex;
import com.wzc.be.es.core.kafka.KafkaProducter;
import com.wzc.be.es.core.order.index.OrderIndex;
import com.wzc.be.es.core.transport.index.TransportIndex;
import com.wzc.be.es.core.warn.index.CscWarnExceptionIndex;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobContext;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.*;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@RequiredArgsConstructor
public class EsDataToDorisJob {

    private final KafkaProducter kafkaProducter;

    private final RestHighLevelClient restHighLevelClient;

    private final KafkaProperties kafkaProperties;

    @Value("${authRecord.search.index:md_audit_index}")
    private String mdAuditIndex;

    @Value("${bms.business.search.index:bms_business_index}")
    private String bmsBusinessIndex;

    @Value("${bms.toc.business.search.index:bms_toc_business_index}")
    private String bmsTocBusinessIndex;

    @Value("${order.search.index:order_index}")
    private String orderIndex;

    @Value("${csc.warn.exception.search.index:csc_warn_exception_index}")
    private String cscWarnExceptionIndex;

    @Value("${waybill.search.index:sub_waybill_index01}")
    private String subWaybillIndex01;

    @Value("${transport.group.no.search.index:group_transport_index}")
    private String groupTransportIndex;

    @Value("${transport.search.index:transport_index}")
    private String transportIndex;

    @Value("${es.data.to.doris.pageSize:5000}")
    private Integer pageSize;


    /**
     * md_audit_index es索引同步doris
     */
    @XxlJob("syncAuthRecordIndex")
    public ReturnT<String> syncAuthRecordIndex() {
        Integer syncType = getSyncType(mdAuditIndex);
        // 定义查询条件和排序方式
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        //创建时间默认倒序排序
        SortOrder sortOrder = SortOrder.ASC;
        searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(AuthRecordIndex::getAuditDate)).order(sortOrder));
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        if(SyncTypeEnums.INCREMENT.getCode().equals(syncType)){
            String startTime = DateUtil.yesterday().toDateStr() + " 00:00:00";
            String endTime = DateUtil.yesterday().toDateStr() + " 23:59:59";
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(AuthRecordIndex::getAuditDate)).gte(startTime));
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(AuthRecordIndex::getAuditDate)).lte(endTime));
            searchSourceBuilder.query(mainQueryBuilder);
        }
        searchSourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
        searchSourceBuilder.trackTotalHits(true);
        searchRecursively(searchSourceBuilder, pageSize, mdAuditIndex,kafkaProperties.getEsDorisSyncTopic());
        log.info("运行syncAuthRecordIndex数据同步结束");
        return ReturnT.SUCCESS;
    }

    /**
     * bms_business_index es索引同步doris
     */
    @XxlJob("bmsBusinessIndex")
    public ReturnT<String> bmsBusinessIndex() {
        Integer syncType = getSyncType(bmsBusinessIndex);
        // 定义查询条件和排序方式
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        //创建时间默认倒序排序
        SortOrder sortOrder = SortOrder.ASC;
        searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(BusinessIndex::getCreateTime)).order(sortOrder));
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        if(SyncTypeEnums.INCREMENT.getCode().equals(syncType)){
            String startTime = DateUtil.yesterday().toDateStr() + " 00:00:00";
            String endTime = DateUtil.yesterday().toDateStr() + " 23:59:59";
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getCreateTime)).gte(startTime));
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(BusinessIndex::getCreateTime)).lte(endTime));
            searchSourceBuilder.query(mainQueryBuilder);
        }
        searchSourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
        searchSourceBuilder.trackTotalHits(true);
        searchRecursively(searchSourceBuilder, pageSize, bmsBusinessIndex,kafkaProperties.getEsDorisSyncTopic());
        log.info("运行bmsBusinessIndex数据同步结束");
        return ReturnT.SUCCESS;
    }

    /**
     * bms_toc_business_index es索引同步doris
     */
    @XxlJob("bmsTocBusinessIndex")
    public ReturnT<String> bmsTocBusinessIndex() {
        Integer syncType = getSyncType(bmsTocBusinessIndex);
        // 定义查询条件和排序方式
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        //创建时间默认倒序排序
        SortOrder sortOrder = SortOrder.ASC;
        searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(TocBusinessIndex::getCreateTime)).order(sortOrder));
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        if(SyncTypeEnums.INCREMENT.getCode().equals(syncType)){
            String startTime = DateUtil.yesterday().toDateStr() + " 00:00:00";
            String endTime = DateUtil.yesterday().toDateStr() + " 23:59:59";
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getCreateTime)).gte(startTime));
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TocBusinessIndex::getCreateTime)).lte(endTime));
            searchSourceBuilder.query(mainQueryBuilder);
        }
        searchSourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
        searchSourceBuilder.trackTotalHits(true);
        searchRecursively(searchSourceBuilder, pageSize, bmsTocBusinessIndex,kafkaProperties.getEsDorisSyncTopic());
        log.info("运行bmsTocBusinessIndex数据同步结束");
        return ReturnT.SUCCESS;
    }

    /**
     * order_index es索引同步doris
     */
    @XxlJob("orderIndex")
    public ReturnT<String> orderIndex() {
        Integer syncType = getSyncType(orderIndex);
        // 定义查询条件和排序方式
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        //创建时间默认倒序排序
        SortOrder sortOrder = SortOrder.ASC;
        searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(OrderIndex::getCreateTime)).order(sortOrder));
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        if(SyncTypeEnums.INCREMENT.getCode().equals(syncType)){
            String startTime = DateUtil.yesterday().toDateStr() + " 00:00:00";
            String endTime = DateUtil.yesterday().toDateStr() + " 23:59:59";
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(OrderIndex::getCreateTime)).gte(startTime));
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(OrderIndex::getCreateTime)).lte(endTime));
            searchSourceBuilder.query(mainQueryBuilder);
        }
        searchSourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
        searchSourceBuilder.trackTotalHits(true);
        searchRecursively(searchSourceBuilder, pageSize, orderIndex,kafkaProperties.getEsDorisSyncTopic());
        log.info("运行orderIndex数据同步结束");
        return ReturnT.SUCCESS;
    }

    /**
     * csc_warn_exception_index es索引同步doris
     */
    @XxlJob("cscWarnExceptionIndex")
    public ReturnT<String> cscWarnExceptionIndex() {
        Integer syncType = getSyncType(cscWarnExceptionIndex);
        // 定义查询条件和排序方式
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        //创建时间默认倒序排序
        SortOrder sortOrder = SortOrder.ASC;
        searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(CscWarnExceptionIndex::getCreateTime)).order(sortOrder));
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        if(SyncTypeEnums.INCREMENT.getCode().equals(syncType)){
            String startTime = DateUtil.yesterday().toDateStr() + " 00:00:00";
            String endTime = DateUtil.yesterday().toDateStr() + " 23:59:59";
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(CscWarnExceptionIndex::getCreateTime)).gte(startTime));
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(CscWarnExceptionIndex::getCreateTime)).lte(endTime));
            searchSourceBuilder.query(mainQueryBuilder);
        }
        searchSourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
        searchSourceBuilder.trackTotalHits(true);
        searchRecursively(searchSourceBuilder, pageSize, cscWarnExceptionIndex,kafkaProperties.getEsDorisSyncTopic());
        log.info("运行cscWarnExceptionIndex数据同步结束");
        return ReturnT.SUCCESS;
    }

    /**
     * sub_waybill_index01 es索引同步doris
     */
    @XxlJob("subWaybillIndex01")
    public ReturnT<String> subWaybillIndex01() {
        Integer syncType = getSyncType(subWaybillIndex01);
        // 定义查询条件和排序方式
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        //创建时间默认倒序排序
        SortOrder sortOrder = SortOrder.ASC;
        searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(SubWaybillIndex::getCreateTime)).order(sortOrder));
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        if(SyncTypeEnums.INCREMENT.getCode().equals(syncType)){
            String startTime = DateUtil.yesterday().toDateStr() + " 00:00:00";
            String endTime = DateUtil.yesterday().toDateStr() + " 23:59:59";
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getCreateTime)).gte(startTime));
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getCreateTime)).lte(endTime));
            searchSourceBuilder.query(mainQueryBuilder);
        }
        searchSourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
        searchSourceBuilder.trackTotalHits(true);
        searchRecursively(searchSourceBuilder, pageSize, subWaybillIndex01,kafkaProperties.getEsDorisSyncTopic());
        log.info("运行subWaybillIndex01数据同步结束");
        return ReturnT.SUCCESS;
    }

    /**
     * group_transport_index es索引同步doris
     */
    @XxlJob("groupTransportIndex")
    public ReturnT<String> groupTransportIndex() {
        Integer syncType = getSyncType(groupTransportIndex);
        // 定义查询条件和排序方式
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        //创建时间默认倒序排序
        SortOrder sortOrder = SortOrder.ASC;
        searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(TransportIndex::getCreateTime)).order(sortOrder));
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        if(SyncTypeEnums.INCREMENT.getCode().equals(syncType)){
            String startTime = DateUtil.yesterday().toDateStr() + " 00:00:00";
            String endTime = DateUtil.yesterday().toDateStr() + " 23:59:59";
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TransportIndex::getCreateTime)).gte(startTime));
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TransportIndex::getCreateTime)).lte(endTime));
            searchSourceBuilder.query(mainQueryBuilder);
        }
        searchSourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
        searchSourceBuilder.trackTotalHits(true);
        searchRecursively(searchSourceBuilder, pageSize, groupTransportIndex,kafkaProperties.getEsDorisSyncTopic());
        log.info("运行groupTransportIndex数据同步结束");
        return ReturnT.SUCCESS;
    }

    /**
     * transport_index es索引同步doris
     */
    @XxlJob("transportIndex")
    public ReturnT<String> transportIndex() {
        Integer syncType = getSyncType(transportIndex);
        // 定义查询条件和排序方式
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        //创建时间默认倒序排序
        SortOrder sortOrder = SortOrder.ASC;
        searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(TransportIndex::getCreateTime)).order(sortOrder));
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        if(SyncTypeEnums.INCREMENT.getCode().equals(syncType)){
            String startTime = DateUtil.yesterday().toDateStr() + " 00:00:00";
            String endTime = DateUtil.yesterday().toDateStr() + " 23:59:59";
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TransportIndex::getCreateTime)).gte(startTime));
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TransportIndex::getCreateTime)).lte(endTime));
            searchSourceBuilder.query(mainQueryBuilder);
        }
        searchSourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
        searchSourceBuilder.trackTotalHits(true);
        searchRecursively(searchSourceBuilder, pageSize, transportIndex,kafkaProperties.getEsDorisSyncTopic());
        log.info("运行transportIndex数据同步结束");
        return ReturnT.SUCCESS;
    }


    private Integer getSyncType(String index){
        XxlJobContext param = XxlJobContext.getXxlJobContext();
        log.info("{}索引，es同步kafka开始，param:{}", index,param.getJobParam());
        BizDataSyncParamBO bizDataSyncParamBO = new BizDataSyncParamBO();
        if (StringUtils.isNotBlank(param.getJobParam())) {
            bizDataSyncParamBO = JSONUtil.toBean(param.getJobParam(), BizDataSyncParamBO.class);
        }
        return bizDataSyncParamBO.getSyncType();
    }

    private void searchRecursively(SearchSourceBuilder sourceBuilder,
                                   int pageSize,
                                   String index,
                                   String topic) {
        SearchRequest searchRequest = new SearchRequest(index);
        sourceBuilder.size(pageSize);
        searchRequest.source(sourceBuilder);
        searchRequest.scroll(TimeValue.timeValueMinutes(1L));
        int pageCount = 0;
        try {
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            String scrollId = searchResponse.getScrollId();
            SearchHit[] hits = searchResponse.getHits().getHits();
            List list = new ArrayList();
            for (SearchHit hit : hits) {
                Map<String, Object> sourceAsMap = hit.getSourceAsMap();
                if(!mdAuditIndex.equals(index)){
                    sourceAsMap.put("id",hit.getId());
                }
                sourceAsMap.put("indexName",index);
                list.add(sourceAsMap);
            }
            List<List> parts = Lists.partition(list, 1000);
            parts.stream().forEach(i -> {
                EsToKafkaBO esToKafkaBO = new EsToKafkaBO();
                esToKafkaBO.setJsonStr(JSONUtil.toJsonStr(i));
                esToKafkaBO.setTopic(topic);
                kafkaProducter.sendEsDataToKafka(esToKafkaBO);
            });
            pageCount = pageCount + list.size();
            while (hits != null && hits.length > 0) {
                SearchScrollRequest scrollRequest = new SearchScrollRequest(scrollId);
                scrollRequest.scroll(TimeValue.timeValueMinutes(1L));
                Thread.sleep(200);
                searchResponse = restHighLevelClient.scroll(scrollRequest, RequestOptions.DEFAULT);
                scrollId = searchResponse.getScrollId();
                hits = searchResponse.getHits().getHits();
                List childList = new ArrayList();
                for (SearchHit hit : hits) {
                    Map<String, Object> sourceAsMap = hit.getSourceAsMap();
                    if(!mdAuditIndex.equals(index)) {
                        sourceAsMap.put("id", hit.getId());
                    }
                    sourceAsMap.put("indexName",index);
                    childList.add(sourceAsMap);
                }
                List<List> childParts = Lists.partition(childList, 1000);
                childParts.stream().forEach(i -> {
                    EsToKafkaBO esToKafkaBO = new EsToKafkaBO();
                    esToKafkaBO.setJsonStr(JSONUtil.toJsonStr(i));
                    esToKafkaBO.setTopic(topic);
                    kafkaProducter.sendEsDataToKafka(esToKafkaBO);
                });
                pageCount = pageCount + childList.size();
                log.info("pageCount:"+pageCount);
            }
            ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
            clearScrollRequest.addScrollId(scrollId);
            ClearScrollResponse clearScrollResponse = restHighLevelClient.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
            boolean succeeded = clearScrollResponse.isSucceeded();
            log.info("es迁移clearScrollResponse结果为{}",succeeded);
        } catch (Exception e) {
            // 处理异常
            log.error("es迁移发送kafka异常",e);
        }
        log.info("执行索引{}的总条数长度为{}",index,pageCount);
    }



}
