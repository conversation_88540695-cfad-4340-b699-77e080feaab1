/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.core.transport.handler;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.adapter.data.DwdTmsTransportIndexClient;
import com.wzc.be.es.common.constant.Constants;
import com.wzc.be.es.common.enums.DeletedEnums;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.common.enums.QueryTypeEnums;
import com.wzc.be.es.common.enums.TransportStatusEnums;
import com.wzc.be.es.common.service.EsCommonAuthSearchService;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.core.common.CommonRedisResultVO;
import com.wzc.be.es.core.transport.converter.TransportConverter;
import com.wzc.be.es.core.transport.index.TransportIndex;
import com.wzc.be.es.data.transport.qo.TransportQueryPageQO;
import com.wzc.be.es.data.transport.vo.TransportGroupVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.wzc.be.es.common.constant.Constants.INSERT_DELETE_INDEX_REDIS_KEY;

/**
 * <AUTHOR>
 * @date 2023年11月16日
 * 货主端，平台端运输计划分页查询
 */
@Slf4j
@Component
public class TransportGroupNoSearchHandler extends TransportBaseSearchHandler<TransportQueryPageQO, EsPageVO<TransportGroupVO>>{

    @Value("${transport.group.no.search.index:group_transport_index}")
    private String index;
    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private EsCommonAuthSearchService esCommonAuthSearchService;

    @Autowired
    private DwdTmsTransportIndexClient DwdTmsTransportIndexClient;

    @Resource
    private RedissonClient redissonClient;

    @Override
    public RestResponse<EsPageVO<TransportGroupVO>> search(TransportQueryPageQO transportQueryPageQo) {
        try {
            if (transportQueryPageQo.getNeedAuth()){
                JSONObject jsonObject = esCommonAuthSearchService.checkQueryAuth(JSONUtil.parseObj(transportQueryPageQo), QueryTypeEnums.TRANSPORT);
                transportQueryPageQo = JSONUtil.toBean(jsonObject, TransportQueryPageQO.class);
            }
            //查询缓存里是否有删除的单号
            RMapCache<Object, String> mapCache = redissonClient.getMapCache(INSERT_DELETE_INDEX_REDIS_KEY + index + ":" + transportQueryPageQo.getCompanyId());
            List<CommonRedisResultVO> commonRedisResultVOList = mapCache.readAllEntrySet().stream().map(e -> JSONUtil.toBean(e.getValue(),CommonRedisResultVO.class)).collect(Collectors.toList());
            log.info("货主端，平台端运输计划模糊查询 commonRedisDeleteResultVOList is : {}", JSONUtil.toJsonStr(commonRedisResultVOList));
            log.info("货主端，平台端运输计划模糊查询 search param is : {}", JSONUtil.toJsonStr(transportQueryPageQo));
            BoolQueryBuilder mainBoolQueryBuilder = getMainBoolQueryBuilder(transportQueryPageQo, commonRedisResultVOList);

            Integer size = transportQueryPageQo.getSize();
            int fromElasticsearch = (transportQueryPageQo.getPage() == 0 ? transportQueryPageQo.getPage() : transportQueryPageQo.getPage() - 1) * size;

            //创建时间默认倒序排序
            SortOrder sortOrder = SortOrder.DESC;
            if (transportQueryPageQo.getIsAsc()){
                sortOrder = SortOrder.ASC;
            }
            SearchRequest searchRequest = new SearchRequest(index);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.trackTotalHits(true);
            sourceBuilder.from(fromElasticsearch);
            sourceBuilder.size(size);
            //要取的字段
            String[] fetchSourceFields = new String[]{FieldUtils.val(TransportIndex::getTransportNo),
                    FieldUtils.val(TransportIndex::getCreateTime),FieldUtils.val(TransportIndex::getAssignType),FieldUtils.val(TransportIndex::getOrderNo),FieldUtils.val(TransportIndex::getLoadDeviceCheck)};
            sourceBuilder.fetchSource(fetchSourceFields,null);
            sourceBuilder.query(mainBoolQueryBuilder);
            sourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(TransportIndex::getCreateTime)).order(sortOrder));
            searchRequest.source(sourceBuilder);
            long count = 0;
            log.info("货主运输计划请求参数DSL:{}", sourceBuilder.toString());
            List<TransportIndex> list = null;
            try {
                SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
                SearchHits searchHits = searchResponse.getHits();
                SearchHit[] hitResult = searchHits.getHits();
                JSONArray array = new JSONArray();
                Stream.of(hitResult).forEach(itemHit -> {
                    Map<String, Object> sourceAsMap = itemHit.getSourceAsMap();
                    array.add(sourceAsMap);
                });
                list = JSONUtil.toList(array.toJSONString(0), TransportIndex.class);
                TotalHits totalHits = searchHits.getTotalHits();
                count = totalHits.value;
            }catch (Exception e){
                log.info("查询运输计划分组es失败，开始查询doris",e);
                //如果不是 es查询异常 便往外抛
                Throwable throwable = ExceptionUtil.getRootCause(e);
                if(!(throwable instanceof ElasticsearchException)
                        || !((ElasticsearchException) throwable).getDetailedMessage().contains(Constants.ES_RESULT_WINDOW_IS_TOO_LARGE)){
                    throw new Exception(e);
                }
                CountRequest countRequest = new CountRequest(getIndex());
                countRequest.source(sourceBuilder);
                CountResponse countResponse = restHighLevelClient.count(countRequest, RequestOptions.DEFAULT);
                count = countResponse.getCount();
                list = DwdTmsTransportIndexClient.groupSearch(transportQueryPageQo,null);
            }
            //过滤暂停/恢复/取消指派的运输计划
            if (CollectionUtil.isNotEmpty(commonRedisResultVOList)){
                List<CommonRedisResultVO> addList = commonRedisResultVOList.stream().filter(c -> ObjectUtil.equals(c.getDelFlag(), DeletedEnums.YES.getCode()))
                        .sorted(Comparator.comparing(CommonRedisResultVO::getCreateTime))
                        .collect(Collectors.toList());
                List<CommonRedisResultVO> removeList = commonRedisResultVOList.stream().filter(c -> ObjectUtil.equals(c.getDelFlag(), DeletedEnums.NO.getCode()))
                        .sorted(Comparator.comparing(CommonRedisResultVO::getCreateTime))
                        .collect(Collectors.toList());
                if(Objects.isNull(transportQueryPageQo.getStatusList()) || transportQueryPageQo.getStatusList().contains(TransportStatusEnums.PAUSE.getCode())) {
                    for (CommonRedisResultVO commonRedisResultVO : addList) {
                        if(!contains(list, commonRedisResultVO.getUnionNo())){
                            TransportIndex transportIndex = new TransportIndex();
                            transportIndex.setTransportNo(commonRedisResultVO.getUnionNo());
                            list.add(0, transportIndex);
                        }
                    }
                }
                if(Objects.nonNull(transportQueryPageQo.getStatusList()) && (transportQueryPageQo.getStatusList().contains(TransportStatusEnums.PAUSE.getCode())
                        || transportQueryPageQo.getStatusList().contains(TransportStatusEnums.CREATED.getCode()))){
                    list.removeIf(p1 -> removeList.stream().anyMatch(p2 ->
                            p1.getOrderNo().equals(p2.getUnionNo())));
                }
            }
            List<TransportIndex> subList = list.stream().limit(size).collect(Collectors.toList());
            EsPageVO<TransportGroupVO> esPageVO = new EsPageVO<>();
            esPageVO.setTotal(count);
            esPageVO.setList(TransportConverter.INSTANCE.indexListToGroupVOList(subList));
            return RestResponse.success(esPageVO);
        } catch (Exception e) {
            log.error("货主端，平台端 运输计划分组查询异常.search error", e);
        }
        return RestResponse.error(ExcepitonEnum.ERROR_UNKNOW);
    }

    private boolean contains(List<TransportIndex> list, String value){
        return list.stream().anyMatch(o -> o.getTransportNo().equals(value));
    }

    @Override
    public String getSearchType() {
        return SearchTypeEnums.TRANSPORT_GROUP_NO_PAGE.getType();
    }

    @Override
    public String getIndex() {
        return index;
    }


}
