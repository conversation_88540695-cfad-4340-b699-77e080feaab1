package com.wzc.be.es.common.enums;

import com.baidu.mapcloud.cloudnative.common.model.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum WaybillCompleteStatusEnum implements IEnum {
    WAIT(1, "运输未完成"),
    UNRECONCILED(3, "待结算"),
    SETTLED(4, "已结算"),
    PAYMENT(5, "已支付");

    private final Integer code;
    private final String name;


    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return this.name;
    }

}
