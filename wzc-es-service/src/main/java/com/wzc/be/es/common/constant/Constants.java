package com.wzc.be.es.common.constant;

/**
 * 常量值类
 * <AUTHOR>
 */
public interface Constants {

    /**
     * 新增或者删除redis key
     */
    String INSERT_DELETE_INDEX_REDIS_KEY = "es_index_insert_delete:";

    /**
     * 修改redis key
     */
    String UPDATE_INDEX_REDIS_KEY = "es_index_update:";

    /**
     * 运输计划删除状态
     */
    Integer TRANSPORT_DELETE_STATUS = 8;

    /**
     * 运输计划删除状态(替换状态)
     */
    Integer TRANSPORT_DELETE_STATUS_9 = 9;

    /**
     *  es执行搜索抛出的异常
     **/
    String ES_RESULT_WINDOW_IS_TOO_LARGE = "Result window is too large";

    /**
     *  es执行搜索抛出的异常
     **/
    String ES_SEARCH_PHASE_EXECUTION_EXCEPTION = "search_phase_execution_exception";

    String DATE_FORMAT_YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

}
