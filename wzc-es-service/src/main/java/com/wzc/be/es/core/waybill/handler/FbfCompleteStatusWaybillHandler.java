package com.wzc.be.es.core.waybill.handler;

import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.enums.SubWaybillStatusEnum;
import com.wzc.be.es.common.enums.ZbStatusEnums;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.core.waybill.mapper.SubWaybillListMapper;
import com.wzc.be.es.data.waybill.qo.FbfCompleteStatusWaybillQO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class FbfCompleteStatusWaybillHandler implements BaseSearchHandler<FbfCompleteStatusWaybillQO, Boolean> {

    @Value("${subWaybill.search.type:fbfCompleteStatusWaybillHandler}")
    private String searchType;

    @Value("${subWaybill.search.index:sub_waybill_index01}")
    private String index;

    @Resource
    private SubWaybillListMapper subWaybillListMapper;

    @Override
    public RestResponse<Boolean> search(FbfCompleteStatusWaybillQO qo) {
        LambdaEsQueryWrapper<SubWaybillIndex> wrapper = new LambdaEsQueryWrapper<>();
        this.genSearch(wrapper, qo);
        wrapper.index(index);
        List<SubWaybillIndex> list = subWaybillListMapper.selectList(wrapper);
        return RestResponse.success(CollectionUtils.isNotEmpty(list));
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.index;
    }

    /**
     * 构建查询条件
     */
    private void genSearch(LambdaEsQueryWrapper<SubWaybillIndex> wrapper, FbfCompleteStatusWaybillQO qo) {
        wrapper.isNotNull(SubWaybillIndex::getCompleteStatus);
        wrapper.eq(SubWaybillIndex::getFbfId,qo.getFbfId());
        wrapper.eq(SubWaybillIndex::getDispatchCarType,qo.getFbfType());
        wrapper.eq(SubWaybillIndex::getZbStatus, ZbStatusEnums.ONLINE.getCode());
        wrapper.ne(SubWaybillIndex::getCompleteStatus,4);
        wrapper.ne(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.CANCELLED.getCode());
    }
}
