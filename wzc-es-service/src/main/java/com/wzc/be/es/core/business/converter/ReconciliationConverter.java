package com.wzc.be.es.core.business.converter;

import com.wzc.be.data.data.es.reconciliation.model.ReconciliationGroupBusinessEsQueryPageQO;
import com.wzc.be.data.data.es.reconciliation.model.ReconciliationGroupVOList;
import com.wzc.be.data.data.es.reconciliation.model.ReconciliationPageBusinessVO;
import com.wzc.be.es.data.business.qo.BusinessEsQueryPageQO;
import com.wzc.be.es.data.business.vo.BmsRcGroupVO;
import com.wzc.be.es.data.business.vo.BusinessEsVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author：chenxingguang
 * @Date：2024/2/4 15:49
 * @Version：1.0.0
 */
@Mapper(componentModel = "spring")
public interface ReconciliationConverter {

    ReconciliationConverter INSTANCE = Mappers.getMapper(ReconciliationConverter.class);

    List<BmsRcGroupVO> esToDorisQueryPage(List<ReconciliationGroupVOList>reconciliationRcGroupVOList);

    ReconciliationGroupBusinessEsQueryPageQO esToDorisQueryDto(BusinessEsQueryPageQO queryPageQo);

    List<BusinessEsVO> esToDorisQueryPageDto(List<ReconciliationPageBusinessVO> reconciliationPageBusinessVOS);
}
