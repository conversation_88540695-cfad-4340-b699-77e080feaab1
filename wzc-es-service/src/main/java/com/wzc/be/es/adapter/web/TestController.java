package com.wzc.be.es.adapter.web;

import cn.hutool.json.JSONObject;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.data.data.es.transport.dto.OrderPageDTO;
import com.wzc.be.data.data.es.transport.dto.TransportEsDTO;
import com.wzc.be.data.data.es.transport.dto.TransportQueryPageDTO;
import com.wzc.be.data.data.es.warn.dto.CscWarnExceptionEsPageDTO;
import com.wzc.be.es.common.enums.QueryTypeEnums;
import com.wzc.be.es.common.service.EsCommonAuthSearchService;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.core.business.handler.BmsBusinessAuditStatusCountSearchHandler;
import com.wzc.be.es.core.business.handler.BmsBusinessPageSearchHandler;
import com.wzc.be.es.core.business.handler.BmsBusinessRcStatusCountSearchHandler;
import com.wzc.be.es.data.business.qo.BmsEsBusinessQueryPageQO;
import com.wzc.be.es.data.business.vo.BmsBusinessEsVO;
import com.wzc.be.es.data.business.vo.BmsEsAuditStatusCountVO;
import com.wzc.be.es.data.business.vo.BmsEsRcStatusCountVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class TestController {

    @Autowired
    private BmsBusinessPageSearchHandler pageSearchHandler;

    @Autowired
    private BmsBusinessRcStatusCountSearchHandler rcStatusCountSearchHandler;

    @Autowired
    private BmsBusinessAuditStatusCountSearchHandler auditStatusCountSearchHandler;
    @Autowired
    private EsCommonAuthSearchService esCommonAuthSearchService;

    @PostMapping("page")
    public RestResponse<EsPageVO<BmsBusinessEsVO>> page(@RequestBody BmsEsBusinessQueryPageQO qo) {
        return pageSearchHandler.search(qo);
    }

    @PostMapping("rcStatusCount")
    public RestResponse<BmsEsRcStatusCountVO> rcStatusCount(@RequestBody BmsEsBusinessQueryPageQO qo) {
        return rcStatusCountSearchHandler.search(qo);
    }

    @PostMapping("auditStatusCount")
    public RestResponse<BmsEsAuditStatusCountVO> auditStatusCount(@RequestBody BmsEsBusinessQueryPageQO qo) {
        return auditStatusCountSearchHandler.search(qo);
    }

    @PostMapping("authQuery")
    public RestResponse<JSONObject> authQuery(@RequestBody BmsEsBusinessQueryPageQO qo) {
        JSONObject jsonObject = esCommonAuthSearchService.checkQueryAuth(new JSONObject(), QueryTypeEnums.ORDER);
        return RestResponse.success(jsonObject);
    }

}
