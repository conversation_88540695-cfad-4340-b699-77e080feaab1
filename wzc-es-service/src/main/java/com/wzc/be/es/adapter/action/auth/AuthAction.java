package com.wzc.be.es.adapter.action.auth;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.api.auth.EsAuthRecordSearchApi;
import com.wzc.be.es.commons.PageVO;
import com.wzc.be.es.core.auth.service.EsAuthService;
import com.wzc.be.es.data.auth.vo.AuthRecordVO;
import com.wzc.be.es.data.waybill.qo.RequestPageQO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023年08月11日
 */
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class AuthAction implements EsAuthRecordSearchApi {

    private final EsAuthService authService;

    /**
     * 审核记录列表搜索
     * @param requestPageQo requestPageQo
     * @return RestResponse<PageVO<AuthRecordVO>>
     */
    @Override
    public RestResponse<PageVO<AuthRecordVO>> authRecordPageQuery(RequestPageQO requestPageQo) {
        return authService.authRecordPageQuery(requestPageQo);
    }
}
