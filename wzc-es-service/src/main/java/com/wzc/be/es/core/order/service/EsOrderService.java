/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.core.order.service;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.common.handler.SearchHandler;
import com.wzc.be.es.common.service.EsBaseSearchService;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.data.order.qo.OrderPageQO;
import com.wzc.be.es.data.order.vo.OrderCountVO;
import com.wzc.be.es.data.order.vo.OrderIndexVO;
import com.wzc.be.es.data.order.vo.PlanOrderCountVO;
import com.wzc.be.es.data.waybill.qo.RequestPageQO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022年10月13日 3:17 PM
 */
@Component
@Slf4j
public class EsOrderService extends EsBaseSearchService {

    /**
     * 订单分页查询
     * @param orderPageQo orderPageQo
     * @return RestResponse
     */
    public RestResponse<EsPageVO<OrderIndexVO>> orderSearch(OrderPageQO orderPageQo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.ORDER_PAGE.getType());
        return searchHandler.search(orderPageQo);
    }

    /**
     * 订单统计
     * @param orderPageQo orderPageQo
     * @return Rest
     */
    public RestResponse<OrderCountVO> orderCount(OrderPageQO orderPageQo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.ORDER_COUNT.getType());
        return searchHandler.search(orderPageQo);
    }

    /**
     * 计划单统计
     */
    public RestResponse<PlanOrderCountVO> planOrderCountVO(RequestPageQO requestPageQo) {
        String searchType = requestPageQo.getSearchType();
        SearchHandler searchHandler = getSearchHandler(searchType);
        return searchHandler.search( requestPageQo.getRequestJson());
    }

    /**
     * 数智院订单分页查询
     * @param orderPageQo
     * @return
     */
    public RestResponse<EsPageVO<OrderIndexVO>> orderSzySearch(OrderPageQO orderPageQo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.ORDER_SZY_PAGE.getType());
        return searchHandler.search(orderPageQo);
    }
}
