package com.wzc.be.es.core.waybill.handler;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.collection.CollectionUtil;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.data.waybill.enums.ExceptionTypeEnum;
import com.wzc.be.es.data.waybill.enums.ProcessTypeEnum;
import com.wzc.be.es.data.waybill.qo.ExceptionWaybillListQO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.ExistsQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;

/**
 * 异常运单检索条件
 *
 * @param <Q> 查询条件
 * @param <V> 响应类型
 * <AUTHOR> Wei
 */
public class ExceptionBaseHandler<Q, V> implements BaseSearchHandler<Q, V> {

    public BoolQueryBuilder getMainBoolQueryBuilder(ExceptionWaybillListQO qo) {
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        // 子运单号字段必须存在
        ExistsQueryBuilder fieldName = QueryBuilders.existsQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillNo));
        mainQueryBuilder.must(fieldName);
        if(qo.getIsSync() != null){
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getIsSync), qo.getIsSync()));
        }
        // 承运商
        if (qo.getCarrierCompanyId() != null) {
            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCarrierCompanyId), qo.getCarrierCompanyId()));
        }

        if(CollectionUtils.isNotEmpty(qo.getCompanyIdList())){
            mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getShipperCompanyId), qo.getCompanyIdList()));
        }else {
            // 货主
            if (qo.getShipperCompanyId() != null) {
                mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getShipperCompanyId), qo.getShipperCompanyId()));
            }
        }


        // 运单号
        if (StringUtils.isNotBlank(qo.getWaybillNo())) {
            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getWaybillNo), wildcardsExp(qo.getWaybillNo())));
        }

        // 发货地
        if (StringUtils.isNotBlank(qo.getSenderAddress())) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//            boolQuery.should(QueryBuilders.wildcardQuery("sender.address",wildcardsExp(qo.getSenderAddress())));
//            boolQuery.should(QueryBuilders.wildcardQuery("sender.fullAddress",wildcardsExp(qo.getSenderAddress())));
            matchPhraseOrWildcardMustQuery(boolQuery,qo.getSenderAddress(),true, CollectionUtil.newArrayList("sender.address","sender.fullAddress"));
            mainQueryBuilder.must(boolQuery);
        }

        // 收货地
        if (StringUtils.isNotBlank(qo.getReceiverAddress())) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
//            boolQuery.should(QueryBuilders.wildcardQuery("receiver.address",wildcardsExp(qo.getReceiverAddress())));
//            boolQuery.should(QueryBuilders.wildcardQuery("receiver.fullAddress",wildcardsExp(qo.getReceiverAddress())));
            matchPhraseOrWildcardMustQuery(boolQuery,qo.getReceiverAddress(),true, CollectionUtil.newArrayList("receiver.address","receiver.fullAddress"));
            mainQueryBuilder.must(boolQuery);
        }

        // 车牌号
        if (StringUtils.isNotBlank(qo.getCarNo())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getCarNo), wildcardsExp(qo.getCarNo())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getCarNo(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getCarNo)));
        }

        // 司机姓名
        if (StringUtils.isNotBlank(qo.getDriverName())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getDriverName), wildcardsExp(qo.getDriverName())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getDriverName(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getDriverName)));
        }

        // 联系电话
        if (StringUtils.isNotBlank(qo.getDriverPhone())) {
//            mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getDriverPhone), wildcardsExp(qo.getDriverPhone())));
            matchPhraseOrWildcardMustQuery(mainQueryBuilder,qo.getDriverPhone(),true, CollectionUtil.newArrayList(FieldUtils.val(SubWaybillIndex::getDriverPhone)));
        }

        // 运输进度
        if (CollectionUtils.isNotEmpty(qo.getWaybillStatus())) {
            mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), qo.getWaybillStatus()));
        }

        // 异常类型
        ExceptionTypeEnum exceptionType = qo.getExceptionType();
        if (exceptionType != null) {
            switch (exceptionType) {
                case SIGN_EXCEPTION:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getExcepSign), 1));
                    BoolQueryBuilder boolQuery1 = new BoolQueryBuilder();
                    RangeQueryBuilder superviseQuery1 = new RangeQueryBuilder(FieldUtils.val(SubWaybillIndex::getSignExcepTime))
                            .gte(qo.getStartTime())
                            .lte(qo.getEndTime());
                    boolQuery1.should(superviseQuery1);
                    mainQueryBuilder.must(boolQuery1);
                    break;
                case SUPERVISE_EXCEPTION:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getExcepSupervise), 1));
                    BoolQueryBuilder boolQuery2 = new BoolQueryBuilder();
                    RangeQueryBuilder superviseQuery2 = new RangeQueryBuilder(FieldUtils.val(SubWaybillIndex::getSuperviseExcepTime))
                            .gte(qo.getStartTime())
                            .lte(qo.getEndTime());
                    boolQuery2.should(superviseQuery2);
                    mainQueryBuilder.must(boolQuery2);
                    break;
                case TRANSPORT_EXCEPTION:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getExcepTrans), 1));
                    BoolQueryBuilder boolQuery3 = new BoolQueryBuilder();
                    RangeQueryBuilder superviseQuery3 = new RangeQueryBuilder(FieldUtils.val(SubWaybillIndex::getTransExcepTime))
                            .gte(qo.getStartTime())
                            .lte(qo.getEndTime());
                    boolQuery3.should(superviseQuery3);
                    mainQueryBuilder.must(boolQuery3);
                    break;
                case REPORT_EXCEPTION:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getExcepReport), 1));
                    BoolQueryBuilder boolQuery4 = new BoolQueryBuilder();
                    RangeQueryBuilder superviseQuery4 = new RangeQueryBuilder(FieldUtils.val(SubWaybillIndex::getReportExcepTime))
                            .gte(qo.getStartTime())
                            .lte(qo.getEndTime());
                    boolQuery4.should(superviseQuery4);
                    mainQueryBuilder.must(boolQuery4);
                    break;
                default:
                    if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                        BoolQueryBuilder boolQuery = new BoolQueryBuilder();
                        RangeQueryBuilder superviseQuery = new RangeQueryBuilder(FieldUtils.val(SubWaybillIndex::getSignExcepTime))
                                .gte(qo.getStartTime())
                                .lte(qo.getEndTime());

                        RangeQueryBuilder reportQuery = new RangeQueryBuilder(FieldUtils.val(SubWaybillIndex::getTransExcepTime))
                                .gte(qo.getStartTime())
                                .lte(qo.getEndTime());

                        RangeQueryBuilder transQuery = new RangeQueryBuilder(FieldUtils.val(SubWaybillIndex::getSuperviseExcepTime))
                                .gte(qo.getStartTime())
                                .lte(qo.getEndTime());

                        RangeQueryBuilder signQuery = new RangeQueryBuilder(FieldUtils.val(SubWaybillIndex::getReportExcepTime))
                                .gte(qo.getStartTime())
                                .lte(qo.getEndTime());

                        boolQuery.should(superviseQuery);
                        boolQuery.should(reportQuery);
                        boolQuery.should(transQuery);
                        boolQuery.should(signQuery);
                        mainQueryBuilder.must(boolQuery);
                    }
                    break;
            }
        }

        // 是否待处理
        ProcessTypeEnum processType = qo.getProcessType();
        if (processType != null) {
            BoolQueryBuilder should = QueryBuilders.boolQuery();
            switch (processType) {
                case PROCESS:
                    should.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getTransProcess), 1));
                    should.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSignProcess), 1));
                    should.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSuperviseProcess), 1));
                    should.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getReportProcess), 1));
                    mainQueryBuilder.must(should);
                    break;
                case UN_PROCESS:
                    should.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getTransProcess), 0));
                    should.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSignProcess), 0));
                    should.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSuperviseProcess), 0));
                    should.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getReportProcess), 0));
                    mainQueryBuilder.must(should);
                    break;
            }
        }
        return mainQueryBuilder;
    }

}
