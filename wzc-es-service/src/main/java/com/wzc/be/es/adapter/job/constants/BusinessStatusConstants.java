package com.wzc.be.es.adapter.job.constants;

/**
 * 业务状态常量类
 * 用于冷数据下线时的业务状态判断
 *
 * <AUTHOR> Assistant
 * @since 2025/02/18
 */
public class BusinessStatusConstants {

    /**
     * 订单状态
     */
    public static class OrderStatus {
        /** 草稿 */
        public static final int DRAFT = 1;
        /** 待派车 */
        public static final int PENDING_DISPATCH = 2;
        /** 运输中 */
        public static final int IN_TRANSIT = 3;
        /** 已完成 */
        public static final int COMPLETED = 6;
        /** 已取消 */
        public static final int CANCELLED = 7;
    }

    /**
     * 子运单状态
     */
    public static class SubWaybillStatus {
        /** 待派车 */
        public static final int PENDING_DISPATCH = 1;
        /** 已派车 */
        public static final int DISPATCHED = 2;
        /** 运输中 */
        public static final int IN_TRANSIT = 3;
        /** 已完成 */
        public static final int COMPLETED = 6;
        /** 已取消 */
        public static final int CANCELLED = 7;
    }

    /**
     * 结算状态
     */
    public static class SettlementStatus {
        /** 无需结算 */
        public static final int NO_SETTLEMENT = 0;
        /** 运输未完成 */
        public static final int TRANSPORT_INCOMPLETE = 1;
        /** 待结算 */
        public static final int PENDING_SETTLEMENT = 3;
        /** 已结算 */
        public static final int SETTLED = 4;
        /** 已支付 */
        public static final int PAID = 5;
        /** 未对账 */
        public static final int NOT_RECONCILED = 6;
        /** 已对账未结算 */
        public static final int RECONCILED_NOT_SETTLED = 7;
        /** 已结算待支付 */
        public static final int SETTLED_PENDING_PAYMENT = 8;
    }

    /**
     * 支付状态
     */
    public static class PaymentStatus {
        /** 未支付 */
        public static final int UNPAID = 0;
        /** 支付中 */
        public static final int PAYING = 1;
        /** 已支付 */
        public static final int PAID = 2;
        /** 支付完成 */
        public static final int PAYMENT_COMPLETED = 3;
        /** 支付异常 */
        public static final int PAYMENT_ERROR = 4;
    }

    /**
     * 判断订单是否可以下线
     * @param orderStatus 订单状态
     * @param settlementStatus 结算状态
     * @return 是否可以下线
     */
    public static boolean canOfflineOrder(Integer orderStatus, Integer settlementStatus) {
        // 订单必须已完成
        if (!(OrderStatus.COMPLETED ==orderStatus)) {
            return false;
        }
        
        // 如果有结算信息，必须已支付
        if (settlementStatus != null && settlementStatus > SettlementStatus.NO_SETTLEMENT) {
            return SettlementStatus.PAID == settlementStatus;
        }
        
        return true;
    }

    /**
     * 判断子运单是否可以下线
     * @param subWaybillStatus 子运单状态
     * @param ownerSignTime 货主签收时间
     * @param completeStatus 对上结算状态
     * @param completeDownStatus 对下结算状态
     * @return 是否可以下线
     */
    public static boolean canOfflineSubWaybill(Integer subWaybillStatus, String ownerSignTime, 
                                               Integer completeStatus, Integer completeDownStatus) {
        // 子运单必须已完成
        if (!(SubWaybillStatus.COMPLETED == subWaybillStatus)) {
            return false;
        }
        
        // 必须已签收
        if (ownerSignTime == null || ownerSignTime.trim().isEmpty()) {
            return false;
        }
        
        // 对上结算状态检查
        if (completeStatus != null && completeStatus > SettlementStatus.NO_SETTLEMENT) {
            if (!(SettlementStatus.PAID ==completeStatus)) {
                return false;
            }
        }
        
        // 对下结算状态检查
        if (completeDownStatus != null && completeDownStatus > SettlementStatus.NO_SETTLEMENT) {
            return SettlementStatus.PAID == completeDownStatus;
        }
        
        return true;
    }
}
