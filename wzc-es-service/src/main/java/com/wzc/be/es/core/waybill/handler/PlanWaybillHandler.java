package com.wzc.be.es.core.waybill.handler;

import cn.easyes.core.biz.EsPageInfo;
import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baidu.mapcloud.cloudnative.common.model.Pagination;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.core.waybill.converter.WaybillConverter;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.core.waybill.mapper.SubWaybillListMapper;
import com.wzc.be.es.data.waybill.qo.PlanWaybillQO;
import com.wzc.be.es.data.waybill.vo.PlanWaybillVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class PlanWaybillHandler implements BaseSearchHandler<PlanWaybillQO, Pagination<PlanWaybillVO>> {

    @Value("${planWaybill.search.type:planWaybillHandler}")
    private String searchType;

    @Resource
    private EsIndexProperties esIndexProperties;
    @Resource
    private SubWaybillListMapper subWaybillListMapper;

    @Override
    public RestResponse<Pagination<PlanWaybillVO>> search(PlanWaybillQO qo) {
        LambdaEsQueryWrapper<SubWaybillIndex> wrapper = new LambdaEsQueryWrapper<>();
        this.genSearch(wrapper, qo);
        wrapper.index(esIndexProperties.getSubWaybillIndex());
        EsPageInfo<SubWaybillIndex> esPageInfo = subWaybillListMapper.pageQuery(wrapper, qo.getPage(), qo.getSize());
        Pagination<PlanWaybillVO> pagination = new Pagination<>();
        pagination.setPage(qo.getPage());
        pagination.setSize(qo.getSize());
        List<SubWaybillIndex> list = esPageInfo.getList();
        if(CollectionUtils.isEmpty(list)){
            pagination.setTotal(0L);
            return RestResponse.success(pagination);
        }

        List<PlanWaybillVO> data = WaybillConverter.INSTANCE.toPlanWaybillVO(list);

        pagination.setTotal(esPageInfo.getTotal());
        pagination.setContent(data);
        return RestResponse.success(pagination);
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.esIndexProperties.getSubWaybillIndex();
    }

    /**
     * 构建查询条件
     */
    private void genSearch(LambdaEsQueryWrapper<SubWaybillIndex> wrapper, PlanWaybillQO qo) {
        wrapper.isNotNull(SubWaybillIndex::getSubWaybillNo);
        wrapper.in(SubWaybillIndex::getParentNo, qo.getParentNoList());

        if (StrUtil.isNotBlank(qo.getWaybillNo())){
            wrapper.like(SubWaybillIndex::getWaybillNo, qo.getWaybillNo());
        }

        if (StrUtil.isNotBlank(qo.getCarNo())){
            wrapper.like(SubWaybillIndex::getCarNo,qo.getCarNo());
        }

        if (StrUtil.isNotBlank(qo.getDriverName())) {
            wrapper.and(wapper ->
                    wapper.like(SubWaybillIndex::getDriverName, qo.getDriverName()).or().like(SubWaybillIndex::getZtDriverName, qo.getDriverName()));
        }

        if(CollectionUtils.isNotEmpty(qo.getStatus())){
            wrapper.in(SubWaybillIndex::getSubWaybillStatus,qo.getStatus());
        }

        if (StrUtil.isNotBlank(qo.getStartTime()) && StrUtil.isNotBlank(qo.getEndTime())){
            wrapper.ge(SubWaybillIndex::getCreateTime, qo.getStartTime());
            wrapper.le(SubWaybillIndex::getCreateTime, qo.getEndTime());
        }
    }
}
