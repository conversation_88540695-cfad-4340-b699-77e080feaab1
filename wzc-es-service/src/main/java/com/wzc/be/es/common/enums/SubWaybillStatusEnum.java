package com.wzc.be.es.common.enums;

import com.baidu.mapcloud.cloudnative.common.model.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SubWaybillStatusEnum implements IEnum {
    WAIT_START(0, "待开启"),
    WAIT_APPOINTMENT_LOAD(1, "待预约装货"),
    WAIT_LOAD_SIGN(2, "待装货签到"),
    WAIT_LOAD(3, "待装货"),
    WAIT_APPOINTMENT_UNLOAD(4, "待预约卸货"),
    WAIT_UNLOAD_SIGN(5, "待卸货签到"),
    WAIT_UNLOAD(6, "待卸货"),
    WAIT_SIGN(7, "待签收"),
    FINISH(8, "已完成"),
    CANCELLED(9, "已撤单"),
    WAIT_ASSIGN(-2, "待指派");

    private final Integer code;
    private final String name;

    @Override
    public Integer getCode() {
        return this.code;
    }

    @Override
    public String getName() {
        return this.name;
    }
}
