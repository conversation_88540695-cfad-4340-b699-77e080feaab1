package com.wzc.be.es.adapter.web;

import cn.hutool.json.JSONObject;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.enums.QueryTypeEnums;
import com.wzc.be.es.common.service.EsCommonAuthSearchService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "权限接口")
@RestController
@RequestMapping("/api/es/v1/auth/")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AuthController {

    @Autowired
    private final EsCommonAuthSearchService esCommonAuthSearchService;

    @PostMapping("checkQueryAuth")
    public RestResponse<JSONObject> checkQueryAuth() {
        JSONObject jsonObject = new JSONObject();
        return RestResponse.success(esCommonAuthSearchService.checkQueryAuth(jsonObject, QueryTypeEnums.ORDER));
    }

}
