package com.wzc.be.es.core.waybill.handler;


import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baidu.mapcloud.cloudnative.authentication.datapermission.core.rule.UserPermissionRule;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.common.enums.SubWaybillStatusEnum;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.data.waybill.qo.OwnerWaybillListQO;
import com.wzc.be.es.data.waybill.vo.CarrierTabGroupCountVO;
import com.wzc.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.ExistsQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 货主列表tab页统计
 *
 * <AUTHOR> Wei
 */
@Slf4j
@Component
public class OwnerTabGroupHandler  extends OwnerBaseHandler<OwnerWaybillListQO, CarrierTabGroupCountVO> {
    @Value("${ownerTabGroup.search.type:ownerTabGroupHandler}")
    private String searchType;

    @Value("${ownerTabGroup.search.index:sub_waybill_index01}")
    private String index;

    @Autowired
    protected UserPermissionRule userPermissionRule;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Override
    public RestResponse<CarrierTabGroupCountVO> search(OwnerWaybillListQO qo) {
        try {
            log.info("货主运单tab请求参数:{}", JSON.toJSONString(qo));
            int from = 1;
            int size = 2;

            // 构建搜索请求
            SearchRequest searchRequest = new SearchRequest(index);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            searchSourceBuilder.from(from);
            searchSourceBuilder.size(size);
            searchSourceBuilder.trackTotalHits(true);

            JSONObject jsonObject = authReq(userPermissionRule, JSONUtil.parseObj(qo));
            qo = JSONUtil.toBean(jsonObject.toJSONString(0),OwnerWaybillListQO.class);

            BoolQueryBuilder mainQueryBuilder1 = getMainBoolQueryBuilder(qo);
            searchSourceBuilder.query(mainQueryBuilder1);
            searchRequest.source(searchSourceBuilder);
            log.info("运单列表统计：{}",searchSourceBuilder.toString());
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            TotalHits status1 = searchHits.getTotalHits();

            BoolQueryBuilder mainQueryBuilder2 = getMainBoolQueryBuilder(qo);
            mainQueryBuilder2.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus),0));
            searchSourceBuilder.query(mainQueryBuilder2);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse2 = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits2 = searchResponse2.getHits();
            TotalHits status2 = searchHits2.getTotalHits();

            BoolQueryBuilder mainQueryBuilder3 = getMainBoolQueryBuilder(qo);
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_LOAD.getCode()));
            boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_UNLOAD.getCode()));
            boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_LOAD_SIGN.getCode()));
            boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_UNLOAD_SIGN.getCode()));
            mainQueryBuilder3.must(boolQuery);
            searchSourceBuilder.query(mainQueryBuilder3);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse3 = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits3 = searchResponse3.getHits();
            TotalHits status3 = searchHits3.getTotalHits();

            BoolQueryBuilder mainQueryBuilder4 = getMainBoolQueryBuilder(qo);
            mainQueryBuilder4.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus),7));
            searchSourceBuilder.query(mainQueryBuilder4);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse4 = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits4 = searchResponse4.getHits();
            TotalHits status4 = searchHits4.getTotalHits();

            BoolQueryBuilder mainQueryBuilder5 = getMainBoolQueryBuilder(qo);
            mainQueryBuilder5.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus),8));
            searchSourceBuilder.query(mainQueryBuilder5);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse5 = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits5 = searchResponse5.getHits();
            TotalHits status5 = searchHits5.getTotalHits();

            BoolQueryBuilder mainQueryBuilder6 = getMainBoolQueryBuilder(qo);
            mainQueryBuilder6.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus),9));
            searchSourceBuilder.query(mainQueryBuilder6);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse6 = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits6 = searchResponse6.getHits();
            TotalHits status6 = searchHits6.getTotalHits();

            BoolQueryBuilder mainQueryBuilder7 = getMainBoolQueryBuilder(qo);
            mainQueryBuilder7.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCompleteStatus),3));
            searchSourceBuilder.query(mainQueryBuilder7);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse7 = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits7 = searchResponse7.getHits();
            TotalHits status7 = searchHits7.getTotalHits();

            BoolQueryBuilder mainQueryBuilder8 = getMainBoolQueryBuilder(qo);
            mainQueryBuilder8.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), 8));
            mainQueryBuilder8.mustNot(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getIsEvaluate), 1));
            searchSourceBuilder.query(mainQueryBuilder8);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse8 = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits8 = searchResponse8.getHits();
            TotalHits status8 = searchHits8.getTotalHits();

            BoolQueryBuilder mainQueryBuilder9 = getMainBoolQueryBuilder(qo);
            searchSourceBuilder.query(mainQueryBuilder9);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse9 = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits9 = searchResponse9.getHits();
            TotalHits status9 = searchHits9.getTotalHits();

            CarrierTabGroupCountVO countVO = new CarrierTabGroupCountVO();
            countVO.setStatusCount0(status1.value);
            countVO.setStatusCount1(status2.value);
            countVO.setStatusCount2(status3.value);
            countVO.setStatusCount3(status4.value);
            countVO.setStatusCount4(status5.value);
            countVO.setStatusCount5(status6.value);
            countVO.setStatusCount6(status7.value);
            countVO.setStatusCount7(status8.value);
            countVO.setStatusCount8(status9.value);

            return RestResponse.success(countVO);
        }catch (Exception e){
            log.error("货主运单tab统计搜索异常:",e);
            throw new ServiceException(ExcepitonEnum.ERROR_UNKNOW);
        }
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return index;
    }


}
