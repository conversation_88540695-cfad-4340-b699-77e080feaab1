package com.wzc.be.es.core.waybill.handler;

import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import com.alibaba.fastjson.JSON;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.enums.SubWaybillStatusEnum;
import com.wzc.be.es.common.enums.WaybillCompleteStatusEnum;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.core.waybill.mapper.SubWaybillListMapper;
import com.wzc.be.es.data.waybill.qo.CheckDriverWaybillQO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class CheckDriverWaybillHandler implements BaseSearchHandler<CheckDriverWaybillQO, Boolean> {

    @Value("${checkDriverWaybill.search.type:checkDriverWaybillHandler}")
    private String searchType;

    @Resource
    private EsIndexProperties esIndexProperties;

    @Resource
    private SubWaybillListMapper subWaybillListMapper;

    @Override
    public RestResponse<Boolean> search(CheckDriverWaybillQO qo) {
        log.info("CheckDriverWaybillHandler请求参数:{}", JSON.toJSONString(qo));
        LambdaEsQueryWrapper<SubWaybillIndex> wrapper = new LambdaEsQueryWrapper<>();
        this.genSearch(wrapper, qo);
        wrapper.index(esIndexProperties.getSubWaybillIndex());
        List<SubWaybillIndex> waybillQueryIndex = subWaybillListMapper.selectList(wrapper);
        if (CollectionUtils.isNotEmpty(waybillQueryIndex)) {
            return RestResponse.success(true);
        }
        return RestResponse.success(false);
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.esIndexProperties.getSubWaybillIndex();
    }

    /**
     * 构建查询条件
     */
    private void genSearch(LambdaEsQueryWrapper<SubWaybillIndex> wrapper, CheckDriverWaybillQO qo) {
        wrapper.eq(SubWaybillIndex::getDriverId, qo.getDriverId());
        wrapper.groupBy(SubWaybillIndex::getWaybillNo);
        if (qo.getType() != null) {
            switch (qo.getType()) {
                case TYPE_1:
                    wrapper.in(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.WAIT_APPOINTMENT_LOAD.getCode(),
                            SubWaybillStatusEnum.WAIT_LOAD_SIGN.getCode(),SubWaybillStatusEnum.WAIT_LOAD.getCode(),
                            SubWaybillStatusEnum.WAIT_APPOINTMENT_UNLOAD.getCode(),SubWaybillStatusEnum.WAIT_UNLOAD_SIGN.getCode()
                            ,SubWaybillStatusEnum.WAIT_UNLOAD.getCode(),SubWaybillStatusEnum.WAIT_SIGN.getCode()); // 待签收
                    break;
                case TYPE_2:
                    wrapper.eq(SubWaybillIndex::getCompleteStatus, WaybillCompleteStatusEnum.UNRECONCILED.getCode());
                    break;
            }
        }
    }
}
