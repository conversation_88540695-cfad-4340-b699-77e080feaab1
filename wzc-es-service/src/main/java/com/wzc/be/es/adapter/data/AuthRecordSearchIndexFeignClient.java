package com.wzc.be.es.adapter.data;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.data.data.es.auth.model.AuthRecordESVO;
import com.wzc.be.data.data.es.auth.model.AuthRecordQueryPageDTO;
import com.wzc.be.es.adapter.data.feign.AuthRecordSearchIndexFeign;
import com.wzc.common.core.api.utils.ApiResultUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author：chenxingguang
 * @Date：2024/2/4 14:28
 * @Version：1.0.0
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class AuthRecordSearchIndexFeignClient {

    private final AuthRecordSearchIndexFeign authRecordSearchIndexFeign;

    public List<AuthRecordESVO> search(AuthRecordQueryPageDTO queryPageQo) {
        RestResponse<List<AuthRecordESVO>> search = authRecordSearchIndexFeign.search(queryPageQo);
        List<AuthRecordESVO> authRecordESVOList = ApiResultUtils.getDataOrThrow(search);
        return authRecordESVOList;
    }

}
