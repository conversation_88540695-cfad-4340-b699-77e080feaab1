package com.wzc.be.es.adapter.job.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 冷数据下线统计BO
 *
 * <AUTHOR> Assistant
 * @since 2025/02/18
 */
@Data
public class ColdDataOfflineStatsBO {

    @Schema(description = "任务开始时间")
    private LocalDateTime startTime;

    @Schema(description = "任务结束时间")
    private LocalDateTime endTime;

    @Schema(description = "任务执行时长（毫秒）")
    private Long durationMs;

    @Schema(description = "总处理数据条数")
    private Integer totalProcessed;

    @Schema(description = "总删除数据条数")
    private Integer totalDeleted;

    @Schema(description = "处理成功的索引数量")
    private Integer successIndexCount;

    @Schema(description = "处理失败的索引数量")
    private Integer failedIndexCount;

    @Schema(description = "各索引处理详情")
    private Map<String, IndexProcessDetail> indexDetails;

    @Schema(description = "任务状态")
    private String status;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "数据保留阈值时间")
    private String retentionThreshold;

    /**
     * 索引处理详情
     */
    @Data
    public static class IndexProcessDetail {
        @Schema(description = "索引名称")
        private String indexName;

        @Schema(description = "索引描述")
        private String description;

        @Schema(description = "处理数据条数")
        private Integer processedCount;

        @Schema(description = "删除数据条数")
        private Integer deletedCount;

        @Schema(description = "处理开始时间")
        private LocalDateTime startTime;

        @Schema(description = "处理结束时间")
        private LocalDateTime endTime;

        @Schema(description = "处理时长（毫秒）")
        private Long durationMs;

        @Schema(description = "处理状态")
        private String status;

        @Schema(description = "错误信息")
        private String errorMessage;
    }
}
