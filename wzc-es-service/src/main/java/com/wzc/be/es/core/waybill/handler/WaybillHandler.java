package com.wzc.be.es.core.waybill.handler;

import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import cn.hutool.core.util.ObjectUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.core.waybill.mapper.SubWaybillListMapper;
import com.wzc.be.es.data.waybill.qo.SelectWaybillInfoQO;
import com.wzc.be.es.data.waybill.vo.WaybillQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class WaybillHandler implements BaseSearchHandler<SelectWaybillInfoQO, WaybillQueryVO> {

    @Value("${waybill.search.type:waybillHandler}")
    private String searchType;

    @Value("${waybill.search.index:sub_waybill_index01}")
    private String index;

    @Resource
    private SubWaybillListMapper subWaybillListMapper;

    @Override
    public RestResponse<WaybillQueryVO> search(SelectWaybillInfoQO qo) {
        LambdaEsQueryWrapper<SubWaybillIndex> wrapper = new LambdaEsQueryWrapper<>();
        this.genSearch(wrapper, qo);
        wrapper.index(index);
        List<SubWaybillIndex> subWaybillList = subWaybillListMapper.selectList(wrapper);
        if(CollectionUtils.isEmpty(subWaybillList)){
            return RestResponse.success();
        }
        SubWaybillIndex subWaybill = subWaybillList.get(0);
        WaybillQueryVO waybillQuery = new WaybillQueryVO();
        waybillQuery.setWaybillNo(subWaybill.getWaybillNo());
        waybillQuery.setWaybillStatus(subWaybill.getSubWaybillStatus());
        waybillQuery.setStatus(subWaybill.getStatus());
        waybillQuery.setCompleteStatus(subWaybill.getCompleteStatus());
        waybillQuery.setCarrierCompanyId(subWaybill.getCarrierCompanyId());
        waybillQuery.setCompanyId(subWaybill.getCompanyId());
        waybillQuery.setDriverId(subWaybill.getDriverId());
        waybillQuery.setDriverName(subWaybill.getDriverName());
        waybillQuery.setCarId(subWaybill.getCarId());
        waybillQuery.setCarNo(subWaybill.getCarNo());
        waybillQuery.setCreateTime(subWaybill.getCreateTime());
        waybillQuery.setIsEvaluate(Objects.isNull(subWaybill.getIsEvaluate()) ? 0 : subWaybill.getIsEvaluate());
        List<WaybillQueryVO.SubWaybillBean> subWaybillBeanList = new ArrayList<>();
        for (SubWaybillIndex item : subWaybillList) {
            WaybillQueryVO.SubWaybillBean waybillBean= new WaybillQueryVO.SubWaybillBean();
            waybillBean.setSubWaybillNo(item.getSubWaybillNo());
            waybillBean.setSubWaybillStatus(item.getSubWaybillStatus());
            waybillBean.setCompleteStatus(item.getCompleteStatus());
            waybillBean.setOrderNo(item.getOrderNo());
            waybillBean.setCarrierCompanyName(item.getCarrierCompanyName());
            waybillBean.setParentNo(item.getParentNo());
            waybillBean.setShipperCompanyId(String.valueOf(item.getShipperCompanyId()));
            waybillBean.setShipperCompanyName(item.getShipperCompanyName());
            List<SubWaybillIndex.Address> receiverList = item.getReceiver();
            if(CollectionUtils.isNotEmpty(receiverList)){
                WaybillQueryVO.SubWaybillBean.ReceiverOrSenderBean receiver = new WaybillQueryVO.SubWaybillBean.ReceiverOrSenderBean();
                for (SubWaybillIndex.Address address : receiverList) {
                    receiver.setId(String.valueOf(address.getId()));
                    receiver.setAddress(address.getAddress());
                    receiver.setFullAddress(address.getFullAddress());
                    receiver.setType(address.getType());
                }
                waybillBean.setReceiver(receiver);
            }

            List<SubWaybillIndex.Address> senderList = item.getSender();
            if(CollectionUtils.isNotEmpty(senderList)){
                WaybillQueryVO.SubWaybillBean.ReceiverOrSenderBean sender = new WaybillQueryVO.SubWaybillBean.ReceiverOrSenderBean();
                for (SubWaybillIndex.Address address : senderList) {
                    sender.setId(String.valueOf(address.getId()));
                    sender.setAddress(address.getAddress());
                    sender.setFullAddress(address.getFullAddress());
                    sender.setType(address.getType());
                }
                waybillBean.setSender(sender);
            }

            List<WaybillQueryVO.SubWaybillBean.ItemsBean> goodsList = new ArrayList<>();
            List<SubWaybillIndex.Item> items = item.getItems();
            if(CollectionUtils.isNotEmpty(items)){
                for (SubWaybillIndex.Item goods : items) {
                    WaybillQueryVO.SubWaybillBean.ItemsBean good = new WaybillQueryVO.SubWaybillBean.ItemsBean();
                    good.setId(goods.getId());
                    good.setItemName(goods.getItemName());
                    good.setItemId(goods.getItemId());
                    goodsList.add(good);
                }
            }
            waybillBean.setItems(goodsList);
            subWaybillBeanList.add(waybillBean);
        }
        waybillQuery.setSubWaybill(subWaybillBeanList);
        return RestResponse.success(waybillQuery);
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.index;
    }

    /**
     * 构建查询条件
     */
    private void genSearch(LambdaEsQueryWrapper<SubWaybillIndex> wrapper, SelectWaybillInfoQO qo) {
        wrapper.isNotNull(SubWaybillIndex::getSubWaybillNo);
        if (StringUtils.isNotBlank(qo.getWaybillNo())){
            wrapper.eq(SubWaybillIndex::getWaybillNo, qo.getWaybillNo());
        }
        if (StringUtils.isNotBlank(qo.getSubWaybillNo())){
            wrapper.eq("subWaybill.subWaybillNo", qo.getSubWaybillNo());
        }
        if (ObjectUtil.isNotNull(qo.getCompanyId())){
            wrapper.and(i -> i.eq(SubWaybillIndex::getShipperCompanyId,qo.getCompanyId())
                    .or().eq("waybillCompany.companyId", qo.getCompanyId())
            );
        }

        if (StringUtils.isNotBlank(qo.getCarNo())){
            wrapper.eq(SubWaybillIndex::getCarNo,qo.getCarNo());
        }

        if(Objects.nonNull(qo.getDriverId())){
            wrapper.eq(SubWaybillIndex::getDriverId,qo.getDriverId());
        }

        if (StringUtils.isNotBlank(qo.getGoodsNo())){
            wrapper.eq(SubWaybillIndex::getGoodsNo,qo.getGoodsNo());
        }

    }
}
