package com.wzc.be.es.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/8/24 22:21
 */
@Getter
@AllArgsConstructor
public enum OrderSourceTypeEnums {

    /**
     * 订单来源枚举
     */
    LINE_SPLIT(5, "线路拆分",""),
    SUBCONTRACT(7, "转包","zbCount"),
    SHARED(8, "共享","");

    private final Integer code;
    private final String name;


    /**
     * 字段名称
     */
    private final String fieldName;

}
