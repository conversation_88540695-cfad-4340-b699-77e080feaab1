package com.wzc.be.es.adapter.action.dispatch;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.api.dispatch.EsDispatchSearchApi;
import com.wzc.be.es.core.dispatch.service.EsDispatchService;
import com.wzc.be.es.data.dispatch.vo.DispatchStatusVO;
import com.wzc.be.es.data.waybill.qo.RequestPageQO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年08月11日
 */
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class DispatchAction implements EsDispatchSearchApi {

    private final EsDispatchService dispatchService;

    /**
     * 查询大单下派车单是否已开启运输
     * @param requestPageQo requestPageQo
     * @return RestResponse<List<DispatchStatusVO>>
     */
    @Override
    public RestResponse<List<DispatchStatusVO>> startDispatch(RequestPageQO requestPageQo) {
        return dispatchService.startDispatch(requestPageQo);
    }
}
