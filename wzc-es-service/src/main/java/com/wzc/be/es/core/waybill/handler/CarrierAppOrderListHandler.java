package com.wzc.be.es.core.waybill.handler;


import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.PageOrderType;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.adapter.client.waybill.WaybillDorisFeignClient;
import com.wzc.be.es.common.enums.SubWaybillStatusEnum;
import com.wzc.be.es.common.enums.ZbStatusEnums;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.commons.PageVO;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.data.waybill.enums.CarrierAppOrderListTabEnum;
import com.wzc.be.es.data.waybill.enums.DispatchQueryEnums;
import com.wzc.be.es.data.waybill.qo.CarrierAppOrderListQO;
import com.wzc.be.es.data.waybill.vo.WaybillQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.ExistsQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

/**
 * 承运商APP运单列表
 *
 * <AUTHOR> Wei
 */
@Slf4j
@Component
public class CarrierAppOrderListHandler implements BaseSearchHandler<CarrierAppOrderListQO, PageVO<WaybillQueryVO>> {

    @Value("${carrierAppOrderList.search.type:carrierAppOrderListHandler}")
    private String searchType;

    @Value("${waybill.search.index:sub_waybill_index01}")
    private String index;

    @Resource
    private RestHighLevelClient restHighLevelClient;

    @Resource
    private WaybillDorisFeignClient waybillDorisFeignClient;

    @Override
    public RestResponse<PageVO<WaybillQueryVO>> search(CarrierAppOrderListQO qo) {
        PageVO<WaybillQueryVO> page = new PageVO<>();
        page.setPage(qo.getPage());
        page.setSize(qo.getSize());
        try {
            // 构建搜索请求
            SearchRequest searchRequest = new SearchRequest(index);
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            // 排序方式
            searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(SubWaybillIndex::getCreateTime)).order(PageOrderType.ASC.equals(qo.getOrderType()) ? SortOrder.ASC : SortOrder.DESC));

            BoolQueryBuilder mainQueryBuilder = this.getMainBoolQueryBuilder(qo);
            int size = qo.getSize();
            int from = (qo.getPage() - 1) * size;
            searchSourceBuilder.from(from);
            searchSourceBuilder.size(qo.getSize());
            searchSourceBuilder.query(mainQueryBuilder);

            searchSourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
            searchSourceBuilder.trackTotalHits(true);
            searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(SubWaybillIndex::getCreateTime)).order(SortOrder.DESC));
            searchRequest.source(searchSourceBuilder);
            List<WaybillQueryVO> data = new ArrayList<>();
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            SearchHit[] hitResult = searchHits.getHits();
            JSONArray array = new JSONArray();
            Stream.of(hitResult).forEach(itemHit -> {
                Map<String, Object> sourceAsMap = itemHit.getSourceAsMap();
                array.add(sourceAsMap);
            });
            TotalHits totalHits = searchHits.getTotalHits();
            List<SubWaybillIndex> list = JSONUtil.toList(array.toJSONString(0), SubWaybillIndex.class);
            if (CollectionUtils.isEmpty(list)) {
                RestResponse.success(page);
            }

            for (SubWaybillIndex subWaybill : list) {
                WaybillQueryVO record = new WaybillQueryVO();
                record.setWaybillNo(subWaybill.getWaybillNo());
                record.setSubWaybillNo(subWaybill.getSubWaybillNo());
                record.setWaybillStatus(subWaybill.getSubWaybillStatus());
                record.setStatus(subWaybill.getStatus());
                record.setCompleteStatus(subWaybill.getCompleteStatus());
                record.setCarrierCompanyId(subWaybill.getCarrierCompanyId());
                record.setCompanyId(subWaybill.getCompanyId());
                record.setDriverId(subWaybill.getDriverId());
                record.setDriverName(subWaybill.getDriverName());
                record.setCarId(subWaybill.getCarId());
                record.setCarNo(subWaybill.getCarNo());
                record.setCreateTime(subWaybill.getCreateTime());
                record.setOldWaybillNo(subWaybill.getOldWaybillNo());
                record.setOldSubWaybillNo(subWaybill.getOldSubWaybillNo());
                data.add(record);
            }
            page.setTotal(totalHits.value);
            page.setContent(data);
            return RestResponse.success(page);
        } catch (Exception e) {
            log.error("承运商app查询异常", e);
        }
        return RestResponse.success(page);
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return index;
    }

    public BoolQueryBuilder getMainBoolQueryBuilder(CarrierAppOrderListQO qo) {
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();

        BoolQueryBuilder comp = QueryBuilders.boolQuery();
        comp.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCarrierCompanyId), qo.getCarrierCompanyId()));
        comp.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getFbfId), qo.getCarrierCompanyId()));
        mainQueryBuilder.must(comp);
        //mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCarrierCompanyId), qo.getCarrierCompanyId()));
        CarrierAppOrderListTabEnum tabType = qo.getTabType();
        // 子运单号字段必须存在
        ExistsQueryBuilder fieldName = QueryBuilders.existsQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillNo));
        mainQueryBuilder.must(fieldName);
        if (null != tabType) {
            switch (tabType) {
                case TAB_1:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_START.getCode()));
                    break;
                case TAB_2:
                    BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                    boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_APPOINTMENT_LOAD.getCode()));
                    boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_LOAD_SIGN.getCode()));
                    boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_LOAD.getCode()));
                    boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_APPOINTMENT_UNLOAD.getCode()));
                    boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_UNLOAD_SIGN.getCode()));
                    boolQuery.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_UNLOAD.getCode()));
                    mainQueryBuilder.must(boolQuery);
                    break;
                case TAB_3: // 待签收
                    if(Objects.nonNull(qo.getOwnerSign())){
                        if(qo.getOwnerSign() == 2){
                            // 承运商签收
                            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_SIGN.getCode()));
                            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getZbStatus), 1));
                            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getFbfId), qo.getCarrierCompanyId()));
                            mainQueryBuilder.must(QueryBuilders.termQuery("fbfSignStatus", 0));
                        }else if(qo.getOwnerSign() == 1){
                            // 货主签收
                            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_SIGN.getCode()));
                            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getZbStatus), 2));
                        }else {
                            // 全部
                            mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_SIGN.getCode()));
                            mainQueryBuilder.must(QueryBuilders.termQuery("fbfSignStatus", 0));
                        }
                    }else {
                        // 全部
                        mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_SIGN.getCode()));
                        mainQueryBuilder.must(QueryBuilders.termQuery("fbfSignStatus", 0));
                    }

                    break;
                case TAB_4: // 待付款
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getZbStatus), ZbStatusEnums.ONLINE.getCode()));
                    mainQueryBuilder.must(QueryBuilders.termQuery("fbfSignStatus", 1));
                    BoolQueryBuilder boolQuery1 = QueryBuilders.boolQuery();
                    boolQuery1.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCompleteStatus), 3));
                    boolQuery1.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCompleteStatus), 6));
                    boolQuery1.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCompleteStatus), 7));
                    boolQuery1.should(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCompleteStatus), 8));
                    mainQueryBuilder.must(boolQuery1);
                    break;
                case TAB_5: // 全部
                    break;
                case TAB_6:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), SubWaybillStatusEnum.WAIT_ASSIGN.getCode()));

                    break;
                default:
                    break;
            }
        }

        // 时间范围
        if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
            //增加日期范围请求参数
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getCreateTime))
                    .gte(qo.getStartTime()));
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getCreateTime))
                    .lte(qo.getEndTime()));
        }

        DispatchQueryEnums queryType = qo.getQueryType();
        if (queryType != null) {
            String queryValue = qo.getQueryValue();
            switch (queryType) {
                case ALL:
                    BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                    boolQuery.should(QueryBuilders.wildcardQuery("carNo", wildcardsExp(queryValue)));
                    boolQuery.should(QueryBuilders.wildcardQuery("sender.fullAddress", wildcardsExp(queryValue)));
                    boolQuery.should(QueryBuilders.wildcardQuery("receiver.fullAddress", wildcardsExp(queryValue)));
                    boolQuery.should(QueryBuilders.wildcardQuery("items.itemName", wildcardsExp(queryValue)));
                    boolQuery.should(QueryBuilders.wildcardQuery("carrierCompanyName", wildcardsExp(queryValue)));
                    boolQuery.should(QueryBuilders.wildcardQuery("waybillNo", wildcardsExp(queryValue)));
                    boolQuery.should(QueryBuilders.wildcardQuery("driverName", wildcardsExp(queryValue)));
                    boolQuery.should(QueryBuilders.wildcardQuery("driverName.keyword", wildcardsExp(queryValue)));
                    mainQueryBuilder.must(boolQuery);
                    break;
                case CAR_NO:
                    mainQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(SubWaybillIndex::getCarNo), wildcardsExp(queryValue)));
                    break;
                case FORM_ADDRESS:
                    mainQueryBuilder.must(QueryBuilders.wildcardQuery("sender.fullAddress", wildcardsExp(queryValue)));
                    break;
                case TO_ADDRESS:
                    mainQueryBuilder.must(QueryBuilders.wildcardQuery("receiver.fullAddress", wildcardsExp(queryValue)));
                    break;
                case CARRIER:
                    mainQueryBuilder.must(QueryBuilders.wildcardQuery("carrierCompanyName", wildcardsExp(queryValue)));
                    break;
                case MATERIAL_NAME:
                    mainQueryBuilder.must(QueryBuilders.wildcardQuery("items.itemName", wildcardsExp(queryValue)));
                    break;
                case DISPATCH_CAR_NO:
                    BoolQueryBuilder boolQuery1 = QueryBuilders.boolQuery();
                    boolQuery1.should(QueryBuilders.wildcardQuery("waybillNo", wildcardsExp(queryValue)));
                    boolQuery1.should(QueryBuilders.wildcardQuery("oldWaybillNo", wildcardsExp(queryValue)));
                    mainQueryBuilder.must(boolQuery1);
                    break;
                default:
                    break;
            }
        }

        // 状态筛选
        List<Integer> subWaybillStatus = qo.getSubWaybillStatus();
        if (CollectionUtils.isNotEmpty(subWaybillStatus)) {
            mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillStatus), subWaybillStatus));
        }

        return mainQueryBuilder;
    }
}
