package com.wzc.be.es.common.properties;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 冷数据下线配置属性
 *
 * <AUTHOR> Assistant
 * @since 2025/02/18
 */
@Getter
@Component
@RefreshScope
public class ColdDataOfflineProperties {

    /**
     * 冷数据下线Kafka Topic
     */
    @Value("${kafka.topic.es-cold-data.offline:TOPIC_ES_COLD_DATA_OFFLINE}")
    private String esColdDataOfflineTopic;

    /**
     * 批量处理大小
     */
    @Value("${es.cold.data.offline.pageSize:1000}")
    private Integer pageSize;

    /**
     * 批量删除大小
     */
    @Value("${es.cold.data.offline.deleteBatchSize:500}")
    private Integer deleteBatchSize;

    /**
     * 冷数据保留月数（默认12个月，即1年）
     */
    @Value("${es.cold.data.offline.retentionMonths:12}")
    private Integer retentionMonths;

    /**
     * 是否启用冷数据下线功能
     */
    @Value("${es.cold.data.offline.enabled:true}")
    private Boolean enabled;

    /**
     * 索引处理间隔时间（毫秒）
     */
    @Value("${es.cold.data.offline.indexIntervalMs:5000}")
    private Long indexIntervalMs;

    /**
     * 批次处理间隔时间（毫秒）
     */
    @Value("${es.cold.data.offline.batchIntervalMs:200}")
    private Long batchIntervalMs;

    /**
     * ES查询超时时间（秒）
     */
    @Value("${es.cold.data.offline.queryTimeoutSeconds:30}")
    private Integer queryTimeoutSeconds;

    /**
     * Scroll查询超时时间（分钟）
     */
    @Value("${es.cold.data.offline.scrollTimeoutMinutes:5}")
    private Integer scrollTimeoutMinutes;

    /**
     * 订单完成状态值
     */
    @Value("${es.cold.data.offline.order.completedStatus:6}")
    private Integer orderCompletedStatus;

    /**
     * 订单结算完成状态值（已支付）
     */
    @Value("${es.cold.data.offline.order.settlementCompletedStatus:5}")
    private Integer orderSettlementCompletedStatus;

    /**
     * 子运单完成状态值
     */
    @Value("${es.cold.data.offline.subWaybill.completedStatus:6}")
    private Integer subWaybillCompletedStatus;

    /**
     * 子运单结算完成状态值（已支付）
     */
    @Value("${es.cold.data.offline.subWaybill.settlementCompletedStatus:5}")
    private Integer subWaybillSettlementCompletedStatus;
}
