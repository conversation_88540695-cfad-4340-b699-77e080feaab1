/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.core.transport.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.google.common.collect.Lists;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.common.enums.QueryTypeEnums;
import com.wzc.be.es.common.enums.TransportStatusEnums;
import com.wzc.be.es.common.service.EsCommonAuthSearchService;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.core.common.CommonRedisResultVO;
import com.wzc.be.es.data.transport.qo.TransportQueryPageQO;
import com.wzc.be.es.data.transport.vo.TransportGroupVO;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.Cardinality;
import org.elasticsearch.search.aggregations.metrics.ParsedMax;
import org.elasticsearch.search.aggregations.pipeline.BucketSortPipelineAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.wzc.be.es.common.constant.Constants.UPDATE_INDEX_REDIS_KEY;
import static com.wzc.be.es.common.enums.TransportStatusEnums.APPLY_END;

/**
 * <AUTHOR>
 * @date 2023年07月05日
 * 运输计划分页查询
 */
@Slf4j
@Component
public class TransportGroupSearchHandler extends TransportBaseSearchHandler<TransportQueryPageQO, EsPageVO<TransportGroupVO>>{

    @Value("${transport.group.search.index:transport_index}")
    private String index;
    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private EsCommonAuthSearchService esCommonAuthSearchService;

    @Resource
    private RedissonClient redissonClient;

    @Override
    public RestResponse<EsPageVO<TransportGroupVO>> search(TransportQueryPageQO transportQueryPageQo) {
        try {
            JSONObject jsonObject = esCommonAuthSearchService.checkQueryAuth(JSONUtil.parseObj(transportQueryPageQo), QueryTypeEnums.TRANSPORT);
            transportQueryPageQo = JSONUtil.toBean(jsonObject, TransportQueryPageQO.class);
            log.info("运输计划分组模糊查询 search param is : {}", jsonObject.toJSONString(0));
            BoolQueryBuilder mainBoolQueryBuilder = getMainBoolQueryBuilder(transportQueryPageQo,null);

            Integer size = transportQueryPageQo.getSize();
            List<Integer> statusList = transportQueryPageQo.getStatusList();
            //redis修改记录集合
            List<CommonRedisResultVO> commonRedisUpdateResultVOList;
            Integer status;
            if (CollectionUtil.isNotEmpty(statusList) && statusList.size() == 1){
                //状态不为空 则从redis查询状态符合查询的运输计划编号集合
                RMapCache<String, String> mapCache = redissonClient.getMapCache(UPDATE_INDEX_REDIS_KEY + index + ":" + transportQueryPageQo.getCompanyId());
                commonRedisUpdateResultVOList = mapCache.readAllEntrySet().stream().map(e -> JSONUtil.toBean(e.getValue(),CommonRedisResultVO.class))
                        .collect(Collectors.toList());
                status = statusList.get(0);
                //查询是否有状态更新的redis缓存 有几个变化 查询就多查几条
                size += commonRedisUpdateResultVOList.size();
            } else {
                status = null;
                commonRedisUpdateResultVOList = null;
            }
            int fromElasticsearch = (transportQueryPageQo.getPage() == 0 ? transportQueryPageQo.getPage() : transportQueryPageQo.getPage() - 1) * size;

            TermsAggregationBuilder aggregation = AggregationBuilders
                    .terms("group_by_groupNo")
                    .field("groupNo").size(Integer.MAX_VALUE);

            //创建时间默认倒序排序
            SortOrder sortOrder = SortOrder.DESC;
            if (transportQueryPageQo.getIsAsc()){
                sortOrder = SortOrder.ASC;
            }
            aggregation.subAggregation(AggregationBuilders.max("max_created_at").field("createTime"));
            aggregation.subAggregation(AggregationBuilders.terms("order_no").field("orderNo.keyword"));
            aggregation.subAggregation(AggregationBuilders.terms("carrierNames").field("carrierName.keyword"));
            aggregation.subAggregation(AggregationBuilders.terms("assignTypeTerms").field("assignType"));
            aggregation.subAggregation(new BucketSortPipelineAggregationBuilder("bucket_sort", Collections.singletonList(
                    new FieldSortBuilder("max_created_at").order(sortOrder))).from(fromElasticsearch).size(size));

            SearchRequest searchRequest = new SearchRequest(index);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.aggregation(aggregation);
            sourceBuilder.size(0);
            sourceBuilder.trackTotalHits(true);
            sourceBuilder.query(mainBoolQueryBuilder);
            searchRequest.source(sourceBuilder);

            sourceBuilder.aggregation(AggregationBuilders.cardinality("cardinality_groupNo_count").field("groupNo"));
            log.info("运输计划请求参数DSL:{}", sourceBuilder.toString());
            SearchResponse response = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            Terms terms = response.getAggregations().get("group_by_groupNo");
            Cardinality cardinalityAgg = response.getAggregations().get("cardinality_groupNo_count");
            long cardinalityCount = cardinalityAgg.getValue();

            List<TransportGroupVO> transportGroupVOList = Lists.newArrayList();

            for (Terms.Bucket bucket : terms.getBuckets()) {
                // 获取bucket key、doc_count等
                // 构建每个对象
                TransportGroupVO item = new TransportGroupVO();
                String groupNo = bucket.getKeyAsString();
                item.setGroupNo(groupNo);
                StringBuilder carrierNames = new StringBuilder();
                Terms carrierNamesTerms = bucket.getAggregations().get("carrierNames");
                ParsedMax maxCreateTime = bucket.getAggregations().get("max_created_at");
                Terms orderNoTerms = bucket.getAggregations().get("order_no");
                Terms assignTypeTerms = bucket.getAggregations().get("assignTypeTerms");
                Terms acceptTimeTerms = bucket.getAggregations().get("transAcceptTime");
                //最大创建时间
                if (ObjectUtil.isNotNull(maxCreateTime)){
                    String createTime = maxCreateTime.getValueAsString();
                    item.setCreateTime(createTime);
                }
                //接单时间
                if (ObjectUtil.isNotNull(acceptTimeTerms)){
                    String acceptTime = acceptTimeTerms.getBuckets().get(0).getKeyAsString();
                    item.setAcceptTime(acceptTime);
                }
                //订单号
                if (ObjectUtil.isNotNull(orderNoTerms) && CollectionUtil.isNotEmpty(orderNoTerms.getBuckets())){
                    String groupOrderNo = orderNoTerms.getBuckets().get(0).getKeyAsString();
                    item.setOrderNo(groupOrderNo);
                }
                //指派方式
                if (ObjectUtil.isNotNull(assignTypeTerms) && CollectionUtil.isNotEmpty(assignTypeTerms.getBuckets())){
                    Integer assignType = Integer.parseInt(assignTypeTerms.getBuckets().get(0).getKeyAsString());
                    item.setAssignType(assignType);
                }
                //承运商名称拼接
                if (ObjectUtil.isNotNull(carrierNamesTerms) && ObjectUtil.isNotNull(carrierNamesTerms.getBuckets())){
                    for (Terms.Bucket carrierBucket : carrierNamesTerms.getBuckets()) {
                        carrierNames.append(carrierBucket.getKeyAsString()).append(",");
                    }
                    if(carrierNames.length() > 1){
                        item.setCarrierNames(carrierNames.deleteCharAt(carrierNames.length()-1).toString());
                    }
                }
                transportGroupVOList.add(item);
            }

            if (CollectionUtil.isNotEmpty(commonRedisUpdateResultVOList)){
                //过滤变化前状态的运输计划编号集合 符合则删除 如果为申请停运状态查询对应暂停中状态
                if (ObjectUtil.equals(status, APPLY_END.getCode())) {
                    status = TransportStatusEnums.PAUSE.getCode();
                }
                Integer finalBeforeStatus = status;
                List<CommonRedisResultVO> removeList = commonRedisUpdateResultVOList.stream().filter(c -> ObjectUtil.equals(c.getStatus(), finalBeforeStatus)).collect(Collectors.toList());
                transportGroupVOList.removeIf(p1 -> removeList.stream().anyMatch(p2 ->
                        p1.getGroupNo().equals(p2.getUnionNo())));
            }
            //截取固定条数集合
            List<TransportGroupVO> subList = transportGroupVOList.stream().limit(size).collect(Collectors.toList());
            EsPageVO<TransportGroupVO> esPageVO = new EsPageVO<>();
            esPageVO.setTotal(cardinalityCount);
            esPageVO.setList(subList);
            return RestResponse.success(esPageVO);
        } catch (Exception e) {
            log.error("运输计划分组查询异常.search error", e);
        }
        return RestResponse.error(ExcepitonEnum.ERROR_UNKNOW);
    }

    @Override
    public String getSearchType() {
        return SearchTypeEnums.TRANSPORT_GROUP_PAGE.getType();
    }

    @Override
    public String getIndex() {
        return index;
    }


}
