package com.wzc.be.es.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SyncTypeEnums {


    /**
     * 是否是存量标记
     */
    HISTORY(0, "存量"),
    INCREMENT(1, "增量"),
    ;

    private Integer code;

    private String name;

    public static String getName(Integer code) {
        for (DeletedEnums c : DeletedEnums.values()) {
            if (c.getCode().equals(code)) {
                return c.getName();
            }
        }
        return null;
    }
}
