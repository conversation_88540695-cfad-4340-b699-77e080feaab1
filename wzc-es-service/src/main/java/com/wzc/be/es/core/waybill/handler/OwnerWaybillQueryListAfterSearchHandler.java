package com.wzc.be.es.core.waybill.handler;


import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baidu.mapcloud.cloudnative.authentication.datapermission.core.rule.UserPermissionRule;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.core.waybill.converter.WaybillConverter;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.data.waybill.qo.OwnerWaybillListQO;
import com.wzc.be.es.data.waybill.vo.SubWaybillVO;
import com.wzc.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 货主PC运单列表
 *
 * <AUTHOR> Wei
 */
@Slf4j
@Component
public class OwnerWaybillQueryListAfterSearchHandler extends OwnerBaseHandler<OwnerWaybillListQO,EsPageVO<SubWaybillVO>>{

    @Value("${ownerWaybillQuery.after.search.type:ownerWaybillQueryAfterSearchHandler}")
    private String searchType;

    @Resource
    private EsIndexProperties esIndexProperties;

    @Autowired
    protected UserPermissionRule userPermissionRule;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Override
    public RestResponse<EsPageVO<SubWaybillVO>> search(OwnerWaybillListQO qo) {
        try {
            JSONObject jsonObject = authReq(userPermissionRule, JSONUtil.parseObj(qo));
            qo = JSONUtil.toBean(jsonObject.toJSONString(0),OwnerWaybillListQO.class);
            // 构建搜索请求
            SearchRequest searchRequest = new SearchRequest(esIndexProperties.getSubWaybillIndex());
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(SubWaybillIndex::getSubWaybillNo)).order(SortOrder.DESC));
            BoolQueryBuilder mainQueryBuilder = getMainBoolQueryBuilder(qo);
            log.info("货主运单请求参数:{}", JSON.toJSONString(qo));
            Integer size = qo.getSize();

            searchSourceBuilder.size(size);
            searchSourceBuilder.query(mainQueryBuilder);

            searchSourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
            searchSourceBuilder.trackTotalHits(true);

            if (ObjectUtil.isNotNull(qo.getLastSortValues()) && qo.getLastSortValues().length > 0){
                searchSourceBuilder.searchAfter(qo.getLastSortValues());
            }
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            SearchHit[] hitResult = searchHits.getHits();
            JSONArray array = new JSONArray();
            Stream.of(hitResult).forEach(itemHit -> {
                Map<String, Object> sourceAsMap = itemHit.getSourceAsMap();
                array.add(sourceAsMap);
            });
            TotalHits totalHits = searchHits.getTotalHits();
            List<SubWaybillIndex> list = JSONUtil.toList(array.toJSONString(0),SubWaybillIndex.class);

            EsPageVO<SubWaybillVO> page = new EsPageVO<>();
            page.setTotal(totalHits.value);
            if (CollectionUtils.isEmpty(list)) {
                return RestResponse.success(page);
            }
            for (SubWaybillIndex subWaybillIndex : list) {
                if(CollectionUtils.isNotEmpty(subWaybillIndex.getItems())){
                    Map<String,SubWaybillIndex.BmsItem> bmsItemMap = new HashMap<>();
                    if(CollectionUtils.isNotEmpty(subWaybillIndex.getBmsItems())){
                        bmsItemMap = subWaybillIndex.getBmsItems().stream().collect(Collectors.toMap(SubWaybillIndex.BmsItem::getItemId, Function.identity(),(o1, o2) -> o1));
                    }
                    for (SubWaybillIndex.Item item : subWaybillIndex.getItems()) {
                        SubWaybillIndex.BmsItem bmsItem = bmsItemMap.get(item.getItemId());
                        if(Objects.nonNull(bmsItem)){
                            item.setHzActualSettleWeight(bmsItem.getHzActualSettleWeight());
                            item.setHzActualSettlePrice(bmsItem.getHzActualSettlePrice());
                            item.setCysActualSettlePrice(bmsItem.getCysActualSettlePrice());
                            item.setCysActualSettleWeight(bmsItem.getCysActualSettleWeight());
                        }else {
                            item.setHzActualSettleWeight("0");
                            item.setHzActualSettlePrice("0");
                            item.setCysActualSettlePrice("0");
                            item.setCysActualSettleWeight("0");
                        }
                    }
                }
                if(Objects.nonNull(subWaybillIndex.getBusinessType()) && subWaybillIndex.getBusinessType() == 1){
                    String reStatementNo = subWaybillIndex.getReStatementNo();
                    String reOrderNo = subWaybillIndex.getReOrderNo();
                    subWaybillIndex.setPayStatementNo(reStatementNo);
                    subWaybillIndex.setPayOrderNo(reOrderNo);
                }
            }

            page.setList(WaybillConverter.INSTANCE.toSubWaybillVOList(list));
            Object[] lastSortValues = hitResult[hitResult.length - 1].getSortValues();
            page.setLastSortValues(lastSortValues);
            return RestResponse.success(page);
        }catch (Exception e){
            log.error("货主运单搜索异常:",e);
            throw new ServiceException(ExcepitonEnum.ERROR_UNKNOW);
        }

    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return esIndexProperties.getSubWaybillIndex();
    }

}
