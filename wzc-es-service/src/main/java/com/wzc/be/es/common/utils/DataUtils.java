package com.wzc.be.es.common.utils;

import com.wzc.be.data.data.es.transport.dto.CommonRedisResultDTO;
import com.wzc.be.es.core.common.CommonRedisResultVO;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class DataUtils {


    public static List<CommonRedisResultDTO> esRedisResultVOToDataDTO(List<CommonRedisResultVO> commonRedisResultVOList){
        return commonRedisResultVOList.stream().flatMap(i->{
            CommonRedisResultDTO commonRedisResultDTO = new CommonRedisResultDTO();
            commonRedisResultDTO.setAfterStatus(i.getAfterStatus());
            commonRedisResultDTO.setCreateTime(i.getCreateTime());
            commonRedisResultDTO.setDelFlag(i.getDelFlag());
            commonRedisResultDTO.setStatus(i.getStatus());
            commonRedisResultDTO.setUnionNo(i.getUnionNo());
            return Stream.of(commonRedisResultDTO);
        }).collect(Collectors.toList());
    }

}
