package com.wzc.be.es.core.common;

import lombok.experimental.UtilityClass;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;

@UtilityClass
public class ListUtils {

    /**
     * 判断集合里是否有包含匹配的数据
     * @param list
     * @param value
     * @param getField
     * @return
     * @param <T>
     */
    public <T> boolean contains(List<T> list, T value, Function<T, ?> getField) {
        return list.stream().anyMatch(item -> Objects.equals(getField.apply(item), value));
    }
}
