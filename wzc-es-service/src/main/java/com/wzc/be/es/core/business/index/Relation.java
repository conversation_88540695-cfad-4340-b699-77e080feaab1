package com.wzc.be.es.core.business.index;

import cn.easyes.annotation.IndexField;
import cn.easyes.annotation.rely.FieldType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class Relation {

    @Schema(description = "运单号")
    private String dispatchNo;

    @Schema(description = "上游货主审核状态（0待审核 1审核通过 2已退回）")
    private Integer apAuditStatus;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "上游货主审核时间")
    private String apAuditTime;

    @Schema(description = "付款方id")
    private Long apCompanyId;
}
