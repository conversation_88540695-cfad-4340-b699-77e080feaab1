package com.wzc.be.es.common.enums;

import com.baidu.mapcloud.cloudnative.common.model.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BusinessQueryTabTypeEnums implements IEnum {

    /**
     * 对账单分页查询tab
     */
    UN_RC(1, "未对账"),
    RC_UN_SETTLE(2, "已对账未结算"),
    SETTLE(3, "已结算"),
    ALL(4, "全部"),
    UN_AUDIT(5, "待审核"),
    AUDIT(6, "已审核"),
    RC_UN_SETTLE_AUDIT(7, "已对账制证中"),
    EXCEPTION(8, "异常处理"),
    ;

    private final Integer code;
    private final String name;
}
