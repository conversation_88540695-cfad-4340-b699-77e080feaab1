///**
// * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
// **/
//package com.wzc.be.es.core.business.service;
//
//import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
//import com.wzc.be.es.common.handler.BaseSearchHandler;
//import com.wzc.be.es.common.service.EsBaseSearchService;
//import com.wzc.be.es.commons.EsPageVO;
//import com.wzc.be.es.commons.enums.SearchTypeEnums;
//import com.wzc.be.es.data.business.qo.BusinessEsQueryPageQO;
//import com.wzc.be.es.data.business.qo.TocBusinessEsPageQO;
//import com.wzc.be.es.data.business.vo.*;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @date 2022年10月13日 3:17 PM
// */
//@Component
//@Slf4j
//@RequiredArgsConstructor(onConstructor = @__(@Autowired))
//public class EsBusinessSearchServiceV1 extends EsBaseSearchService {
//
//    /**
//     * 对账单分页查询-运单模式
//     * @param queryPageQo queryPageQo
//     * @return RestResponse
//     */
//    public RestResponse<EsPageVO<BusinessEsVO>> reconciliationPage(BusinessEsQueryPageQO queryPageQo) {
//        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.BUSINESS_RC_YD_PAGE.getType());
//        return searchHandler.search(queryPageQo);
//    }
//
//    /**
//     * 对账单对账状态统计-运单模式
//     * @param requestPageQo requestPageQo
//     * @return EsReconciliationStatusCountVO
//     */
//    public RestResponse<BmsEsRcStatusCountVO> rcStatusCount(BusinessEsQueryPageQO requestPageQo) {
//        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.RECONCILIATION_STATUS_COUNT.getType());
//        return searchHandler.search(requestPageQo);
//    }
//
//    /**
//     * 对账单对账状态统计-运输计划模式
//     * @param requestPageQo
//     * @return
//     */
//    public RestResponse<EsReconciliationStatusCountVO> rcStatusTransportCount(BusinessEsQueryPageQO requestPageQo) {
//        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.RECONCILIATION_STATUS_TRANSPORT_COUNT.getType());
//        return searchHandler.search(requestPageQo);
//    }
//
//    /**
//     * 对账单审核状态统计-运单模式
//     * @param requestPageQo
//     * @return
//     */
//    public RestResponse<BmsEsAuditStatusCountVO> rcAuditStatusCount(BusinessEsQueryPageQO requestPageQo) {
//        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.RC_AUDIT_STATUS_COUNT.getType());
//        return searchHandler.search(requestPageQo);
//    }
//
//
//    /**
//     * 对账单审核状态统计-运输计划模式
//     * @param requestPageQo
//     * @return
//     */
//    public RestResponse<BmsEsAuditStatusCountVO> rcAuditStatusTransportCount(BusinessEsQueryPageQO requestPageQo) {
//        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.RC_AUDIT_STATUS_TRANSPORT_COUNT.getType());
//        return searchHandler.search(requestPageQo);
//    }
//
//    /**
//     * 对账单列表查询-运单模式
//     * @param requestPageQo
//     * @return
//     */
//    public RestResponse<List<BusinessEsVO>> reconciliationList(BusinessEsQueryPageQO requestPageQo) {
//        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.BUSINESS_RC_YD_LIST.getType());
//        return searchHandler.search(requestPageQo);
//    }
//
//    /**
//     * 对账单分页查询-运输计划模式
//     * @param requestPageQo
//     * @return
//     */
//    public RestResponse<EsPageVO<BmsRcGroupVO>> reconciliationGroupPage(BusinessEsQueryPageQO requestPageQo) {
//        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.BUSINESS_RC_TRANSPORT_PAGE.getType());
//        return searchHandler.search(requestPageQo);
//    }
//
//    /**
//     * 网货对账单分页查询
//     * @param requestPageQo
//     * @return
//     */
//    public RestResponse<EsPageVO<TocEsBusinessVO>> tocReconciliationPage(TocBusinessEsPageQO requestPageQo) {
//        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.TOC_BUSINESS_RC_YD_PAGE.getType());
//        return searchHandler.search(requestPageQo);
//    }
//}
