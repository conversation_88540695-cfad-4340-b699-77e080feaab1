package com.wzc.be.es.adapter.action.common;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.api.common.EsCommonSearchApi;
import com.wzc.be.es.commons.qo.CommonRequestQO;
import com.wzc.be.es.core.common.EsCommonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023年08月11日
 */
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class CommonAction implements EsCommonSearchApi {

    private final EsCommonService esCommonService;

    /**
     * 索引关联数据 新增同步到redis
     * @param qo qo
     * @return RestResponse
     */
    @Override
    public RestResponse<Boolean> insert(CommonRequestQO qo) {
        return esCommonService.insert(qo);
    }

    @Override
    public RestResponse<Boolean> delete(CommonRequestQO qo) {
        return esCommonService.delete(qo);
    }

    @Override
    public RestResponse<Boolean> update(CommonRequestQO qo) {
        return esCommonService.update(qo);
    }
}
