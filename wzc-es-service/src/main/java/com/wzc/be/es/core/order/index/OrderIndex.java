package com.wzc.be.es.core.order.index;

import cn.easyes.annotation.IndexField;
import cn.easyes.annotation.rely.FieldType;
import cn.easyes.common.enums.OrderTypeEnum;
import com.wzc.be.es.common.index.ContractRelation;
import com.wzc.be.es.common.index.Items;
import com.wzc.be.es.common.index.Shipping;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单索引实体类
 * <AUTHOR>
 * @Date: 2023-06-13
 */
@Data
@ToString
public class OrderIndex {

    private String id;

    private Long orderId;

    private Long ownerId;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "第三方订单号")
    private String thirdNo;

    private Integer orderType;

    private String baseNo;

    @Schema(description = "货物名称")
    private String itemNames;

    @Schema(description = "业务类型 1.平台总包，2.货主自营，3.平台自营，4.网络货运")
    private Integer businessType;

    @Schema(description = "订单来源 1、手动创建 2、竞价货源 3、抢单货源 4、外部推送 5、线路拆分 6、转接 7.转包")
    private Integer sourceType;

    @Schema(description = "发货单位")
    private String deliveryCustomer;

    @Schema(description = "发货地址")
    private String deliveryAddress;

    @Schema(description = "收货单位")
    private String receiptCustomer;

    @Schema(description = "收货地址")
    private String receiptAddress;

    @Schema(description = "订单类型（1.采购订单，2.销售订单，3.调拨订单）")
    private Integer waybillType;

    @Schema(description = "订单状态")
    private Integer status;

    @Schema(description = "运输模式")
    private Integer transportType;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "公司id")
    private Long companyId;

    @Schema(description = "公司id")
    private Long companyName;

    @Schema(description = "托运人公司ID")
    private Long shipperCompanyId;

    @Schema(description = "托运人公司名称")
    private Long shipperCompanyName;

    @Schema(description = "制单人名称")
    private String makerName;

    @Schema(description = "转网货标识 1网货订单 2非网货订单")
    private Integer netCargo;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String createTime;

    @Schema(description = "部门id")
    private Long ownerDepartment;

    @Schema(description = "二期运单号")
    private String waybillNoSecond;

    @Schema(description = "是否同步二期数据 (1是  2否)")
    private Integer isSync;

    @Schema(description = "是否固定流向 (1是  2否)")
    private Integer isFixed;

    @Schema(description = "是否开启校验载重设备功能  1是，2否，默认否")
    private Integer loadDeviceCheck;

    private Long createId;

    private List<Items> items;

    private List<Shipping> sender;

    private List<Shipping> receiver;

    @Schema(description = "可指派量")
    private BigDecimal assignedWeight;

    @Schema(description = "隐藏状态 0-不隐藏 1-隐藏")
    private Integer hideStatus;

    @Schema(description = "合同签署要求（0-无需签署 1-需要签署 2-强制签署）")
    private Integer contractRequire;

    @Schema(description = "合同平台合同审核状态：（-1=草稿,0=审核中,1=审核通过,2=审核不通过,10=已归档,20=变更中,30-已盖章,31:赛马物联已盖章,32:用户已盖章,101-线下签署中），聚合层维护合同审核状态：（3=待生成）")
    private Integer contractStatus;

    @Schema(description = "合同信息")
    private List<ContractRelation> contractRelation;

    @Schema(description = "过期自动完成运输 （1-否，2-是，默认否）")
    private Integer isAutoStop;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String netCargoCreateTime;

    @Schema(description = "运营端是否展示")
    private Integer isShowAdmin;

}
