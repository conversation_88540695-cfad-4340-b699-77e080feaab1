package com.wzc.be.es.adapter.action.warn;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.api.warn.EsCscWarnSearchApi;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.core.warn.service.EsCscWarnExceptionSearchService;
import com.wzc.be.es.data.warn.qo.CscWarnExceptionEsPageQO;
import com.wzc.be.es.data.warn.vo.CscWarnExceptionVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023年10月13日
 */
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class CscWarnExceptionAction implements EsCscWarnSearchApi {

    private final EsCscWarnExceptionSearchService cscWarnExceptionSearchService;

    @Override
    public RestResponse<EsPageVO<CscWarnExceptionVO>> cscWarnExceptionPage(CscWarnExceptionEsPageQO requestPageQo) {
        return cscWarnExceptionSearchService.cscWarnExceptionPage(requestPageQo);
    }
}
