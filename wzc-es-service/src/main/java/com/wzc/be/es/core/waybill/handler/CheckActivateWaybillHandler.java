package com.wzc.be.es.core.waybill.handler;


import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.core.waybill.mapper.SubWaybillListMapper;
import com.wzc.be.es.data.waybill.qo.CheckActivateWaybillQO;
import com.wzc.be.es.data.waybill.vo.WaybillQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 司机开启运单校验是否有已开启运单查询
 *
 * <AUTHOR> Wei
 */
@Slf4j
@Component
public class CheckActivateWaybillHandler implements BaseSearchHandler<CheckActivateWaybillQO, WaybillQueryVO> {

    @Value("${checkActivateWaybill.search.type:checkActivateWaybillHandler}")
    private String searchType;

    @Value("${waybill.search.index:sub_waybill_index01}")
    private String index;

    @Resource
    private SubWaybillListMapper subWaybillListMapper;

    @Override
    public RestResponse<WaybillQueryVO> search(CheckActivateWaybillQO qo) {
        LambdaEsQueryWrapper<SubWaybillIndex> queryWrapper = new LambdaEsQueryWrapper<>();
        queryWrapper.isNotNull(SubWaybillIndex::getSubWaybillNo);
        if (qo.getDriverId() != null) {
            queryWrapper.eq(SubWaybillIndex::getDriverId, qo.getDriverId());
        }

        if (qo.getCarId() != null) {
            queryWrapper.eq(SubWaybillIndex::getCarId, qo.getCarId());
        }
        // 派车单运输中
        // 进行中
        queryWrapper.and(i -> i.eq(SubWaybillIndex::getSubWaybillStatus, 1)
                .or().eq(SubWaybillIndex::getSubWaybillStatus, 2)
                .or().eq(SubWaybillIndex::getSubWaybillStatus, 3)
                .or().eq(SubWaybillIndex::getSubWaybillStatus, 4)
                .or().eq(SubWaybillIndex::getSubWaybillStatus, 5)
                .or().eq(SubWaybillIndex::getSubWaybillStatus, 6)
        );
        queryWrapper.index(getIndex());
        queryWrapper.limit(1);
        SubWaybillIndex subWaybill = subWaybillListMapper.selectOne(queryWrapper);
        if (subWaybill == null) {
            return RestResponse.success();
        }
        WaybillQueryVO record = new WaybillQueryVO();
        record.setWaybillNo(subWaybill.getWaybillNo());
        record.setWaybillStatus(subWaybill.getSubWaybillStatus());
        record.setStatus(subWaybill.getStatus());
        record.setCompleteStatus(subWaybill.getCompleteStatus());
        record.setCarrierCompanyId(subWaybill.getCarrierCompanyId());
        record.setCompanyId(subWaybill.getCompanyId());
        record.setDriverId(subWaybill.getDriverId());
        record.setDriverName(subWaybill.getDriverName());
        record.setCarId(subWaybill.getCarId());
        record.setCarNo(subWaybill.getCarNo());
        record.setCreateTime(subWaybill.getCreateTime());
        record.setSubWaybillNo(subWaybill.getSubWaybillNo());
        record.setOrderNo(subWaybill.getOrderNo());
        return RestResponse.success(record);
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.index;
    }
}
