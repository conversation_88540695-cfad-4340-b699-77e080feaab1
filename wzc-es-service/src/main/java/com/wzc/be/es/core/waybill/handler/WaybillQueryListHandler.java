package com.wzc.be.es.core.waybill.handler;

import cn.easyes.core.biz.EsPageInfo;
import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import com.alibaba.fastjson.JSON;
import com.baidu.mapcloud.cloudnative.common.model.PageOrderType;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.enums.SubWaybillStatusEnum;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.commons.PageVO;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.core.waybill.mapper.SubWaybillListMapper;
import com.wzc.be.es.data.waybill.enums.DispatchQueryEnums;
import com.wzc.be.es.data.waybill.enums.DriverTabTypeEnum;
import com.wzc.be.es.data.waybill.qo.DriverWaybillListQO;
import com.wzc.be.es.data.waybill.vo.WaybillQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class WaybillQueryListHandler implements BaseSearchHandler<DriverWaybillListQO, PageVO<WaybillQueryVO>> {

    @Value("${waybillRecord.search.type:waybillQueryListHandler}")
    private String searchType;

    @Resource
    private EsIndexProperties esIndexProperties;

    @Resource
    private SubWaybillListMapper subWaybillListMapper;

    @Override
    public RestResponse<PageVO<WaybillQueryVO>> search(DriverWaybillListQO qo) {
        log.info("司机运单列表ES查询参数:{}", JSON.toJSONString(qo));
        PageVO<WaybillQueryVO> page = new PageVO<>();
        page.setPage(qo.getPage());
        page.setSize(qo.getSize());

        List<SubWaybillIndex> list = new ArrayList<>();

        // 司机运输中的运单始终排在运单列表Top
        List<SubWaybillIndex> transportWaybill = getTransportWaybill(qo);
        if (CollectionUtils.isNotEmpty(transportWaybill)) {
            list.addAll(transportWaybill);
        }

        LambdaEsQueryWrapper<SubWaybillIndex> wrapper = new LambdaEsQueryWrapper<>();
        this.genSearch(wrapper, qo);
        wrapper.index(esIndexProperties.getSubWaybillIndex());
        List<WaybillQueryVO> data = new ArrayList<>();

        EsPageInfo<SubWaybillIndex> esPageInfo = subWaybillListMapper.pageQuery(wrapper, qo.getPage(), qo.getSize());
        if (CollectionUtils.isNotEmpty(esPageInfo.getList())) {
            list.addAll(esPageInfo.getList());
        }
        if (CollectionUtils.isEmpty(list)) {
            return RestResponse.success(page);
        }
        List<SubWaybillIndex> collect = new ArrayList<>(list.stream().collect(Collectors.toMap(SubWaybillIndex::getWaybillNo, waybill -> waybill, (o1, o2) -> o1)).values());
        for (SubWaybillIndex subWaybill : collect) {
            WaybillQueryVO record = new WaybillQueryVO();
            record.setWaybillNo(subWaybill.getWaybillNo());
            record.setWaybillStatus(subWaybill.getSubWaybillStatus());
            record.setStatus(subWaybill.getStatus());
            record.setCompleteStatus(subWaybill.getCompleteStatus());
            record.setCarrierCompanyId(subWaybill.getCarrierCompanyId());
            record.setCompanyId(subWaybill.getCompanyId());
            record.setDriverId(subWaybill.getDriverId());
            record.setDriverName(subWaybill.getDriverName());
            record.setCarId(subWaybill.getCarId());
            record.setCarNo(subWaybill.getCarNo());
            record.setCreateTime(subWaybill.getCreateTime());
            record.setIsEvaluate(Objects.isNull(subWaybill.getIsEvaluate()) ? 0 : subWaybill.getIsEvaluate());
            record.setDispatchComplianceType(subWaybill.getDispatchComplianceType());
            record.setBusinessType(subWaybill.getBusinessType());
            data.add(record);
        }
        page.setTotal(esPageInfo.getTotal());

        //是否还有下一页 true = 有 false = 无
        page.setHasNext(qo.getPage() + qo.getSize() < page.getTotal());
        PageOrderType orderType = qo.getOrderType();
        if (orderType != null) {
            List<WaybillQueryVO> collect1;
            if (PageOrderType.ASC.equals(orderType)) {
                collect1 = data.stream().sorted(Comparator.comparing(WaybillQueryVO::getCreateTime)).collect(Collectors.toList());
            } else {
                collect1 = data.stream().sorted(Comparator.comparing(WaybillQueryVO::getCreateTime).reversed()).collect(Collectors.toList());
            }
            page.setContent(collect1);
        }
        return RestResponse.success(page);
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.esIndexProperties.getSubWaybillIndex();
    }

    /**
     * 构建查询条件
     */
    private void genSearch(LambdaEsQueryWrapper<SubWaybillIndex> wrapper, DriverWaybillListQO qo) {
        wrapper.isNotNull(SubWaybillIndex::getSubWaybillNo);
        // 司机id
        wrapper.eq(SubWaybillIndex::getDriverId, qo.getDriverId());

        DriverTabTypeEnum tabType = qo.getStatus();
        switch (tabType) {
            case STATUS_1:
                // 待执行
                wrapper.eq(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.WAIT_START.getCode());
                break;
            case STATUS_2:
                // 待签收
                wrapper.eq(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.WAIT_SIGN.getCode());
                break;
            case STATUS_3:
                // 待收款
                wrapper.eq(SubWaybillIndex::getManualCheckStatus, 1);
                wrapper.and(i -> i.eq(SubWaybillIndex::getCompleteStatus, 3).or().eq(SubWaybillIndex::getCompleteStatus, 6).or().eq(SubWaybillIndex::getCompleteStatus, 7).or().eq(SubWaybillIndex::getCompleteStatus, 8));
                break;
            default:
                // 全部
                wrapper.and(i -> i.eq(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.WAIT_APPOINTMENT_LOAD.getCode())
                        .or().eq(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.WAIT_START.getCode())
                        .or().eq(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.WAIT_LOAD_SIGN.getCode())
                        .or().eq(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.WAIT_LOAD.getCode())
                        .or().eq(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.WAIT_APPOINTMENT_UNLOAD.getCode())
                        .or().eq(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.WAIT_UNLOAD_SIGN.getCode())
                        .or().eq(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.WAIT_UNLOAD.getCode())
                        .or().eq(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.WAIT_SIGN.getCode())
                        .or().eq(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.FINISH.getCode())
                        .or().eq(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.WAIT_ASSIGN.getCode())
                        .or().eq(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.CANCELLED.getCode())
                );
                break;
        }

        // 时间范围
        if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
            //增加日期范围请求参数
            wrapper.ge(SubWaybillIndex::getCreateTime, qo.getStartTime());
            wrapper.le(SubWaybillIndex::getCreateTime, qo.getEndTime());
        }

        // 排序方式
        PageOrderType orderType = qo.getOrderType();
        if (orderType != null) {
            if (PageOrderType.ASC.equals(orderType)) {
                wrapper.orderByAsc(SubWaybillIndex::getCreateTime);
            } else {
                wrapper.orderByDesc(SubWaybillIndex::getCreateTime);
            }
        }

        // 搜索
        DispatchQueryEnums queryType = qo.getQueryType();
        String queryValue = qo.getQueryValue();
        if (StringUtils.isNotBlank(queryValue)) {
            switch (queryType) {
                case CAR_NO:
                    wrapper.like("carNo", queryValue);
                    break;
                case FORM_ADDRESS:
                    wrapper.like("sender.fullAddress", queryValue);
                    break;
                case TO_ADDRESS:
                    wrapper.like("receiver.fullAddress", queryValue);
                    break;
                case CARRIER:
                    wrapper.like("shipperCompanyName", queryValue);
                    break;
                case MATERIAL_NAME:
                    wrapper.like("items.itemName", queryValue);
                    break;
                case DISPATCH_CAR_NO:
                    wrapper.and(i -> i.like("waybillNo", queryValue).or().like("oldWaybillNo", queryValue));
                    break;
                case CONTRACT_NO:
                    wrapper.like("contractNo", queryValue);
                    break;
                default:
                    // ALL
                    wrapper.and(i -> i.like("carNo", queryValue).or().like("sender.fullAddress", queryValue).or().like("receiver.fullAddress", queryValue).or().like("items.itemName", queryValue).or().like("shipperCompanyName", queryValue).or().like("waybillNo", queryValue).or().like("contractNo", queryValue));
                    break;
            }
        }

        if (qo.getWaybillType() != null && qo.getWaybillType() != 0) {
            wrapper.eq(SubWaybillIndex::getOrderType, qo.getWaybillType());
        }

        // 合同状态
        if (Objects.nonNull(qo.getContractStatus())) {
            wrapper.eq(SubWaybillIndex::getContractStatus, qo.getContractStatus());
        }

        // 签订合同
        if (Objects.nonNull(qo.getSignContract())) {
            wrapper.eq(SubWaybillIndex::getSignContract, qo.getSignContract());
        }

        // 是否需要合同
        if (Objects.nonNull(qo.getContractRequire())) {
            wrapper.eq(SubWaybillIndex::getNeedContract, qo.getContractRequire());
        }

        // 运单状态筛选
        if (CollectionUtils.isNotEmpty(qo.getWaybillStatus())) {
            wrapper.in(SubWaybillIndex::getSubWaybillStatus, qo.getWaybillStatus());
        }
    }


    /**
     * 查询司机运输中的运单
     */
    private List<SubWaybillIndex> getTransportWaybill(DriverWaybillListQO qo) {
        DriverTabTypeEnum tabType = qo.getStatus();
        if (DriverTabTypeEnum.STATUS_1.equals(tabType) || DriverTabTypeEnum.STATUS_4.equals(tabType)) {
            LambdaEsQueryWrapper<SubWaybillIndex> mustQuery = new LambdaEsQueryWrapper<>();
            mustQuery.index(esIndexProperties.getSubWaybillIndex());
            mustQuery.groupBy(SubWaybillIndex::getWaybillNo);
            mustQuery.eq(SubWaybillIndex::getDriverId, qo.getDriverId());
            mustQuery.and(i -> i.eq(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.WAIT_APPOINTMENT_LOAD.getCode()).or().eq(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.WAIT_LOAD_SIGN.getCode()).or().eq(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.WAIT_LOAD.getCode()).or().eq(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.WAIT_APPOINTMENT_UNLOAD.getCode()).or().eq(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.WAIT_UNLOAD_SIGN.getCode()).or().eq(SubWaybillIndex::getSubWaybillStatus, SubWaybillStatusEnum.WAIT_UNLOAD.getCode()));

            if (StringUtils.isNotBlank(qo.getStartTime()) && StringUtils.isNotBlank(qo.getEndTime())) {
                mustQuery.ge(SubWaybillIndex::getCreateTime, qo.getStartTime());
                mustQuery.le(SubWaybillIndex::getCreateTime, qo.getEndTime());
            }

            // 搜索
            DispatchQueryEnums queryType = qo.getQueryType();
            String queryValue = qo.getQueryValue();
            if (StringUtils.isNotBlank(queryValue)) {
                switch (queryType) {
                    case CAR_NO:
                        mustQuery.like("carNo", queryValue);
                        break;
                    case FORM_ADDRESS:
                        mustQuery.like("sender.fullAddress", queryValue);
                        break;
                    case TO_ADDRESS:
                        mustQuery.like("receiver.fullAddress", queryValue);
                        break;
                    case CARRIER:
                        mustQuery.like("shipperCompanyName", queryValue);
                        break;
                    case MATERIAL_NAME:
                        mustQuery.like("items.itemName", queryValue);
                        break;
                    case DISPATCH_CAR_NO:
                        mustQuery.and(i -> i.like("waybillNo", queryValue).or().like("oldWaybillNo", queryValue));
                        break;
                    case CONTRACT_NO:
                        mustQuery.like("contractNo", queryValue);
                        break;
                    default:
                        // ALL
                        mustQuery.and(i -> i.like("carNo", queryValue).or().like("sender.fullAddress", queryValue).or().like("receiver.fullAddress", queryValue).or().like("items.itemName", queryValue).or().like("shipperCompanyName", queryValue).or().like("waybillNo", queryValue).or().like("contractNo", queryValue));
                        break;
                }
            }

            // 合同状态
            if (Objects.nonNull(qo.getContractStatus())) {
                mustQuery.eq(SubWaybillIndex::getContractStatus, qo.getContractStatus());
            }

            // 签订合同
            if (Objects.nonNull(qo.getSignContract())) {
                mustQuery.eq(SubWaybillIndex::getSignContract, qo.getSignContract());
            }

            // 是否需要合同
            if (Objects.nonNull(qo.getContractRequire())) {
                mustQuery.eq(SubWaybillIndex::getNeedContract, qo.getContractRequire());
            }

            EsPageInfo<SubWaybillIndex> mustPage = subWaybillListMapper.pageQuery(mustQuery, qo.getPage(), 1);
            return mustPage.getList();
        }
        return null;
    }
}
