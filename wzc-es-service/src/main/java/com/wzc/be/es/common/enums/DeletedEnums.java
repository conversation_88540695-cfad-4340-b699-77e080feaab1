package com.wzc.be.es.common.enums;

import com.baidu.mapcloud.cloudnative.common.model.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DeletedEnums implements IEnum {

    /**
     * 删除标记
     */
    YES(0, "正常"),
    NO(1, "删除"),
    ;

    private Integer code;

    private String name;

    public static String getName(Integer code) {
        for (DeletedEnums c : DeletedEnums.values()) {
            if (c.getCode().equals(code)) {
                return c.getName();
            }
        }
        return null;
    }
}
