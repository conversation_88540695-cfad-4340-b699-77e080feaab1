package com.wzc.be.es.common.enums;

import com.baidu.mapcloud.cloudnative.common.model.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @<PERSON> zhanwang
 * @Date 2023/11/10 1:21
 */
@Getter
@AllArgsConstructor
public enum SuperviseTrackEnum implements IEnum {

    TRACK_NO(0, "轨迹不合规"),
    TRACK_YES(1, "轨迹合规");

    private Integer code;
    private String name;

    public Integer getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }
}
