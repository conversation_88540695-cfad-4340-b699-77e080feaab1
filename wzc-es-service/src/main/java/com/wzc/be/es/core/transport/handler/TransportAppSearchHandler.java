/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.core.transport.handler;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.google.common.collect.Lists;
import com.wzc.be.es.adapter.data.DwdTmsTransportIndexClient;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.constant.Constants;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.common.enums.TransportSearchTypeEnums;
import com.wzc.be.es.common.enums.TransportStatusEnums;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.core.common.CommonRedisResultVO;
import com.wzc.be.es.core.transport.index.TransportIndex;
import com.wzc.be.es.data.transport.qo.TransportQueryPageQO;
import com.wzc.be.es.data.transport.vo.TransportEsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.wzc.be.es.common.constant.Constants.UPDATE_INDEX_REDIS_KEY;
import static com.wzc.be.es.common.enums.TransportStatusEnums.APPLY_END;
import static com.wzc.be.es.common.enums.TransportStatusEnums.WAITING_DISPATCH;
import static java.util.Objects.nonNull;

/**
 * <AUTHOR>
 * @date 2023年07月05日
 * 运输计划app分页查询
 */
@Slf4j
@Component
public class TransportAppSearchHandler implements BaseSearchHandler<TransportQueryPageQO, EsPageVO<TransportEsVO>> {

    @Value("${transport.app.search.type:transportApp}")
    private String searchType;

    @Resource
    private EsIndexProperties esIndexProperties;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private DwdTmsTransportIndexClient dwdTmsTransportIndexClient;

    @Override
    public RestResponse<EsPageVO<TransportEsVO>> search(TransportQueryPageQO transportQueryPageQo) {
        log.info("运输计划app模糊查询 search param is : {}", JSONUtil.toJsonStr(transportQueryPageQo));
        try {
            Integer status = transportQueryPageQo.getStatus();
            RMapCache<String, String> mapCache = null;
            List<CommonRedisResultVO> commonRedisResultVOList;
            if (ObjectUtil.isNotNull(status)){
                //状态不为空 则从redis查询状态符合查询的运输计划编号集合
                mapCache = redissonClient.getMapCache(UPDATE_INDEX_REDIS_KEY + esIndexProperties.getTransportIndex() + ":" + transportQueryPageQo.getCarrierId());
                commonRedisResultVOList = mapCache.readAllEntrySet().stream().map(e -> JSONUtil.toBean(e.getValue(),CommonRedisResultVO.class))
                        .collect(Collectors.toList());
            } else {
                commonRedisResultVOList = null;
            }
            SearchRequest searchRequest = new SearchRequest(getIndex());
            SearchSourceBuilder sourceBuilder = generateSearchSourceBuilder(transportQueryPageQo,mapCache);
            searchRequest.source(sourceBuilder);
            List<TransportEsVO> transportEsVOList;
            SearchHits searchHits = null;
            long count = 0;
            try {
                SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
                searchHits = searchResponse.getHits();
                SearchHit[] hitResult = searchHits.getHits();
                JSONArray array = new JSONArray();
                Stream.of(hitResult).forEach(itemHit -> {
                    Map<String, Object> sourceAsMap = itemHit.getSourceAsMap();
                    array.add(sourceAsMap);
                });
                //比对两个集合，查询回来的运输计划有在变更状态的集合里，则删掉
                transportEsVOList = JSONUtil.toList(array, TransportEsVO.class);
            }catch (Exception e){
                log.error("查询app运输计划es失败，开始查询doris",e);
                //如果不是 es查询异常 便往外抛
                Throwable throwable = ExceptionUtil.getRootCause(e);
                if(!(throwable instanceof ElasticsearchException)
                        || !((ElasticsearchException) throwable).getDetailedMessage().contains(Constants.ES_RESULT_WINDOW_IS_TOO_LARGE)){
                    throw new Exception(e);
                }
                CountRequest countRequest = new CountRequest(getIndex());
                countRequest.source(sourceBuilder);
                CountResponse countResponse = restHighLevelClient.count(countRequest, RequestOptions.DEFAULT);
                count = countResponse.getCount();
                transportEsVOList = dwdTmsTransportIndexClient.appSearch(transportQueryPageQo);
            }
            if (CollectionUtil.isNotEmpty(commonRedisResultVOList)){
                //过滤变化前状态的运输计划编号集合 符合则删除
                List<CommonRedisResultVO> removeList = commonRedisResultVOList.stream().filter(c -> ObjectUtil.equals(c.getStatus(), status)).collect(Collectors.toList());
                transportEsVOList.removeIf(p1 -> removeList.stream().anyMatch(p2 ->
                        p1.getTransportNo().equals(p2.getUnionNo())));
                //待派车对应运输计划状态进行中
                Integer matchAfterStatus;
                if (ObjectUtil.equals(status,WAITING_DISPATCH.getCode())){
                    matchAfterStatus = TransportStatusEnums.RUNNING.getCode();
                }
                //申请停运状态 对应 暂停状态
                else if (ObjectUtil.equals(status,APPLY_END.getCode())){
                    matchAfterStatus = TransportStatusEnums.PAUSE.getCode();
                }
                else {
                    matchAfterStatus = status;
                }
                //是否有符合变化后状态的集合
                List<CommonRedisResultVO> addList = commonRedisResultVOList.stream().filter(c -> ObjectUtil.equals(c.getAfterStatus(), matchAfterStatus)).collect(Collectors.toList());
                for(CommonRedisResultVO commonRedisResultVO : addList){
                    if (!contains(transportEsVOList,commonRedisResultVO.getUnionNo())){
                        TransportEsVO transportEsVO = new TransportEsVO();
                        transportEsVO.setTransportNo(commonRedisResultVO.getUnionNo());
                        transportEsVOList.add(0,transportEsVO);
                    }
                }
            }
            List<TransportEsVO> subList = transportEsVOList.stream().limit(transportQueryPageQo.getSize()).collect(Collectors.toList());
            TotalHits totalHits = searchHits.getTotalHits();
            EsPageVO<TransportEsVO> esPageRes = new EsPageVO<>();
            if(count != 0){
                esPageRes.setTotal(count);
            }else{
                esPageRes.setTotal(totalHits.value);
            }
            esPageRes.setList(subList);
            return RestResponse.success(esPageRes);
        } catch (Exception e) {
            log.error("运输计划app查询异常.search error", e);
        }
        return RestResponse.error(ExcepitonEnum.ERROR_UNKNOW);
    }

    private boolean contains(List<TransportEsVO> list, String value){
        return list.stream().anyMatch(o -> o.getTransportNo().equals(value));
    }


    /**
     * 查询条件组装
     *
     * @param transportQueryPageQo waybillQueryPageQo
     * @return LambdaEsQueryWrapper
     */
    private SearchSourceBuilder generateSearchSourceBuilder(TransportQueryPageQO transportQueryPageQo,RMapCache<String, String> mapCache) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder mainBoolQueryBuilder = QueryBuilders.boolQuery();

        String thirdNo = transportQueryPageQo.getThirdNo();
        if (StrUtil.isNotEmpty(thirdNo)) {
            if (thirdNo.length() > 1) {
                mainBoolQueryBuilder.must(QueryBuilders.matchPhraseQuery(FieldUtils.val(TransportIndex::getThirdNo), thirdNo));
            } else {
                mainBoolQueryBuilder.must(QueryBuilders.wildcardQuery(FieldUtils.val(TransportIndex::getThirdNo), wildcardsExp(thirdNo)));
            }
        }
        // 运输计划编号不为空
        mainBoolQueryBuilder.must(QueryBuilders.existsQuery(FieldUtils.val(TransportIndex::getTransportNo)));
        String transportNo = transportQueryPageQo.getTransportNo();
        if (StrUtil.isNotEmpty(transportNo)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getTransportNo), transportNo));
        }
        // 公司
        Long companyId = transportQueryPageQo.getCompanyId();
        if (nonNull(companyId)) {
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getCompanyId), companyId));
        }
        Integer transportType = transportQueryPageQo.getTransportType();
        if (nonNull(transportType)) {
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getTransportType), transportType));
        }
        // 公司集合
        List<Long> companyIds = transportQueryPageQo.getCompanyIds();
        if (CollectionUtil.isNotEmpty(companyIds)) {
            mainBoolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(TransportIndex::getCompanyId), companyIds));
        }
        //承运商id
        Long carrierId = transportQueryPageQo.getCarrierId();
        if (nonNull(carrierId)){
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getCarrierId), carrierId));
        }
        //搜索类型  1, "全部"  2, "货物名称搜索"  3, "货主名称搜索" 4, "装货地搜索" 5, "收货地搜索" 6, "运输计划编号搜索"
        Integer searchType = transportQueryPageQo.getSearchType();
        TransportSearchTypeEnums transportSearchTypeEnums = EnumUtil.getBy(TransportSearchTypeEnums::getCode, searchType);
        //搜索关键字
        String keyword = transportQueryPageQo.getKeyword();
        if (StrUtil.isNotEmpty(keyword)) {
            //搜索字段名称
            List<String> columnNames = Lists.newArrayList();
            boolean canMatchPhraseQuery = true;
            if (ObjectUtil.isNotNull(transportSearchTypeEnums)) {
                switch (transportSearchTypeEnums) {
                    //查询全部
                    case ALL:
                        allSearch(keyword, mainBoolQueryBuilder);
                        break;
                    //货主名称搜索
                    case HZ:
                        columnNames.add(FieldUtils.val(TransportIndex::getCompanyName));
                        break;
                    //货物名称搜索
                    case GOODS_NAME:
                        columnNames.add("items.itemName");
                        break;
                    //发货地搜索
                    case SEND_ADDR:
                        columnNames.add("sender.address");
                        columnNames.add("sender.fullAddress");
                        break;
                    //收货地搜索
                    case RECEIVE_ADDR:
                        columnNames.add("receiver.address");
                        columnNames.add("receiver.fullAddress");
                        break;
                    //运输计划编号搜索
                    case TRANSPORT_NO:
                        columnNames.add(FieldUtils.val(TransportIndex::getTransportNo));
                        canMatchPhraseQuery = false;
                        break;
                    default:
                        break;
                }
            }
            if (CollectionUtil.isNotEmpty(columnNames)) {
                matchPhraseOrWildcardMustQuery(mainBoolQueryBuilder, keyword, canMatchPhraseQuery, columnNames);
            }
        }

        //运输计划状态：1 待接单,2 已拒绝,3 进行中,4 已完成,5 暂停 6已取消
        Integer status = transportQueryPageQo.getStatus();
        if (nonNull(status)) {
            //状态为7查询的为停运状态为已申请的运输计划
            if (Objects.equals(status, APPLY_END.getCode())) {
                mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getEndStatus), TransportStatusEnums.RUNNING.getCode()));
            }
            //待派车 状态 查询 状态为进行中 剩余量大于0
            if (Objects.equals(status,WAITING_DISPATCH.getCode())){
                mainBoolQueryBuilder.must(QueryBuilders.existsQuery("items.itemWeight"));
                mainBoolQueryBuilder.must(QueryBuilders.existsQuery("items.deliveryWeight"));
                Script script = new Script(ScriptType.INLINE, "painless", "doc['items.itemWeight'].value - doc['items.deliveryWeight'].value > 0", Collections.emptyMap());
                mainBoolQueryBuilder.must(QueryBuilders.scriptQuery(script));
                mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getStatus),TransportStatusEnums.RUNNING.getCode()));
            }
            else {
                mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getStatus), status));
            }
        }
        //状态集合过滤
        List<Integer> statusList = transportQueryPageQo.getStatusList();
        if (CollectionUtil.isNotEmpty(statusList)){
            mainBoolQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(TransportIndex::getStatus), statusList));
        }
        //创建时间比较
        String startTime = transportQueryPageQo.getStartTime();
        String endTime = transportQueryPageQo.getEndTime();
        if (StrUtil.isNotEmpty(startTime)) {
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TransportIndex::getCreateTime))
                    .gte(startTime));
        }
        if (StrUtil.isNotEmpty(endTime)) {
            mainBoolQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(TransportIndex::getCreateTime))
                    .lte(endTime));
        }
        //来源
        Integer sourceType = transportQueryPageQo.getSourceType();
        if (Objects.nonNull(sourceType)) {
            mainBoolQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(TransportIndex::getSourceType), sourceType));
        }
        //查询是否有状态更新的redis缓存 有几个变化 查询就多查几条
        Integer from = transportQueryPageQo.getPage();
        Integer size = transportQueryPageQo.getSize();
        if (CollectionUtil.isNotEmpty(mapCache)){
            size += mapCache.size();
        }
        int fromElasticsearch = (from - 1) * size;
        sourceBuilder.size(size);
        sourceBuilder.from(fromElasticsearch);

        sourceBuilder.query(mainBoolQueryBuilder);
        //默认倒序排序
        SortOrder sortOrder = SortOrder.DESC;
        if (transportQueryPageQo.getIsAsc()){
            sortOrder = SortOrder.ASC;
        }
        sourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(TransportIndex::getCreateTime)).order(sortOrder));
        sourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
        sourceBuilder.trackTotalHits(true);

        return sourceBuilder;
    }

    /**
     * 全部类型的搜索 货主 收发地 运输计划编号 货物名称 都进行模糊匹配
     *
     * @param keyword 搜索关键字
     */
    private void allSearch(String keyword, BoolQueryBuilder mainBoolQueryBuilder) {
        BoolQueryBuilder innerBoolQueryBuilder = QueryBuilders.boolQuery();
        if (keyword.length() > 1){
            innerBoolQueryBuilder.should(getMatchPhraseQuery("items.itemName", keyword));
            innerBoolQueryBuilder.should(getMatchPhraseQuery("companyName", keyword));
            innerBoolQueryBuilder.should(getMatchPhraseQuery("sender.address", keyword));
            innerBoolQueryBuilder.should(getMatchPhraseQuery("receiver.address", keyword));
        }else {
            innerBoolQueryBuilder.should(getWildcardQuery("items.itemName", keyword));
            innerBoolQueryBuilder.should(getWildcardQuery("companyName", keyword));
            innerBoolQueryBuilder.should(getWildcardQuery("sender.address", keyword));
            innerBoolQueryBuilder.should(getWildcardQuery("receiver.address", keyword));
        }
        innerBoolQueryBuilder.should(getWildcardQuery(FieldUtils.val(TransportIndex::getTransportNo), keyword));
        mainBoolQueryBuilder.must(innerBoolQueryBuilder);
    }

    @Override
    public String getSearchType() {
        return searchType;
    }

    @Override
    public String getIndex() {
        return esIndexProperties.getTransportIndex();
    }


}
