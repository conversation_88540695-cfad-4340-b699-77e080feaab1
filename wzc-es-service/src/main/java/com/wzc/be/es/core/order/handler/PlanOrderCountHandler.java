package com.wzc.be.es.core.order.handler;


import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.handler.SearchHandler;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.core.waybill.mapper.SubWaybillListMapper;
import com.wzc.be.es.data.order.vo.PlanOrderCountVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 计划单统计
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PlanOrderCountHandler implements SearchHandler {

    @Value("${planOrderCount.search.type:planOrderCountHandler}")
    private String searchType;

    @Value("${planOrderCount.search.index:sub_waybill_index01}")
    private String index;

    @Resource
    private SubWaybillListMapper subWaybillListMapper;


    @Override
    public RestResponse<PlanOrderCountVO> search(JSONObject json) {

        LambdaEsQueryWrapper<SubWaybillIndex> queryWrapper = new LambdaEsQueryWrapper<>();

        queryWrapper.eq(SubWaybillIndex::getParentNo, json.getStr("parentNo"));
        queryWrapper.index(index);
        List<SubWaybillIndex> waybillList = subWaybillListMapper.selectList(queryWrapper);
        PlanOrderCountVO planOrderCountVO = new PlanOrderCountVO();
        if(CollectionUtils.isNotEmpty(waybillList)){
            Map<Integer, List<SubWaybillIndex>> statusMap = waybillList.stream().filter(s -> ObjectUtil.isNotNull(s.getSubWaybillStatus())).collect(Collectors.groupingBy(SubWaybillIndex::getSubWaybillStatus));
            planOrderCountVO.setDispatchCount(waybillList.size());
            planOrderCountVO.setArrive(statusMap.get(7) == null ? 0: statusMap.get(7).size());
            planOrderCountVO.setSign(statusMap.get(8) == null ? 0: statusMap.get(8).size());
        }

        return RestResponse.success(planOrderCountVO);
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return index;
    }

}
