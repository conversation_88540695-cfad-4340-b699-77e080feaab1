/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.core.order.handler;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.google.common.collect.Lists;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.common.enums.OrderStatusEnums;
import com.wzc.be.es.common.enums.QueryTypeEnums;
import com.wzc.be.es.common.service.EsCommonAuthSearchService;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.core.common.CommonRedisResultVO;
import com.wzc.be.es.data.order.qo.OrderPageQO;
import com.wzc.be.es.data.order.vo.OrderCountVO;
import com.wzc.be.es.data.order.vo.OrderIndexVO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.wzc.be.es.common.constant.Constants.UPDATE_INDEX_REDIS_KEY;

/**
 * <AUTHOR>
 * @date 2023年06月20日
 * 订单状态统计查询
 */
@Slf4j
@Component
public class OrderCountCopySearchHandler extends OrderBaseSearchHandler<OrderPageQO, OrderCountVO> {

    @Value("${order.count.search.type:orderCount}")
    private String searchType;

    @Resource
    private EsIndexProperties esIndexProperties;

    @Resource
    private EsCommonAuthSearchService esCommonAuthSearchService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private OrderSearchHandler orderSearchHandler;

    @Override
    public RestResponse<OrderCountVO> search(OrderPageQO orderPageQo) {
        try {
            JSONObject jsonObject = esCommonAuthSearchService.checkQueryAuth(JSONUtil.parseObj(orderPageQo), QueryTypeEnums.ORDER);
            orderPageQo = JSONUtil.toBean(jsonObject,OrderPageQO.class);
            log.info("订单状态统计搜索:{}", jsonObject.toJSONString(0));
            OrderCountVO orderCountVO = new OrderCountVO();
            //查询缓存里是否有变化的单号
            RMapCache<String, String> mapCache = redissonClient.getMapCache(UPDATE_INDEX_REDIS_KEY + esIndexProperties.getOrderIndex() + ":" + orderPageQo.getCompanyId());
            List<CommonRedisResultVO> commonRedisResultVOList = mapCache.readAllEntrySet().stream().map(e -> JSONUtil.toBean(e.getValue(),CommonRedisResultVO.class)).collect(Collectors.toList());
            // 不符合状态的订单号集合
            List<String> noInOrderNoList;
            orderPageQo.setNeedAuth(false);
            orderPageQo.setSize(1);
            orderPageQo.setStatus(OrderStatusEnums.CREATED.getCode());
            noInOrderNoList = commonRedisResultVOList.stream().filter(c -> ObjectUtil.equals(c.getStatus(),OrderStatusEnums.CREATED.getCode())).map(CommonRedisResultVO::getUnionNo).collect(Collectors.toList());
            orderPageQo.setNoInOrderNoList(noInOrderNoList);
            //待审核数据查询
            RestResponse<EsPageVO<OrderIndexVO>> createdSearch = orderSearchHandler.search(orderPageQo);
            if (createdSearch.getSuccess()){
                Long createTotal = createdSearch.getData().getTotal();
                orderCountVO.setWaitCheckCount(Integer.parseInt(StrUtil.toString(createTotal)));
            }
            //执行中数据统计
            orderPageQo.setStatus(OrderStatusEnums.RUNNING.getCode());
            noInOrderNoList = commonRedisResultVOList.stream().filter(c -> ObjectUtil.equals(c.getStatus(),OrderStatusEnums.RUNNING.getCode())).map(CommonRedisResultVO::getUnionNo).collect(Collectors.toList());
            orderPageQo.setNoInOrderNoList(noInOrderNoList);
            RestResponse<EsPageVO<OrderIndexVO>> runningSearch = orderSearchHandler.search(orderPageQo);
            if (runningSearch.getSuccess()){
                Long runningTotal = runningSearch.getData().getTotal();
                orderCountVO.setExecutionCount(Integer.parseInt(StrUtil.toString(runningTotal)));
            }
            //已暂停数据统计
            orderPageQo.setStatus(OrderStatusEnums.PAUSE.getCode());
            noInOrderNoList = commonRedisResultVOList.stream().filter(c -> ObjectUtil.equals(c.getStatus(),OrderStatusEnums.PAUSE.getCode())).map(CommonRedisResultVO::getUnionNo).collect(Collectors.toList());
            orderPageQo.setNoInOrderNoList(noInOrderNoList);
            RestResponse<EsPageVO<OrderIndexVO>> pauseSearch = orderSearchHandler.search(orderPageQo);
            if (pauseSearch.getSuccess()){
                Long pauseTotal = pauseSearch.getData().getTotal();
                orderCountVO.setStopCount(Integer.parseInt(StrUtil.toString(pauseTotal)));
            }
            //已完成数据统计
            orderPageQo.setStatus(OrderStatusEnums.FINISH.getCode());
            noInOrderNoList = commonRedisResultVOList.stream().filter(c -> ObjectUtil.equals(c.getStatus(),OrderStatusEnums.FINISH.getCode())).map(CommonRedisResultVO::getUnionNo).collect(Collectors.toList());
            orderPageQo.setNoInOrderNoList(noInOrderNoList);
            RestResponse<EsPageVO<OrderIndexVO>> finishSearch = orderSearchHandler.search(orderPageQo);
            if (finishSearch.getSuccess()){
                Long finishTotal = finishSearch.getData().getTotal();
                orderCountVO.setFinishCount(Integer.parseInt(StrUtil.toString(finishTotal)));
            }
            //全部数据统计
            orderPageQo.setStatus(null);
            orderPageQo.setNoInOrderNoList(null);
            RestResponse<EsPageVO<OrderIndexVO>> allSearch = orderSearchHandler.search(orderPageQo);
            if (allSearch.getSuccess()){
                Long allTotal = allSearch.getData().getTotal();
                orderCountVO.setAllCount(Integer.parseInt(StrUtil.toString(allTotal)));
            }
            return RestResponse.success(orderCountVO);
        }catch (Exception e){
            log.info("订单统计异常: ",e);
            return RestResponse.error(ExcepitonEnum.ERROR_UNKNOW);
        }

    }


    @Override
    public String getSearchType() {
        return searchType;
    }

    @Override
    public String getIndex() {
        return esIndexProperties.getOrderIndex();
    }


}
