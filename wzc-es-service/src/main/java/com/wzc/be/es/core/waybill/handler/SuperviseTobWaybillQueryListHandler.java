package com.wzc.be.es.core.waybill.handler;


import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.authentication.datapermission.core.rule.UserPermissionRule;
import com.baidu.mapcloud.cloudnative.common.model.PageOrderType;
import com.baidu.mapcloud.cloudnative.common.model.Pagination;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.enums.*;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.data.waybill.enums.*;
import com.wzc.be.es.data.waybill.enums.CarReportStatusEnum;
import com.wzc.be.es.data.waybill.enums.DriverReportStatusEnum;
import com.wzc.be.es.data.waybill.qo.SuperviseOrderPageQO;
import com.wzc.be.es.data.waybill.vo.SuperviseOrderPageVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.ExistsQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

/**
 * tob监管上报
 *
 */
@Slf4j
@Component
public class SuperviseTobWaybillQueryListHandler implements BaseSearchHandler<SuperviseOrderPageQO,Pagination<SuperviseOrderPageVO>> {

    @Value("${superviseWaybillQueryList.search.type:superviseTobWaybillQueryListHandler}")
    private String searchType;

    @Resource
    private EsIndexProperties esIndexProperties;

    @Autowired
    protected UserPermissionRule userPermissionRule;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Override
    public RestResponse<Pagination<SuperviseOrderPageVO>> search(SuperviseOrderPageQO qo) {
        Pagination<SuperviseOrderPageVO> page = new Pagination<>();
        page.setPage(qo.getPage());
        page.setSize(qo.getSize());
        try {
            // 构建搜索请求
            SearchRequest searchRequest = new SearchRequest(esIndexProperties.getSubWaybillIndex());
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            // 排序方式
            PageOrderType orderType = qo.getOrderType();
            if (orderType != null) {
                if (PageOrderType.ASC.equals(orderType)) {
                    searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(SubWaybillIndex::getCreateTime)).order(SortOrder.ASC));
                } else {
                    searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(SubWaybillIndex::getCreateTime)).order(SortOrder.DESC));
                }
            }

            BoolQueryBuilder mainQueryBuilder = this.getMainBoolQueryBuilder(qo);
            Integer size = qo.getSize();
            Integer from = (qo.getPage() - 1) * size;

            searchSourceBuilder.from(from);
            searchSourceBuilder.size(size);
            searchSourceBuilder.query(mainQueryBuilder);

            searchSourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
            searchSourceBuilder.trackTotalHits(true);
            searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(SubWaybillIndex::getCreateTime)).order(SortOrder.DESC));
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            SearchHit[] hitResult = searchHits.getHits();
            JSONArray array = new JSONArray();
            Stream.of(hitResult).forEach(itemHit -> {
                Map<String, Object> sourceAsMap = itemHit.getSourceAsMap();
                array.add(sourceAsMap);
            });
            TotalHits totalHits = searchHits.getTotalHits();
            List<SubWaybillIndex> list = JSONUtil.toList(array.toJSONString(0), SubWaybillIndex.class);


            if (CollectionUtils.isEmpty(list)) {
                RestResponse.success(page);
            }
            List<SuperviseOrderPageVO> data = new ArrayList<>();
            for (SubWaybillIndex subWaybill : list) {
                SuperviseOrderPageVO superviseOrderPageVO = new SuperviseOrderPageVO();
                superviseOrderPageVO.setDispatchCarNo(subWaybill.getWaybillNo());
                superviseOrderPageVO.setSubWaybillNo(subWaybill.getSubWaybillNo());
                superviseOrderPageVO.setOrderNo(subWaybill.getOrderNo());
                SubWaybillIndex.LocationUpload loadUploadNode = subWaybill.getLoadUploadNode();
                if(!Objects.isNull(loadUploadNode)){
                    superviseOrderPageVO.setLoadUploadStatus(LoadUploadStatusEnum.getEnum(loadUploadNode.getUploadSuccess()));
                }
                SubWaybillIndex.LocationUpload unloadUploadNode = subWaybill.getUnloadUploadNode();
                if(!Objects.isNull(unloadUploadNode)){
                    superviseOrderPageVO.setUnloadUploadStatus(LoadUploadStatusEnum.getEnum(unloadUploadNode.getUploadSuccess()));
                }
                Integer completeStatus = subWaybill.getCompleteStatus();
                if (OrderStatusEnums.PAUSE.getCode().equals(completeStatus)) {
                    superviseOrderPageVO.setPayStatus(PayStatusEnum.ALREADY_PAY);
                }else {
                    superviseOrderPageVO.setPayStatus(PayStatusEnum.NOT_PAY);
                }

                superviseOrderPageVO.setDriverReportStatus(DriverReportStatusEnum.getEnum(subWaybill.getDriverReportStatus()));
                superviseOrderPageVO.setCarReportStatus(CarReportStatusEnum.getEnum(subWaybill.getCarReportStatus()));
            
                superviseOrderPageVO.setDriverName(subWaybill.getDriverName());
                superviseOrderPageVO.setCarNo(subWaybill.getCarNo());
                superviseOrderPageVO.setTrack(SuperviseTrackEnum.TRACK_YES.getCode());
                superviseOrderPageVO.setDriverPhone(subWaybill.getDriverPhone());
                if(CollectionUtils.isNotEmpty(subWaybill.getSender())){
                    superviseOrderPageVO.setFromAddress(subWaybill.getSender().get(0).getAddress());
                }
                if(CollectionUtils.isNotEmpty(subWaybill.getReceiver())){
                    superviseOrderPageVO.setToAddress(subWaybill.getReceiver().get(0).getAddress());
                }
                if(CollectionUtils.isNotEmpty(subWaybill.getItems())){
                    superviseOrderPageVO.setMaterialName(subWaybill.getItems().get(0).getItemName());
                }
                superviseOrderPageVO.setCreateTime(subWaybill.getCreateTime());
                superviseOrderPageVO.setReportWaybillStatus(SuperviseReportStatusEnum.getEnum(subWaybill.getReportWaybillStatus()));
                superviseOrderPageVO.setReportRecordStatus(SuperviseReportStatusEnum.getEnum(subWaybill.getReportRecordStatus()));
                data.add(superviseOrderPageVO);
            }
            page.setTotal(totalHits.value);
            page.setContent(data);
            long totalPage = page.getTotal() / qo.getSize();
            if(page.getTotal() % qo.getSize() != 0){
                totalPage += 1;
            }
            page.setTotalPage((int) totalPage);
            //是否还有下一页 true = 有 false = 无
            page.setHasNext(qo.getPage() + qo.getSize() < page.getTotal());
            return RestResponse.success(page);
        } catch (Exception e) {
            log.error("承运商app查询异常", e);
        }
        return RestResponse.success(page);

    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return esIndexProperties.getSubWaybillIndex();
    }


    public BoolQueryBuilder getMainBoolQueryBuilder(SuperviseOrderPageQO qo) {
        BoolQueryBuilder mainQueryBuilder = QueryBuilders.boolQuery();
        // 子运单号字段必须存在
        ExistsQueryBuilder fieldName = QueryBuilders.existsQuery(FieldUtils.val(SubWaybillIndex::getSubWaybillNo));
        mainQueryBuilder.must(fieldName);
        SuperviseOrderListTabEnum tabType = qo.getTabType();
        if (null != tabType) {
            switch (tabType) {
                case TAB_1:
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDriverReportStatus), DriverReportStatusEnum.HAS_REPORTED.getCode()));
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCarReportStatus), CarReportStatusEnum.REPORT_PASS.getCode()));
                    //sdk上传状态暂时不考虑
//                    mainQueryBuilder.must(QueryBuilders.termQuery("loadUploadNode.uploadSuccess", UploadSuccessEnum.UPLOAD_SUCCESS.getCode()));
//                    mainQueryBuilder.must(QueryBuilders.termQuery("unloadUploadNode.uploadSuccess", UploadSuccessEnum.UPLOAD_SUCCESS.getCode()));
                    mainQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getReportWaybillStatus), SuperviseReportStatusEnum.report_status_1.getCode()));
                    mainQueryBuilder.mustNot(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getReportRecordStatus), SuperviseReportStatusEnum.report_status_1.getCode()));
                    //支付完成状态
                    mainQueryBuilder.must(QueryBuilders.termQuery("completeStatus", 5));
                    break;
                case TAB_2:
                    //支付状态
                    BoolQueryBuilder queryBuilder1 = QueryBuilders.boolQuery();
                    queryBuilder1.mustNot(QueryBuilders.termQuery("completeStatus", 5));
                    //司机上报状态
                    BoolQueryBuilder queryBuilder2 = QueryBuilders.boolQuery();
                    queryBuilder2.mustNot(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getDriverReportStatus), DriverReportStatusEnum.HAS_REPORTED.getCode()));
                    //车辆上报状态
                    BoolQueryBuilder queryBuilder3 = QueryBuilders.boolQuery();
                    queryBuilder3.mustNot(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getCarReportStatus), CarReportStatusEnum.REPORT_PASS.getCode()));
                    //or
                    BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                            .should(queryBuilder1)
                            .should(queryBuilder2)
                            .should(queryBuilder3);
                    mainQueryBuilder.must(queryBuilder);
                    break;
                case TAB_3:
                    // TODO: 2023/11/12 监管上报状态 成功条件
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getReportWaybillStatus), SuperviseReportStatusEnum.report_status_1.getCode()));/*
                    mainQueryBuilder.must(QueryBuilders.termQuery(FieldUtils.val(SubWaybillIndex::getReportRecordStatus), SuperviseReportStatusEnum.report_status_1.getCode()));*/
                    break;
            }
        }
        //转网状态
        mainQueryBuilder.must(QueryBuilders.termQuery("zbStatus", ZbStatusEnums.NOT_ONLINE.getCode()));
        // 时间范围
        if (StringUtils.isNotBlank(qo.getCreateStartTime()) && StringUtils.isNotBlank(qo.getCreateEndTime())) {
            //增加日期范围请求参数
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getCreateTime))
                    .gte(qo.getCreateStartTime()));
            mainQueryBuilder.must(QueryBuilders.rangeQuery(FieldUtils.val(SubWaybillIndex::getCreateTime))
                    .lte(qo.getCreateEndTime()));
        }
        if(StringUtils.isNotBlank(qo.getDispatchCarNo())){
            mainQueryBuilder.must(QueryBuilders.wildcardQuery("waybillNo", wildcardsExp(qo.getDispatchCarNo())));
        }
        if(StringUtils.isNotBlank(qo.getCarNo())){
            mainQueryBuilder.must(QueryBuilders.wildcardQuery("carNo", wildcardsExp(qo.getCarNo())));
        }
        if(StringUtils.isNotBlank(qo.getDriverName())){
            mainQueryBuilder.must(QueryBuilders.wildcardQuery("driverName", wildcardsExp(qo.getDriverName())));
        }
        if(StringUtils.isNotBlank(qo.getOrderNo())){
            mainQueryBuilder.must(QueryBuilders.wildcardQuery("orderNo", wildcardsExp(qo.getOrderNo())));
        }
        return mainQueryBuilder;
    }
}
