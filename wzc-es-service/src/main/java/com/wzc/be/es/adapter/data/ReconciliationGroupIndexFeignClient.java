package com.wzc.be.es.adapter.data;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.data.data.es.reconciliation.model.ReconciliationGroupBusinessEsQueryPageQO;
import com.wzc.be.data.data.es.reconciliation.model.ReconciliationGroupVO;
import com.wzc.be.es.adapter.data.feign.ReconciliationGroupIndexFeign;
import com.wzc.common.core.api.utils.ApiResultUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author：chenxingguang
 * @Date：2024/2/4 14:34
 * @Version：1.0.0
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class ReconciliationGroupIndexFeignClient {

    private final ReconciliationGroupIndexFeign reconciliationGroupIndexFeign;

    public ReconciliationGroupVO search(ReconciliationGroupBusinessEsQueryPageQO businessEsQueryPageQO) {
        RestResponse<ReconciliationGroupVO> search = reconciliationGroupIndexFeign.search(businessEsQueryPageQO);
        ReconciliationGroupVO bmsRcGroupVOS = ApiResultUtils.getDataOrThrow(search);
        return bmsRcGroupVOS;
    }
}
