/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.core.transport.handler;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.adapter.data.DwdTmsTransportIndexClient;
import com.wzc.be.es.common.constant.Constants;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.common.enums.QueryTypeEnums;
import com.wzc.be.es.common.enums.TransportStatusEnums;
import com.wzc.be.es.common.service.EsCommonAuthSearchService;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.core.common.CommonRedisResultVO;
import com.wzc.be.es.core.order.index.OrderIndex;
import com.wzc.be.es.core.transport.index.TransportIndex;
import com.wzc.be.es.data.transport.qo.TransportQueryPageQO;
import com.wzc.be.es.data.transport.vo.TransportEsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.wzc.be.es.common.constant.Constants.INSERT_DELETE_INDEX_REDIS_KEY;
import static com.wzc.be.es.common.constant.Constants.UPDATE_INDEX_REDIS_KEY;
import static com.wzc.be.es.common.enums.TransportStatusEnums.APPLY_END;
import static com.wzc.be.es.common.enums.TransportStatusEnums.WAITING_DISPATCH;

/**
 * <AUTHOR>
 * @date 2023年07月05日
 * 运输计划分页查询
 */
@Slf4j
@Component
public class TransportSearchHandler extends TransportBaseSearchHandler<TransportQueryPageQO, EsPageVO<TransportEsVO>> {

    @Value("${transport.page.search.type:transport}")
    private String searchType;

    @Value("${transport.search.index:transport_index}")
    private String index;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private EsCommonAuthSearchService esCommonAuthSearchService;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private DwdTmsTransportIndexClient dwdTmsTransportIndexClient;

    @Override
    public RestResponse<EsPageVO<TransportEsVO>> search(TransportQueryPageQO transportQueryPageQo) {
        try {
            if (transportQueryPageQo.getNeedAuth()){
                JSONObject jsonObject = esCommonAuthSearchService.checkQueryAuth(JSONUtil.parseObj(transportQueryPageQo), QueryTypeEnums.TRANSPORT);
                transportQueryPageQo = JSONUtil.toBean(jsonObject, TransportQueryPageQO.class);
            }

            List<Integer> statusList = transportQueryPageQo.getStatusList();
            //redis修改记录集合
            List<CommonRedisResultVO> commonRedisUpdateResultVOList;
            Integer status;
            Integer size = transportQueryPageQo.getSize();
            if (CollectionUtil.isNotEmpty(statusList) && statusList.size() == 1){
                //状态不为空 则从redis查询状态符合查询的运输计划编号集合
                RMapCache<String, String> mapCache = redissonClient.getMapCache(UPDATE_INDEX_REDIS_KEY + index + ":" + transportQueryPageQo.getCarrierId());
                commonRedisUpdateResultVOList = mapCache.readAllEntrySet().stream().map(e -> JSONUtil.toBean(e.getValue(),CommonRedisResultVO.class))
                        .collect(Collectors.toList());
                status = statusList.get(0);
                //查询是否有状态更新的redis缓存 有几个变化 查询就多查几条
                size += commonRedisUpdateResultVOList.size();
            } else {
                status = null;
                commonRedisUpdateResultVOList = null;
            }
            //redis删除记录集合
            RMapCache<Object, String> deleteMapCache = redissonClient.getMapCache(INSERT_DELETE_INDEX_REDIS_KEY + index + ":" + transportQueryPageQo.getCarrierId());
            List<CommonRedisResultVO> commonRedisDeleteResultVOList = deleteMapCache.readAllEntrySet().stream().map(e -> JSONUtil.toBean(e.getValue(),CommonRedisResultVO.class)).collect(Collectors.toList());;
            BoolQueryBuilder mainBoolQueryBuilder = getMainBoolQueryBuilder(transportQueryPageQo,commonRedisDeleteResultVOList);

            int fromElasticsearch = (transportQueryPageQo.getPage() == 0?transportQueryPageQo.getPage():transportQueryPageQo.getPage() - 1) * size;

            SearchRequest searchRequest = new SearchRequest(index);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
            sourceBuilder.from(fromElasticsearch);
            sourceBuilder.size(size);
            sourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
            sourceBuilder.trackTotalHits(true);
            String[] fetchSourceFields = {FieldUtils.val(TransportIndex::getTransportNo),FieldUtils.val(TransportIndex::getAcceptTime), FieldUtils.val(TransportIndex::getContractStatus)};
            sourceBuilder.fetchSource(fetchSourceFields,null);
            sourceBuilder.query(mainBoolQueryBuilder);
            //创建时间默认倒序排序
            SortOrder sortOrder = SortOrder.DESC;
            if (transportQueryPageQo.getIsAsc()){
                sortOrder = SortOrder.ASC;
            }
            sourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(TransportIndex::getCreateTime)).order(sortOrder));
            searchRequest.source(sourceBuilder);
            List<TransportEsVO> transportEsVOList = null;
            long count = 0;
            log.info("承运商运输计划pc模糊搜索:{}", sourceBuilder.toString());
            try {
                SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
                SearchHits searchHits = searchResponse.getHits();
                SearchHit[] hitResult = searchHits.getHits();
                JSONArray array = new JSONArray();
                Stream.of(hitResult).forEach(itemHit -> {
                    Map<String, Object> sourceAsMap = itemHit.getSourceAsMap();
                    array.add(sourceAsMap);
                });
                TotalHits totalHits = searchHits.getTotalHits();
                transportEsVOList = JSONUtil.toList(array.toJSONString(0), TransportEsVO.class);
                count = totalHits.value;
            }catch (Exception e){
                log.error("查询运输计划es失败，开始查询doris",e);
                //如果不是 es查询异常 便往外抛
                Throwable throwable = ExceptionUtil.getRootCause(e);
                if(!(throwable instanceof ElasticsearchException)
                        || !((ElasticsearchException) throwable).getDetailedMessage().contains(Constants.ES_RESULT_WINDOW_IS_TOO_LARGE)){
                    throw new Exception(e);
                }
                CountRequest countRequest = new CountRequest(getIndex());
                countRequest.source(sourceBuilder);
                CountResponse countResponse = restHighLevelClient.count(countRequest, RequestOptions.DEFAULT);
                count = countResponse.getCount();
                transportEsVOList = dwdTmsTransportIndexClient.search(transportQueryPageQo,commonRedisDeleteResultVOList);
            }
            if (CollectionUtil.isNotEmpty(commonRedisUpdateResultVOList)){
                //过滤变化前状态的运输计划编号集合 符合则删除
                List<CommonRedisResultVO> removeList = commonRedisUpdateResultVOList.stream().filter(c -> ObjectUtil.equals(c.getStatus(), status)).collect(Collectors.toList());
                transportEsVOList.removeIf(p1 -> removeList.stream().anyMatch(p2 ->
                        p1.getTransportNo().equals(p2.getUnionNo())));
                //待派车对应运输计划状态进行中
                Integer matchAfterStatus;
                if (ObjectUtil.equals(status, WAITING_DISPATCH.getCode())) {
                    matchAfterStatus = TransportStatusEnums.RUNNING.getCode();
                }
                //申请停运状态 对应 暂停状态
                else if (ObjectUtil.equals(status, APPLY_END.getCode())) {
                    matchAfterStatus = TransportStatusEnums.PAUSE.getCode();
                } else {
                    matchAfterStatus = status;
                }
                //是否有符合变化后状态的集合
                List<CommonRedisResultVO> addList = commonRedisUpdateResultVOList.stream().filter(c -> ObjectUtil.equals(c.getAfterStatus(), matchAfterStatus)).collect(Collectors.toList());
                for(CommonRedisResultVO commonRedisResultVO : addList){
                    if (!contains(transportEsVOList,commonRedisResultVO.getUnionNo())){
                        TransportEsVO transportEsVO = new TransportEsVO();
                        transportEsVO.setTransportNo(commonRedisResultVO.getUnionNo());
                        transportEsVOList.add(0,transportEsVO);
                    }
                }
            }
            EsPageVO<TransportEsVO> esPageRes = new EsPageVO<>();
            esPageRes.setTotal(count);
            //截取固定条数集合
            List<TransportEsVO> subList = transportEsVOList.stream().limit(size).collect(Collectors.toList());
            esPageRes.setList(subList);
            return RestResponse.success(esPageRes);
        } catch (Exception e) {
            log.error("运输计划查询异常.search error", e);
        }
        return RestResponse.error(ExcepitonEnum.ERROR_UNKNOW);
    }

    private boolean contains(List<TransportEsVO> list,String value){
        return list.stream().anyMatch(t -> t.getTransportNo().equals(value));
    }

    @Override
    public String getSearchType() {
        return searchType;
    }

    @Override
    public String getIndex() {
        return index;
    }


}
