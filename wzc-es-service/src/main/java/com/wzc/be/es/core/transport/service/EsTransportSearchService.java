/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.core.transport.service;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.common.service.EsBaseSearchService;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.data.transport.qo.TransportQueryPageQO;
import com.wzc.be.es.data.transport.vo.TransportEsVO;
import com.wzc.be.es.data.transport.vo.TransportGroupVO;
import com.wzc.be.es.data.transport.vo.TransportStatusCountVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年10月13日 3:17 PM
 */
@Component
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class EsTransportSearchService extends EsBaseSearchService {

    /**
     * 运输计划分页查询
     * @param waybillQueryPageQo waybillQueryPageQo
     * @return RestResponse
     */
    public RestResponse<EsPageVO<TransportEsVO>> transportSearch(TransportQueryPageQO waybillQueryPageQo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.TRANSPORT_PAGE.getType());
        return searchHandler.search(waybillQueryPageQo);
    }

    /**
     * 运输计划按分组编号分组分页查询
     * @param waybillQueryPageQo waybillQueryPageQo
     * @return RestResponse
     */
    public RestResponse<EsPageVO<TransportGroupVO>> transportGroupSearch(TransportQueryPageQO waybillQueryPageQo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.TRANSPORT_GROUP_PAGE.getType());
        return searchHandler.search(waybillQueryPageQo);
    }

    public RestResponse<EsPageVO<TransportGroupVO>> transportGroupNoSearch(TransportQueryPageQO requestPageQo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.TRANSPORT_GROUP_NO_PAGE.getType());
        return searchHandler.search(requestPageQo);
    }

    /**
     * 运输计划统计
     * @param requestPageQo requestPageQo
     * @return RestResponse
     */
    public RestResponse<TransportStatusCountVO> transportCount(TransportQueryPageQO requestPageQo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.TRANSPORT_COUNT.getType());
        return searchHandler.search(requestPageQo);
    }

    /**
     * 运输计划app分页查询
     * @param waybillQueryPageQo waybillQueryPageQo
     * @return RestResponse<EsPageVO<TransportIndexVO>>
     */
    public RestResponse<EsPageVO<TransportEsVO>> appTransportSearch(TransportQueryPageQO waybillQueryPageQo) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.TRANSPORT_APP_PAGE.getType());
        return searchHandler.search(waybillQueryPageQo);
    }

    public RestResponse<List<TransportEsVO>> transportList(TransportQueryPageQO queryPageQO) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.TRANSPORT_LIST.getType());
        return searchHandler.search(queryPageQO);
    }

    public RestResponse<Boolean> transportUpdate(TransportQueryPageQO queryPageQO) {
        BaseSearchHandler searchHandler = getBaseSearchHandler(SearchTypeEnums.TRANSPORT_UPDATE.getType());
        return searchHandler.search(queryPageQO);
    }
}
