package com.wzc.be.es.core.kafka;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.support.SendResult;
import org.springframework.util.concurrent.ListenableFutureCallback;

@Slf4j
@AllArgsConstructor
public class KafkaPushCallback implements ListenableFutureCallback<SendResult<String, String>> {

    /**
     * 队列主题
     */
    private String topic;

    /**
     * 业务单号
     */
    private String businessNo;

    @Override
    public void onFailure(Throwable throwable) {
        log.error(String.format("kafka发送失败，topic：%s，businessNo：%s", topic, businessNo), throwable);
    }

    @Override
    public void onSuccess(SendResult<String, String> stringStringSendResult) {
        log.info("kafka发送成功，topic：{}，businessNo：{}", topic, businessNo);
    }
}
