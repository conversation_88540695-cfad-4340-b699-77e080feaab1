package com.wzc.be.es.adapter.action.order;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.api.order.EsOrderSearchApi;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.core.order.service.EsOrderService;
import com.wzc.be.es.data.order.qo.OrderPageQO;
import com.wzc.be.es.data.order.vo.OrderCountVO;
import com.wzc.be.es.data.order.vo.OrderIndexVO;
import com.wzc.be.es.data.order.vo.PlanOrderCountVO;
import com.wzc.be.es.data.waybill.qo.RequestPageQO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023年08月11日
 */
@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class OrderAction implements EsOrderSearchApi {

    private final EsOrderService orderService;

    @Override
    public RestResponse<EsPageVO<OrderIndexVO>> orderSearch(OrderPageQO requestPageQo) {
        return orderService.orderSearch(requestPageQo);
    }

    @Override
    public RestResponse<OrderCountVO> orderCount(OrderPageQO requestPageQo) {
        return orderService.orderCount(requestPageQo);
    }

    @Override
    public RestResponse<PlanOrderCountVO> selectPlanOrderCount(RequestPageQO requestPageQo) {
        return orderService.planOrderCountVO(requestPageQo);
    }

    @Override
    public RestResponse<EsPageVO<OrderIndexVO>> orderSzySearch(OrderPageQO requestPageQo) {
        return orderService.orderSzySearch(requestPageQo);
    }
}
