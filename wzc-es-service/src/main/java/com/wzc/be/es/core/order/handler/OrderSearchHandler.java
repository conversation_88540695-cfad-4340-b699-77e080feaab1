/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.core.order.handler;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.google.common.collect.Lists;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.enums.*;
import com.wzc.be.es.adapter.data.DwdOmsOrderIndexClient;
import com.wzc.be.es.common.constant.Constants;
import com.wzc.be.es.common.enums.DeletedEnums;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.common.enums.OrderStatusEnums;
import com.wzc.be.es.common.enums.QueryTypeEnums;
import com.wzc.be.es.common.service.EsCommonAuthSearchService;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.core.common.CommonRedisResultVO;
import com.wzc.be.es.core.order.converter.OrderConverter;
import com.wzc.be.es.core.order.index.OrderIndex;
import com.wzc.be.es.core.transport.index.TransportIndex;
import com.wzc.be.es.data.order.qo.OrderPageQO;
import com.wzc.be.es.data.order.vo.OrderIndexVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.ElasticsearchException;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.wzc.be.es.common.constant.Constants.INSERT_DELETE_INDEX_REDIS_KEY;
import static com.wzc.be.es.common.constant.Constants.UPDATE_INDEX_REDIS_KEY;

/**
 * <AUTHOR>
 * @date 2023年06月13日
 * 订单分页查询
 */
@Slf4j
@Component
public class OrderSearchHandler extends OrderBaseSearchHandler<OrderPageQO, EsPageVO<OrderIndexVO>> {

    @Value("${order.page.search.type:order}")
    private String searchType;

    @Resource
    private EsIndexProperties esIndexProperties;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private EsCommonAuthSearchService esCommonAuthSearchService;

    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Autowired
    private DwdOmsOrderIndexClient dwdOmsOrderIndexClient;

    @Override
    public RestResponse<EsPageVO<OrderIndexVO>> search(OrderPageQO orderPageQo) {
        try {
            if (orderPageQo.getNeedAuth()){
                JSONObject jsonObject = esCommonAuthSearchService.checkQueryAuth(JSONUtil.parseObj(orderPageQo), QueryTypeEnums.ORDER);
                orderPageQo = JSONUtil.toBean(jsonObject,OrderPageQO.class);
            }
            //查询缓存里是否有新增的单号
            RMapCache<String, String> mapCache = redissonClient.getMapCache(INSERT_DELETE_INDEX_REDIS_KEY + esIndexProperties.getOrderIndex() + ":" + orderPageQo.getCompanyId());
            List<CommonRedisResultVO> commonRedisResultVOList = mapCache.readAllEntrySet().stream().map(e -> JSONUtil.toBean(e.getValue(),CommonRedisResultVO.class)).collect(Collectors.toList());
            // 构建搜索请求
            SearchRequest searchRequest = new SearchRequest(esIndexProperties.getOrderIndex());
            BoolQueryBuilder mainQueryBuilder = getMainBoolQueryBuilder(orderPageQo,commonRedisResultVOList);
            //app需要根据类型判断筛选字段
            SearchSourceBuilder searchSourceBuilder = generateSourceBuilder(orderPageQo, mainQueryBuilder);
            Integer size = orderPageQo.getSize();
            //状态请求
            Integer status = orderPageQo.getStatus();
            //redis修改记录集合
            List<CommonRedisResultVO> commonRedisUpdateResultVOList;
            if (ObjectUtil.isNotNull(status)){
                //状态不为空 则从redis查询状态符合查询的运输计划编号集合
                RMapCache<String, String> updateMapCache = redissonClient.getMapCache(UPDATE_INDEX_REDIS_KEY + esIndexProperties.getOrderIndex() + ":" + orderPageQo.getCompanyId());
                commonRedisUpdateResultVOList = updateMapCache.readAllEntrySet().stream().map(e -> JSONUtil.toBean(e.getValue(),CommonRedisResultVO.class))
                        .collect(Collectors.toList());
                //查询是否有状态更新的redis缓存 有几个变化 查询就多查几条
                size += commonRedisUpdateResultVOList.size();
            }else {
                commonRedisUpdateResultVOList = null;
            }
            Integer page = orderPageQo.getPage();
            if (ObjectUtil.isNull(page)){
                page = 1;
            }
            int from = (page == 0 ? page : page - 1) * size;
            searchSourceBuilder.from(from);
            searchSourceBuilder.size(size);
            String[] fetchSourceFields = {FieldUtils.val(OrderIndex::getOrderNo),FieldUtils.val(OrderIndex::getWaybillNoSecond)};
            searchSourceBuilder.fetchSource(fetchSourceFields,null);
            searchSourceBuilder.query(mainQueryBuilder);
            //创建时间默认倒序排序
            SortOrder sortOrder = SortOrder.DESC;
            if (orderPageQo.getIsAsc()){
                sortOrder = SortOrder.ASC;
            }
            searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(OrderIndex::getCreateTime)).order(sortOrder));

            searchSourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
            searchSourceBuilder.trackTotalHits(true);
            searchRequest.source(searchSourceBuilder);
            List<OrderIndex> list = null;
            long count = 0;
            log.info("订单模糊搜索:{}", searchSourceBuilder.toString());
            try {
                SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
                SearchHits searchHits = searchResponse.getHits();
                SearchHit[] hitResult = searchHits.getHits();
                JSONArray array = new JSONArray();
                Stream.of(hitResult).forEach(itemHit -> {
                    Map<String, Object> sourceAsMap = itemHit.getSourceAsMap();
                    array.add(sourceAsMap);
                });
                TotalHits totalHits = searchHits.getTotalHits();
                count = totalHits.value;
                list = JSONUtil.toList(array.toJSONString(0), OrderIndex.class);
            }catch (Exception e){
                log.error("查询订单es失败，开始查询doris",e);
                //如果不是 es查询异常 便往外抛
                Throwable throwable = ExceptionUtil.getRootCause(e);
                if(!(throwable instanceof ElasticsearchException)
                        || !((ElasticsearchException) throwable).getDetailedMessage().contains(Constants.ES_RESULT_WINDOW_IS_TOO_LARGE)){
                    throw new Exception(e);
                }
                CountRequest countRequest = new CountRequest(getIndex());
                countRequest.source(searchSourceBuilder);
                CountResponse countResponse = restHighLevelClient.count(countRequest, RequestOptions.DEFAULT);
                count = countResponse.getCount();
                list = dwdOmsOrderIndexClient.search(orderPageQo,commonRedisResultVOList);
            }
            EsPageVO<OrderIndexVO> esPageRes = new EsPageVO<>();

            if (CollectionUtil.isNotEmpty(commonRedisResultVOList)){
                //状态有值且为待审核 或者状态为空才处理新增的订单数据
                if (ObjectUtil.isNotNull(status) && ObjectUtil.equals(orderPageQo.getStatus(), OrderStatusEnums.CREATED.getCode()) || ObjectUtil.isNull(orderPageQo.getStatus())){
                    List<CommonRedisResultVO> newCommonRedisResultVOList = commonRedisResultVOList.stream().filter(c -> ObjectUtil.equals(c.getDelFlag(), DeletedEnums.YES.getCode()))
                            .sorted(Comparator.comparing(CommonRedisResultVO::getCreateTime))
                            .collect(Collectors.toList());
                    for(CommonRedisResultVO commonRedisResultVO : newCommonRedisResultVOList){
                        if (!contains(list,commonRedisResultVO.getUnionNo())){
                            OrderIndex orderIndex = new OrderIndex();
                            orderIndex.setOrderNo(commonRedisResultVO.getUnionNo());
                            list.add(0,orderIndex);
                        }
                    }
                }
            }
            if (CollectionUtil.isNotEmpty(commonRedisUpdateResultVOList)){
                //过滤变化前状态的订单编号集合 符合则删除
                List<CommonRedisResultVO> removeList = commonRedisUpdateResultVOList.stream().filter(c -> ObjectUtil.equals(c.getStatus(), status)).collect(Collectors.toList());
                list.removeIf(p1 -> removeList.stream().anyMatch(p2 ->
                        p1.getOrderNo().equals(p2.getUnionNo())));
                //是否有符合变化后状态的集合
                List<CommonRedisResultVO> addList = commonRedisUpdateResultVOList.stream().filter(c -> ObjectUtil.equals(c.getAfterStatus(), status)).collect(Collectors.toList());
                for(CommonRedisResultVO commonRedisResultVO : addList){
                    if (!contains(list,commonRedisResultVO.getUnionNo())){
                        OrderIndex orderIndex = new OrderIndex();
                        orderIndex.setOrderNo(commonRedisResultVO.getUnionNo());
                        list.add(0,orderIndex);
                    }
                }
            }
            //截取固定条数集合
            List<OrderIndex> subList = list.stream().limit(size).collect(Collectors.toList());
            esPageRes.setTotal(count);
            esPageRes.setList(OrderConverter.INSTANCE.indexListToVoList(subList));
            return RestResponse.success(esPageRes);
        } catch (Exception e) {
            log.error("订单列表查询异常.search error", e);
        }
        return RestResponse.error(ExcepitonEnum.ERROR_UNKNOW);
    }

    private SearchSourceBuilder generateSourceBuilder(OrderPageQO orderPageQO, BoolQueryBuilder mainQueryBuilder){
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        //搜索类型  1, "全部"  2, "订单号搜索"  3, "第三方订单号搜索" 4, "发货地址搜索" 5, "收货地址搜索"
        Integer searchType = orderPageQO.getSearchType();
        OrderSearchTypeEnums orderSearchTypeEnums = EnumUtil.getBy(OrderSearchTypeEnums::getCode, searchType);
        String keyword = orderPageQO.getKeyword();
        if (StrUtil.isNotEmpty(keyword)) {
            //搜索字段名称
            List<String> columnNames = Lists.newArrayList();
            boolean canMatchPhraseQuery = true;
            if (ObjectUtil.isNotNull(orderSearchTypeEnums)) {
                switch (orderSearchTypeEnums) {
                    //查询全部
                    case ALL:
                        allSearch(keyword, mainQueryBuilder);
                        break;
                    //订单号搜索
                    case ORDER_NO:
                        mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(OrderIndex::getOrderNo), keyword));
                        break;
                    //第三方订单号搜索
                    case THIRD_ORDER_NO:
                        mainQueryBuilder.must(QueryBuilders.termsQuery(FieldUtils.val(OrderIndex::getThirdNo), keyword));
                       break;
                    //发货地搜索
                    case SEND_ADDRESS:
                        columnNames.add("sender.address");
                        columnNames.add("sender.addressName");
                        columnNames.add("sender.fullAddress");
                        break;
                    //收货地搜索
                    case RECEIVE_ADDRESS:
                        columnNames.add("receiver.address");
                        columnNames.add("receiver.addressName");
                        columnNames.add("receiver.fullAddress");
                        break;
                    //货物名称搜索
                    case GOODS_NAME:
                        columnNames.add("items.itemName");
                        break;
                    //合同编号搜索
                    case CONTRACT_NO:
                        columnNames.addAll(StrUtil.split("contractRelation.contractNo", ","));
                        break;
                    default:
                        break;
                }
            }
            if (CollectionUtil.isNotEmpty(columnNames)) {
                matchPhraseOrWildcardMustQuery(mainQueryBuilder, keyword, canMatchPhraseQuery, columnNames);
            }
        }
        return searchSourceBuilder;
    }

    /**
     * 全部类型的搜索 都进行模糊匹配
     *
     * @param keyword 搜索关键字
     */
    private void allSearch(String keyword, BoolQueryBuilder mainBoolQueryBuilder) {
        BoolQueryBuilder innerBoolQueryBuilder = QueryBuilders.boolQuery();
        if (keyword.length() > 1) {
            innerBoolQueryBuilder.should(getMatchPhraseQuery("items.itemName", keyword));
            innerBoolQueryBuilder.should(getMatchPhraseQuery("sender.address", keyword));
            innerBoolQueryBuilder.should(getMatchPhraseQuery("receiver.address", keyword));
        } else {
            innerBoolQueryBuilder.should(getWildcardQuery("items.itemName", keyword));
            innerBoolQueryBuilder.should(getWildcardQuery("sender.address", keyword));
            innerBoolQueryBuilder.should(getWildcardQuery("receiver.address", keyword));
        }
        innerBoolQueryBuilder.should(QueryBuilders.termsQuery(FieldUtils.val(OrderIndex::getOrderNo), keyword));
        innerBoolQueryBuilder.should(QueryBuilders.termsQuery(FieldUtils.val(OrderIndex::getThirdNo), keyword));
        mainBoolQueryBuilder.must(innerBoolQueryBuilder);
    }


    private boolean contains(List<OrderIndex> list,String value){
        return list.stream().anyMatch(o -> o.getOrderNo().equals(value));
    }
    @Override
    public String getSearchType() {
        return searchType;
    }

    @Override
    public String getIndex() {
        return this.esIndexProperties.getOrderIndex();
    }


}
