package com.wzc.be.es.core.auth.converter;

import com.wzc.be.data.data.es.auth.model.AuthRecordESVO;
import com.wzc.be.es.core.auth.index.AuthRecordIndex;
import com.wzc.be.es.data.auth.vo.AuthRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年07月13日 15:05
 */
@Mapper(componentModel = "spring")
public interface AuthConverter {

    AuthConverter INSTANCE = Mappers.getMapper(AuthConverter.class);

    List<AuthRecordVO> indexListToVoList(List<AuthRecordIndex> authRecordIndexList);

    List<AuthRecordIndex> esToDorisQueryPage(List<AuthRecordESVO> authRecordESVOList);

}
