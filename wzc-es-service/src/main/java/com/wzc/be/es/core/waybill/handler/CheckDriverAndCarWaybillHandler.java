package com.wzc.be.es.core.waybill.handler;

import cn.easyes.core.conditions.LambdaEsQueryWrapper;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.core.waybill.mapper.SubWaybillListMapper;
import com.wzc.be.es.data.waybill.qo.CheckDriverAndCarWaybillQO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class CheckDriverAndCarWaybillHandler implements BaseSearchHandler<CheckDriverAndCarWaybillQO, Boolean> {

    @Value("${checkDriverAndCarWaybill.search.type:CheckDriverAndCarWaybillHandler}")
    private String searchType;

    @Value("${checkDriverAndCarWaybill.search.index:sub_waybill_index01}")
    private String index;

    @Resource
    private SubWaybillListMapper subWaybillListMapper;

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.index;
    }


    @Override
    public RestResponse<Boolean> search(CheckDriverAndCarWaybillQO qo) {
        LambdaEsQueryWrapper<SubWaybillIndex> wrapper = new LambdaEsQueryWrapper<>();
        wrapper.index(index);
        genSearch(wrapper,qo);
        List<SubWaybillIndex> selectList = subWaybillListMapper.selectList(wrapper);
        return RestResponse.success(CollectionUtils.isNotEmpty(selectList));
    }


    private void genSearch(LambdaEsQueryWrapper<SubWaybillIndex> wrapper,CheckDriverAndCarWaybillQO qo){
        wrapper.ne(SubWaybillIndex::getWaybillNo,qo.getWaybillNo());
        wrapper.ge(SubWaybillIndex::getCreateTime, qo.getStartTime());
        wrapper.le(SubWaybillIndex::getCreateTime, qo.getEndTime());
        wrapper.eq(SubWaybillIndex::getCarId, qo.getCarId());
        wrapper.eq(SubWaybillIndex::getDriverId, qo.getDriverId());
    }

}
