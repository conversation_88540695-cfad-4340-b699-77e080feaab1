package com.wzc.be.es.core.waybill.converter;

import cn.easyes.core.biz.EsPageInfo;
import cn.hutool.core.util.EnumUtil;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.core.waybill.index.WaybillQueryIndex;
import com.wzc.be.es.data.waybill.enums.AddressTypeEnum;
import com.wzc.be.es.data.waybill.vo.CarrierWaybillListVO;
import com.wzc.be.es.data.waybill.vo.PlanWaybillVO;
import com.wzc.be.es.data.waybill.vo.FbfSignListVO;
import com.wzc.be.es.data.waybill.vo.RelevanceWaybillVO;
import com.wzc.be.es.data.waybill.vo.SubWaybillVO;
import com.wzc.be.es.data.waybill.vo.WaybillQueryVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023年07月13日 15:05
 */
@Mapper(componentModel = "spring")
public interface WaybillConverter {

    WaybillConverter INSTANCE = Mappers.getMapper(WaybillConverter.class);

    WaybillQueryVO indexToVo(WaybillQueryIndex waybillQueryIndex);

    List<WaybillQueryVO> indexListToVoList(List<WaybillQueryIndex> waybillQueryIndexList);

    List<SubWaybillVO> toSubWaybillVOList(List<SubWaybillIndex> list);

    default AddressTypeEnum addressTypeEnumConverter(Integer code) {
        return EnumUtil.getBy(AddressTypeEnum::getCode, code);
    }

    CarrierWaybillListVO toCarrierWaybillListVO(SubWaybillIndex data);

    @Mapping(source = "czStatement",target = "czStatement")
    @Mapping(source = "cdzIncomeLegal",target = "cdzIncomeLegal")
    List<FbfSignListVO> toFbfWaybill(List<SubWaybillIndex> list);

    @Mapping(source = "czStatement",target = "czStatement")
    @Mapping(source = "cdzIncomeLegal",target = "cdzIncomeLegal")
    FbfSignListVO toFbfWaybillVO(SubWaybillIndex subWaybillIndex);

    List<SubWaybillVO> toSubWaybillVO(List<SubWaybillIndex> subWaybillList);

    List<PlanWaybillVO> toPlanWaybillVO(List<SubWaybillIndex> list);

    List<RelevanceWaybillVO> toRelevanceWaybillVO(List<SubWaybillIndex> subWaybillIndices);

    List<WaybillQueryVO> toWaybillQueryVO(List<SubWaybillIndex> listIndex);
}
