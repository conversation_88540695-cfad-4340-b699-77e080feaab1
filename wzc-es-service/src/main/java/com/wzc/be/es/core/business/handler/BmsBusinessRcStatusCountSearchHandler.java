package com.wzc.be.es.core.business.handler;

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.commons.enums.BusinessQueryTabTypeEnums;
import com.wzc.be.es.data.business.qo.BmsEsBusinessQueryPageQO;
import com.wzc.be.es.data.business.vo.BmsEsRcStatusCountVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

import static com.wzc.be.es.commons.enums.SearchTypeEnums.RECONCILIATION_STATUS_COUNT;

@Slf4j
@Component
public class BmsBusinessRcStatusCountSearchHandler extends BmsBusinessBaseSearchHandler<BmsEsBusinessQueryPageQO, BmsEsRcStatusCountVO> {

    @Resource
    private EsIndexProperties esIndexProperties;
    @Autowired
    private RestHighLevelClient restHighLevelClient;

    @Override
    public RestResponse<BmsEsRcStatusCountVO> search(BmsEsBusinessQueryPageQO qo) {
        log.info("对账单对账状态统计参数:{}", JSONUtil.toJsonStr(qo));
        SearchRequest searchRequest = new SearchRequest(esIndexProperties.getBmsBusinessIndex());
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(0);
        searchSourceBuilder.timeout(new TimeValue(60, TimeUnit.SECONDS));
        searchSourceBuilder.trackTotalHits(true);
        BmsEsRcStatusCountVO esRcStatusCountVO = new BmsEsRcStatusCountVO();
        try {
            // 未对账
            qo.setQueryTabType(BusinessQueryTabTypeEnums.UN_RC.getCode());
            BoolQueryBuilder noRcboolQueryBuilder = super.getMainBoolQueryBuilder(qo);
            searchSourceBuilder.query(noRcboolQueryBuilder);
            searchRequest.source(searchSourceBuilder);
            SearchResponse noRcSearchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            TotalHits noRcTotalHits = noRcSearchResponse.getHits().getTotalHits();
            esRcStatusCountVO.setNoRcCount(Convert.toInt(noRcTotalHits.value));
            // 已对账未结算
            qo.setQueryTabType(BusinessQueryTabTypeEnums.RC_UN_SETTLE.getCode());
            BoolQueryBuilder rcNoSettleBoolQueryBuilder = super.getMainBoolQueryBuilder(qo);
            searchSourceBuilder.query(rcNoSettleBoolQueryBuilder);
            searchRequest.source(searchSourceBuilder);
            SearchResponse rcNoSettleSearchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            TotalHits rcNoSettleTotalHits = rcNoSettleSearchResponse.getHits().getTotalHits();
            esRcStatusCountVO.setRcNoSettleCount(Convert.toInt(rcNoSettleTotalHits));
            // 已结算
            qo.setQueryTabType(BusinessQueryTabTypeEnums.SETTLE.getCode());
            BoolQueryBuilder settleBoolQueryBuilder = super.getMainBoolQueryBuilder(qo);
            searchSourceBuilder.query(settleBoolQueryBuilder);
            searchRequest.source(searchSourceBuilder);
            log.info("对账单统计3搜索参数:{}",searchSourceBuilder.toString());
            SearchResponse settleSearchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            TotalHits settleTotalHits = settleSearchResponse.getHits().getTotalHits();
            esRcStatusCountVO.setSettleCount(Convert.toInt(settleTotalHits));
            // 已对账制证中
            qo.setQueryTabType(BusinessQueryTabTypeEnums.RC_UN_SETTLE_AUDIT.getCode());
            BoolQueryBuilder rcNoSettleBoolQueryBuilderGs = super.getMainBoolQueryBuilder(qo);
            searchSourceBuilder.query(rcNoSettleBoolQueryBuilderGs);
            searchRequest.source(searchSourceBuilder);
            SearchResponse rcNoSettleSearchResponseGs = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            TotalHits rcNoSettleTotalHitsGs = rcNoSettleSearchResponseGs.getHits().getTotalHits();
            esRcStatusCountVO.setRcNoSettleGsCount(Convert.toInt(rcNoSettleTotalHitsGs));
            // 异常
            qo.setQueryTabType(BusinessQueryTabTypeEnums.EXCEPTION.getCode());
            BoolQueryBuilder exceptionBoolQueryBuilder = super.getMainBoolQueryBuilder(qo);
            searchSourceBuilder.query(exceptionBoolQueryBuilder);
            searchRequest.source(searchSourceBuilder);
            log.info("对账单统计8搜索参数:{}",searchSourceBuilder.toString());
            SearchResponse exceptionNoSettleSearchResponseGs = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            TotalHits exceptionNoSettleTotalHitsGs = exceptionNoSettleSearchResponseGs.getHits().getTotalHits();
            esRcStatusCountVO.setAbnormalCount(Convert.toInt(exceptionNoSettleTotalHitsGs));
            return RestResponse.success(esRcStatusCountVO);
        } catch (Exception e) {
            log.error("对账单对账状态统计异常: ", e);
            return RestResponse.error(ExcepitonEnum.ERROR_UNKNOW);
        }
    }

    @Override
    public String getSearchType() {
        return RECONCILIATION_STATUS_COUNT.getType();
    }

    @Override
    public String getIndex() {
        return esIndexProperties.getBmsBusinessIndex();
    }
}
