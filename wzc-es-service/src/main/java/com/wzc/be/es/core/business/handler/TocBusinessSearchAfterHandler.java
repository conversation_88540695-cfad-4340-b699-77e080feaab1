/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.core.business.handler;

import cn.easyes.core.toolkit.FieldUtils;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.data.data.es.toc.model.TocRdBusinessPageQO;
import com.wzc.be.data.data.es.toc.model.TocRdBusinessVO;
import com.wzc.be.es.adapter.data.TocBusinessAfterIndexFeignClient;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.constant.Constants;
import com.wzc.be.es.common.enums.ExcepitonEnum;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.commons.enums.SearchTypeEnums;
import com.wzc.be.es.core.business.converter.TocRdConverter;
import com.wzc.be.es.core.business.index.TocBusinessIndex;
import com.wzc.be.es.data.business.qo.TocBusinessEsPageQO;
import com.wzc.be.es.data.business.vo.TocEsBusinessVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023年12月25日
 * 网货对账单分页深度查询 导出用
 */
@Slf4j
@Component
public class TocBusinessSearchAfterHandler extends TocBusinessBaseSearchHandler<TocBusinessEsPageQO, EsPageVO<TocEsBusinessVO>> {

    @Resource
    private EsIndexProperties esIndexProperties;

    @Autowired
    private RestHighLevelClient restHighLevelClient;
    @Autowired
    private TocBusinessAfterIndexFeignClient tocBusinessAfterIndexFeignClient;

    @Override
    public RestResponse<EsPageVO<TocEsBusinessVO>> search(TocBusinessEsPageQO pageQo) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        EsPageVO<TocEsBusinessVO> esPageRes = new EsPageVO<>();
        try {
            log.info("网货对账单深度模糊搜索:{}", JSONUtil.toJsonStr(pageQo));

            // 构建搜索请求
            SearchRequest searchRequest = new SearchRequest(esIndexProperties.getBmsTocBusinessIndex());
            BoolQueryBuilder mainQueryBuilder = getMainBoolQueryBuilder(pageQo,null);

            searchSourceBuilder.size(pageQo.getSize());
            searchSourceBuilder.query(mainQueryBuilder);
            //更新时间默认倒序排序
            SortOrder sortOrder = SortOrder.DESC;
            searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(TocBusinessIndex::getUpdateTime)).order(sortOrder));
            searchSourceBuilder.timeout(new TimeValue(30, TimeUnit.SECONDS));
            searchSourceBuilder.trackTotalHits(true);
            if (ObjectUtil.isNotNull(pageQo.getLastSortValues())){
                searchSourceBuilder.searchAfter(pageQo.getLastSortValues());
            }
            searchRequest.source(searchSourceBuilder);

            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits = searchResponse.getHits();
            SearchHit[] hitResult = searchHits.getHits();
            Object[] lastSortValues = hitResult[hitResult.length - 1].getSortValues();

            JSONArray array = new JSONArray();
            Stream.of(hitResult).forEach(itemHit -> {
                Map<String, Object> sourceAsMap = itemHit.getSourceAsMap();
                array.add(sourceAsMap);
            });
            TotalHits totalHits = searchHits.getTotalHits();

            List<TocEsBusinessVO> list = JSONUtil.toList(array.toJSONString(0),TocEsBusinessVO.class);
            esPageRes.setTotal(totalHits.value);
            esPageRes.setList(list);
            esPageRes.setLastSortValues(lastSortValues);
            return RestResponse.success(esPageRes);
        } catch (Exception e) {
            log.error("网货对账单列表深度查询异常.search error", e);

            try {
                //如果不是 es查询异常 便往外抛
                if (!(e instanceof ElasticsearchStatusException)
                        || !((ElasticsearchStatusException) e).getDetailedMessage().contains(Constants.ES_SEARCH_PHASE_EXECUTION_EXCEPTION)) {
                    throw e;
                }
                //查询es异常获取不到总数，重新查询
                CountRequest countRequest = new CountRequest(getIndex());
                countRequest.source(searchSourceBuilder);
                CountResponse countResponse = null;

                countResponse = restHighLevelClient.count(countRequest, RequestOptions.DEFAULT);
                esPageRes.setTotal(countResponse.getCount());
            } catch (Exception exception) {
                exception.printStackTrace();
                return RestResponse.error(ExcepitonEnum.ERROR_UNKNOW);
            }
            TocRdBusinessPageQO tocRdBusinessPageQO = TocRdConverter.INSTANCE.esToDorisQueryDto(pageQo);
            List<TocRdBusinessVO> tocEsBusinessVOList = tocBusinessAfterIndexFeignClient.search(tocRdBusinessPageQO);
            List<TocEsBusinessVO> list = TocRdConverter.INSTANCE.esToDorisQueryPageDto(tocEsBusinessVOList);
            esPageRes.setList(list);
            return RestResponse.success(esPageRes);
        }
    }

    @Override
    public String getSearchType() {
        return SearchTypeEnums.TOC_BUSINESS_RC_YD_AFTER_PAGE.getType();
    }

    @Override
    public String getIndex() {
        return esIndexProperties.getBmsTocBusinessIndex();
    }


}
