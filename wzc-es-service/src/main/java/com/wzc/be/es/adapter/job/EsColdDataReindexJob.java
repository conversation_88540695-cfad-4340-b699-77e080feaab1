package com.wzc.be.es.adapter.job;


import com.wzc.be.es.adapter.service.ColdDataIndexService;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.properties.ColdDataOfflineProperties;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.admin.indices.create.CreateIndexRequest;
import org.elasticsearch.action.admin.indices.create.CreateIndexResponse;
import org.elasticsearch.action.admin.indices.get.GetIndexRequest;
import org.elasticsearch.action.admin.indices.mapping.get.GetMappingsRequest;
import org.elasticsearch.action.admin.indices.mapping.get.GetMappingsResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.GetIndexResponse;
import org.elasticsearch.cluster.metadata.MappingMetadata;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.ReindexRequest;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * ES冷数据Reindex迁移Job
 * 将冷数据通过Reindex API迁移到专门的冷数据索引，然后删除原索引中的冷数据
 *
 * <AUTHOR> Assistant
 * @since 2025/02/18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EsColdDataReindexJob {

    private final RestHighLevelClient restHighLevelClient;
    private final ColdDataOfflineProperties coldDataProperties;
    private final EsIndexProperties esIndexProperties;
    private final ColdDataIndexService coldDataIndexService;

    /**
     * 冷数据Reindex迁移主任务
     */
    @XxlJob("esColdDataReindex")
    public ReturnT<String> esColdDataReindex() {
        if (!coldDataProperties.getEnabled()) {
            log.info("冷数据迁移功能已禁用，跳过执行");
            return ReturnT.SUCCESS;
        }

        log.info("开始执行ES冷数据Reindex迁移任务");

        // 计算保留时间阈值
        String retentionThreshold = LocalDateTime.now().minusMonths(coldDataProperties.getRetentionMonths())
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        
        // 获取当前年份用于冷数据索引命名
        String currentYear = String.valueOf(LocalDateTime.now().getYear());

        log.info("冷数据迁移时间阈值: {} (保留{}个月)", retentionThreshold, coldDataProperties.getRetentionMonths());

        // 按照指定顺序处理索引
        List<IndexConfig> indexConfigs = Arrays.asList(
            new IndexConfig(esIndexProperties.getOrderIndex(), "createTime", "订单索引"),
            new IndexConfig(esIndexProperties.getTransportIndex(), "createTime", "运输计划索引"),
            new IndexConfig(esIndexProperties.getGroupTransportIndex(), "createTime", "分组运输索引"),
            new IndexConfig(esIndexProperties.getSubWaybillIndex(), "createTime", "子运单索引"),
            new IndexConfig(esIndexProperties.getCscWarnExceptionIndex(), "updateTime", "异常预警索引")
        );

        int totalMigrated = 0;
        int totalDeleted = 0;
        long totalFound = 0;
        int successIndexCount = 0;
        int failedIndexCount = 0;
        List<String> errorMessages = new ArrayList<>();

        for (IndexConfig config : indexConfigs) {
            try {
                log.info("开始处理索引: {} ({})", config.indexName, config.description);
                long startTime = System.currentTimeMillis();

                // 生成冷数据索引名称
                String coldIndexName = coldDataIndexService.generateColdIndexName(config.indexName, currentYear);

                ReindexResult result = processColdDataReindex(config, coldIndexName, retentionThreshold);

                totalMigrated += result.migratedCount;
                totalDeleted += result.deletedCount;
                totalFound += result.totalCount;

                long duration = System.currentTimeMillis() - startTime;

                if (result.hasError()) {
                    failedIndexCount++;
                    errorMessages.add(config.indexName + ": " + result.errorMessage);
                    log.error("索引 {} 迁移失败 - 查询: {}, 迁移: {}, 删除: {}, 成功率: {}%, 耗时: {}ms, 错误: {}",
                        config.indexName, result.totalCount, result.migratedCount, result.deletedCount,
                        String.format("%.1f", result.getSuccessRate()), duration, result.errorMessage);
                } else {
                    successIndexCount++;
                    log.info("索引 {} 迁移完成 - 查询: {}, 迁移: {}, 删除: {}, 成功率: {}%, 耗时: {}ms, 目标索引: {}",
                        config.indexName, result.totalCount, result.migratedCount, result.deletedCount,
                        String.format("%.1f", result.getSuccessRate()), duration, coldIndexName);
                }

                // 索引间间隔，避免对ES造成过大压力
                Thread.sleep(coldDataProperties.getIndexIntervalMs());

            } catch (Exception e) {
                failedIndexCount++;
                errorMessages.add(config.indexName + ": " + e.getMessage());
                log.error("处理索引 {} 时发生异常", config.indexName, e);
            }
        }

        // 输出最终统计
        log.info("=== ES冷数据Reindex迁移任务完成 ===");
        log.info("索引统计: 成功 {}, 失败 {}, 总计 {}", successIndexCount, failedIndexCount, indexConfigs.size());
        log.info("数据统计: 查询 {}, 迁移 {}, 删除 {}, 整体成功率: {}%",
            totalFound, totalMigrated, totalDeleted,
            String.format("%.1f", totalFound > 0 ? (totalMigrated * 100.0 / totalFound) : 0.0));

        if (!errorMessages.isEmpty()) {
            log.warn("处理过程中的错误信息:");
            errorMessages.forEach(msg -> log.warn("  - {}", msg));
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 处理单个索引的冷数据Reindex迁移
     */
    private ReindexResult processColdDataReindex(IndexConfig config, String coldIndexName, String retentionThreshold) {
        try {
            // 1. 先查询总数
            long totalCount = queryTotalCount(config, retentionThreshold);
            if (totalCount == 0) {
                log.info("索引 {} 没有符合条件的冷数据", config.indexName);
                return new ReindexResult(0, 0, totalCount);
            }

            log.info("索引 {} 查询到 {} 条符合条件的冷数据，开始迁移", config.indexName, totalCount);

            // 2. 创建或验证冷数据索引
            ensureColdIndexExists(config.indexName, coldIndexName);

            // 3. 执行Reindex迁移
            int migratedCount = executeReindex(config, coldIndexName, retentionThreshold);

            // 4. 验证迁移结果
            long migratedTotal = queryTotalCountInColdIndex(coldIndexName, retentionThreshold);
            if (migratedTotal != migratedCount) {
                log.warn("迁移数据验证不一致: 预期 {}, 实际 {}", migratedCount, migratedTotal);
            }

            // 5. 删除原索引中的冷数据
            int deletedCount = 0;
            if (migratedCount > 0) {
                deletedCount = deleteOriginalColdData(config, retentionThreshold);
            }

            // 6. 优化冷数据索引设置
            if (migratedCount > 0 && coldDataProperties.getAutoOptimizeColdIndex()) {
                coldDataIndexService.optimizeColdIndexSettings(coldIndexName);
            }

            // 7. 创建冷数据别名
            if (migratedCount > 0 && coldDataProperties.getAutoCreateColdAlias()) {
                coldDataIndexService.createColdDataAlias(config.indexName);
            }

            return new ReindexResult(migratedCount, deletedCount, totalCount);

        } catch (Exception e) {
            log.error("处理索引 {} 的冷数据迁移失败", config.indexName, e);
            return new ReindexResult(0, 0, 0, e.getMessage());
        }
    }



    /**
     * 查询符合条件的冷数据总数
     */
    private long queryTotalCount(IndexConfig config, String retentionThreshold) throws Exception {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(0);
        searchSourceBuilder.trackTotalHits(true);
        searchSourceBuilder.timeout(new TimeValue(coldDataProperties.getQueryTimeoutSeconds(), TimeUnit.SECONDS));

        BoolQueryBuilder queryBuilder = buildQueryBuilder(config, retentionThreshold);
        searchSourceBuilder.query(queryBuilder);

        SearchRequest searchRequest = new SearchRequest(config.indexName);
        searchRequest.source(searchSourceBuilder);

        try {
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            long totalHits = searchResponse.getHits().getTotalHits().value;
            log.info("索引 {} 符合条件的冷数据总数: {}", config.indexName, totalHits);
            return totalHits;
        } catch (Exception e) {
            log.error("查询索引 {} 的冷数据总数失败", config.indexName, e);
            throw e;
        }
    }

    /**
     * 查询冷数据索引中的数据总数
     */
    private long queryTotalCountInColdIndex(String coldIndexName, String retentionThreshold) throws Exception {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.size(0);
        searchSourceBuilder.trackTotalHits(true);

        SearchRequest searchRequest = new SearchRequest(coldIndexName);
        searchRequest.source(searchSourceBuilder);

        try {
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            return searchResponse.getHits().getTotalHits().value;
        } catch (Exception e) {
            log.warn("查询冷数据索引 {} 总数失败", coldIndexName, e);
            return 0;
        }
    }

    /**
     * 构建查询条件
     */
    private BoolQueryBuilder buildQueryBuilder(IndexConfig config, String retentionThreshold) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        queryBuilder.must(QueryBuilders.rangeQuery(config.timeField).lt(retentionThreshold));

        // 为订单索引添加业务状态检查
        if (esIndexProperties.getOrderIndex().equals(config.indexName)) {
            addOrderBusinessStatusFilter(queryBuilder);
        }

        // 为子运单索引添加业务状态检查
        if (esIndexProperties.getSubWaybillIndex().equals(config.indexName)) {
            addSubWaybillBusinessStatusFilter(queryBuilder);
        }

        return queryBuilder;
    }

    /**
     * 为订单索引添加业务状态过滤条件
     * 订单必须已经完成才能被迁移到冷数据索引
     */
    private void addOrderBusinessStatusFilter(BoolQueryBuilder queryBuilder) {
        // 订单状态：已完成 (6表示已完成状态)
        queryBuilder.must(QueryBuilders.termQuery("status", coldDataProperties.getOrderCompletedStatus()));

        log.debug("为订单索引添加业务状态过滤：订单状态=已完成({})",
            coldDataProperties.getOrderCompletedStatus());
    }

    /**
     * 为子运单索引添加业务状态过滤条件
     * 子运单必须已经完成、已签收、且结算完成才能被迁移到冷数据索引
     */
    private void addSubWaybillBusinessStatusFilter(BoolQueryBuilder queryBuilder) {
        // 1. 子运单状态：已完成 (6表示已完成状态)
        queryBuilder.must(QueryBuilders.termQuery("subWaybillStatus", coldDataProperties.getSubWaybillCompletedStatus()));

        // 2. 必须已签收：货主签收时间不为空
        queryBuilder.must(QueryBuilders.existsQuery("ownerSignTime"));

        Integer settlementCompletedStatus = coldDataProperties.getSubWaybillSettlementCompletedStatus();

        // 3. 对上结算状态：要么无需结算，要么已支付 (5表示已支付)
        BoolQueryBuilder upSettlementQuery = QueryBuilders.boolQuery();
        upSettlementQuery.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("completeStatus")));
        upSettlementQuery.should(QueryBuilders.termQuery("completeStatus", 0)); // 无需结算
        upSettlementQuery.should(QueryBuilders.termQuery("completeStatus", settlementCompletedStatus)); // 已支付

        // 4. 对下结算状态：要么无需结算，要么已支付 (5表示已支付)
        BoolQueryBuilder downSettlementQuery = QueryBuilders.boolQuery();
        downSettlementQuery.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("completeDownStatus")));
        downSettlementQuery.should(QueryBuilders.termQuery("completeDownStatus", 0)); // 无需结算
        downSettlementQuery.should(QueryBuilders.termQuery("completeDownStatus", settlementCompletedStatus)); // 已支付

        queryBuilder.must(upSettlementQuery);
        queryBuilder.must(downSettlementQuery);

        log.debug("为子运单索引添加业务状态过滤：子运单状态=已完成({}), 已签收, 对上结算状态=已支付({}), 对下结算状态=已支付({})",
            coldDataProperties.getSubWaybillCompletedStatus(), settlementCompletedStatus, settlementCompletedStatus);
    }

    /**
     * 索引配置类
     */
    private static class IndexConfig {
        final String indexName;
        final String timeField;
        final String description;

        IndexConfig(String indexName, String timeField, String description) {
            this.indexName = indexName;
            this.timeField = timeField;
            this.description = description;
        }
    }

    /**
     * 确保冷数据索引存在，如果不存在则自动创建
     */
    private void ensureColdIndexExists(String originalIndexName, String coldIndexName) throws Exception {
        // 检查冷数据索引是否已存在
        GetIndexRequest getIndexRequest = new GetIndexRequest(coldIndexName);
        boolean exists = restHighLevelClient.indices().exists(getIndexRequest, RequestOptions.DEFAULT);

        if (exists) {
            log.info("冷数据索引 {} 已存在，跳过创建", coldIndexName);
            return;
        }

        log.info("开始创建冷数据索引: {}", coldIndexName);

        // 获取原索引的mapping和settings
        GetMappingsRequest getMappingsRequest = new GetMappingsRequest();
        getMappingsRequest.indices(originalIndexName);
        GetMappingsResponse getMappingsResponse = restHighLevelClient.indices().getMapping(getMappingsRequest, RequestOptions.DEFAULT);

        GetIndexRequest getOriginalIndexRequest = new GetIndexRequest(originalIndexName);
        GetIndexResponse getIndexResponse = restHighLevelClient.indices().get(getOriginalIndexRequest, RequestOptions.DEFAULT);

        // 创建冷数据索引
        CreateIndexRequest createIndexRequest = new CreateIndexRequest(coldIndexName);

        // 复制原索引的mapping
        MappingMetadata originalMapping = getMappingsResponse.mappings().get(originalIndexName);
        if (originalMapping != null) {
            createIndexRequest.mapping(originalMapping.source());
            log.debug("复制原索引 {} 的mapping到冷数据索引 {}", originalIndexName, coldIndexName);
        }

        // 设置冷数据索引的settings（优化存储）
        Settings.Builder settingsBuilder = Settings.builder()
            .put("index.number_of_shards", 1) // 冷数据使用较少分片
            .put("index.number_of_replicas", 0) // 冷数据可以不设置副本
            .put("index.refresh_interval", "30s") // 降低刷新频率
            .put("index.merge.policy.max_merged_segment", "5gb") // 优化合并策略
            .put("index.codec", "best_compression"); // 使用最佳压缩

        // 复制部分原索引settings（如分析器等）
        Settings originalSettings = getIndexResponse.getSettings().get(originalIndexName);
        if (originalSettings != null) {
            // 复制分析器相关设置
            if (originalSettings.hasValue("index.analysis")) {
                settingsBuilder.put("index.analysis", originalSettings.get("index.analysis"));
            }
        }

        createIndexRequest.settings(settingsBuilder);

        CreateIndexResponse createIndexResponse = restHighLevelClient.indices().create(createIndexRequest, RequestOptions.DEFAULT);

        if (createIndexResponse.isAcknowledged()) {
            log.info("成功创建冷数据索引: {}", coldIndexName);
        } else {
            throw new RuntimeException("创建冷数据索引失败: " + coldIndexName);
        }
    }

    /**
     * 执行Reindex迁移
     */
    private int executeReindex(IndexConfig config, String coldIndexName, String retentionThreshold) throws Exception {
        log.info("开始执行Reindex迁移: {} -> {}", config.indexName, coldIndexName);

        ReindexRequest reindexRequest = new ReindexRequest();
        reindexRequest.setSourceIndices(config.indexName);
        reindexRequest.setDestIndex(coldIndexName);

        // 设置查询条件
        BoolQueryBuilder queryBuilder = buildQueryBuilder(config, retentionThreshold);
        reindexRequest.setSourceQuery(queryBuilder);

        // 设置批量大小和超时
        reindexRequest.setSourceBatchSize(coldDataProperties.getPageSize());
        reindexRequest.setTimeout(TimeValue.timeValueMinutes(30));
        reindexRequest.setRefresh(true);

        try {
            BulkByScrollResponse bulkResponse = restHighLevelClient.reindex(reindexRequest, RequestOptions.DEFAULT);

            long created = bulkResponse.getCreated();
            long updated = bulkResponse.getUpdated();
            long total = created + updated;

            if (bulkResponse.getTimedOut()) {
                log.warn("Reindex操作超时，但已处理 {} 条数据", total);
            }

            if (!bulkResponse.getBulkFailures().isEmpty()) {
                log.warn("Reindex过程中有 {} 个失败项", bulkResponse.getBulkFailures().size());
                bulkResponse.getBulkFailures().forEach(failure ->
                    log.warn("Reindex失败项: {}", failure.getMessage()));
            }

            log.info("Reindex迁移完成: {} -> {}, 迁移数据: {} 条, 耗时: {}ms",
                config.indexName, coldIndexName, total, bulkResponse.getTook().millis());

            return (int) total;

        } catch (Exception e) {
            log.error("执行Reindex迁移失败: {} -> {}", config.indexName, coldIndexName, e);
            throw e;
        }
    }

    /**
     * 删除原索引中的冷数据
     */
    private int deleteOriginalColdData(IndexConfig config, String retentionThreshold) throws Exception {
        log.info("开始删除原索引 {} 中的冷数据", config.indexName);

        // 使用deleteByQuery API删除数据
        org.elasticsearch.index.reindex.DeleteByQueryRequest deleteRequest =
            new org.elasticsearch.index.reindex.DeleteByQueryRequest(config.indexName);

        BoolQueryBuilder queryBuilder = buildQueryBuilder(config, retentionThreshold);
        deleteRequest.setQuery(queryBuilder);
        deleteRequest.setBatchSize(coldDataProperties.getDeleteBatchSize());
        deleteRequest.setTimeout(TimeValue.timeValueMinutes(10));
        deleteRequest.setRefresh(true);

        try {
            BulkByScrollResponse deleteResponse = restHighLevelClient.deleteByQuery(deleteRequest, RequestOptions.DEFAULT);

            long deleted = deleteResponse.getDeleted();

            if (deleteResponse.getTimedOut()) {
                log.warn("删除操作超时，但已删除 {} 条数据", deleted);
            }

            if (!deleteResponse.getBulkFailures().isEmpty()) {
                log.warn("删除过程中有 {} 个失败项", deleteResponse.getBulkFailures().size());
            }

            log.info("删除原索引冷数据完成: {}, 删除数据: {} 条, 耗时: {}ms",
                config.indexName, deleted, deleteResponse.getTook().millis());

            return (int) deleted;

        } catch (Exception e) {
            log.error("删除原索引 {} 冷数据失败", config.indexName, e);
            throw e;
        }
    }

    /**
     * Reindex结果类
     */
    private static class ReindexResult {
        final int migratedCount;
        final int deletedCount;
        final long totalCount;
        final String errorMessage;

        ReindexResult(int migratedCount, int deletedCount, long totalCount) {
            this.migratedCount = migratedCount;
            this.deletedCount = deletedCount;
            this.totalCount = totalCount;
            this.errorMessage = null;
        }

        ReindexResult(int migratedCount, int deletedCount, long totalCount, String errorMessage) {
            this.migratedCount = migratedCount;
            this.deletedCount = deletedCount;
            this.totalCount = totalCount;
            this.errorMessage = errorMessage;
        }

        public boolean hasError() {
            return errorMessage != null;
        }

        public double getSuccessRate() {
            return totalCount > 0 ? (migratedCount * 100.0 / totalCount) : 0.0;
        }
    }
}
