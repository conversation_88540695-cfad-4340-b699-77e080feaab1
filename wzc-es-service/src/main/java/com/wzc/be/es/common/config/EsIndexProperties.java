package com.wzc.be.es.common.config;


import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Getter
@Setter
public class EsIndexProperties {

    @Value("${authRecord.search.index:md_audit_index}")
    private String mdAuditIndex;

    @Value("${bms.business.search.index:bms_business_index}")
    private String bmsBusinessIndex;

    @Value("${bms.toc.business.search.index:bms_toc_business_index}")
    private String bmsTocBusinessIndex;

    @Value("${order.search.index:order_index}")
    private String orderIndex;

    @Value("${csc.warn.exception.search.index:csc_warn_exception_index}")
    private String cscWarnExceptionIndex;

    @Value("${waybill.search.index:sub_waybill_index01}")
    private String subWaybillIndex;

    @Value("${transport.group.no.search.index:group_transport_index}")
    private String groupTransportIndex;

    @Value("${transport.search.index:transport_index}")
    private String transportIndex;

    @Value("${es.data.to.doris.pageSize:5000}")
    private Integer pageSize;
}
