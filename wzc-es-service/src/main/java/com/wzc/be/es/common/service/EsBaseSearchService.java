/**
 * Copyright (c) 2020 Baidu.com, Inc. All Rights Reserved
 **/
package com.wzc.be.es.common.service;

import com.wzc.be.es.common.handler.BaseSearchHandler;
import com.wzc.be.es.common.handler.SearchHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022年10月13日 3:17 PM
 */
@Component
@Slf4j
public class EsBaseSearchService {

    @Autowired
    private  List<SearchHandler> searchHandlerList;

    @Autowired
    private List<BaseSearchHandler> baseSearchHandlerList;

    /**
     * 获取搜索实现类
     * @param searchType searchType
     * @return SearchHandler
     */
    public SearchHandler getSearchHandler(String searchType){
        return searchHandlerList.stream().filter(item -> StringUtils.isNotBlank(item.getSearchType()) && item.getSearchType().equals(searchType)).findFirst().orElse(null);
    }

    /**
     * 获取基础搜索实现类
     * @param searchType searchType
     * @return SearchHandler
     */
    public BaseSearchHandler getBaseSearchHandler(String searchType){
        return baseSearchHandlerList.stream().filter(item -> StringUtils.isNotBlank(item.getSearchType()) && item.getSearchType().equals(searchType)).findFirst().orElse(null);
    }
}
