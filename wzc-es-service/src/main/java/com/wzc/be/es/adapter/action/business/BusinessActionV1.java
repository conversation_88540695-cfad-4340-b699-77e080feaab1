//package com.wzc.be.es.adapter.action.business;
//
//import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
//import com.wzc.be.es.api.business.EsBusinessSearchApi;
//import com.wzc.be.es.commons.EsPageVO;
//import com.wzc.be.es.core.business.service.EsBusinessSearchService;
//import com.wzc.be.es.data.business.qo.BusinessEsQueryPageQO;
//import com.wzc.be.es.data.business.vo.*;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @date 2023年10月13日
// */
//@RestController
//@RequiredArgsConstructor(onConstructor = @__(@Autowired))
//@Slf4j
//public class BusinessAction implements EsBusinessSearchApi {
//
//    private final EsBusinessSearchService businessSearchService;
//
//    @Override
//    public RestResponse<EsPageVO<BusinessEsVO>> reconciliationPage(BusinessEsQueryPageQO requestPageQo) {
//        return businessSearchService.reconciliationPage(requestPageQo);
//    }
//
//    @Override
//    public RestResponse<EsPageVO<BmsRcGroupVO>> reconciliationGroupPage(BusinessEsQueryPageQO requestPageQo) {
//        return businessSearchService.reconciliationGroupPage(requestPageQo);
//    }
//
//    @Override
//    public RestResponse<List<BusinessEsVO>> reconciliationList(BusinessEsQueryPageQO requestPageQo) {
//        return businessSearchService.reconciliationList(requestPageQo);
//    }
//
//    @Override
//    public RestResponse<BmsEsRcStatusCountVO> rcStatusCount(BusinessEsQueryPageQO requestPageQo) {
//        return businessSearchService.rcStatusCount(requestPageQo);
//    }
//
//    @Override
//    public RestResponse<EsReconciliationStatusCountVO> rcStatusTransportCount(BusinessEsQueryPageQO requestPageQo) {
//        return businessSearchService.rcStatusTransportCount(requestPageQo);
//    }
//
//    @Override
//    public RestResponse<BmsEsAuditStatusCountVO> auditStatusCount(BusinessEsQueryPageQO requestPageQo) {
//        return businessSearchService.rcAuditStatusCount(requestPageQo);
//    }
//
//    @Override
//    public RestResponse<BmsEsAuditStatusCountVO> rcAuditStatusTransportCount(BusinessEsQueryPageQO requestPageQo) {
//        return businessSearchService.rcAuditStatusTransportCount(requestPageQo);
//    }
//}
