package com.wzc.be.es.core.kafka;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.wzc.be.common.kafka.service.KafkaService;
import com.wzc.be.es.adapter.job.bo.EsToKafkaBO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class KafkaProducter {

    private final KafkaService kafkaService;

    /**
     * 将es的数据同步到kafka
     * @param esToKafkaBO
     */
    public void sendEsDataToKafka(EsToKafkaBO esToKafkaBO) {
        log.info("开始发送es数据到kafka，topic为{}",esToKafkaBO.getTopic());
        String jsonStr = esToKafkaBO.getJsonStr();
        JSONArray jsonArray = JSON.parseArray(jsonStr);
        jsonArray.forEach(i-> kafkaService.send(esToKafkaBO.getTopic(), JSONUtil.toJsonStr(i)));
    }

}