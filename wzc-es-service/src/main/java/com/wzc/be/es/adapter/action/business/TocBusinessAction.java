package com.wzc.be.es.adapter.action.business;

import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.api.business.EsTocBusinessSearchApi;
import com.wzc.be.es.commons.EsPageVO;
import com.wzc.be.es.core.business.service.EsTocBusinessSearchService;
import com.wzc.be.es.data.business.qo.TocBusinessEsPageQO;
import com.wzc.be.es.data.business.vo.TocEsBusinessVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@Slf4j
public class TocBusinessAction implements EsTocBusinessSearchApi {

    private final EsTocBusinessSearchService tocBusinessSearchService;

    @Override
    public RestResponse<EsPageVO<TocEsBusinessVO>> tocReconciliationPage(TocBusinessEsPageQO requestPageQo) {
        return tocBusinessSearchService.tocReconciliationPage(requestPageQo);
    }
}
