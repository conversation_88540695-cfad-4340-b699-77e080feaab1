package com.wzc.be.es.core.transport.index;

import cn.easyes.annotation.IndexField;
import cn.easyes.annotation.rely.FieldType;
import com.wzc.be.es.common.index.ContractRelation;
import com.wzc.be.es.common.index.Items;
import com.wzc.be.es.common.index.Shipping;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023-06-13
 */
@Data
@ToString
public class TransportIndex {

    private String id;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "第三方订单号")
    private String thirdNo;

    @Schema(description = "运输计划编号")
    private String transportNo;

    @Schema(description = "共享运输计划分组编号")
    private String groupNo;

    private String baseNo;

    @Schema(description = "运输计划状态")
    private Integer status;


    @Schema(description = "订单类型")
    private Integer orderType;

    @Schema(description = "停运申请状态")
    private Integer endStatus;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "公司id")
    private Long companyId;

    @Schema(description = "线路id")
    private String trackRouteId;

    @Schema(description = "创建人id")
    private Long createId;

    @Schema(description = "托运人公司ID")
    private Long shipperCompanyId;

    @Schema(description = "托运人公司名称")
    private String shipperCompanyName;

    @Schema(description = "承运商id")
    private Long carrierId;

    @Schema(description = "承运商名称")
    private String carrierName;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String createTime;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String startTime;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String endTime;

    @Schema(description = "来源")
    private Integer sourceType;

    @Schema(description = "公司名称")
    private String companyName;

    @Schema(description = "运输方式")
    private Integer transportType;

    @Schema(description = "部门id")
    private Long ownerDepartment;

    @Schema(description = "地址名称")
    private String addressName;

    @Schema(description = "转网货标识 1网货订单 2非网货订单")
    private Integer netCargo;

    @Schema(description = "指派方式 1、共享余量 2、固定量")
    private Integer assignType;

    @Schema(description = "是否同步二期数据 (1是  2否)")
    private Integer isSync;

    @Schema(description = "是否开启校验载重设备功能  1是，2否，默认否")
    private Integer loadDeviceCheck;

    private List<Items> items;

    private List<Shipping> sender;

    private List<Shipping> receiver;

    @Schema(description = "货源单号")
    private String goodsNo;

    @Schema(description = "合同签署要求（0-无需签署 1-需要签署 2-强制签署）")
    private Integer contractRequire;

    @Schema(description = "合同平台合同审核状态：（-1=草稿,0=审核中,1=审核通过,2=审核不通过,10=已归档,20=变更中,30-已盖章,31:赛马物联已盖章,32:用户已盖章,101-线下签署中），聚合层维护合同审核状态：（3=待生成）")
    private Integer contractStatus;

    @Schema(description = "合同信息")
    private List<ContractRelation> contractRelation;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "接单时间")
    private String acceptTime;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "接单时间")
    private String transAcceptTime;

    @IndexField(fieldType = FieldType.DATE, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String netCargoCreateTime;


    @Schema(description = "业务类型0-默认 1-总包 2-自营 3-平台")
    private Integer businessType;

}
