package com.wzc.be.es.core.waybill.handler;

import cn.easyes.core.toolkit.FieldUtils;
import com.baidu.mapcloud.cloudnative.common.model.PageOrderType;
import com.baidu.mapcloud.cloudnative.common.model.RestResponse;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.core.waybill.index.SubWaybillIndex;
import com.wzc.be.es.data.waybill.enums.ExceptionTypeEnum;
import com.wzc.be.es.data.waybill.qo.ExceptionWaybillListQO;
import com.wzc.be.es.data.waybill.vo.ExceptionCountVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.search.TotalHits;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.pipeline.BucketSortPipelineAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * 异常运单相关
 *
 * <AUTHOR> Wei
 */
@Slf4j
@Component
public class ExceptionTabCountHandler extends ExceptionBaseHandler<ExceptionWaybillListQO, ExceptionCountVO> {

    @Value("${exceptionTabCount.search.type:exceptionTabCountHandler}")
    private String searchType;

    @Resource
    private EsIndexProperties esIndexProperties;

    @Resource
    private RestHighLevelClient restHighLevelClient;

    @Override
    public RestResponse<ExceptionCountVO> search(ExceptionWaybillListQO qo) {
        ExceptionCountVO countVO = new ExceptionCountVO();
        // 构建搜索请求
        SearchRequest searchRequest = new SearchRequest(esIndexProperties.getSubWaybillIndex());
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.trackTotalHits(true);
        try {
            // 承运商
            if (qo.getCarrierCompanyId() != null) {
                TermsAggregationBuilder aggregation = AggregationBuilders
                        .terms("group_by_waybillNo")
                        .field(FieldUtils.val(SubWaybillIndex::getWaybillNo)).size(Integer.MAX_VALUE);
                // 排序
                PageOrderType orderType = qo.getOrderType();
                if (orderType != null) {
                    switch (orderType) {
                        case ASC:
                            aggregation.subAggregation(new BucketSortPipelineAggregationBuilder("bucket_sort", Collections.singletonList(new FieldSortBuilder("max_created_at").order(SortOrder.ASC))).from(0).size(1));
                            break;
                        case DESC:
                            aggregation.subAggregation(new BucketSortPipelineAggregationBuilder("bucket_sort", Collections.singletonList(new FieldSortBuilder("max_created_at").order(SortOrder.DESC))).from(0).size(1));
                            break;
                    }
                }
                searchSourceBuilder.aggregation(aggregation);
            }

            if (qo.getShipperCompanyId() != null) {
                searchSourceBuilder.from(0);
                searchSourceBuilder.size(1);
                // 排序
                PageOrderType orderType = qo.getOrderType();
                if (orderType != null) {
                    switch (orderType) {
                        case ASC:
                            searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(SubWaybillIndex::getCreateTime)).order(SortOrder.ASC));
                            break;
                        case DESC:
                            searchSourceBuilder.sort(SortBuilders.fieldSort(FieldUtils.val(SubWaybillIndex::getCreateTime)).order(SortOrder.DESC));
                            break;
                    }
                }
            }


            // 全部
            BoolQueryBuilder mainQueryBuilder0 = getMainBoolQueryBuilder(qo);
            searchSourceBuilder.query(mainQueryBuilder0);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse0 = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits0 = searchResponse0.getHits();
            TotalHits status0 = searchHits0.getTotalHits();
            countVO.setAllCount(status0.value);

            // 签收
            qo.setExceptionType(ExceptionTypeEnum.SIGN_EXCEPTION);
            BoolQueryBuilder mainQueryBuilder1 = getMainBoolQueryBuilder(qo);
            searchSourceBuilder.query(mainQueryBuilder1);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse1 = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits1 = searchResponse1.getHits();
            TotalHits status1 = searchHits1.getTotalHits();
            countVO.setSignExceptionCount(status1.value);

            // 在途
            qo.setExceptionType(ExceptionTypeEnum.TRANSPORT_EXCEPTION);
            BoolQueryBuilder mainQueryBuilder2 = getMainBoolQueryBuilder(qo);
            searchSourceBuilder.query(mainQueryBuilder2);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse2 = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits2 = searchResponse2.getHits();
            TotalHits status2 = searchHits2.getTotalHits();
            countVO.setTransportExceptionCount(status2.value);

            // 监管
            qo.setExceptionType(ExceptionTypeEnum.SUPERVISE_EXCEPTION);
            BoolQueryBuilder mainQueryBuilder3 = getMainBoolQueryBuilder(qo);
            searchSourceBuilder.query(mainQueryBuilder3);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse3 = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits3 = searchResponse3.getHits();
            TotalHits status3 = searchHits3.getTotalHits();
            countVO.setSuperviseExceptionCount(status3.value);

            // 司机异常上报
            qo.setExceptionType(ExceptionTypeEnum.REPORT_EXCEPTION);
            BoolQueryBuilder mainQueryBuilder4 = getMainBoolQueryBuilder(qo);
            searchSourceBuilder.query(mainQueryBuilder4);
            searchRequest.source(searchSourceBuilder);
            SearchResponse searchResponse4 = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            SearchHits searchHits4 = searchResponse4.getHits();
            TotalHits status4 = searchHits4.getTotalHits();
            countVO.setReportExceptionCount(status4.value);
        } catch (Exception e) {
            log.error("异常运单相查询异常", e);
        }
        return RestResponse.success(countVO);
    }

    @Override
    public String getSearchType() {
        return this.searchType;
    }

    @Override
    public String getIndex() {
        return this.esIndexProperties.getSubWaybillIndex();
    }
}
