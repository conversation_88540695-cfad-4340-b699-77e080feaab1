package com.wzc.be.es.adapter.job;

import cn.hutool.json.JSONUtil;
import com.wzc.be.es.adapter.job.util.ColdDataMockUtil;
import com.wzc.be.es.adapter.job.util.ColdDataValidationUtil;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 冷数据Mock测试类
 * 验证各个索引的Mock数据格式和完整性
 *
 * <AUTHOR> Assistant
 * @since 2025/02/18
 */
class ColdDataMockTest {

    @Test
    @DisplayName("测试订单索引Mock数据")
    void testOrderIndexMockData() {
        Map<String, Object> orderData = ColdDataMockUtil.mockOrderIndexData();
        
        // 验证ES元数据
        assertEquals("order_20230101_123456", orderData.get("_id"));
        assertEquals("order_index01", orderData.get("_index"));
        assertEquals("_doc", orderData.get("_type"));
        assertEquals(1.0, orderData.get("_score"));
        
        // 验证下线元信息
        assertEquals("order_index01", orderData.get("indexName"));
        assertEquals("订单索引", orderData.get("indexDescription"));
        assertEquals("OFFLINE", orderData.get("operation"));
        assertNotNull(orderData.get("offlineTime"));
        assertNotNull(orderData.get("offlineTimestamp"));
        assertEquals("12个月", orderData.get("retentionThreshold"));
        
        // 验证业务关键字段
        assertEquals(123456L, orderData.get("orderId"));
        assertEquals("ORD20230101001", orderData.get("orderNo"));
        assertEquals(6, orderData.get("status")); // 已完成
        assertEquals(5, orderData.get("settlementStatus")); // 已支付
        assertNotNull(orderData.get("createTime"));
        
        // 验证复杂字段
        assertNotNull(orderData.get("items"));
        assertNotNull(orderData.get("sender"));
        assertNotNull(orderData.get("receiver"));
        
        // 验证数据完整性
        List<Map<String, Object>> dataList = List.of(orderData);
        ColdDataValidationUtil.ValidationResult result = 
            ColdDataValidationUtil.validateColdData(dataList, "order_index01");
        
        assertFalse(result.hasErrors(), "订单索引Mock数据验证应该通过");
        assertEquals(1, result.getTotalCount());
        assertEquals(1, result.getValidCount());
        
        // 打印JSON格式（用于文档）
        System.out.println("=== 订单索引Mock数据 ===");
        System.out.println(JSONUtil.toJsonPrettyStr(List.of(orderData)));
    }

    @Test
    @DisplayName("测试子运单索引Mock数据")
    void testSubWaybillIndexMockData() {
        Map<String, Object> subWaybillData = ColdDataMockUtil.mockSubWaybillIndexData();
        
        // 验证ES元数据
        assertEquals("sub_waybill_20230101_567890", subWaybillData.get("_id"));
        assertEquals("sub_waybill_index01", subWaybillData.get("_index"));
        
        // 验证业务关键字段
        assertEquals("567890", subWaybillData.get("id"));
        assertEquals(123456L, subWaybillData.get("orderId"));
        assertEquals("SUB20230101001", subWaybillData.get("subWaybillNo"));
        assertEquals(6, subWaybillData.get("subWaybillStatus")); // 已完成
        assertEquals(5, subWaybillData.get("completeStatus")); // 已支付
        assertEquals(5, subWaybillData.get("completeDownStatus")); // 已支付
        assertNotNull(subWaybillData.get("ownerSignTime")); // 已签收
        
        // 验证数据完整性
        List<Map<String, Object>> dataList = List.of(subWaybillData);
        ColdDataValidationUtil.ValidationResult result = 
            ColdDataValidationUtil.validateColdData(dataList, "sub_waybill_index01");
        
        assertFalse(result.hasErrors(), "子运单索引Mock数据验证应该通过");
        
        // 打印JSON格式
        System.out.println("=== 子运单索引Mock数据 ===");
        System.out.println(JSONUtil.toJsonPrettyStr(List.of(subWaybillData)));
    }

    @Test
    @DisplayName("测试运输计划索引Mock数据")
    void testTransportIndexMockData() {
        Map<String, Object> transportData = ColdDataMockUtil.mockTransportIndexData();
        
        // 验证ES元数据
        assertEquals("transport_20230101_789012", transportData.get("_id"));
        assertEquals("transport_index01", transportData.get("_index"));
        
        // 验证业务关键字段
        assertEquals(789012L, transportData.get("transportId"));
        assertEquals("TP20230101001", transportData.get("transportNo"));
        assertEquals(6, transportData.get("transportStatus"));
        assertNotNull(transportData.get("createTime"));
        
        // 打印JSON格式
        System.out.println("=== 运输计划索引Mock数据 ===");
        System.out.println(JSONUtil.toJsonPrettyStr(List.of(transportData)));
    }

    @Test
    @DisplayName("测试异常预警索引Mock数据")
    void testWarnExceptionIndexMockData() {
        Map<String, Object> warnData = ColdDataMockUtil.mockWarnExceptionIndexData();
        
        // 验证ES元数据
        assertEquals("warn_exception_20230101_901234", warnData.get("_id"));
        assertEquals("csc_warn_exception_index", warnData.get("_index"));
        
        // 验证业务关键字段
        assertEquals("901234", warnData.get("id"));
        assertEquals("WARN20230101001", warnData.get("warnId"));
        assertEquals(1, warnData.get("warnType"));
        assertEquals(2, warnData.get("warnLevel"));
        assertNotNull(warnData.get("createTime"));
        assertNotNull(warnData.get("updateTime"));
        
        // 打印JSON格式
        System.out.println("=== 异常预警索引Mock数据 ===");
        System.out.println(JSONUtil.toJsonPrettyStr(List.of(warnData)));
    }

    @Test
    @DisplayName("测试所有索引Mock数据")
    void testAllIndexMockData() {
        List<Map<String, Object>> allData = ColdDataMockUtil.mockAllIndexData();
        
        assertEquals(4, allData.size(), "应该包含4个索引的Mock数据");
        
        // 验证每个数据都包含必要的元数据
        for (Map<String, Object> data : allData) {
            assertNotNull(data.get("_id"), "每条数据都应该有_id");
            assertNotNull(data.get("_index"), "每条数据都应该有_index");
            assertEquals("OFFLINE", data.get("operation"), "每条数据都应该标记为OFFLINE");
            assertNotNull(data.get("offlineTime"), "每条数据都应该有下线时间");
        }
        
        // 打印所有数据的统计信息
        System.out.println("=== 所有索引Mock数据统计 ===");
        for (Map<String, Object> data : allData) {
            String indexName = (String) data.get("indexName");
            String description = (String) data.get("indexDescription");
            int fieldCount = data.size();
            System.out.printf("索引: %s (%s) - 字段数: %d%n", indexName, description, fieldCount);
        }
    }

    @Test
    @DisplayName("测试字段数量统计")
    void testFieldCountStatistics() {
        Map<String, Object> orderData = ColdDataMockUtil.mockOrderIndexData();
        Map<String, Object> subWaybillData = ColdDataMockUtil.mockSubWaybillIndexData();
        Map<String, Object> transportData = ColdDataMockUtil.mockTransportIndexData();
        Map<String, Object> warnData = ColdDataMockUtil.mockWarnExceptionIndexData();
        
        System.out.println("=== 各索引字段数量统计 ===");
        System.out.printf("订单索引字段数: %d%n", orderData.size());
        System.out.printf("子运单索引字段数: %d%n", subWaybillData.size());
        System.out.printf("运输计划索引字段数: %d%n", transportData.size());
        System.out.printf("异常预警索引字段数: %d%n", warnData.size());
        
        // 验证字段数量合理性
        assertTrue(orderData.size() > 30, "订单索引应该有足够多的字段");
        assertTrue(subWaybillData.size() > 40, "子运单索引应该有足够多的字段");
        assertTrue(transportData.size() > 25, "运输计划索引应该有足够多的字段");
        assertTrue(warnData.size() > 30, "异常预警索引应该有足够多的字段");
    }

    @Test
    @DisplayName("测试JSON序列化和反序列化")
    void testJsonSerialization() {
        List<Map<String, Object>> allData = ColdDataMockUtil.mockAllIndexData();
        
        // 测试序列化
        String jsonStr = JSONUtil.toJsonStr(allData);
        assertNotNull(jsonStr);
        assertFalse(jsonStr.isEmpty());
        
        // 测试反序列化
        List<?> deserializedData = JSONUtil.toList(jsonStr, Map.class);
        assertEquals(allData.size(), deserializedData.size());
        
        System.out.println("=== JSON序列化测试通过 ===");
        System.out.printf("序列化后JSON长度: %d 字符%n", jsonStr.length());
    }
}
