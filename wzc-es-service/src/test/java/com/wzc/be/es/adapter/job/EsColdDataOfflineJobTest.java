package com.wzc.be.es.adapter.job;

import com.wzc.be.es.adapter.job.constants.BusinessStatusConstants;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * ES冷数据下线Job测试类
 *
 * <AUTHOR> Assistant
 * @since 2025/02/18
 */
class EsColdDataOfflineJobTest {

    @Test
    @DisplayName("测试订单下线条件 - 已完成且无结算")
    void testCanOfflineOrder_CompletedWithoutSettlement() {
        assertTrue(BusinessStatusConstants.canOfflineOrder(
            BusinessStatusConstants.OrderStatus.COMPLETED, 
            BusinessStatusConstants.SettlementStatus.NO_SETTLEMENT
        ));
        
        assertTrue(BusinessStatusConstants.canOfflineOrder(
            BusinessStatusConstants.OrderStatus.COMPLETED, 
            null
        ));
    }

    @Test
    @DisplayName("测试订单下线条件 - 已完成且结算已支付")
    void testCanOfflineOrder_CompletedWithPaidSettlement() {
        assertTrue(BusinessStatusConstants.canOfflineOrder(
            BusinessStatusConstants.OrderStatus.COMPLETED, 
            BusinessStatusConstants.SettlementStatus.PAID
        ));
    }

    @Test
    @DisplayName("测试订单下线条件 - 未完成")
    void testCanOfflineOrder_NotCompleted() {
        assertFalse(BusinessStatusConstants.canOfflineOrder(
            BusinessStatusConstants.OrderStatus.IN_TRANSIT, 
            BusinessStatusConstants.SettlementStatus.PAID
        ));
        
        assertFalse(BusinessStatusConstants.canOfflineOrder(
            BusinessStatusConstants.OrderStatus.PENDING_DISPATCH, 
            null
        ));
    }

    @Test
    @DisplayName("测试订单下线条件 - 已完成但结算未支付")
    void testCanOfflineOrder_CompletedButSettlementNotPaid() {
        assertFalse(BusinessStatusConstants.canOfflineOrder(
            BusinessStatusConstants.OrderStatus.COMPLETED, 
            BusinessStatusConstants.SettlementStatus.PENDING_SETTLEMENT
        ));
        
        assertFalse(BusinessStatusConstants.canOfflineOrder(
            BusinessStatusConstants.OrderStatus.COMPLETED, 
            BusinessStatusConstants.SettlementStatus.SETTLED
        ));
    }

    @Test
    @DisplayName("测试子运单下线条件 - 已完成已签收且结算已支付")
    void testCanOfflineSubWaybill_CompletedSignedAndPaid() {
        assertTrue(BusinessStatusConstants.canOfflineSubWaybill(
            BusinessStatusConstants.SubWaybillStatus.COMPLETED,
            "2024-01-01 10:00:00",
            BusinessStatusConstants.SettlementStatus.PAID,
            BusinessStatusConstants.SettlementStatus.PAID
        ));
    }

    @Test
    @DisplayName("测试子运单下线条件 - 已完成已签收但无结算")
    void testCanOfflineSubWaybill_CompletedSignedWithoutSettlement() {
        assertTrue(BusinessStatusConstants.canOfflineSubWaybill(
            BusinessStatusConstants.SubWaybillStatus.COMPLETED,
            "2024-01-01 10:00:00",
            BusinessStatusConstants.SettlementStatus.NO_SETTLEMENT,
            null
        ));
    }

    @Test
    @DisplayName("测试子运单下线条件 - 未完成")
    void testCanOfflineSubWaybill_NotCompleted() {
        assertFalse(BusinessStatusConstants.canOfflineSubWaybill(
            BusinessStatusConstants.SubWaybillStatus.IN_TRANSIT,
            "2024-01-01 10:00:00",
            BusinessStatusConstants.SettlementStatus.PAID,
            BusinessStatusConstants.SettlementStatus.PAID
        ));
    }

    @Test
    @DisplayName("测试子运单下线条件 - 已完成但未签收")
    void testCanOfflineSubWaybill_CompletedButNotSigned() {
        assertFalse(BusinessStatusConstants.canOfflineSubWaybill(
            BusinessStatusConstants.SubWaybillStatus.COMPLETED,
            null,
            BusinessStatusConstants.SettlementStatus.PAID,
            BusinessStatusConstants.SettlementStatus.PAID
        ));
        
        assertFalse(BusinessStatusConstants.canOfflineSubWaybill(
            BusinessStatusConstants.SubWaybillStatus.COMPLETED,
            "",
            BusinessStatusConstants.SettlementStatus.PAID,
            BusinessStatusConstants.SettlementStatus.PAID
        ));
    }

    @Test
    @DisplayName("测试子运单下线条件 - 已完成已签收但结算未支付")
    void testCanOfflineSubWaybill_CompletedSignedButSettlementNotPaid() {
        // 对上结算未支付
        assertFalse(BusinessStatusConstants.canOfflineSubWaybill(
            BusinessStatusConstants.SubWaybillStatus.COMPLETED,
            "2024-01-01 10:00:00",
            BusinessStatusConstants.SettlementStatus.PENDING_SETTLEMENT,
            BusinessStatusConstants.SettlementStatus.PAID
        ));
        
        // 对下结算未支付
        assertFalse(BusinessStatusConstants.canOfflineSubWaybill(
            BusinessStatusConstants.SubWaybillStatus.COMPLETED,
            "2024-01-01 10:00:00",
            BusinessStatusConstants.SettlementStatus.PAID,
            BusinessStatusConstants.SettlementStatus.SETTLED
        ));
    }
}
