package com.wzc.be.es.adapter.job;

import com.wzc.be.es.adapter.service.ColdDataIndexService;
import com.wzc.be.es.common.config.EsIndexProperties;
import com.wzc.be.es.common.properties.ColdDataOfflineProperties;
import org.elasticsearch.client.RestHighLevelClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * ES冷数据Reindex迁移Job测试类
 *
 * <AUTHOR> Assistant
 * @since 2025/02/18
 */
@ExtendWith(MockitoExtension.class)
class EsColdDataReindexJobTest {

    @Mock
    private RestHighLevelClient restHighLevelClient;

    @Mock
    private ColdDataOfflineProperties coldDataProperties;

    @Mock
    private EsIndexProperties esIndexProperties;

    @Mock
    private ColdDataIndexService coldDataIndexService;

    private EsColdDataReindexJob reindexJob;

    @BeforeEach
    void setUp() {
        reindexJob = new EsColdDataReindexJob(
            restHighLevelClient,
            coldDataProperties,
            esIndexProperties,
            coldDataIndexService
        );
    }

    @Test
    @DisplayName("测试冷数据索引名称生成")
    void testColdIndexNameGeneration() {
        // 模拟服务调用
        when(coldDataIndexService.generateColdIndexName("order_index01", "2025"))
            .thenReturn("order_index01_cold_2025");

        String coldIndexName = coldDataIndexService.generateColdIndexName("order_index01", "2025");
        
        assertEquals("order_index01_cold_2025", coldIndexName);
        verify(coldDataIndexService).generateColdIndexName("order_index01", "2025");
    }

    @Test
    @DisplayName("测试功能禁用时的行为")
    void testDisabledFunctionality() {
        // 模拟功能禁用
        when(coldDataProperties.getEnabled()).thenReturn(false);

        var result = reindexJob.esColdDataReindex();

        assertEquals("SUCCESS", result.getMsg());
        verify(coldDataProperties).getEnabled();
        // 验证没有进行其他操作
        verifyNoInteractions(restHighLevelClient);
    }

    @Test
    @DisplayName("测试配置参数获取")
    void testConfigurationParameters() {
        // 模拟配置参数
        when(coldDataProperties.getEnabled()).thenReturn(true);
        when(coldDataProperties.getRetentionMonths()).thenReturn(12);
        when(coldDataProperties.getIndexIntervalMs()).thenReturn(5000L);
        when(coldDataProperties.getAutoOptimizeColdIndex()).thenReturn(true);
        when(coldDataProperties.getAutoCreateColdAlias()).thenReturn(true);

        // 验证配置获取
        assertTrue(coldDataProperties.getEnabled());
        assertEquals(12, coldDataProperties.getRetentionMonths());
        assertEquals(5000L, coldDataProperties.getIndexIntervalMs());
        assertTrue(coldDataProperties.getAutoOptimizeColdIndex());
        assertTrue(coldDataProperties.getAutoCreateColdAlias());
    }

    @Test
    @DisplayName("测试索引属性获取")
    void testIndexProperties() {
        // 模拟索引配置
        when(esIndexProperties.getOrderIndex()).thenReturn("order_index01");
        when(esIndexProperties.getSubWaybillIndex()).thenReturn("sub_waybill_index01");
        when(esIndexProperties.getTransportIndex()).thenReturn("transport_index01");
        when(esIndexProperties.getGroupTransportIndex()).thenReturn("group_transport_index");
        when(esIndexProperties.getCscWarnExceptionIndex()).thenReturn("csc_warn_exception_index");

        // 验证索引名称获取
        assertEquals("order_index01", esIndexProperties.getOrderIndex());
        assertEquals("sub_waybill_index01", esIndexProperties.getSubWaybillIndex());
        assertEquals("transport_index01", esIndexProperties.getTransportIndex());
        assertEquals("group_transport_index", esIndexProperties.getGroupTransportIndex());
        assertEquals("csc_warn_exception_index", esIndexProperties.getCscWarnExceptionIndex());
    }

    @Test
    @DisplayName("测试冷数据索引服务集成")
    void testColdDataIndexServiceIntegration() {
        String originalIndex = "order_index01";
        String coldIndex = "order_index01_cold_2025";

        // 模拟索引服务方法
        when(coldDataIndexService.generateColdIndexName(originalIndex, "2025"))
            .thenReturn(coldIndex);
        when(coldDataIndexService.indexExists(coldIndex)).thenReturn(false);
        when(coldDataIndexService.getIndexDocumentCount(coldIndex)).thenReturn(1000L);

        // 测试索引名称生成
        String generatedName = coldDataIndexService.generateColdIndexName(originalIndex, "2025");
        assertEquals(coldIndex, generatedName);

        // 测试索引存在性检查
        boolean exists = coldDataIndexService.indexExists(coldIndex);
        assertFalse(exists);

        // 测试文档数量获取
        long docCount = coldDataIndexService.getIndexDocumentCount(coldIndex);
        assertEquals(1000L, docCount);

        // 验证方法调用
        verify(coldDataIndexService).generateColdIndexName(originalIndex, "2025");
        verify(coldDataIndexService).indexExists(coldIndex);
        verify(coldDataIndexService).getIndexDocumentCount(coldIndex);
    }

    @Test
    @DisplayName("测试业务状态配置")
    void testBusinessStatusConfiguration() {
        // 模拟业务状态配置
        when(coldDataProperties.getOrderCompletedStatus()).thenReturn(6);
        when(coldDataProperties.getOrderSettlementCompletedStatus()).thenReturn(5);
        when(coldDataProperties.getSubWaybillCompletedStatus()).thenReturn(6);
        when(coldDataProperties.getSubWaybillSettlementCompletedStatus()).thenReturn(5);

        // 验证业务状态配置
        assertEquals(6, coldDataProperties.getOrderCompletedStatus());
        assertEquals(5, coldDataProperties.getOrderSettlementCompletedStatus());
        assertEquals(6, coldDataProperties.getSubWaybillCompletedStatus());
        assertEquals(5, coldDataProperties.getSubWaybillSettlementCompletedStatus());
    }

    @Test
    @DisplayName("测试Reindex配置参数")
    void testReindexConfiguration() {
        // 模拟Reindex配置
        when(coldDataProperties.getReindexMode()).thenReturn(true);
        when(coldDataProperties.getReindexBatchSize()).thenReturn(1000);
        when(coldDataProperties.getReindexTimeoutMinutes()).thenReturn(30);
        when(coldDataProperties.getColdIndexShards()).thenReturn(1);
        when(coldDataProperties.getColdIndexReplicas()).thenReturn(0);
        when(coldDataProperties.getColdIndexRetentionYears()).thenReturn(5);

        // 验证Reindex配置
        assertTrue(coldDataProperties.getReindexMode());
        assertEquals(1000, coldDataProperties.getReindexBatchSize());
        assertEquals(30, coldDataProperties.getReindexTimeoutMinutes());
        assertEquals(1, coldDataProperties.getColdIndexShards());
        assertEquals(0, coldDataProperties.getColdIndexReplicas());
        assertEquals(5, coldDataProperties.getColdIndexRetentionYears());
    }

    @Test
    @DisplayName("测试冷数据统计信息")
    void testColdDataStatistics() {
        String originalIndex = "order_index01";
        
        // 创建模拟统计信息
        ColdDataIndexService.ColdIndexStats stats = new ColdDataIndexService.ColdIndexStats();
        stats.originalIndexName = originalIndex;
        stats.totalIndexes = 3;
        stats.totalDocuments = 15000L;
        stats.coldIndexes = new java.util.ArrayList<>();

        // 添加冷数据索引信息
        ColdDataIndexService.ColdIndexInfo info1 = new ColdDataIndexService.ColdIndexInfo();
        info1.indexName = "order_index01_cold_2023";
        info1.year = 2023;
        info1.documentCount = 5000L;
        info1.exists = true;
        stats.coldIndexes.add(info1);

        ColdDataIndexService.ColdIndexInfo info2 = new ColdDataIndexService.ColdIndexInfo();
        info2.indexName = "order_index01_cold_2024";
        info2.year = 2024;
        info2.documentCount = 10000L;
        info2.exists = true;
        stats.coldIndexes.add(info2);

        // 模拟服务调用
        when(coldDataIndexService.getColdIndexStats(originalIndex)).thenReturn(stats);

        // 测试统计信息获取
        ColdDataIndexService.ColdIndexStats result = coldDataIndexService.getColdIndexStats(originalIndex);
        
        assertNotNull(result);
        assertEquals(originalIndex, result.originalIndexName);
        assertEquals(3, result.totalIndexes);
        assertEquals(15000L, result.totalDocuments);
        assertEquals(2, result.coldIndexes.size());
        
        // 验证第一个冷数据索引信息
        ColdDataIndexService.ColdIndexInfo firstIndex = result.coldIndexes.get(0);
        assertEquals("order_index01_cold_2023", firstIndex.indexName);
        assertEquals(2023, firstIndex.year);
        assertEquals(5000L, firstIndex.documentCount);
        assertTrue(firstIndex.exists);

        verify(coldDataIndexService).getColdIndexStats(originalIndex);
    }

    @Test
    @DisplayName("测试索引优化和别名创建")
    void testIndexOptimizationAndAliasCreation() {
        String coldIndex = "order_index01_cold_2025";
        String originalIndex = "order_index01";

        // 模拟优化和别名创建
        when(coldDataIndexService.optimizeColdIndexSettings(coldIndex)).thenReturn(true);
        when(coldDataIndexService.createColdDataAlias(originalIndex)).thenReturn(true);

        // 测试索引优化
        boolean optimized = coldDataIndexService.optimizeColdIndexSettings(coldIndex);
        assertTrue(optimized);

        // 测试别名创建
        boolean aliasCreated = coldDataIndexService.createColdDataAlias(originalIndex);
        assertTrue(aliasCreated);

        // 验证方法调用
        verify(coldDataIndexService).optimizeColdIndexSettings(coldIndex);
        verify(coldDataIndexService).createColdDataAlias(originalIndex);
    }

    @Test
    @DisplayName("测试过期索引清理")
    void testExpiredIndexCleanup() {
        String originalIndex = "order_index01";
        int retentionYears = 5;
        int deletedCount = 2;

        // 模拟过期索引清理
        when(coldDataIndexService.deleteExpiredColdIndexes(originalIndex, retentionYears))
            .thenReturn(deletedCount);

        // 测试过期索引清理
        int result = coldDataIndexService.deleteExpiredColdIndexes(originalIndex, retentionYears);
        assertEquals(deletedCount, result);

        verify(coldDataIndexService).deleteExpiredColdIndexes(originalIndex, retentionYears);
    }
}
