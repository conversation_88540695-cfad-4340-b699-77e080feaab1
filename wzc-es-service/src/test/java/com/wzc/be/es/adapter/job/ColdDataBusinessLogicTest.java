package com.wzc.be.es.adapter.job;

import com.wzc.be.es.adapter.job.constants.ColdDataBusinessStatus;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 冷数据业务逻辑测试
 * 验证各种业务状态下的数据迁移规则
 *
 * <AUTHOR> Assistant
 * @since 2025/02/18
 */
class ColdDataBusinessLogicTest {

    @Test
    @DisplayName("测试订单迁移条件 - 已完成订单")
    void testOrderMigration_Completed() {
        // 已完成的订单可以迁移
        assertTrue(ColdDataBusinessStatus.canMigrateOrder(6));
    }

    @Test
    @DisplayName("测试订单迁移条件 - 未完成订单")
    void testOrderMigration_NotCompleted() {
        // 草稿状态不能迁移
        assertFalse(ColdDataBusinessStatus.canMigrateOrder(1));
        
        // 待派车状态不能迁移
        assertFalse(ColdDataBusinessStatus.canMigrateOrder(2));
        
        // 运输中状态不能迁移
        assertFalse(ColdDataBusinessStatus.canMigrateOrder(3));
        
        // 已取消状态不能迁移
        assertFalse(ColdDataBusinessStatus.canMigrateOrder(7));
        
        // null状态不能迁移
        assertFalse(ColdDataBusinessStatus.canMigrateOrder(null));
    }

    @Test
    @DisplayName("测试子运单迁移条件 - 完全符合条件")
    void testSubWaybillMigration_FullyCompliant() {
        // 已完成 + 已签收 + 无需结算
        assertTrue(ColdDataBusinessStatus.canMigrateSubWaybill(
            6,                              // 已完成
            "2023-01-02 15:30:00",         // 已签收
            null,                          // 无需对上结算
            null                           // 无需对下结算
        ));
        
        // 已完成 + 已签收 + 无需结算(0)
        assertTrue(ColdDataBusinessStatus.canMigrateSubWaybill(
            6,                              // 已完成
            "2023-01-02 15:30:00",         // 已签收
            0,                             // 无需对上结算
            0                              // 无需对下结算
        ));
        
        // 已完成 + 已签收 + 结算已支付
        assertTrue(ColdDataBusinessStatus.canMigrateSubWaybill(
            6,                              // 已完成
            "2023-01-02 15:30:00",         // 已签收
            5,                             // 对上结算已支付
            5                              // 对下结算已支付
        ));
        
        // 已完成 + 已签收 + 混合结算状态
        assertTrue(ColdDataBusinessStatus.canMigrateSubWaybill(
            6,                              // 已完成
            "2023-01-02 15:30:00",         // 已签收
            0,                             // 对上无需结算
            5                              // 对下结算已支付
        ));
    }

    @Test
    @DisplayName("测试子运单迁移条件 - 子运单状态不符合")
    void testSubWaybillMigration_InvalidStatus() {
        // 待派车状态不能迁移
        assertFalse(ColdDataBusinessStatus.canMigrateSubWaybill(
            1,                              // 待派车
            "2023-01-02 15:30:00",         // 已签收
            5,                             // 对上结算已支付
            5                              // 对下结算已支付
        ));
        
        // 运输中状态不能迁移
        assertFalse(ColdDataBusinessStatus.canMigrateSubWaybill(
            3,                              // 运输中
            "2023-01-02 15:30:00",         // 已签收
            5,                             // 对上结算已支付
            5                              // 对下结算已支付
        ));
        
        // null状态不能迁移
        assertFalse(ColdDataBusinessStatus.canMigrateSubWaybill(
            null,                          // 状态为空
            "2023-01-02 15:30:00",         // 已签收
            5,                             // 对上结算已支付
            5                              // 对下结算已支付
        ));
    }

    @Test
    @DisplayName("测试子运单迁移条件 - 未签收")
    void testSubWaybillMigration_NotSigned() {
        // 签收时间为null不能迁移
        assertFalse(ColdDataBusinessStatus.canMigrateSubWaybill(
            6,                              // 已完成
            null,                          // 未签收
            5,                             // 对上结算已支付
            5                              // 对下结算已支付
        ));
    }

    @Test
    @DisplayName("测试子运单迁移条件 - 对上结算未完成")
    void testSubWaybillMigration_UpSettlementIncomplete() {
        // 对上结算待结算不能迁移
        assertFalse(ColdDataBusinessStatus.canMigrateSubWaybill(
            6,                              // 已完成
            "2023-01-02 15:30:00",         // 已签收
            3,                             // 对上结算待结算
            5                              // 对下结算已支付
        ));
        
        // 对上结算已结算但未支付不能迁移
        assertFalse(ColdDataBusinessStatus.canMigrateSubWaybill(
            6,                              // 已完成
            "2023-01-02 15:30:00",         // 已签收
            4,                             // 对上结算已结算
            5                              // 对下结算已支付
        ));
    }

    @Test
    @DisplayName("测试子运单迁移条件 - 对下结算未完成")
    void testSubWaybillMigration_DownSettlementIncomplete() {
        // 对下结算待结算不能迁移
        assertFalse(ColdDataBusinessStatus.canMigrateSubWaybill(
            6,                              // 已完成
            "2023-01-02 15:30:00",         // 已签收
            5,                             // 对上结算已支付
            3                              // 对下结算待结算
        ));
        
        // 对下结算已结算但未支付不能迁移
        assertFalse(ColdDataBusinessStatus.canMigrateSubWaybill(
            6,                              // 已完成
            "2023-01-02 15:30:00",         // 已签收
            5,                             // 对上结算已支付
            4                              // 对下结算已结算
        ));
    }

    @Test
    @DisplayName("测试业务规则描述获取")
    void testBusinessRuleDescription() {
        // 订单索引
        String orderRule = ColdDataBusinessStatus.getBusinessRuleDescription("order_index01");
        assertTrue(orderRule.contains("订单状态=已完成"));
        
        // 子运单索引
        String subWaybillRule = ColdDataBusinessStatus.getBusinessRuleDescription("sub_waybill_index01");
        assertTrue(subWaybillRule.contains("子运单状态=已完成"));
        assertTrue(subWaybillRule.contains("已签收"));
        assertTrue(subWaybillRule.contains("结算"));
        
        // 运输计划索引
        String transportRule = ColdDataBusinessStatus.getBusinessRuleDescription("transport_index01");
        assertTrue(transportRule.contains("创建时间"));
        
        // 分组运输索引
        String groupTransportRule = ColdDataBusinessStatus.getBusinessRuleDescription("group_transport_index");
        assertTrue(groupTransportRule.contains("创建时间"));
        
        // 异常预警索引
        String warnRule = ColdDataBusinessStatus.getBusinessRuleDescription("csc_warn_exception_index");
        assertTrue(warnRule.contains("更新时间"));
        
        // 未知索引
        String unknownRule = ColdDataBusinessStatus.getBusinessRuleDescription("unknown_index");
        assertEquals("仅基于时间过滤", unknownRule);
    }

    @Test
    @DisplayName("测试状态常量值")
    void testStatusConstants() {
        // 订单状态常量
        assertEquals(1, ColdDataBusinessStatus.OrderStatus.DRAFT);
        assertEquals(2, ColdDataBusinessStatus.OrderStatus.PENDING_DISPATCH);
        assertEquals(3, ColdDataBusinessStatus.OrderStatus.IN_TRANSIT);
        assertEquals(6, ColdDataBusinessStatus.OrderStatus.COMPLETED);
        assertEquals(7, ColdDataBusinessStatus.OrderStatus.CANCELLED);
        
        // 子运单状态常量
        assertEquals(1, ColdDataBusinessStatus.SubWaybillStatus.PENDING_DISPATCH);
        assertEquals(2, ColdDataBusinessStatus.SubWaybillStatus.DISPATCHED);
        assertEquals(3, ColdDataBusinessStatus.SubWaybillStatus.IN_TRANSIT);
        assertEquals(6, ColdDataBusinessStatus.SubWaybillStatus.COMPLETED);
        assertEquals(7, ColdDataBusinessStatus.SubWaybillStatus.CANCELLED);
        
        // 结算状态常量
        assertEquals(0, ColdDataBusinessStatus.SettlementStatus.NO_SETTLEMENT);
        assertEquals(1, ColdDataBusinessStatus.SettlementStatus.TRANSPORT_INCOMPLETE);
        assertEquals(3, ColdDataBusinessStatus.SettlementStatus.PENDING_SETTLEMENT);
        assertEquals(4, ColdDataBusinessStatus.SettlementStatus.SETTLED);
        assertEquals(5, ColdDataBusinessStatus.SettlementStatus.PAID);
        assertEquals(6, ColdDataBusinessStatus.SettlementStatus.NOT_RECONCILED);
        assertEquals(7, ColdDataBusinessStatus.SettlementStatus.RECONCILED_NOT_SETTLED);
        assertEquals(8, ColdDataBusinessStatus.SettlementStatus.SETTLED_PENDING_PAYMENT);
    }

    @Test
    @DisplayName("测试边界条件")
    void testBoundaryConditions() {
        // 测试所有参数为null的情况
        assertFalse(ColdDataBusinessStatus.canMigrateSubWaybill(null, null, null, null));
        
        // 测试部分参数为null的情况
        assertFalse(ColdDataBusinessStatus.canMigrateSubWaybill(6, null, null, null));
        
        // 测试签收时间为空字符串的情况（这里简化处理，实际可能需要更复杂的验证）
        assertTrue(ColdDataBusinessStatus.canMigrateSubWaybill(6, "", null, null));
        
        // 测试极端的结算状态值
        assertFalse(ColdDataBusinessStatus.canMigrateSubWaybill(6, "2023-01-02", 999, 5));
        assertFalse(ColdDataBusinessStatus.canMigrateSubWaybill(6, "2023-01-02", 5, 999));
    }

    @Test
    @DisplayName("测试实际业务场景")
    void testRealBusinessScenarios() {
        // 场景1: 正常完成的订单和子运单
        assertTrue(ColdDataBusinessStatus.canMigrateOrder(6));
        assertTrue(ColdDataBusinessStatus.canMigrateSubWaybill(6, "2023-01-02 15:30:00", 5, 5));
        
        // 场景2: 订单完成但子运单未签收
        assertTrue(ColdDataBusinessStatus.canMigrateOrder(6));
        assertFalse(ColdDataBusinessStatus.canMigrateSubWaybill(6, null, 5, 5));
        
        // 场景3: 订单和子运单都完成，但结算未完成
        assertTrue(ColdDataBusinessStatus.canMigrateOrder(6));
        assertFalse(ColdDataBusinessStatus.canMigrateSubWaybill(6, "2023-01-02 15:30:00", 3, 5));
        
        // 场景4: 无需结算的情况
        assertTrue(ColdDataBusinessStatus.canMigrateOrder(6));
        assertTrue(ColdDataBusinessStatus.canMigrateSubWaybill(6, "2023-01-02 15:30:00", 0, 0));
        
        // 场景5: 混合结算状态
        assertTrue(ColdDataBusinessStatus.canMigrateSubWaybill(6, "2023-01-02 15:30:00", 0, 5));
        assertTrue(ColdDataBusinessStatus.canMigrateSubWaybill(6, "2023-01-02 15:30:00", 5, 0));
    }
}
