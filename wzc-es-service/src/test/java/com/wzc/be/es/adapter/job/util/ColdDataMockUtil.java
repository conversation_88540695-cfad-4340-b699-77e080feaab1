package com.wzc.be.es.adapter.job.util;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

/**
 * 冷数据Mock工具类
 * 用于生成测试数据和验证Kafka消息格式
 *
 * <AUTHOR> Assistant
 * @since 2025/02/18
 */
public class ColdDataMockUtil {

    /**
     * 生成订单索引Mock数据
     */
    public static Map<String, Object> mockOrderIndexData() {
        Map<String, Object> data = Maps.newHashMap();
        
        // ES元数据
        data.put("_id", "order_20230101_123456");
        data.put("_index", "order_index01");
        data.put("_type", "_doc");
        data.put("_score", 1.0);
        
        // 下线元信息
        addOfflineMetadata(data, "order_index01", "订单索引");
        
        // 订单业务数据
        data.put("orderId", 123456L);
        data.put("orderNo", "ORD20230101001");
        data.put("thirdNo", "THIRD_20230101_001");
        data.put("orderType", 1);
        data.put("baseNo", "BASE_001");
        data.put("itemNames", "钢材,水泥,建材");
        data.put("businessType", 1);
        data.put("sourceType", 1);
        data.put("deliveryCustomer", "上海钢铁有限公司");
        data.put("deliveryAddress", "上海市浦东新区张江高科技园区");
        data.put("receiptCustomer", "北京建筑工程有限公司");
        data.put("receiptAddress", "北京市朝阳区CBD核心区");
        data.put("waybillType", 1);
        data.put("status", 6); // 已完成
        data.put("transportType", 1);
        data.put("customerName", "华东物流客户");
        data.put("companyId", 1001L);
        data.put("companyName", 2001L);
        data.put("shipperCompanyId", 3001L);
        data.put("shipperCompanyName", 4001L);
        data.put("makerName", "张制单员");
        data.put("netCargo", 2);
        data.put("createTime", "2023-01-01 10:00:00");
        data.put("ownerDepartment", 5001L);
        data.put("waybillNoSecond", "WB_SECOND_001");
        data.put("isSync", 1);
        data.put("isFixed", 2);
        data.put("loadDeviceCheck", 2);
        data.put("createId", 6001L);
        data.put("assignedWeight", 3500.5);
        data.put("hideStatus", 0);
        data.put("contractRequire", 1);
        data.put("contractStatus", 30);
        data.put("contractRelation", Lists.newArrayList());
        data.put("isAutoStop", 1);
        data.put("netCargoCreateTime", "2023-01-01 10:00:00");
        data.put("isShowAdmin", 1);
        data.put("settlementStatus", 5); // 已支付
        data.put("totalAmount", 4850.0);
        data.put("paidAmount", 4850.0);
        data.put("paymentStatus", 2);
        
        // 货物信息
        List<Map<String, Object>> items = Lists.newArrayList();
        Map<String, Object> item1 = Maps.newHashMap();
        item1.put("itemId", "ITEM_001");
        item1.put("itemName", "螺纹钢");
        item1.put("weight", 2500.5);
        item1.put("price", 4500.0);
        item1.put("unit", "吨");
        items.add(item1);
        data.put("items", items);
        
        // 发货信息
        List<Map<String, Object>> sender = Lists.newArrayList();
        Map<String, Object> senderInfo = Maps.newHashMap();
        senderInfo.put("address", "上海市浦东新区张江高科技园区祖冲之路887号");
        senderInfo.put("province", "上海市");
        senderInfo.put("city", "上海市");
        senderInfo.put("district", "浦东新区");
        senderInfo.put("phone", "13800138000");
        senderInfo.put("contact", "李发货员");
        sender.add(senderInfo);
        data.put("sender", sender);
        
        // 收货信息
        List<Map<String, Object>> receiver = Lists.newArrayList();
        Map<String, Object> receiverInfo = Maps.newHashMap();
        receiverInfo.put("address", "北京市朝阳区CBD核心区建国门外大街1号");
        receiverInfo.put("province", "北京市");
        receiverInfo.put("city", "北京市");
        receiverInfo.put("district", "朝阳区");
        receiverInfo.put("phone", "13900139000");
        receiverInfo.put("contact", "王收货员");
        receiver.add(receiverInfo);
        data.put("receiver", receiver);
        
        return data;
    }

    /**
     * 生成子运单索引Mock数据
     */
    public static Map<String, Object> mockSubWaybillIndexData() {
        Map<String, Object> data = Maps.newHashMap();
        
        // ES元数据
        data.put("_id", "sub_waybill_20230101_567890");
        data.put("_index", "sub_waybill_index01");
        data.put("_type", "_doc");
        data.put("_score", 1.0);
        
        // 下线元信息
        addOfflineMetadata(data, "sub_waybill_index01", "子运单索引");
        
        // 子运单业务数据
        data.put("id", "567890");
        data.put("orderId", 123456L);
        data.put("orderNo", "ORD20230101001");
        data.put("subWaybillNo", "SUB20230101001");
        data.put("subWaybillStatus", 6); // 已完成
        data.put("status", 6);
        data.put("waybillNo", "WB20230101001");
        data.put("parentWaybillNo", "PWB20230101001");
        data.put("driverId", 7001L);
        data.put("driverName", "张师傅");
        data.put("ztDriverName", "张师傅");
        data.put("driverPhone", "13700137000");
        data.put("driverIdCard", "310101199001011234");
        data.put("carId", 8001L);
        data.put("carNo", "沪A12345");
        data.put("carType", "重型半挂牵引车");
        data.put("carLength", 17.5);
        data.put("carLoad", 25.0);
        data.put("createTime", "2023-01-01 10:00:00");
        data.put("completeStatus", 5); // 已支付
        data.put("completeDownStatus", 5); // 已支付
        data.put("ownerSignTime", "2023-01-02 15:30:00"); // 已签收
        data.put("transStartTime", "2023-01-01 12:00:00");
        data.put("transEndTime", "2023-01-02 15:30:00");
        data.put("loadSignInTime", "2023-01-01 14:00:00");
        data.put("loadConfirmTime", "2023-01-01 15:00:00");
        data.put("unloadSignInTime", "2023-01-02 14:00:00");
        data.put("fbfSignTime", "2023-01-02 15:30:00");
        data.put("actualWeight", 2500.5);
        data.put("actualVolume", 12.5);
        data.put("transportFee", 2800.0);
        data.put("driverFee", 600.0);
        data.put("fuelFee", 450.0);
        data.put("tollFee", 280.0);
        data.put("otherFee", 120.0);
        data.put("totalFee", 4250.0);
        data.put("settlementStatus", 5);
        data.put("paymentStatus", 2);
        data.put("companyId", 1001L);
        data.put("distance", 1200.5);
        data.put("duration", 1635);
        data.put("avgSpeed", 44.2);
        data.put("fuelConsumption", 180.5);
        data.put("gpsTrackCount", 1250);
        data.put("exceptionCount", 0);
        data.put("qualityScore", 98.5);
        data.put("timelinessScore", 95.8);
        data.put("safetyScore", 100.0);
        data.put("serviceScore", 97.2);
        data.put("overallScore", 97.9);
        
        return data;
    }

    /**
     * 生成运输计划索引Mock数据
     */
    public static Map<String, Object> mockTransportIndexData() {
        Map<String, Object> data = Maps.newHashMap();
        
        // ES元数据
        data.put("_id", "transport_20230101_789012");
        data.put("_index", "transport_index01");
        data.put("_type", "_doc");
        data.put("_score", 1.0);
        
        // 下线元信息
        addOfflineMetadata(data, "transport_index01", "运输计划索引");
        
        // 运输计划业务数据
        data.put("transportId", 789012L);
        data.put("transportNo", "TP20230101001");
        data.put("planType", 1);
        data.put("routeId", 2001L);
        data.put("routeName", "上海-北京专线");
        data.put("driverId", 3001L);
        data.put("driverName", "张师傅");
        data.put("driverPhone", "13700137000");
        data.put("carId", 4001L);
        data.put("carNo", "沪A12345");
        data.put("carType", "重型货车");
        data.put("loadCapacity", 25.0);
        data.put("startLocation", "上海市浦东新区");
        data.put("endLocation", "北京市朝阳区");
        data.put("planStartTime", "2023-01-01 08:00:00");
        data.put("planEndTime", "2023-01-02 18:00:00");
        data.put("actualStartTime", "2023-01-01 08:30:00");
        data.put("actualEndTime", "2023-01-02 17:45:00");
        data.put("transportStatus", 6);
        data.put("createTime", "2023-01-01 07:00:00");
        data.put("createId", 5001L);
        data.put("creatorName", "调度员李四");
        data.put("companyId", 1001L);
        data.put("companyName", "华东物流有限公司");
        data.put("orderIds", Lists.newArrayList(123456L, 123457L, 123458L));
        data.put("totalWeight", 22.5);
        data.put("totalVolume", 45.8);
        data.put("transportCost", 3500.0);
        data.put("fuelCost", 800.0);
        data.put("tollCost", 450.0);
        data.put("driverCost", 600.0);
        data.put("remarks", "正常运输，按时到达");
        
        return data;
    }

    /**
     * 生成异常预警索引Mock数据
     */
    public static Map<String, Object> mockWarnExceptionIndexData() {
        Map<String, Object> data = Maps.newHashMap();
        
        // ES元数据
        data.put("_id", "warn_exception_20230101_901234");
        data.put("_index", "csc_warn_exception_index");
        data.put("_type", "_doc");
        data.put("_score", 1.0);
        
        // 下线元信息
        addOfflineMetadata(data, "csc_warn_exception_index", "异常预警索引");
        
        // 异常预警业务数据
        data.put("id", "901234");
        data.put("warnId", "WARN20230101001");
        data.put("warnType", 1);
        data.put("warnLevel", 2);
        data.put("warnTitle", "车辆超速预警");
        data.put("warnContent", "车辆沪A12345在G2京沪高速超速行驶，当前速度120km/h，限速100km/h");
        data.put("orderId", 123456L);
        data.put("orderNo", "ORD20230101001");
        data.put("subWaybillId", 567890L);
        data.put("subWaybillNo", "SUB20230101001");
        data.put("driverId", 7001L);
        data.put("driverName", "张师傅");
        data.put("driverPhone", "13700137000");
        data.put("carId", 8001L);
        data.put("carNo", "沪A12345");
        data.put("carrierId", 9001L);
        data.put("carrierName", "华东运输有限公司");
        data.put("companyId", 1001L);
        data.put("companyName", "华东物流有限公司");
        data.put("warnTime", "2023-01-01 14:30:00");
        data.put("location", "G2京沪高速K1250+500");
        data.put("longitude", 121.473701);
        data.put("latitude", 31.230416);
        data.put("speed", 120.5);
        data.put("direction", 85.2);
        data.put("gpsTime", "2023-01-01 14:30:00");
        data.put("handleStatus", 3);
        data.put("handleTime", "2023-01-01 14:35:00");
        data.put("handleUser", "调度员李四");
        data.put("handleRemark", "已电话通知司机减速，司机已确认");
        data.put("createTime", "2023-01-01 14:30:05");
        data.put("updateTime", "2023-01-01 14:35:20");
        data.put("isDeleted", 0);
        data.put("severity", "中等");
        data.put("category", "行驶安全");
        data.put("source", "GPS监控");
        data.put("riskScore", 75.5);
        data.put("impactLevel", 2);
        data.put("responseTime", 300);
        data.put("resolutionTime", 600);
        data.put("preventionMeasures", "加强司机安全教育，设置速度提醒");
        
        return data;
    }

    /**
     * 添加下线元信息
     */
    private static void addOfflineMetadata(Map<String, Object> data, String indexName, String description) {
        data.put("indexName", indexName);
        data.put("indexDescription", description);
        data.put("operation", "OFFLINE");
        data.put("offlineTime", DateUtil.now());
        data.put("offlineTimestamp", System.currentTimeMillis());
        data.put("retentionThreshold", "12个月");
    }

    /**
     * 生成所有索引的Mock数据列表
     */
    public static List<Map<String, Object>> mockAllIndexData() {
        return Lists.newArrayList(
            mockOrderIndexData(),
            mockTransportIndexData(),
            mockSubWaybillIndexData(),
            mockWarnExceptionIndexData()
        );
    }
}
