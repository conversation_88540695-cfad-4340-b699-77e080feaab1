package com.wzc.be.es.adapter.job;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 日志参数验证测试
 * 检查日志语句中占位符和参数数量是否匹配
 *
 * <AUTHOR> Assistant
 * @since 2025/02/18
 */
class LogParameterValidationTest {

    private static final String JOB_FILE_PATH = "src/main/java/com/wzc/be/es/adapter/job/EsColdDataOfflineJob.java";
    
    @Test
    @DisplayName("验证日志参数匹配")
    void testLogParameterMatching() throws IOException {
        List<String> lines = Files.readAllLines(Paths.get(JOB_FILE_PATH));
        
        Pattern logPattern = Pattern.compile("\\s*log\\.(info|warn|error|debug)\\s*\\(\\s*\"([^\"]*)\",?(.*)\\);?");
        Pattern placeholderPattern = Pattern.compile("\\{[^}]*\\}");
        
        int lineNumber = 0;
        boolean hasErrors = false;
        
        for (String line : lines) {
            lineNumber++;
            
            Matcher logMatcher = logPattern.matcher(line);
            if (logMatcher.find()) {
                String logLevel = logMatcher.group(1);
                String message = logMatcher.group(2);
                String parameters = logMatcher.group(3).trim();
                
                // 跳过异常参数的情况（最后一个参数是异常）
                if (parameters.endsWith(", e)") || parameters.endsWith(",e)")) {
                    continue;
                }
                
                // 计算占位符数量
                Matcher placeholderMatcher = placeholderPattern.matcher(message);
                int placeholderCount = 0;
                while (placeholderMatcher.find()) {
                    placeholderCount++;
                }
                
                // 计算参数数量（简单计算逗号分隔）
                int parameterCount = 0;
                if (!parameters.isEmpty() && !parameters.equals(")")) {
                    // 移除最后的 )
                    if (parameters.endsWith(")")) {
                        parameters = parameters.substring(0, parameters.length() - 1);
                    }
                    
                    if (!parameters.trim().isEmpty()) {
                        // 简单的参数计数（不处理复杂的嵌套情况）
                        String[] parts = parameters.split(",");
                        parameterCount = parts.length;
                        
                        // 过滤掉空参数
                        for (String part : parts) {
                            if (part.trim().isEmpty()) {
                                parameterCount--;
                            }
                        }
                    }
                }
                
                // 验证参数数量匹配
                if (placeholderCount != parameterCount) {
                    System.err.printf("第%d行日志参数不匹配: %s%n", lineNumber, line.trim());
                    System.err.printf("  占位符数量: %d, 参数数量: %d%n", placeholderCount, parameterCount);
                    System.err.printf("  消息: %s%n", message);
                    System.err.printf("  参数: %s%n", parameters);
                    System.err.println();
                    hasErrors = true;
                }
            }
        }
        
        assertFalse(hasErrors, "发现日志参数不匹配的问题");
    }
    
    @Test
    @DisplayName("检查常见的日志问题")
    void testCommonLogIssues() throws IOException {
        List<String> lines = Files.readAllLines(Paths.get(JOB_FILE_PATH));
        
        int lineNumber = 0;
        boolean hasIssues = false;
        
        for (String line : lines) {
            lineNumber++;
            
            // 检查是否有未闭合的日志语句
            if (line.contains("log.") && line.contains("(") && !line.contains(")")) {
                System.err.printf("第%d行可能有未闭合的日志语句: %s%n", lineNumber, line.trim());
                hasIssues = true;
            }
            
            // 检查是否有多余的占位符
            if (line.contains("log.") && line.contains("{:.1f}")) {
                // 这是格式化占位符，需要特别检查
                System.out.printf("第%d行使用了格式化占位符: %s%n", lineNumber, line.trim());
            }
        }
        
        if (hasIssues) {
            System.err.println("发现潜在的日志问题，请检查");
        }
    }
    
    @Test
    @DisplayName("列出所有日志语句")
    void listAllLogStatements() throws IOException {
        List<String> lines = Files.readAllLines(Paths.get(JOB_FILE_PATH));
        
        Pattern logPattern = Pattern.compile("\\s*log\\.(info|warn|error|debug)\\s*\\(");
        
        int lineNumber = 0;
        System.out.println("=== 所有日志语句 ===");
        
        for (String line : lines) {
            lineNumber++;
            
            if (logPattern.matcher(line).find()) {
                System.out.printf("第%d行: %s%n", lineNumber, line.trim());
            }
        }
    }
}
