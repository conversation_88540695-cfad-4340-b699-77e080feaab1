# 日志修复说明

## 修复的问题

### 1. SLF4J格式化占位符问题

**问题**: 使用了`{:.1f}`这样的格式化占位符，但SLF4J不支持这种格式。

**修复前**:
```java
log.info("索引 {} 处理完成 - 成功率: {:.1f}%", indexName, successRate);
```

**修复后**:
```java
log.info("索引 {} 处理完成 - 成功率: {}%", indexName, String.format("%.1f", successRate));
```

### 2. 修复的日志语句

| 行号 | 修复内容 | 说明 |
|---|---|---|
| 113-115 | 错误日志格式化 | 将`{:.1f}`改为`{}`，使用`String.format("%.1f", value)` |
| 118-120 | 成功日志格式化 | 同上 |
| 137-139 | 统计日志格式化 | 同上 |
| 257-259 | 进度日志格式化 | 同上 |

### 3. 依赖注入问题

**问题**: 使用了未注入的`esIndexProperties`字段。

**修复前**:
```java
if (esIndexProperties.getOrderIndex().equals(config.indexName)) {
```

**修复后**:
```java
if (orderIndex.equals(config.indexName)) {
```

## 验证方法

### 1. 运行日志格式测试
```bash
cd wzc-es-service
mvn test -Dtest=LogFormatTest
```

### 2. 检查日志参数匹配
```bash
cd wzc-es-service
mvn test -Dtest=LogParameterValidationTest
```

### 3. 手动验证
```bash
# 查看所有日志语句
grep -n "log\." src/main/java/com/wzc/be/es/adapter/job/EsColdDataOfflineJob.java

# 检查是否还有格式化占位符
grep -n "{:.*}" src/main/java/com/wzc/be/es/adapter/job/EsColdDataOfflineJob.java
```

## 修复后的日志示例

### 成功处理日志
```
INFO  - 开始执行ES冷数据下线任务
INFO  - 冷数据下线时间阈值: 2024-02-18 10:30:00 (保留12个月)
INFO  - 开始处理索引: order_index01 (订单索引)
INFO  - 索引 order_index01 符合条件的冷数据总数: 5000
INFO  - 索引 order_index01 查询到 5000 条符合条件的冷数据，开始处理
INFO  - 索引 order_index01 处理进度: 1000/5000 (20.0%)
INFO  - 索引 order_index01 处理进度: 2000/5000 (40.0%)
INFO  - 完成发送冷数据到Kafka - 索引: order_index01, Topic: TOPIC_ES_COLD_DATA_OFFLINE_ZT, 成功: 5000, 失败: 0, 字段数: 45
INFO  - 开始批量删除索引 order_index01 的 5000 条冷数据
INFO  - 第 1/5 批删除完成: 成功 1000, 失败 0
INFO  - 批量删除完成 - 索引: order_index01, 总计: 5000, 成功: 5000, 失败: 0
INFO  - 索引 order_index01 处理完成 - 查询: 5000, 处理: 5000, 删除: 5000, 成功率: 100.0%, 耗时: 45000ms
INFO  - === ES冷数据下线任务完成 ===
INFO  - 索引统计: 成功 5, 失败 0, 总计 5
INFO  - 数据统计: 查询 15000, 处理 15000, 删除 15000, 整体成功率: 100.0%
```

### 错误处理日志
```
ERROR - 索引 order_index01 处理失败 - 查询: 5000, 处理: 4800, 删除: 4800, 成功率: 96.0%, 耗时: 45000ms, 错误: 部分数据处理失败
WARN  - 冷数据验证发现问题，但继续发送 - 索引: order_index01, 错误数: 5/1000
ERROR - 发送批次数据到Kafka失败，索引: order_index01, 数据量: 1000
ERROR - 处理单个文档时发生异常，文档ID: doc_123456
```

### Debug日志
```
DEBUG - 为订单索引添加业务状态过滤：订单状态=6，结算状态=无结算或5
DEBUG - 为子运单索引添加业务状态过滤：子运单状态=6，已签收，结算状态=5
DEBUG - 成功发送第 1/5 批冷数据到Kafka - 索引: order_index01, 数量: 1000
DEBUG - 冷数据样例字段: [_id, _index, _type, _score, indexName, orderId, orderNo, ...]
```

## 注意事项

1. **SLF4J占位符**: 只支持`{}`，不支持格式化如`{:.1f}`
2. **参数数量**: 占位符数量必须与参数数量完全匹配
3. **异常日志**: 异常参数不计入占位符数量
4. **性能考虑**: Debug日志使用`log.isDebugEnabled()`检查
5. **格式化**: 需要格式化的数值使用`String.format()`

## 测试覆盖

- ✅ 所有日志语句参数匹配验证
- ✅ 格式化占位符修复验证  
- ✅ 异常处理日志验证
- ✅ Debug日志条件检查验证
- ✅ 业务状态过滤日志验证
