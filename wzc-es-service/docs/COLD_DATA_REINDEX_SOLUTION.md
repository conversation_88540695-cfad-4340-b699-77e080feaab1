# ES冷数据Reindex迁移技术方案

## 🎯 **方案概述**

将原有的Kafka发送方案改为基于Elasticsearch Reindex API的冷数据迁移方案，通过定时任务将冷数据迁移到专门的冷数据索引中，实现数据分层存储和成本优化。

## 🏗️ **整体架构**

```
┌─────────────────┐    Reindex API    ┌─────────────────────┐
│   热数据索引     │ ─────────────────→ │    冷数据索引        │
│ order_index01   │                   │ order_index01_cold_ │
│                 │                   │ 2024/2025/2026...   │
└─────────────────┘                   └─────────────────────┘
         │                                       │
         ▼                                       ▼
   删除冷数据                              长期存储+压缩优化
```

## 📋 **核心功能**

### 1. **自动索引创建**
- 基于原索引mapping自动创建冷数据索引
- 复制原索引的字段结构和分析器配置
- 优化冷数据索引的存储设置

### 2. **Reindex数据迁移**
- 使用ES原生Reindex API进行数据迁移
- 支持复杂的业务状态过滤条件
- 批量处理，避免内存溢出

### 3. **数据验证和清理**
- 迁移完成后验证数据完整性
- 验证通过后删除原索引中的冷数据
- 详细的统计和日志记录

### 4. **索引管理**
- 按年份分片的冷数据索引
- 自动创建冷数据别名便于查询
- 过期冷数据索引自动清理

## 🗂️ **索引命名规则**

| 原索引 | 冷数据索引 | 别名 |
|---|---|---|
| `order_index01` | `order_index01_cold_2024` | `order_index01_cold_all` |
| `sub_waybill_index01` | `sub_waybill_index01_cold_2024` | `sub_waybill_index01_cold_all` |
| `transport_index01` | `transport_index01_cold_2024` | `transport_index01_cold_all` |

## ⚙️ **配置参数**

### 基础配置
```properties
# 启用Reindex模式
es.cold.data.offline.reindexMode=true

# 数据保留月数
es.cold.data.offline.retentionMonths=12

# 冷数据索引保留年数
es.cold.data.offline.coldIndexRetentionYears=5
```

### 性能配置
```properties
# Reindex批量大小
es.cold.data.offline.reindexBatchSize=1000

# Reindex超时时间（分钟）
es.cold.data.offline.reindexTimeoutMinutes=30

# 冷数据索引分片数
es.cold.data.offline.coldIndexShards=1

# 冷数据索引副本数
es.cold.data.offline.coldIndexReplicas=0
```

### 自动化配置
```properties
# 自动优化冷数据索引设置
es.cold.data.offline.autoOptimizeColdIndex=true

# 自动创建冷数据别名
es.cold.data.offline.autoCreateColdAlias=true
```

## 🔄 **处理流程**

### 1. **数据查询**
```java
// 查询1年前的数据
BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
    .must(QueryBuilders.rangeQuery("createTime").lt("2024-02-18 00:00:00"))
    .must(QueryBuilders.termQuery("status", 6)); // 已完成状态
```

### 2. **索引创建**
```java
// 自动创建冷数据索引
String coldIndexName = "order_index01_cold_2025";
coldDataIndexService.ensureColdIndexExists("order_index01", coldIndexName);
```

### 3. **Reindex迁移**
```java
// 执行Reindex操作
ReindexRequest reindexRequest = new ReindexRequest();
reindexRequest.setSourceIndices("order_index01");
reindexRequest.setDestIndex("order_index01_cold_2025");
reindexRequest.setSourceQuery(queryBuilder);
```

### 4. **数据清理**
```java
// 删除原索引中的冷数据
DeleteByQueryRequest deleteRequest = new DeleteByQueryRequest("order_index01");
deleteRequest.setQuery(queryBuilder);
```

## 📊 **冷数据索引优化**

### 存储优化设置
```json
{
  "settings": {
    "index.number_of_shards": 1,
    "index.number_of_replicas": 0,
    "index.refresh_interval": "60s",
    "index.blocks.write": true,
    "index.codec": "best_compression",
    "index.merge.policy.max_merged_segment": "5gb"
  }
}
```

### 优化效果
- **存储空间**: 压缩率可达50-70%
- **查询性能**: 冷数据查询通过别名统一访问
- **维护成本**: 自动化管理，无需人工干预

## 🎮 **使用方法**

### XXL-Job配置
```
任务名称: esColdDataReindex
Cron表达式: 0 0 2 * * ? (每天凌晨2点执行)
任务描述: ES冷数据Reindex迁移任务
```

### 手动执行
```bash
# 通过XXL-Job控制台手动触发
curl -X POST "http://xxl-job-admin/api/trigger" \
  -H "Content-Type: application/json" \
  -d '{"jobId": 124, "executorParam": ""}'
```

### 查询冷数据
```bash
# 查询特定年份的冷数据
GET /order_index01_cold_2024/_search

# 查询所有冷数据
GET /order_index01_cold_all/_search

# 获取冷数据统计
GET /order_index01_cold_*/_stats
```

## 📈 **监控指标**

### 关键指标
- **迁移成功率**: 迁移成功数据量 / 总查询数据量
- **迁移耗时**: 各索引Reindex操作耗时
- **存储节省**: 原索引大小 vs 冷数据索引大小
- **查询性能**: 冷数据查询响应时间

### 日志监控
```
INFO  - 索引 order_index01 迁移完成 - 查询: 5000, 迁移: 5000, 删除: 5000, 成功率: 100.0%, 耗时: 45000ms, 目标索引: order_index01_cold_2025
INFO  - 成功创建冷数据索引: order_index01_cold_2025
INFO  - 成功优化冷数据索引设置: order_index01_cold_2025
INFO  - 成功为原索引 order_index01 创建冷数据别名: order_index01_cold_all
```

## 🛡️ **安全保障**

### 数据一致性
1. **先迁移后删除**: 确保数据成功迁移到冷数据索引后再删除
2. **数据验证**: 迁移完成后验证数据数量一致性
3. **事务性**: 使用ES原生API保证操作原子性

### 故障恢复
1. **幂等性**: 支持重复执行，不会产生重复数据
2. **断点续传**: 失败后可以从中断点继续执行
3. **回滚机制**: 支持手动回滚操作

## 🔧 **运维管理**

### 索引管理API
```java
// 获取冷数据统计
ColdDataIndexService.ColdIndexStats stats = 
    coldDataIndexService.getColdIndexStats("order_index01");

// 删除过期冷数据索引
int deletedCount = coldDataIndexService.deleteExpiredColdIndexes("order_index01", 5);

// 优化冷数据索引
coldDataIndexService.optimizeColdIndexSettings("order_index01_cold_2024");
```

### 定期维护
- **每月**: 检查冷数据索引状态和大小
- **每季度**: 清理过期的冷数据索引
- **每年**: 评估存储策略和保留期限

## 🚀 **部署步骤**

### 1. 配置更新
```properties
# 在application.yml中添加Reindex配置
es.cold.data.offline.reindexMode=true
es.cold.data.offline.retentionMonths=12
es.cold.data.offline.coldIndexRetentionYears=5
```

### 2. 任务配置
```
# 在XXL-Job中创建新任务
任务名称: esColdDataReindex
执行器: es-service
Cron: 0 0 2 * * ?
```

### 3. 验证测试
```bash
# 测试环境验证
mvn test -Dtest=EsColdDataReindexJobTest

# 手动执行验证
curl -X POST "http://xxl-job-admin/api/trigger"
```

## 📋 **对比分析**

| 特性 | Kafka方案 | Reindex方案 |
|---|---|---|
| **数据存储** | 外部Kafka | ES内部冷数据索引 |
| **查询能力** | 需要额外系统 | 原生ES查询 |
| **存储成本** | Kafka+ES双份存储 | ES单份压缩存储 |
| **运维复杂度** | 需要维护Kafka | 纯ES方案，简单 |
| **数据恢复** | 从Kafka重建 | 直接查询冷数据索引 |
| **性能影响** | 网络传输开销 | 本地Reindex操作 |

## ✅ **方案优势**

1. **成本优化**: 冷数据压缩存储，节省50-70%空间
2. **查询便利**: 通过别名统一查询热数据和冷数据
3. **运维简化**: 纯ES方案，无需额外组件
4. **性能提升**: 热数据索引更小，查询更快
5. **自动化**: 全自动化管理，无需人工干预
