# ES冷数据下线 - Kafka数据样例

本文档展示了各个索引发送到Kafka的完整JSON数据格式。

## 1. 订单索引 (order_index01)

### 业务条件
- 订单状态为已完成 (status = 6)
- 如果有结算，必须结算完成 (settlementStatus = 5 或 无结算)

### JSON数据样例
```json
[
  {
    // === ES文档元数据 ===
    "_id": "order_20230101_123456",
    "_index": "order_index01",
    "_type": "_doc",
    "_score": 1.0,
    
    // === 冷数据下线元信息 ===
    "indexName": "order_index01",
    "indexDescription": "订单索引",
    "operation": "OFFLINE",
    "offlineTime": "2025-02-18 10:30:00",
    "offlineTimestamp": 1708225800000,
    "retentionThreshold": "12个月",
    
    // === 完整的订单业务数据 ===
    "orderId": 123456,
    "orderNo": "ORD20230101001",
    "thirdNo": "THIRD_20230101_001",
    "orderType": 1,
    "baseNo": "BASE_001",
    "itemNames": "钢材,水泥,建材",
    "businessType": 1,
    "sourceType": 1,
    "deliveryCustomer": "上海钢铁有限公司",
    "deliveryAddress": "上海市浦东新区张江高科技园区",
    "receiptCustomer": "北京建筑工程有限公司",
    "receiptAddress": "北京市朝阳区CBD核心区",
    "waybillType": 1,
    "status": 6,
    "transportType": 1,
    "customerName": "华东物流客户",
    "companyId": 1001,
    "companyName": 2001,
    "shipperCompanyId": 3001,
    "shipperCompanyName": 4001,
    "makerName": "张制单员",
    "netCargo": 2,
    "createTime": "2023-01-01 10:00:00",
    "ownerDepartment": 5001,
    "waybillNoSecond": "WB_SECOND_001",
    "isSync": 1,
    "isFixed": 2,
    "loadDeviceCheck": 2,
    "createId": 6001,
    "items": [
      {
        "itemId": "ITEM_001",
        "itemName": "螺纹钢",
        "weight": 2500.5,
        "price": 4500.0,
        "unit": "吨"
      },
      {
        "itemId": "ITEM_002", 
        "itemName": "水泥",
        "weight": 1000.0,
        "price": 350.0,
        "unit": "吨"
      }
    ],
    "sender": [
      {
        "address": "上海市浦东新区张江高科技园区祖冲之路887号",
        "province": "上海市",
        "city": "上海市",
        "district": "浦东新区",
        "phone": "13800138000",
        "contact": "李发货员"
      }
    ],
    "receiver": [
      {
        "address": "北京市朝阳区CBD核心区建国门外大街1号",
        "province": "北京市",
        "city": "北京市", 
        "district": "朝阳区",
        "phone": "13900139000",
        "contact": "王收货员"
      }
    ],
    "assignedWeight": 3500.5,
    "hideStatus": 0,
    "contractRequire": 1,
    "contractStatus": 30,
    "contractRelation": [],
    "isAutoStop": 1,
    "netCargoCreateTime": "2023-01-01 10:00:00",
    "isShowAdmin": 1,
    "settlementStatus": 5,
    "totalAmount": 4850.0,
    "paidAmount": 4850.0,
    "paymentStatus": 2
  }
]
```

## 2. 运输计划索引 (transport_index01)

### JSON数据样例
```json
[
  {
    // === ES文档元数据 ===
    "_id": "transport_20230101_789012",
    "_index": "transport_index01",
    "_type": "_doc",
    "_score": 1.0,
    
    // === 冷数据下线元信息 ===
    "indexName": "transport_index01",
    "indexDescription": "运输计划索引",
    "operation": "OFFLINE",
    "offlineTime": "2025-02-18 10:30:00",
    "offlineTimestamp": 1708225800000,
    "retentionThreshold": "12个月",
    
    // === 完整的运输计划业务数据 ===
    "transportId": 789012,
    "transportNo": "TP20230101001",
    "planType": 1,
    "routeId": 2001,
    "routeName": "上海-北京专线",
    "driverId": 3001,
    "driverName": "张师傅",
    "driverPhone": "13700137000",
    "carId": 4001,
    "carNo": "沪A12345",
    "carType": "重型货车",
    "loadCapacity": 25.0,
    "startLocation": "上海市浦东新区",
    "endLocation": "北京市朝阳区",
    "planStartTime": "2023-01-01 08:00:00",
    "planEndTime": "2023-01-02 18:00:00",
    "actualStartTime": "2023-01-01 08:30:00",
    "actualEndTime": "2023-01-02 17:45:00",
    "transportStatus": 6,
    "createTime": "2023-01-01 07:00:00",
    "createId": 5001,
    "creatorName": "调度员李四",
    "companyId": 1001,
    "companyName": "华东物流有限公司",
    "orderIds": [123456, 123457, 123458],
    "totalWeight": 22.5,
    "totalVolume": 45.8,
    "transportCost": 3500.0,
    "fuelCost": 800.0,
    "tollCost": 450.0,
    "driverCost": 600.0,
    "remarks": "正常运输，按时到达"
  }
]
```

## 3. 分组运输索引 (group_transport_index)

### JSON数据样例
```json
[
  {
    // === ES文档元数据 ===
    "_id": "group_transport_20230101_345678",
    "_index": "group_transport_index",
    "_type": "_doc",
    "_score": 1.0,
    
    // === 冷数据下线元信息 ===
    "indexName": "group_transport_index",
    "indexDescription": "分组运输索引",
    "operation": "OFFLINE",
    "offlineTime": "2025-02-18 10:30:00",
    "offlineTimestamp": 1708225800000,
    "retentionThreshold": "12个月",
    
    // === 完整的分组运输业务数据 ===
    "groupTransportId": 345678,
    "groupTransportNo": "GT20230101001",
    "groupType": 1,
    "groupName": "华东-华北专线组",
    "routeId": 2001,
    "routeName": "上海-北京-天津环线",
    "leadDriverId": 3001,
    "leadDriverName": "张组长",
    "leadCarNo": "沪A12345",
    "memberCount": 5,
    "members": [
      {
        "driverId": 3002,
        "driverName": "李师傅",
        "carNo": "沪A12346",
        "position": 1
      },
      {
        "driverId": 3003,
        "driverName": "王师傅", 
        "carNo": "沪A12347",
        "position": 2
      }
    ],
    "totalOrders": 15,
    "totalWeight": 125.5,
    "totalVolume": 280.3,
    "groupStatus": 6,
    "createTime": "2023-01-01 06:00:00",
    "startTime": "2023-01-01 08:00:00",
    "endTime": "2023-01-03 18:00:00",
    "createId": 5001,
    "companyId": 1001,
    "estimatedCost": 15000.0,
    "actualCost": 14800.0,
    "efficiency": 98.7,
    "remarks": "分组运输效率良好"
  }
]
```

## 4. 子运单索引 (sub_waybill_index01)

### 业务条件
- 子运单状态为已完成 (subWaybillStatus = 6)
- 必须已签收 (ownerSignTime 不为空)
- 结算状态为已支付 (completeStatus = 5, completeDownStatus = 5)

### JSON数据样例
```json
[
  {
    // === ES文档元数据 ===
    "_id": "sub_waybill_20230101_567890",
    "_index": "sub_waybill_index01",
    "_type": "_doc",
    "_score": 1.0,
    
    // === 冷数据下线元信息 ===
    "indexName": "sub_waybill_index01",
    "indexDescription": "子运单索引",
    "operation": "OFFLINE",
    "offlineTime": "2025-02-18 10:30:00",
    "offlineTimestamp": 1708225800000,
    "retentionThreshold": "12个月",
    
    // === 完整的子运单业务数据 ===
    "id": "567890",
    "orderId": 123456,
    "orderNo": "ORD20230101001",
    "subWaybillNo": "SUB20230101001",
    "subWaybillStatus": 6,
    "status": 6,
    "waybillNo": "WB20230101001",
    "parentWaybillNo": "PWB20230101001",
    "driverId": 7001,
    "driverName": "张师傅",
    "ztDriverName": "张师傅",
    "driverPhone": "13700137000",
    "driverIdCard": "310101199001011234",
    "carId": 8001,
    "carNo": "沪A12345",
    "carType": "重型半挂牵引车",
    "carLength": 17.5,
    "carLoad": 25.0,
    "createTime": "2023-01-01 10:00:00",
    "sender": [
      {
        "address": "上海市浦东新区张江高科技园区祖冲之路887号",
        "customerName": "上海钢铁有限公司",
        "contact": "李发货员",
        "phone": "13800138000",
        "province": "上海市",
        "city": "上海市",
        "district": "浦东新区"
      }
    ],
    "receiver": [
      {
        "address": "北京市朝阳区CBD核心区建国门外大街1号",
        "customerName": "北京建筑工程有限公司",
        "contact": "王收货员",
        "phone": "13900139000",
        "province": "北京市",
        "city": "北京市",
        "district": "朝阳区"
      }
    ],
    "items": [
      {
        "itemId": "ITEM_001",
        "itemName": "螺纹钢",
        "weight": 2500.5,
        "volume": 12.5,
        "quantity": 50,
        "unit": "根"
      }
    ],
    "completeStatus": 5,
    "completeDownStatus": 5,
    "ownerSignTime": "2023-01-02 15:30:00",
    "transStartTime": "2023-01-01 12:00:00",
    "transEndTime": "2023-01-02 15:30:00",
    "loadSignInTime": "2023-01-01 14:00:00",
    "loadConfirmTime": "2023-01-01 15:00:00",
    "unloadSignInTime": "2023-01-02 14:00:00",
    "fbfSignTime": "2023-01-02 15:30:00",
    "actualWeight": 2500.5,
    "actualVolume": 12.5,
    "transportFee": 2800.0,
    "driverFee": 600.0,
    "fuelFee": 450.0,
    "tollFee": 280.0,
    "otherFee": 120.0,
    "totalFee": 4250.0,
    "settlementStatus": 5,
    "paymentStatus": 2,
    "companyId": 1001,
    "distance": 1200.5,
    "duration": 1635,
    "avgSpeed": 44.2,
    "fuelConsumption": 180.5,
    "gpsTrackCount": 1250,
    "exceptionCount": 0,
    "qualityScore": 98.5,
    "timelinessScore": 95.8,
    "safetyScore": 100.0,
    "serviceScore": 97.2,
    "overallScore": 97.9
  }
]
```

## 5. 异常预警索引 (csc_warn_exception_index)

### JSON数据样例
```json
[
  {
    // === ES文档元数据 ===
    "_id": "warn_exception_20230101_901234",
    "_index": "csc_warn_exception_index",
    "_type": "_doc",
    "_score": 1.0,
    
    // === 冷数据下线元信息 ===
    "indexName": "csc_warn_exception_index",
    "indexDescription": "异常预警索引",
    "operation": "OFFLINE",
    "offlineTime": "2025-02-18 10:30:00",
    "offlineTimestamp": 1708225800000,
    "retentionThreshold": "12个月",
    
    // === 完整的异常预警业务数据 ===
    "id": "901234",
    "warnId": "WARN20230101001",
    "warnType": 1,
    "warnLevel": 2,
    "warnTitle": "车辆超速预警",
    "warnContent": "车辆沪A12345在G2京沪高速超速行驶，当前速度120km/h，限速100km/h",
    "orderId": 123456,
    "orderNo": "ORD20230101001",
    "subWaybillId": 567890,
    "subWaybillNo": "SUB20230101001",
    "driverId": 7001,
    "driverName": "张师傅",
    "driverPhone": "13700137000",
    "carId": 8001,
    "carNo": "沪A12345",
    "carrierId": 9001,
    "carrierName": "华东运输有限公司",
    "companyId": 1001,
    "companyName": "华东物流有限公司",
    "warnTime": "2023-01-01 14:30:00",
    "location": "G2京沪高速K1250+500",
    "longitude": 121.473701,
    "latitude": 31.230416,
    "speed": 120.5,
    "direction": 85.2,
    "gpsTime": "2023-01-01 14:30:00",
    "handleStatus": 3,
    "handleTime": "2023-01-01 14:35:00",
    "handleUser": "调度员李四",
    "handleRemark": "已电话通知司机减速，司机已确认",
    "createTime": "2023-01-01 14:30:05",
    "updateTime": "2023-01-01 14:35:20",
    "isDeleted": 0,
    "severity": "中等",
    "category": "行驶安全",
    "source": "GPS监控",
    "relatedEvents": [
      {
        "eventId": "EVENT_001",
        "eventType": "超速",
        "eventTime": "2023-01-01 14:30:00"
      }
    ],
    "riskScore": 75.5,
    "impactLevel": 2,
    "responseTime": 300,
    "resolutionTime": 600,
    "preventionMeasures": "加强司机安全教育，设置速度提醒"
  }
]
```

## 数据特点总结

### 共同元数据字段
所有索引的数据都包含以下元数据：
- **ES元数据**: `_id`, `_index`, `_type`, `_score`
- **下线信息**: `indexName`, `indexDescription`, `operation`, `offlineTime`, `offlineTimestamp`, `retentionThreshold`

### 各索引特色字段

| 索引 | 核心业务字段 | 特色数据 |
|---|---|---|
| **订单索引** | orderId, orderNo, status, items | 发货收货信息、结算状态 |
| **运输计划索引** | transportId, routeId, driverId, carNo | 路线规划、成本统计 |
| **分组运输索引** | groupTransportId, members, efficiency | 分组成员、效率指标 |
| **子运单索引** | subWaybillNo, completeStatus, ownerSignTime | 签收信息、评分数据 |
| **异常预警索引** | warnType, location, handleStatus | GPS位置、处理状态 |

### 数据完整性保证
- 包含ES文档的**所有原始字段**
- 添加**追踪元信息**便于数据恢复
- 保持**业务数据完整性**用于后续分析
- 支持**字段级别验证**确保数据质量

## 字段数量统计

| 索引名称 | 预估字段数 | 核心业务字段 | 复杂对象字段 |
|---|---|---|---|
| **order_index01** | 45+ | orderId, orderNo, status, settlementStatus | items[], sender[], receiver[] |
| **transport_index01** | 30+ | transportId, transportNo, routeId, driverId | orderIds[] |
| **group_transport_index** | 25+ | groupTransportId, groupType, leadDriverId | members[] |
| **sub_waybill_index01** | 50+ | subWaybillNo, completeStatus, ownerSignTime | sender[], receiver[], items[] |
| **csc_warn_exception_index** | 35+ | warnId, warnType, handleStatus, location | relatedEvents[] |

## 测试和验证

### Mock数据生成
```java
// 使用Mock工具类生成测试数据
Map<String, Object> orderData = ColdDataMockUtil.mockOrderIndexData();
Map<String, Object> subWaybillData = ColdDataMockUtil.mockSubWaybillIndexData();

// 验证数据完整性
ColdDataValidationUtil.ValidationResult result =
    ColdDataValidationUtil.validateColdData(dataList, indexName);
```

### JSON格式验证
```bash
# 运行测试验证JSON格式
mvn test -Dtest=ColdDataMockTest

# 查看生成的JSON样例
tail -f logs/test.log | grep "Mock数据"
```
