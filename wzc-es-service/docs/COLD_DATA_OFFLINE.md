# ES冷数据下线功能

## 功能概述

ES冷数据下线功能用于自动清理Elasticsearch中的历史数据，释放存储空间，提高查询性能。该功能按照指定的索引顺序，查询超过保留期限的数据，先发送到Kafka进行备份，然后从ES中删除，确保数据一致性。

## 处理索引顺序

按照以下顺序依次处理各个索引：

1. `order_index01` - 订单索引（基于createTime字段）
   - **业务条件**: 订单状态为已完成，且如果有结算信息则必须结算完成（已支付）
2. `transport_index01` - 运输计划索引（基于createTime字段）
3. `group_transport_index` - 分组运输索引（基于createTime字段）
4. `sub_waybill_index01` - 子运单索引（基于createTime字段）
   - **业务条件**: 子运单状态为已完成，必须已签收（有货主签收时间），且结算状态为已支付
5. `csc_warn_exception_index` - 异常预警索引（基于updateTime字段）

## 配置参数

### 基础配置

```properties
# 是否启用冷数据下线功能
es.cold.data.offline.enabled=true

# 数据保留月数（默认12个月）
es.cold.data.offline.retentionMonths=12

# 冷数据下线Kafka Topic
kafka.topic.es-cold-data.offline=TOPIC_ES_COLD_DATA_OFFLINE_ZT
```

### 性能调优配置

```properties
# 批量处理大小
es.cold.data.offline.pageSize=1000

# 批量删除大小
es.cold.data.offline.deleteBatchSize=500

# 索引处理间隔时间（毫秒）
es.cold.data.offline.indexIntervalMs=5000

# 批次处理间隔时间（毫秒）
es.cold.data.offline.batchIntervalMs=200

# ES查询超时时间（秒）
es.cold.data.offline.queryTimeoutSeconds=30

# Scroll查询超时时间（分钟）
es.cold.data.offline.scrollTimeoutMinutes=5

# 业务状态配置
# 订单完成状态值
es.cold.data.offline.order.completedStatus=6
# 订单结算完成状态值（已支付）
es.cold.data.offline.order.settlementCompletedStatus=5
# 子运单完成状态值
es.cold.data.offline.subWaybill.completedStatus=6
# 子运单结算完成状态值（已支付）
es.cold.data.offline.subWaybill.settlementCompletedStatus=5
```

## 数据流程

### 1. 数据查询
- 按照时间字段查询超过保留期限的数据
- 使用Scroll API进行分页查询，避免内存溢出
- 按照时间升序排序，优先处理最老的数据
- **业务状态过滤**：
  - **订单索引**: 必须是已完成状态，且如果有结算则必须结算完成
  - **子运单索引**: 必须是已完成状态，已签收，且结算状态为已支付

### 2. 数据备份
- 将查询到的数据发送到Kafka Topic: `TOPIC_ES_COLD_DATA_OFFLINE_ZT`
- 数据格式与EsDataToDorisJob保持一致
- 添加额外字段：
  - `operation`: "OFFLINE" - 标记为下线操作
  - `offlineTime`: 下线时间戳
  - `indexName`: 索引名称
  - `id`: 文档ID

### 3. 数据删除
- 在Kafka发送成功后，批量删除ES中的文档
- 使用Bulk API提高删除效率
- 记录删除成功和失败的数量

## Kafka消息格式

发送到Kafka的数据包含**完整的ES文档数据**以及额外的元信息：

### 订单索引数据示例
```json
[
  {
    // === ES文档元数据 ===
    "_id": "document_id_123",
    "_index": "order_index01",
    "_type": "_doc",
    "_score": 1.0,

    // === 冷数据下线元信息 ===
    "indexName": "order_index01",
    "indexDescription": "订单索引",
    "operation": "OFFLINE",
    "offlineTime": "2025-02-18 10:30:00",
    "offlineTimestamp": 1708225800000,
    "retentionThreshold": "12个月",

    // === 完整的业务数据字段 ===
    "orderId": 123456,
    "orderNo": "ORD20240101001",
    "thirdNo": "THIRD_001",
    "orderType": 1,
    "baseNo": "BASE_001",
    "itemNames": "钢材,水泥",
    "businessType": 1,
    "sourceType": 1,
    "deliveryCustomer": "发货公司A",
    "deliveryAddress": "上海市浦东新区xxx",
    "receiptCustomer": "收货公司B",
    "receiptAddress": "北京市朝阳区xxx",
    "waybillType": 1,
    "status": 6,
    "transportType": 1,
    "customerName": "客户名称",
    "companyId": 1001,
    "companyName": 2001,
    "shipperCompanyId": 3001,
    "shipperCompanyName": 4001,
    "makerName": "制单人",
    "netCargo": 2,
    "createTime": "2023-01-01 10:00:00",
    "ownerDepartment": 5001,
    "waybillNoSecond": "WB_SECOND_001",
    "isSync": 1,
    "isFixed": 2,
    "loadDeviceCheck": 2,
    "createId": 6001,
    "items": [
      {
        "itemId": "ITEM_001",
        "itemName": "钢材",
        "weight": 1000.5,
        "price": 500.0
      }
    ],
    "sender": [
      {
        "address": "发货地址详情",
        "province": "上海市",
        "city": "上海市",
        "district": "浦东新区",
        "phone": "13800138000"
      }
    ],
    "receiver": [
      {
        "address": "收货地址详情",
        "province": "北京市",
        "city": "北京市",
        "district": "朝阳区",
        "phone": "13900139000"
      }
    ],
    "assignedWeight": 1000.5,
    "hideStatus": 0,
    "contractRequire": 1,
    "contractStatus": 30,
    "contractRelation": [],
    "isAutoStop": 1,
    "netCargoCreateTime": "2023-01-01 10:00:00",
    "isShowAdmin": 1
  }
]
```

### 子运单索引数据示例
```json
[
  {
    // === ES文档元数据 ===
    "_id": "sub_waybill_id_456",
    "_index": "sub_waybill_index01",
    "_type": "_doc",
    "_score": 1.0,

    // === 冷数据下线元信息 ===
    "indexName": "sub_waybill_index01",
    "indexDescription": "子运单索引",
    "operation": "OFFLINE",
    "offlineTime": "2025-02-18 10:30:00",
    "offlineTimestamp": 1708225800000,
    "retentionThreshold": "12个月",

    // === 完整的子运单业务数据 ===
    "id": "456",
    "orderId": 123456,
    "orderNo": "ORD20240101001",
    "createTime": "2023-01-01 10:00:00",
    "subWaybillNo": "SUB_001",
    "subWaybillStatus": 6,
    "status": 6,
    "waybillNo": "WB_001",
    "driverId": 7001,
    "driverName": "张师傅",
    "ztDriverName": "张师傅",
    "driverPhone": "13700137000",
    "carId": 8001,
    "carNo": "京A12345",
    "sender": [
      {
        "address": "发货地址",
        "customerName": "发货公司"
      }
    ],
    "receiver": [
      {
        "address": "收货地址",
        "customerName": "收货公司"
      }
    ],
    "items": [
      {
        "itemId": "ITEM_001",
        "itemName": "钢材"
      }
    ],
    "completeStatus": 5,
    "completeDownStatus": 5,
    "ownerSignTime": "2023-01-02 15:30:00",
    "transStartTime": "2023-01-01 12:00:00",
    "transEndTime": "2023-01-02 15:30:00",
    "loadSignInTime": "2023-01-01 14:00:00",
    "loadConfirmTime": "2023-01-01 15:00:00",
    "unloadSignInTime": "2023-01-02 14:00:00",
    "fbfSignTime": "2023-01-02 15:30:00",
    // ... 其他所有字段
  }
]
```

## XXL-Job配置

### 任务配置
- **任务名称**: `esColdDataOffline`
- **Cron表达式**: `0 0 2 * * ?` （每天凌晨2点执行）
- **任务描述**: ES冷数据下线任务
- **负责人**: 运维团队

### 执行参数
无需额外参数，所有配置通过配置文件管理。

## 监控和告警

### 日志监控
- 任务开始和结束日志
- 各索引处理进度日志
- 错误和异常日志
- 性能统计日志

### 关键指标
- 总处理数据量
- 总删除数据量
- 各索引处理时间
- 错误率统计

### 告警规则
- 任务执行失败告警
- 单个索引处理时间过长告警
- 删除失败率过高告警

## 安全保障

### 数据一致性
1. **先备份后删除**: 确保数据先成功发送到Kafka再删除
2. **批量处理**: 分批处理避免大事务
3. **错误重试**: 支持失败重试机制
4. **事务回滚**: 删除失败时记录日志，不影响后续处理

### 性能保护
1. **限流控制**: 通过间隔时间控制处理速度
2. **资源监控**: 监控ES集群负载
3. **优雅降级**: 异常时跳过当前索引，继续处理下一个

## 使用示例

### 手动执行
```bash
# 通过XXL-Job控制台手动触发
# 或者通过API调用
curl -X POST "http://xxl-job-admin/api/trigger" \
  -H "Content-Type: application/json" \
  -d '{"jobId": 123, "executorParam": ""}'
```

### 配置调整
```properties
# 调整保留期为6个月
es.cold.data.offline.retentionMonths=6

# 提高处理速度
es.cold.data.offline.pageSize=2000
es.cold.data.offline.deleteBatchSize=1000
es.cold.data.offline.indexIntervalMs=3000
```

## 注意事项

1. **执行时间**: 建议在业务低峰期执行，避免影响正常查询
2. **存储空间**: 确保Kafka有足够空间存储备份数据
3. **网络带宽**: 大量数据传输可能占用网络带宽
4. **ES性能**: 监控ES集群性能，避免影响在线服务
5. **数据恢复**: 如需恢复数据，可从Kafka消息中重建

## 故障排查

### 常见问题
1. **Kafka发送失败**: 检查Kafka连接和Topic配置
2. **ES删除失败**: 检查ES集群状态和权限
3. **内存溢出**: 调小pageSize参数
4. **执行超时**: 增加超时时间或分批执行

### 日志查看
```bash
# 查看任务执行日志
tail -f /logs/wzc-es-service/application.log | grep "esColdDataOffline"

# 查看错误日志
grep "ERROR" /logs/wzc-es-service/application.log | grep "ColdData"
```

## 版本历史

- **v1.0.0**: 初始版本，支持基础冷数据下线功能
- **v1.1.0**: 增加配置化支持和性能优化
- **v1.2.0**: 增加监控统计和错误处理
