# ES冷数据迁移业务逻辑说明

## 🎯 **业务规则概述**

冷数据迁移必须严格遵循业务完整性原则，确保只有完全完成的业务数据才会被迁移到冷数据索引。

## 📋 **各索引业务规则**

### 1. **订单索引 (order_index01)**

#### 业务条件
```sql
-- 订单必须已完成
status = 6 (已完成)
AND createTime < 保留期限
```

#### 字段说明
- `status`: 订单状态字段
  - `6`: 已完成状态，表示订单已经完全处理完毕
- `createTime`: 订单创建时间

#### 业务逻辑
```java
// 只有已完成的订单才能迁移
queryBuilder.must(QueryBuilders.termQuery("status", 6));
```

### 2. **子运单索引 (sub_waybill_index01)**

#### 业务条件
```sql
-- 子运单必须已完成、已签收、且结算完成
subWaybillStatus = 6 (已完成)
AND ownerSignTime IS NOT NULL (已签收)
AND (completeStatus IS NULL OR completeStatus = 0 OR completeStatus = 5) (对上结算完成)
AND (completeDownStatus IS NULL OR completeDownStatus = 0 OR completeDownStatus = 5) (对下结算完成)
AND createTime < 保留期限
```

#### 字段说明
- `subWaybillStatus`: 子运单状态
  - `6`: 已完成状态
- `ownerSignTime`: 货主签收时间
  - 不为空表示已签收
- `completeStatus`: 对上结算状态
  - `NULL`或`0`: 无需结算
  - `5`: 已支付
- `completeDownStatus`: 对下结算状态
  - `NULL`或`0`: 无需结算
  - `5`: 已支付

#### 业务逻辑
```java
// 1. 子运单状态：已完成
queryBuilder.must(QueryBuilders.termQuery("subWaybillStatus", 6));

// 2. 必须已签收
queryBuilder.must(QueryBuilders.existsQuery("ownerSignTime"));

// 3. 对上结算状态：无需结算或已支付
BoolQueryBuilder upSettlementQuery = QueryBuilders.boolQuery();
upSettlementQuery.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("completeStatus")));
upSettlementQuery.should(QueryBuilders.termQuery("completeStatus", 0));
upSettlementQuery.should(QueryBuilders.termQuery("completeStatus", 5));

// 4. 对下结算状态：无需结算或已支付
BoolQueryBuilder downSettlementQuery = QueryBuilders.boolQuery();
downSettlementQuery.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("completeDownStatus")));
downSettlementQuery.should(QueryBuilders.termQuery("completeDownStatus", 0));
downSettlementQuery.should(QueryBuilders.termQuery("completeDownStatus", 5));
```

### 3. **运输计划索引 (transport_index01)**

#### 业务条件
```sql
-- 仅基于时间过滤
createTime < 保留期限
```

#### 业务逻辑
- 运输计划索引不涉及复杂的业务状态
- 仅基于创建时间进行过滤

### 4. **分组运输索引 (group_transport_index)**

#### 业务条件
```sql
-- 仅基于时间过滤
createTime < 保留期限
```

#### 业务逻辑
- 分组运输索引不涉及复杂的业务状态
- 仅基于创建时间进行过滤

### 5. **异常预警索引 (csc_warn_exception_index)**

#### 业务条件
```sql
-- 基于更新时间过滤
updateTime < 保留期限
```

#### 业务逻辑
- 异常预警索引使用更新时间作为过滤条件
- 不涉及业务状态判断

## 🔍 **状态值定义**

### 订单状态 (status)
```java
public static class OrderStatus {
    public static final int DRAFT = 1;                // 草稿
    public static final int PENDING_DISPATCH = 2;     // 待派车
    public static final int IN_TRANSIT = 3;           // 运输中
    public static final int COMPLETED = 6;            // 已完成 ✅ 可迁移
    public static final int CANCELLED = 7;            // 已取消
}
```

### 子运单状态 (subWaybillStatus)
```java
public static class SubWaybillStatus {
    public static final int PENDING_DISPATCH = 1;     // 待派车
    public static final int DISPATCHED = 2;           // 已派车
    public static final int IN_TRANSIT = 3;           // 运输中
    public static final int COMPLETED = 6;            // 已完成 ✅ 可迁移
    public static final int CANCELLED = 7;            // 已取消
}
```

### 结算状态 (completeStatus/completeDownStatus)
```java
public static class SettlementStatus {
    public static final int NO_SETTLEMENT = 0;              // 无需结算 ✅ 可迁移
    public static final int TRANSPORT_INCOMPLETE = 1;       // 运输未完成
    public static final int PENDING_SETTLEMENT = 3;         // 待结算
    public static final int SETTLED = 4;                    // 已结算
    public static final int PAID = 5;                       // 已支付 ✅ 可迁移
    public static final int NOT_RECONCILED = 6;             // 未对账
    public static final int RECONCILED_NOT_SETTLED = 7;     // 已对账未结算
    public static final int SETTLED_PENDING_PAYMENT = 8;    // 已结算待支付
}
```

## 📊 **业务验证示例**

### 订单数据验证
```json
{
  "_id": "order_123456",
  "orderId": 123456,
  "orderNo": "ORD20230101001",
  "status": 6,                    // ✅ 已完成，可以迁移
  "createTime": "2023-01-01 10:00:00"
}
```

### 子运单数据验证
```json
{
  "_id": "sub_waybill_789012",
  "orderId": 123456,
  "subWaybillNo": "SUB20230101001",
  "subWaybillStatus": 6,          // ✅ 已完成
  "ownerSignTime": "2023-01-02 15:30:00",  // ✅ 已签收
  "completeStatus": 5,            // ✅ 对上结算已支付
  "completeDownStatus": 5,        // ✅ 对下结算已支付
  "createTime": "2023-01-01 10:00:00"
}
```

### 不符合条件的数据示例
```json
// ❌ 订单未完成
{
  "status": 3,                    // 运输中，不能迁移
  "createTime": "2023-01-01 10:00:00"
}

// ❌ 子运单未签收
{
  "subWaybillStatus": 6,          // 已完成
  "ownerSignTime": null,          // 未签收，不能迁移
  "completeStatus": 5
}

// ❌ 子运单结算未完成
{
  "subWaybillStatus": 6,          // 已完成
  "ownerSignTime": "2023-01-02 15:30:00",  // 已签收
  "completeStatus": 3,            // 待结算，不能迁移
  "completeDownStatus": 5
}
```

## 🔧 **配置参数**

### 业务状态配置
```yaml
es:
  cold:
    data:
      offline:
        # 订单业务状态
        order:
          completedStatus: 6              # 订单完成状态值
        
        # 子运单业务状态
        subWaybill:
          completedStatus: 6              # 子运单完成状态值
          settlementCompletedStatus: 5    # 结算完成状态值（已支付）
```

## 🧪 **测试验证**

### 单元测试
```java
@Test
void testOrderBusinessLogic() {
    // 测试已完成订单
    assertTrue(ColdDataBusinessStatus.canMigrateOrder(6));
    
    // 测试未完成订单
    assertFalse(ColdDataBusinessStatus.canMigrateOrder(3));
}

@Test
void testSubWaybillBusinessLogic() {
    // 测试符合条件的子运单
    assertTrue(ColdDataBusinessStatus.canMigrateSubWaybill(
        6,                              // 已完成
        "2023-01-02 15:30:00",         // 已签收
        5,                             // 对上结算已支付
        5                              // 对下结算已支付
    ));
    
    // 测试不符合条件的子运单
    assertFalse(ColdDataBusinessStatus.canMigrateSubWaybill(
        6,                              // 已完成
        null,                          // 未签收
        5,                             // 对上结算已支付
        5                              // 对下结算已支付
    ));
}
```

### 查询验证
```bash
# 验证订单查询条件
curl -X POST "localhost:9200/order_index01/_search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": {
      "bool": {
        "must": [
          {"range": {"createTime": {"lt": "2024-02-18 00:00:00"}}},
          {"term": {"status": 6}}
        ]
      }
    },
    "size": 0
  }'

# 验证子运单查询条件
curl -X POST "localhost:9200/sub_waybill_index01/_search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": {
      "bool": {
        "must": [
          {"range": {"createTime": {"lt": "2024-02-18 00:00:00"}}},
          {"term": {"subWaybillStatus": 6}},
          {"exists": {"field": "ownerSignTime"}},
          {
            "bool": {
              "should": [
                {"bool": {"must_not": {"exists": {"field": "completeStatus"}}}},
                {"term": {"completeStatus": 0}},
                {"term": {"completeStatus": 5}}
              ]
            }
          },
          {
            "bool": {
              "should": [
                {"bool": {"must_not": {"exists": {"field": "completeDownStatus"}}}},
                {"term": {"completeDownStatus": 0}},
                {"term": {"completeDownStatus": 5}}
              ]
            }
          }
        ]
      }
    },
    "size": 0
  }'
```

## ⚠️ **注意事项**

1. **数据完整性**: 确保业务流程完全结束后才迁移数据
2. **状态一致性**: 订单和子运单的状态必须保持一致
3. **签收确认**: 子运单必须有货主签收确认
4. **结算完成**: 所有相关的结算流程必须完成
5. **时间窗口**: 保留足够的时间窗口确保业务流程完整

## 🔄 **业务流程图**

```
订单创建 → 派车 → 运输 → 签收 → 结算 → 完成 → (保留期后) → 冷数据迁移
   ↓        ↓      ↓      ↓      ↓      ↓                    ↓
  草稿    待派车  运输中  已签收  结算中  已完成              可迁移
  (1)     (2)    (3)    (4)    (4)    (6)               status=6
                                                        AND 结算完成
```

这样的业务逻辑确保了只有完全完成的业务数据才会被迁移，保证了数据的完整性和业务的连续性。
