# ES冷数据异步Reindex设计方案

## 🎯 **异步Reindex设计目标**

将原有的同步Reindex改为异步执行，通过轮询task状态来监控进度，避免长时间阻塞和超时问题。

## 🔄 **异步执行流程**

```
1. 提交异步Reindex任务
   ↓
2. 获取Task ID
   ↓
3. 轮询Task状态
   ↓
4. 任务完成处理
   ↓
5. 数据验证和清理
```

## 📋 **核心功能设计**

### 1. **异步任务提交**

```java
// 提交异步Reindex任务
private String submitAsyncReindexTask(IndexConfig config, String coldIndexName, String retentionThreshold) {
    // 构建Reindex请求JSON
    String reindexJson = buildReindexRequestJson(sourceIndex, destIndex, queryBuilder);
    
    // 使用低级客户端提交异步任务
    Request request = new Request("POST", "/_reindex");
    request.addParameter("wait_for_completion", "false"); // 关键：异步执行
    request.setJsonEntity(reindexJson);
    
    Response response = restHighLevelClient.getLowLevelClient().performRequest(request);
    return extractTaskIdFromResponse(responseBody);
}
```

### 2. **任务状态轮询**

```java
// 轮询任务状态直到完成
private ReindexTaskResult waitForReindexCompletion(String taskId, String sourceIndex, String destIndex) {
    while (true) {
        ReindexTaskStatus taskStatus = getReindexTaskStatus(taskId);
        
        if (taskStatus.isCompleted()) {
            return new ReindexTaskResult(true, taskStatus.hasError(), 
                taskStatus.getProcessedCount(), elapsedTime, taskStatus.getErrorMessage());
        }
        
        // 检查超时
        if (elapsedTime > maxWaitTimeMs) {
            return new ReindexTaskResult(false, true, 0, elapsedTime, "任务超时");
        }
        
        // 等待下次轮询
        Thread.sleep(pollIntervalMs);
    }
}
```

### 3. **任务状态查询**

```java
// 查询任务状态
private ReindexTaskStatus getReindexTaskStatus(String taskId) {
    Request request = new Request("GET", "/_tasks/" + taskId);
    Response response = restHighLevelClient.getLowLevelClient().performRequest(request);
    
    String responseBody = Streams.copyToString(
        new InputStreamReader(response.getEntity().getContent(), StandardCharsets.UTF_8));
    
    return parseTaskStatusResponse(responseBody);
}
```

## ⚙️ **配置参数**

### 异步任务配置
```yaml
es:
  cold:
    data:
      offline:
        # 异步任务轮询间隔（秒）
        taskPollIntervalSeconds: 10
        
        # 异步任务最大等待时间（分钟）
        taskMaxWaitMinutes: 120
        
        # Reindex超时时间（分钟）
        reindexTimeoutMinutes: 30
```

### 性能优化配置
```yaml
es:
  cold:
    data:
      offline:
        # Reindex批量大小
        pageSize: 1000
        
        # 索引处理间隔
        indexIntervalMs: 5000
```

## 📊 **异步执行优势**

### 1. **避免超时问题**
- **同步模式**: 长时间运行可能导致HTTP超时
- **异步模式**: 任务在ES后台执行，不受HTTP超时限制

### 2. **更好的监控**
- **实时进度**: 可以实时查看任务处理进度
- **详细状态**: 获取任务的详细执行状态和错误信息

### 3. **资源优化**
- **非阻塞**: 不占用HTTP连接资源
- **可中断**: 可以主动取消长时间运行的任务

## 🔍 **任务状态监控**

### Task API响应示例

#### 运行中的任务
```json
{
  "completed": false,
  "task": {
    "node": "node1",
    "id": 12345,
    "type": "transport",
    "action": "indices:data/write/reindex",
    "status": {
      "total": 10000,
      "updated": 0,
      "created": 5000,
      "deleted": 0,
      "batches": 5,
      "version_conflicts": 0,
      "noops": 0,
      "retries": {
        "bulk": 0,
        "search": 0
      }
    },
    "running_time_in_nanos": 30000000000
  }
}
```

#### 完成的任务
```json
{
  "completed": true,
  "task": {
    "node": "node1",
    "id": 12345,
    "type": "transport",
    "action": "indices:data/write/reindex",
    "status": {
      "total": 10000,
      "updated": 0,
      "created": 10000,
      "deleted": 0,
      "batches": 10,
      "version_conflicts": 0,
      "noops": 0,
      "retries": {
        "bulk": 0,
        "search": 0
      }
    },
    "running_time_in_nanos": 60000000000
  },
  "response": {
    "took": 60000,
    "timed_out": false,
    "total": 10000,
    "updated": 0,
    "created": 10000,
    "deleted": 0,
    "batches": 10,
    "version_conflicts": 0,
    "noops": 0,
    "retries": {
      "bulk": 0,
      "search": 0
    },
    "throttled_millis": 0,
    "requests_per_second": -1.0,
    "throttled_until_millis": 0,
    "failures": []
  }
}
```

## 🛡️ **错误处理**

### 1. **任务失败处理**
```java
if (taskStatus.hasError()) {
    log.error("异步Reindex任务失败: {}, 错误: {}", taskId, taskStatus.getErrorMessage());
    throw new RuntimeException("Reindex任务失败: " + taskStatus.getErrorMessage());
}
```

### 2. **超时处理**
```java
if (elapsedTime > maxWaitTimeMs) {
    log.warn("异步Reindex任务超时: {}, 已等待: {}ms", taskId, elapsedTime);
    return new ReindexTaskResult(false, true, 0, elapsedTime, "任务超时");
}
```

### 3. **任务不存在处理**
```java
catch (ResponseException e) {
    if (e.getResponse().getStatusLine().getStatusCode() == 404) {
        // 任务不存在，可能已经完成并被清理
        log.warn("任务 {} 不存在，可能已完成", taskId);
        return new ReindexTaskStatus(true, false, 0, null);
    }
}
```

## 📈 **监控和日志**

### 进度日志
```
INFO  - 开始执行异步Reindex迁移: order_index01 -> order_index01_cold_2025
INFO  - 异步Reindex任务已提交: order_index01 -> order_index01_cold_2025, TaskID: node1:12345
INFO  - 开始监控异步Reindex任务: node1:12345, 源索引: order_index01, 目标索引: order_index01_cold_2025
INFO  - 异步Reindex任务进行中: node1:12345, 已处理: 5000 条, 耗时: 30000ms
INFO  - 异步Reindex任务进行中: node1:12345, 已处理: 8000 条, 耗时: 45000ms
INFO  - 异步Reindex任务完成: node1:12345, 耗时: 60000ms
INFO  - 异步Reindex迁移完成: order_index01 -> order_index01_cold_2025, 迁移数据: 10000 条, 耗时: 60000ms
```

### 错误日志
```
ERROR - 异步Reindex任务失败: node1:12345, 错误: version conflict
WARN  - 异步Reindex任务超时: node1:12345, 已等待: 7200000ms, 最大等待: 7200000ms
ERROR - 查询异步Reindex任务状态失败: node1:12345
```

## 🧪 **测试验证**

### 单元测试
```java
@Test
void testAsyncReindexSubmission() {
    String taskId = reindexJob.submitAsyncReindexTask(config, coldIndexName, retentionThreshold);
    assertNotNull(taskId);
    assertTrue(taskId.contains(":"));
}

@Test
void testTaskStatusPolling() {
    ReindexTaskStatus status = reindexJob.getReindexTaskStatus("node1:12345");
    assertNotNull(status);
}
```

### 集成测试
```java
@Test
void testAsyncReindexComplete() {
    // 提交异步任务
    String taskId = reindexJob.submitAsyncReindexTask(config, coldIndexName, retentionThreshold);
    
    // 等待完成
    ReindexTaskResult result = reindexJob.waitForReindexCompletion(taskId, sourceIndex, destIndex);
    
    assertTrue(result.isCompleted());
    assertFalse(result.hasError());
    assertTrue(result.getProcessedCount() > 0);
}
```

## 🔧 **部署配置**

### application.yml配置
```yaml
es:
  cold:
    data:
      offline:
        enabled: true
        reindexMode: true
        
        # 异步任务配置
        taskPollIntervalSeconds: 10      # 10秒轮询一次
        taskMaxWaitMinutes: 120          # 最大等待2小时
        reindexTimeoutMinutes: 30        # Reindex超时30分钟
        
        # 性能配置
        pageSize: 1000                   # 批量大小
        indexIntervalMs: 5000            # 索引间隔
```

### XXL-Job任务配置
```
任务名称: esColdDataAsyncReindex
Cron表达式: 0 0 2 * * ?
任务超时时间: 14400秒 (4小时)
阻塞处理策略: 单机串行
```

## 📋 **对比分析**

| 特性 | 同步Reindex | 异步Reindex |
|---|---|---|
| **执行方式** | 阻塞等待 | 后台执行 |
| **超时风险** | 高 | 低 |
| **进度监控** | 无 | 实时 |
| **资源占用** | HTTP连接 | 轮询开销 |
| **错误处理** | 简单 | 详细 |
| **可中断性** | 难 | 易 |
| **适用场景** | 小数据量 | 大数据量 |

## ✅ **总结**

异步Reindex方案通过以下方式解决了同步模式的问题：

1. **避免超时**: 任务在ES后台执行，不受HTTP超时限制
2. **实时监控**: 可以实时查看任务进度和状态
3. **更好的错误处理**: 详细的错误信息和状态反馈
4. **资源优化**: 不占用长时间的HTTP连接
5. **可扩展性**: 支持大数据量的迁移操作

这种设计特别适合处理大量数据的冷数据迁移场景，提供了更好的稳定性和可监控性。
