# ES冷数据Reindex迁移部署指南

## 🚀 **快速开始**

### 1. 配置更新
```yaml
# 在application.yml中添加配置
es:
  cold:
    data:
      offline:
        enabled: true
        reindexMode: true
        retentionMonths: 12
        coldIndexRetentionYears: 5
```

### 2. XXL-Job任务配置
```
任务名称: esColdDataReindex
执行器: es-service
Cron表达式: 0 0 2 * * ?
任务描述: ES冷数据Reindex迁移任务
路由策略: 第一个
阻塞处理策略: 单机串行
```

### 3. 验证部署
```bash
# 检查配置
curl http://localhost:8080/actuator/configprops | grep coldData

# 手动触发任务
curl -X POST "http://xxl-job-admin/api/trigger" \
  -H "Content-Type: application/json" \
  -d '{"jobId": 124}'
```

## 📋 **详细部署步骤**

### 步骤1: 环境准备

#### 1.1 检查ES版本兼容性
```bash
# 确保ES版本支持Reindex API (6.0+)
curl -X GET "localhost:9200"
```

#### 1.2 检查磁盘空间
```bash
# 确保有足够空间存储冷数据索引
df -h /data/elasticsearch
```

#### 1.3 备份重要数据
```bash
# 创建快照备份
curl -X PUT "localhost:9200/_snapshot/backup/snapshot_$(date +%Y%m%d)" \
  -H "Content-Type: application/json" \
  -d '{"indices": "order_index01,sub_waybill_index01"}'
```

### 步骤2: 代码部署

#### 2.1 更新代码
```bash
# 拉取最新代码
git pull origin main

# 编译打包
mvn clean package -DskipTests
```

#### 2.2 配置文件更新
```bash
# 复制配置模板
cp src/main/resources/application-cold-data-reindex.yml \
   src/main/resources/application-prod.yml

# 根据环境调整配置
vim src/main/resources/application-prod.yml
```

#### 2.3 部署应用
```bash
# 停止旧版本
systemctl stop es-service

# 部署新版本
cp target/wzc-es-service.jar /opt/es-service/
systemctl start es-service

# 检查启动状态
systemctl status es-service
tail -f /var/log/es-service/application.log
```

### 步骤3: XXL-Job配置

#### 3.1 创建执行器
```
执行器名称: es-service
注册方式: 自动注册
机器地址: 自动获取
```

#### 3.2 创建任务
```
基础配置:
- 任务名称: esColdDataReindex
- 任务描述: ES冷数据Reindex迁移任务

调度配置:
- 调度类型: CRON
- Cron表达式: 0 0 2 * * ?
- 时区: Asia/Shanghai

任务配置:
- 运行模式: BEAN
- JobHandler: esColdDataReindex
- 执行参数: (留空)

高级配置:
- 路由策略: 第一个
- 子任务: (无)
- 调度过期策略: 忽略
- 阻塞处理策略: 单机串行
- 任务超时时间: 3600秒
- 失败重试次数: 1
```

### 步骤4: 监控配置

#### 4.1 日志监控
```bash
# 配置日志轮转
cat > /etc/logrotate.d/es-service << EOF
/var/log/es-service/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 es-service es-service
}
EOF
```

#### 4.2 指标监控
```yaml
# 在application.yml中添加
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  metrics:
    export:
      prometheus:
        enabled: true
```

#### 4.3 告警配置
```yaml
# Prometheus告警规则
groups:
  - name: es-cold-data-reindex
    rules:
      - alert: ColdDataReindexFailed
        expr: increase(xxl_job_failed_total{job="esColdDataReindex"}[1h]) > 0
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "ES冷数据Reindex任务失败"
          description: "任务 {{ $labels.job }} 在过去1小时内失败了 {{ $value }} 次"
```

## 🔧 **配置调优**

### 性能优化配置
```yaml
es:
  cold:
    data:
      offline:
        # 根据ES集群性能调整
        pageSize: 2000                    # 大集群可以增大
        reindexBatchSize: 2000            # 大集群可以增大
        indexIntervalMs: 10000            # 高负载时增大间隔
        
        # 根据存储需求调整
        coldIndexShards: 1                # 冷数据通常1个分片足够
        coldIndexReplicas: 0              # 冷数据可以不要副本
```

### 业务配置调整
```yaml
es:
  cold:
    data:
      offline:
        # 根据业务需求调整保留期
        retentionMonths: 12               # 热数据保留期
        coldIndexRetentionYears: 5        # 冷数据保留期
        
        # 根据业务状态调整
        order:
          completedStatus: 6              # 订单完成状态
          settlementCompletedStatus: 5    # 结算完成状态
```

## 🔍 **验证和测试**

### 功能验证
```bash
# 1. 检查配置加载
curl http://localhost:8080/actuator/configprops | grep coldData

# 2. 手动执行任务
curl -X POST "http://xxl-job-admin/api/trigger" \
  -H "Content-Type: application/json" \
  -d '{"jobId": 124}'

# 3. 检查执行日志
tail -f /var/log/es-service/application.log | grep "esColdDataReindex"

# 4. 验证冷数据索引创建
curl -X GET "localhost:9200/_cat/indices/*_cold_*?v"

# 5. 验证数据迁移
curl -X GET "localhost:9200/order_index01_cold_2025/_count"
```

### 性能测试
```bash
# 1. 监控ES集群负载
curl -X GET "localhost:9200/_cluster/stats"

# 2. 监控任务执行时间
grep "迁移完成" /var/log/es-service/application.log | tail -10

# 3. 检查存储使用情况
curl -X GET "localhost:9200/_cat/indices?v&s=store.size:desc"
```

## 📊 **运维监控**

### 关键指标监控
```bash
# 1. 任务执行成功率
grep "迁移完成" /var/log/es-service/application.log | wc -l

# 2. 数据迁移量统计
grep "迁移.*条" /var/log/es-service/application.log | tail -10

# 3. 存储空间节省
curl -X GET "localhost:9200/_cat/indices?v&h=index,store.size" | grep cold

# 4. 冷数据索引数量
curl -X GET "localhost:9200/_cat/indices/*_cold_*" | wc -l
```

### 日常维护
```bash
# 1. 每周检查冷数据索引状态
curl -X GET "localhost:9200/_cat/indices/*_cold_*?v&s=creation.date"

# 2. 每月清理过期冷数据索引
# (通过配置自动执行，也可手动执行)

# 3. 每季度评估存储策略
curl -X GET "localhost:9200/_cat/allocation?v"
```

## 🚨 **故障排查**

### 常见问题

#### 1. 任务执行失败
```bash
# 检查错误日志
grep "ERROR" /var/log/es-service/application.log | grep "esColdDataReindex"

# 检查ES集群状态
curl -X GET "localhost:9200/_cluster/health"

# 检查磁盘空间
df -h /data/elasticsearch
```

#### 2. Reindex操作超时
```yaml
# 调整超时配置
es:
  cold:
    data:
      offline:
        reindexTimeoutMinutes: 60         # 增加超时时间
        reindexBatchSize: 500             # 减小批量大小
```

#### 3. 冷数据索引创建失败
```bash
# 检查ES日志
tail -f /var/log/elasticsearch/elasticsearch.log

# 检查索引模板冲突
curl -X GET "localhost:9200/_template"

# 手动创建测试
curl -X PUT "localhost:9200/test_cold_index" \
  -H "Content-Type: application/json" \
  -d '{"settings": {"number_of_shards": 1}}'
```

### 回滚方案
```bash
# 1. 停止定时任务
# 在XXL-Job控制台暂停任务

# 2. 恢复数据（如果需要）
curl -X POST "localhost:9200/_reindex" \
  -H "Content-Type: application/json" \
  -d '{
    "source": {"index": "order_index01_cold_2025"},
    "dest": {"index": "order_index01"}
  }'

# 3. 删除冷数据索引（如果需要）
curl -X DELETE "localhost:9200/order_index01_cold_2025"
```

## 📈 **性能优化建议**

### ES集群优化
```yaml
# elasticsearch.yml
cluster.routing.allocation.disk.threshold.enabled: true
cluster.routing.allocation.disk.watermark.low: 85%
cluster.routing.allocation.disk.watermark.high: 90%
cluster.routing.allocation.disk.watermark.flood_stage: 95%
```

### JVM优化
```bash
# 增加堆内存
export JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC"
```

### 网络优化
```yaml
# 如果ES集群分布在多台机器
es:
  cold:
    data:
      offline:
        indexIntervalMs: 15000            # 增加间隔减少网络压力
        batchIntervalMs: 500              # 增加批次间隔
```

## ✅ **部署检查清单**

- [ ] ES版本兼容性检查 (6.0+)
- [ ] 磁盘空间充足 (至少50%可用)
- [ ] 配置文件正确更新
- [ ] 应用成功启动
- [ ] XXL-Job任务创建成功
- [ ] 手动执行测试通过
- [ ] 监控和告警配置完成
- [ ] 日志轮转配置完成
- [ ] 备份策略确认
- [ ] 回滚方案准备完成

## 📞 **技术支持**

如遇到问题，请提供以下信息：
1. 错误日志片段
2. ES集群状态
3. 配置文件内容
4. 执行环境信息

联系方式：
- 技术支持群：ES运维群
- 邮箱：<EMAIL>
- 文档：http://wiki.company.com/es-cold-data
