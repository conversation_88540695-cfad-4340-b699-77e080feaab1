#!/bin/bash

# ES冷数据Reindex最终错误检查脚本

echo "=== ES冷数据Reindex最终错误检查 ==="
echo "开始时间: $(date)"
echo

REINDEX_JOB="src/main/java/com/wzc/be/es/adapter/job/EsColdDataReindexJob.java"

# 1. 检查文件是否存在
if [ ! -f "$REINDEX_JOB" ]; then
    echo "❌ 文件不存在: $REINDEX_JOB"
    exit 1
fi

echo "✅ 文件存在: $REINDEX_JOB"
echo

# 2. 检查基本语法
echo "2. 检查基本语法..."

# 检查括号匹配
open_braces=$(grep -o "{" "$REINDEX_JOB" | wc -l)
close_braces=$(grep -o "}" "$REINDEX_JOB" | wc -l)
echo "大括号: 开 $open_braces, 闭 $close_braces"

if [ "$open_braces" -eq "$close_braces" ]; then
    echo "✅ 大括号匹配"
else
    echo "❌ 大括号不匹配"
fi

# 检查圆括号匹配
open_parens=$(grep -o "(" "$REINDEX_JOB" | wc -l)
close_parens=$(grep -o ")" "$REINDEX_JOB" | wc -l)
echo "圆括号: 开 $open_parens, 闭 $close_parens"

if [ "$open_parens" -eq "$close_parens" ]; then
    echo "✅ 圆括号匹配"
else
    echo "❌ 圆括号不匹配"
fi

echo

# 3. 检查import语句
echo "3. 检查import语句..."

# 检查重复import
echo "检查重复import..."
duplicate_imports=$(grep "^import" "$REINDEX_JOB" | sort | uniq -d)
if [ -z "$duplicate_imports" ]; then
    echo "✅ 没有重复import"
else
    echo "❌ 发现重复import:"
    echo "$duplicate_imports"
fi

# 检查通配符import
wildcard_imports=$(grep "import.*\*" "$REINDEX_JOB")
if [ -z "$wildcard_imports" ]; then
    echo "✅ 没有通配符import"
else
    echo "❌ 发现通配符import:"
    echo "$wildcard_imports"
fi

# 统计import数量
import_count=$(grep -c "^import" "$REINDEX_JOB")
echo "Import语句总数: $import_count"

echo

# 4. 检查关键API使用
echo "4. 检查关键API使用..."

# 检查ES API
es_apis=(
    "RestHighLevelClient"
    "SearchRequest"
    "SearchResponse"
    "ReindexRequest"
    "DeleteByQueryRequest"
    "BulkByScrollResponse"
)

for api in "${es_apis[@]}"; do
    if grep -q "$api" "$REINDEX_JOB"; then
        echo "✅ ES API: $api"
    else
        echo "❌ 缺失ES API: $api"
    fi
done

echo

# 5. 检查方法定义
echo "5. 检查方法定义..."

key_methods=(
    "esColdDataReindex"
    "processColdDataReindex"
    "ensureColdIndexExists"
    "executeReindex"
    "deleteOriginalColdData"
    "buildQueryBuilder"
    "addOrderBusinessStatusFilter"
    "addSubWaybillBusinessStatusFilter"
)

for method in "${key_methods[@]}"; do
    if grep -q "private.*$method\|public.*$method" "$REINDEX_JOB"; then
        echo "✅ 方法: $method"
    else
        echo "❌ 缺失方法: $method"
    fi
done

echo

# 6. 检查注解
echo "6. 检查注解..."

annotations=(
    "@Slf4j"
    "@Component"
    "@RequiredArgsConstructor"
    "@XxlJob"
)

for annotation in "${annotations[@]}"; do
    if grep -q "$annotation" "$REINDEX_JOB"; then
        echo "✅ 注解: $annotation"
    else
        echo "❌ 缺失注解: $annotation"
    fi
done

echo

# 7. 检查依赖注入
echo "7. 检查依赖注入..."

dependencies=(
    "RestHighLevelClient"
    "ColdDataOfflineProperties"
    "EsIndexProperties"
    "ColdDataIndexService"
)

for dep in "${dependencies[@]}"; do
    if grep -q "private final.*$dep" "$REINDEX_JOB"; then
        echo "✅ 依赖: $dep"
    else
        echo "❌ 缺失依赖: $dep"
    fi
done

echo

# 8. 检查业务逻辑
echo "8. 检查业务逻辑..."

# 检查订单状态过滤
if grep -q "status.*coldDataProperties.getOrderCompletedStatus" "$REINDEX_JOB"; then
    echo "✅ 订单状态过滤逻辑"
else
    echo "❌ 订单状态过滤逻辑缺失"
fi

# 检查子运单状态过滤
if grep -q "subWaybillStatus.*coldDataProperties.getSubWaybillCompletedStatus" "$REINDEX_JOB"; then
    echo "✅ 子运单状态过滤逻辑"
else
    echo "❌ 子运单状态过滤逻辑缺失"
fi

# 检查签收时间过滤
if grep -q "ownerSignTime" "$REINDEX_JOB"; then
    echo "✅ 签收时间过滤逻辑"
else
    echo "❌ 签收时间过滤逻辑缺失"
fi

# 检查结算状态过滤
if grep -q "completeStatus" "$REINDEX_JOB" && grep -q "completeDownStatus" "$REINDEX_JOB"; then
    echo "✅ 结算状态过滤逻辑"
else
    echo "❌ 结算状态过滤逻辑缺失"
fi

echo

# 9. 检查可能的编译错误
echo "9. 检查可能的编译错误..."

# 检查未闭合的字符串
unclosed_strings=$(grep -n "\"[^\"]*$" "$REINDEX_JOB" | grep -v "//" | head -5)
if [ -z "$unclosed_strings" ]; then
    echo "✅ 没有未闭合的字符串"
else
    echo "❌ 可能的未闭合字符串:"
    echo "$unclosed_strings"
fi

# 检查缺少分号的语句
missing_semicolons=$(grep -n "[^;{}]$" "$REINDEX_JOB" | grep -v "^\s*//\|^\s*\*\|^\s*@\|^\s*}\|^\s*{\|^\s*$" | head -5)
if [ -z "$missing_semicolons" ]; then
    echo "✅ 语句结束符正常"
else
    echo "⚠️  可能缺少分号的语句:"
    echo "$missing_semicolons"
fi

echo

# 10. 生成修复建议
echo "10. 修复建议..."

echo "如果仍有编译错误，请检查:"
echo "1. ES客户端版本兼容性"
echo "2. Maven依赖是否完整"
echo "3. Java版本是否支持"
echo "4. IDE缓存是否需要清理"

echo
echo "常用修复命令:"
echo "mvn clean compile                    # 清理并编译"
echo "mvn dependency:resolve               # 解析依赖"
echo "mvn dependency:tree                  # 查看依赖树"

echo

# 11. 检查文件完整性
echo "11. 检查文件完整性..."

total_lines=$(wc -l < "$REINDEX_JOB")
echo "文件总行数: $total_lines"

if [ "$total_lines" -gt 400 ]; then
    echo "✅ 文件大小正常"
else
    echo "⚠️  文件可能不完整"
fi

# 检查类定义
if grep -q "public class EsColdDataReindexJob" "$REINDEX_JOB"; then
    echo "✅ 类定义正确"
else
    echo "❌ 类定义错误"
fi

# 检查包声明
if grep -q "^package com.wzc.be.es.adapter.job;" "$REINDEX_JOB"; then
    echo "✅ 包声明正确"
else
    echo "❌ 包声明错误"
fi

echo
echo "=== 检查完成 ==="
echo "结束时间: $(date)"

echo
echo "📋 总结:"
echo "如果所有检查项都显示 ✅，则代码应该可以正常编译"
echo "如果有 ❌ 项目，请根据提示进行修复"
echo "如果仍有问题，请提供具体的错误信息"
