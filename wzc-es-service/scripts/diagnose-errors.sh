#!/bin/bash

# ES冷数据Reindex代码错误诊断脚本
# 详细检查可能的编译和运行时错误

echo "=== ES冷数据Reindex错误诊断 ==="
echo "开始时间: $(date)"
echo

# 1. 检查Java版本兼容性
echo "1. 检查Java环境..."
if command -v java &> /dev/null; then
    java_version=$(java -version 2>&1 | head -n 1)
    echo "Java版本: $java_version"
    
    # 检查是否支持Java 8+
    if java -version 2>&1 | grep -q "1\.[8-9]\|1[0-9]\|[2-9][0-9]"; then
        echo "✅ Java版本兼容"
    else
        echo "❌ Java版本过低，需要Java 8+"
    fi
else
    echo "❌ 未找到Java环境"
fi

echo

# 2. 检查Maven依赖
echo "2. 检查Maven依赖..."
if [ -f "pom.xml" ]; then
    echo "✅ pom.xml 存在"
    
    # 检查关键依赖
    key_dependencies=(
        "elasticsearch-rest-high-level-client"
        "xxl-job-core"
        "lombok"
        "spring-boot-starter"
    )
    
    for dep in "${key_dependencies[@]}"; do
        if grep -q "$dep" pom.xml; then
            echo "✅ 依赖: $dep"
        else
            echo "❌ 缺失依赖: $dep"
        fi
    done
else
    echo "❌ pom.xml 不存在"
fi

echo

# 3. 检查具体的编译错误
echo "3. 检查具体编译错误..."
REINDEX_JOB="src/main/java/com/wzc/be/es/adapter/job/EsColdDataReindexJob.java"

# 检查可能的语法错误
echo "检查常见语法错误..."

# 检查未闭合的字符串
if grep -n "\"[^\"]*$" "$REINDEX_JOB" | grep -v "//"; then
    echo "❌ 发现未闭合的字符串"
else
    echo "✅ 字符串闭合正常"
fi

# 检查未闭合的括号
open_parens=$(grep -o "(" "$REINDEX_JOB" | wc -l)
close_parens=$(grep -o ")" "$REINDEX_JOB" | wc -l)
if [ "$open_parens" -eq "$close_parens" ]; then
    echo "✅ 圆括号匹配: $open_parens 对"
else
    echo "❌ 圆括号不匹配: 开 $open_parens, 闭 $close_parens"
fi

# 检查未闭合的大括号
open_braces=$(grep -o "{" "$REINDEX_JOB" | wc -l)
close_braces=$(grep -o "}" "$REINDEX_JOB" | wc -l)
if [ "$open_braces" -eq "$close_braces" ]; then
    echo "✅ 大括号匹配: $open_braces 对"
else
    echo "❌ 大括号不匹配: 开 $open_braces, 闭 $close_braces"
fi

echo

# 4. 检查import冲突
echo "4. 检查import冲突..."

# 检查重复import
duplicate_imports=$(sort "$REINDEX_JOB" | grep "^import" | uniq -d)
if [ -z "$duplicate_imports" ]; then
    echo "✅ 没有重复import"
else
    echo "❌ 发现重复import:"
    echo "$duplicate_imports"
fi

# 检查通配符import
wildcard_imports=$(grep "import.*\*" "$REINDEX_JOB")
if [ -z "$wildcard_imports" ]; then
    echo "✅ 没有通配符import"
else
    echo "❌ 发现通配符import:"
    echo "$wildcard_imports"
fi

echo

# 5. 检查类路径问题
echo "5. 检查类路径问题..."

# 检查包声明
package_line=$(head -n 10 "$REINDEX_JOB" | grep "^package")
if [ -n "$package_line" ]; then
    echo "✅ 包声明: $package_line"
    
    # 检查文件路径是否与包名匹配
    expected_path=$(echo "$package_line" | sed 's/package //; s/;//; s/\./\//g')
    actual_path=$(dirname "$REINDEX_JOB" | sed 's|src/main/java/||')
    
    if [ "$expected_path" = "$actual_path" ]; then
        echo "✅ 文件路径与包名匹配"
    else
        echo "❌ 文件路径不匹配: 期望 $expected_path, 实际 $actual_path"
    fi
else
    echo "❌ 缺少包声明"
fi

echo

# 6. 检查方法签名问题
echo "6. 检查方法签名问题..."

# 检查方法返回类型
methods_with_issues=$(grep -n "public.*(" "$REINDEX_JOB" | grep -v "ReturnT\|void\|int\|long\|boolean\|String")
if [ -z "$methods_with_issues" ]; then
    echo "✅ 方法返回类型正常"
else
    echo "⚠️  可能的方法签名问题:"
    echo "$methods_with_issues"
fi

echo

# 7. 检查注解问题
echo "7. 检查注解问题..."

# 检查Lombok注解
lombok_annotations=("@Slf4j" "@RequiredArgsConstructor" "@Getter" "@Setter")
for annotation in "${lombok_annotations[@]}"; do
    if grep -q "$annotation" "$REINDEX_JOB"; then
        echo "✅ Lombok注解: $annotation"
    fi
done

# 检查Spring注解
spring_annotations=("@Component" "@Service" "@Autowired")
for annotation in "${spring_annotations[@]}"; do
    if grep -q "$annotation" "$REINDEX_JOB"; then
        echo "✅ Spring注解: $annotation"
    fi
done

echo

# 8. 检查ES客户端版本兼容性
echo "8. 检查ES客户端版本兼容性..."

# 检查使用的ES API
es_apis=(
    "RestHighLevelClient"
    "ReindexRequest"
    "DeleteByQueryRequest"
    "BulkByScrollResponse"
    "CreateIndexRequest"
)

for api in "${es_apis[@]}"; do
    if grep -q "$api" "$REINDEX_JOB"; then
        echo "✅ ES API: $api"
    else
        echo "❌ 缺失ES API: $api"
    fi
done

echo

# 9. 检查配置类问题
echo "9. 检查配置类问题..."

CONFIG_FILE="src/main/java/com/wzc/be/es/common/properties/ColdDataOfflineProperties.java"
if [ -f "$CONFIG_FILE" ]; then
    echo "✅ 配置类存在"
    
    # 检查@ConfigurationProperties注解
    if grep -q "@ConfigurationProperties" "$CONFIG_FILE"; then
        echo "✅ @ConfigurationProperties注解存在"
    else
        echo "❌ 缺少@ConfigurationProperties注解"
    fi
    
    # 检查@Component注解
    if grep -q "@Component" "$CONFIG_FILE"; then
        echo "✅ @Component注解存在"
    else
        echo "❌ 缺少@Component注解"
    fi
else
    echo "❌ 配置类不存在"
fi

echo

# 10. 生成错误修复建议
echo "10. 生成错误修复建议..."

echo "常见错误修复方法:"
echo "1. 清理重复import: 删除重复的import语句"
echo "2. 检查依赖版本: 确保ES客户端版本兼容"
echo "3. 重新编译: mvn clean compile"
echo "4. 清理IDE缓存: 重启IDE并清理缓存"
echo "5. 检查Java版本: 确保使用Java 8+"

echo
echo "如果仍有错误，请提供具体的错误信息:"
echo "- 编译错误信息"
echo "- IDE错误提示"
echo "- 运行时异常堆栈"

echo

# 11. 尝试简单编译测试
echo "11. 尝试简单编译测试..."

if command -v javac &> /dev/null; then
    echo "尝试编译单个文件..."
    
    # 创建临时的简化版本进行测试
    temp_file="/tmp/SimpleTest.java"
    cat > "$temp_file" << 'EOF'
import java.util.List;
import java.util.ArrayList;

public class SimpleTest {
    private final List<String> items = new ArrayList<>();
    
    public void test() {
        System.out.println("Test compilation");
    }
}
EOF
    
    if javac "$temp_file" 2>/dev/null; then
        echo "✅ 基本Java编译正常"
        rm -f "/tmp/SimpleTest.class"
    else
        echo "❌ 基本Java编译失败"
    fi
    
    rm -f "$temp_file"
else
    echo "⚠️  javac命令不可用，跳过编译测试"
fi

echo
echo "=== 诊断完成 ==="
echo "结束时间: $(date)"

echo
echo "📋 诊断总结:"
echo "如果所有检查都显示正常，但仍有编译错误，可能的原因:"
echo "1. IDE缓存问题 - 重启IDE并清理项目"
echo "2. Maven依赖问题 - 执行 mvn clean install"
echo "3. 版本兼容性问题 - 检查ES客户端版本"
echo "4. 环境配置问题 - 检查Java版本和CLASSPATH"
echo
echo "请提供具体的错误信息以便进一步诊断！"
