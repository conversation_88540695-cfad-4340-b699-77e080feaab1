#!/bin/bash

# 检查ES冷数据Reindex代码编译错误

echo "=== 检查编译错误 ==="
echo "开始时间: $(date)"
echo

# 1. 检查Java语法错误
echo "1. 检查Java语法错误..."
REINDEX_JOB="src/main/java/com/wzc/be/es/adapter/job/EsColdDataReindexJob.java"

# 检查是否有未闭合的括号
echo "检查括号匹配..."
if ! grep -q "^}" "$REINDEX_JOB"; then
    echo "❌ 可能存在未闭合的括号"
else
    echo "✅ 括号匹配正常"
fi

# 检查是否有未完成的语句
echo "检查语句完整性..."
if grep -q ";\s*$" "$REINDEX_JOB"; then
    echo "✅ 语句结束符正常"
else
    echo "⚠️  可能存在未完成的语句"
fi

echo

# 2. 检查import语句
echo "2. 检查import语句..."
echo "当前import数量: $(grep -c "^import" "$REINDEX_JOB")"

# 检查关键的import
key_imports=(
    "org.elasticsearch.index.reindex.ReindexRequest"
    "org.elasticsearch.index.reindex.BulkByScrollResponse"
    "org.elasticsearch.index.reindex.DeleteByQueryRequest"
    "org.elasticsearch.action.admin.indices.create.CreateIndexRequest"
    "org.elasticsearch.client.indices.GetIndexResponse"
    "org.elasticsearch.cluster.metadata.MappingMetadata"
    "com.xxl.job.core.handler.annotation.XxlJob"
    "lombok.RequiredArgsConstructor"
    "lombok.extern.slf4j.Slf4j"
)

missing_imports=()
for import in "${key_imports[@]}"; do
    if grep -q "import $import" "$REINDEX_JOB"; then
        echo "✅ $import"
    else
        echo "❌ 缺失: $import"
        missing_imports+=("$import")
    fi
done

echo

# 3. 检查类和方法定义
echo "3. 检查类和方法定义..."

# 检查类定义
if grep -q "public class EsColdDataReindexJob" "$REINDEX_JOB"; then
    echo "✅ 类定义正确"
else
    echo "❌ 类定义错误"
fi

# 检查关键方法
key_methods=(
    "esColdDataReindex"
    "processColdDataReindex"
    "ensureColdIndexExists"
    "executeReindex"
    "deleteOriginalColdData"
    "buildQueryBuilder"
    "addOrderBusinessStatusFilter"
    "addSubWaybillBusinessStatusFilter"
)

missing_methods=()
for method in "${key_methods[@]}"; do
    if grep -q "private.*$method\|public.*$method" "$REINDEX_JOB"; then
        echo "✅ 方法: $method"
    else
        echo "❌ 缺失方法: $method"
        missing_methods+=("$method")
    fi
done

echo

# 4. 检查依赖注入
echo "4. 检查依赖注入..."
dependencies=(
    "RestHighLevelClient restHighLevelClient"
    "ColdDataOfflineProperties coldDataProperties"
    "EsIndexProperties esIndexProperties"
    "ColdDataIndexService coldDataIndexService"
)

for dep in "${dependencies[@]}"; do
    if grep -q "private final.*$dep" "$REINDEX_JOB"; then
        echo "✅ 依赖: $dep"
    else
        echo "❌ 缺失依赖: $dep"
    fi
done

echo

# 5. 检查注解
echo "5. 检查注解..."
annotations=(
    "@Slf4j"
    "@Component"
    "@RequiredArgsConstructor"
    "@XxlJob"
)

for annotation in "${annotations[@]}"; do
    if grep -q "$annotation" "$REINDEX_JOB"; then
        echo "✅ 注解: $annotation"
    else
        echo "❌ 缺失注解: $annotation"
    fi
done

echo

# 6. 检查配置属性方法调用
echo "6. 检查配置属性方法调用..."
config_methods=(
    "getEnabled"
    "getRetentionMonths"
    "getOrderCompletedStatus"
    "getSubWaybillCompletedStatus"
    "getSubWaybillSettlementCompletedStatus"
    "getAutoOptimizeColdIndex"
    "getAutoCreateColdAlias"
)

for method in "${config_methods[@]}"; do
    if grep -q "coldDataProperties\.$method" "$REINDEX_JOB"; then
        echo "✅ 配置方法: $method"
    else
        echo "❌ 未使用配置方法: $method"
    fi
done

echo

# 7. 生成修复建议
echo "7. 生成修复建议..."

if [ ${#missing_imports[@]} -gt 0 ]; then
    echo "需要添加的import语句:"
    for import in "${missing_imports[@]}"; do
        echo "  import $import;"
    done
    echo
fi

if [ ${#missing_methods[@]} -gt 0 ]; then
    echo "需要实现的方法:"
    for method in "${missing_methods[@]}"; do
        echo "  - $method"
    done
    echo
fi

# 8. 检查其他服务类
echo "8. 检查其他服务类..."
SERVICE_FILE="src/main/java/com/wzc/be/es/adapter/service/ColdDataIndexService.java"
if [ -f "$SERVICE_FILE" ]; then
    echo "✅ ColdDataIndexService.java 存在"
    
    # 检查关键方法
    service_methods=(
        "generateColdIndexName"
        "indexExists"
        "optimizeColdIndexSettings"
        "createColdDataAlias"
    )
    
    for method in "${service_methods[@]}"; do
        if grep -q "public.*$method" "$SERVICE_FILE"; then
            echo "✅ 服务方法: $method"
        else
            echo "❌ 缺失服务方法: $method"
        fi
    done
else
    echo "❌ ColdDataIndexService.java 不存在"
fi

echo

# 9. 总结
echo "9. 总结..."
if [ ${#missing_imports[@]} -eq 0 ] && [ ${#missing_methods[@]} -eq 0 ]; then
    echo "✅ 代码结构完整，应该可以编译通过"
else
    echo "❌ 发现 ${#missing_imports[@]} 个缺失import，${#missing_methods[@]} 个缺失方法"
    echo "需要修复后才能编译通过"
fi

echo
echo "=== 检查完成 ==="
echo "结束时间: $(date)"
