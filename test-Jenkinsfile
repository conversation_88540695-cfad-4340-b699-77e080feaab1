pipeline{
  agent{
    node {
      label 'wzc-jnlp11'
    }
  }
  environment {
    BUILD_DATE = sh(script: 'date +%Y%m%d', returnStdout: true).trim()

  }
  stages{
    stage('Prepare') {
      steps{
        //checkout scm
        script {
          echo "Prepare Stage"
          BUILD_TAG = sh(returnStdout: true, script: 'git rev-parse --short HEAD').trim()
          BUILD_TAG = "${BUILD_TAG}_${BUILD_TIMESTAMP}"
          BRANCH = sh(script: "echo ${branch}|sed -e 's#origin/##'|sed -e 's#_#-#'", returnStdout: true).trim()
          echo "${BUILD_TAG}"
          echo "${BRANCH}"
        }
      }
    }

    stage('Build') {
      steps{
        script {
          withCredentials([usernamePassword(credentialsId: 'ea382ef7-1157-4ccc-9c61-1b2e80a49dba', passwordVariable: 'dockerHubPassword', usernameVariable: 'dockerHubUser')]){
            sh "mvn -U -B -f pom.xml clean package -am -Dmaven.test.skip=true -P test-cloud"
            // sh "mvn -U -B -f pom.xml clean package -am -pl *指定目录 -Dmaven.test.skip=true"
            sh "docker login iregistry.baidu-int.com -u ${dockerHubUser} -p ${dockerHubPassword}"
            sh "sed -i 's/<ENV>/${ENV}/' ./${APP}-Dockerfile"
            sh "sed -i 's/<APP>/${APP}/' ./${APP}-Dockerfile"
            sh "docker build -t iregistry.baidu-int.com/${ENV}/${APP}:${BUILD_TAG} -f ./${APP}-Dockerfile ."

          }
        }
      }
    }

    stage('Push image') {
      steps{
        script {
          withCredentials([usernamePassword(credentialsId: 'ea382ef7-1157-4ccc-9c61-1b2e80a49dba', passwordVariable: 'dockerHubPassword', usernameVariable: 'dockerHubUser')]){
            sh "docker login iregistry.baidu-int.com -u ${dockerHubUser} -p ${dockerHubPassword}"
            sh "docker push iregistry.baidu-int.com/${ENV}/${APP}:${BUILD_TAG}"
            sh "docker rmi iregistry.baidu-int.com/${ENV}/${APP}:${BUILD_TAG}"

          }
        }
      }
    }

    stage('Deploy') {
      steps{
        catchError(buildResult: 'SUCCESS', stageResult: 'SUCCESS') {
          script {
            sh "sed -i 's/<BUILD_TAG>/${BUILD_TAG}/' test-k8s.yaml"
            //sh "sed -i 's/<MAIN>/${MAIN}/' test-k8s.yaml"
            //sh "sed -i 's/<BRANCH>/${BRANCH}/' test-k8s.yaml"
            sh "sed -i 's/<ENV>/${ENV}/' test-k8s.yaml"
            sh "sed -i 's/<APP>/${APP}/' test-k8s.yaml"

            if ( MAIN == 'false' ) {
              sh "sed -i 's/<RELEASE_NAME>/${BRANCH}/' test-k8s.yaml"
              sh "sed -i 's/replicas:.*/replicas: 1/' test-k8s.yaml"
              sh "cat test-k8s.yaml"
              sh "kubectl apply -f test-k8s.yaml"
            }
            else if ( MAIN == 'true' ){
              sh "sed -i 's/-<RELEASE_NAME>//' test-k8s.yaml"
              sh "sed -i 's/<RELEASE_NAME>/main/' test-k8s.yaml"
              sh "sed -i 's/<REPLICAS>/${REPLICAS}/' test-k8s.yaml"
              sh "cat test-k8s.yaml"
              sh "kubectl apply -f test-k8s.yaml"

              currentBuild.result = 'SUCCESS'
              error("中断Pipeline的执行")


            }
            else {
              sh "sed -i 's/<RELEASE_NAME>//' test-k8s.yaml"
              sh "cat test-k8s.yaml"
              sh "kubectl apply -f test-k8s.yaml"
              currentBuild.result = 'SUCCESS'
              error("中断Pipeline的执行")

            }
          }
        }
      }
    }

  }
  post {
    always {
      echo "Pipeline执行完成"
    }
  }
}
